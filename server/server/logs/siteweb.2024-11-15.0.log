2024-11-15 11:30:04.930 [main] INFO  org.siteweb.config.server.Application - Starting Application using Java 17.0.9 with PID 35120 (D:\IdeaProjects\siteweb6-config-server\server\server\target\classes started by Peng.Shixin.Habits in D:\IdeaProjects\siteweb6-config-server\server\server)
2024-11-15 11:30:04.930 [main] INFO  org.siteweb.config.server.Application - The following 1 profile is active: "mysql"
2024-11-15 11:30:11.192 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.CConfigChangeMicroLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.473 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblCategoryIdMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.494 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblCommandBaseMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.510 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblConfigChangeDefine ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.529 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblConfigChangeMicroLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.548 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblControlBaseConfirm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.566 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblControlLogAction ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.692 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblEquipmentCMCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.717 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblEquipmentCUCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.736 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblEquipmentProjectInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.755 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblEventBaseConfirm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.776 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblEventBaseMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.887 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblOperationDetail ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.903 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblPrimaryKeyIdentity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.940 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblRoomCMCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.964 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblSignalBaseConfirm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:11.985 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblSignalBaseMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.048 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStandardBack ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.081 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStandardDicControl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.107 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStandardDicEvent ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.131 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStandardDicSig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.151 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStationBaseMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.217 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStationStructureMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.264 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStatusBaseDic ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.315 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblSysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.358 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TslMonitorUnitCMCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.405 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TslMonitorUnitCUCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.424 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TslMonitorUnitEvent ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.698 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblDoorCard ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.714 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblDoor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.728 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblDoorTimeGroup ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:12.863 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblLogicClassEntry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:13.699 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStandardType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.130 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.DbVersionRecord ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.171 [main] DEBUG org.siteweb.config.primary.InitManager - Data cache initialization starts
2024-11-15 11:30:14.181 [pool-2-thread-4] DEBUG org.siteweb.config.primary.InitManager - Data Cache ResourceStructureTypeCache is loaded, records: 15, Use 8.8921ms
2024-11-15 11:30:14.187 [pool-2-thread-1] DEBUG org.siteweb.config.primary.InitManager - Data Cache ConfigChangeDefineCache is loaded, records: 29, Use 14.8914ms
2024-11-15 11:30:14.195 [pool-2-thread-3] DEBUG org.siteweb.config.primary.InitManager - Data Cache ResourceStructureCache is loaded, records: 104, Use 23.5342ms
2024-11-15 11:30:14.274 [pool-2-thread-2] DEBUG org.siteweb.config.primary.InitManager - Data Cache DataItemCache is loaded, records: 153, Use 102.8298ms
2024-11-15 11:30:14.311 [main] INFO  org.siteweb.config.primary.InitManager - Data initialization is complete, Use 138.497ms
2024-11-15 11:30:14.339 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblDeviceSubTypeCMCC ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.492 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblLogInformList ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.691 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblStationSwatchMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.704 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.BizExpStationsMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.720 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.BizExpEquSignalsMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.784 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblConfigChangeMacroLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:14.842 [main] WARN  o.s.c.c.config.InsertBatchSqlInjector - class org.siteweb.config.common.entity.TblConfigChangeMap ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2024-11-15 11:30:16.001 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-15 11:30:16.003 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2024-11-15 11:30:16.024 [main] INFO  org.siteweb.config.server.Application - Started Application in 11.711 seconds (process running for 13.427)
