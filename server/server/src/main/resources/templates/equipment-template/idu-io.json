<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="设备模板列表">
<EquipmentTemplate EquipmentTemplateId="755000013" ParentTemplateId="0" EquipmentTemplateName="IDU-IO设备" ProtocolCode="IDU-IO6-00" EquipmentCategory="51" EquipmentType="1" Memo="tieta" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
<Signals Name="模板信号">
<Signal SignalId="510000141" SignalName="I2C湿度" SignalCategory="1" SignalType="1" ChannelNo="13" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="%RH" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000131" SignalName="I2C温度" SignalCategory="1" SignalType="1" ChannelNo="12" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="28800" AbsValueThreshold="5" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000340" SignalName="灯控状态" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:灯关;1:灯开" />
<Signal SignalId="510000111" SignalName="电池1总电压" SignalCategory="1" SignalType="1" ChannelNo="10" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000121" SignalName="电池2总电压" SignalCategory="1" SignalType="1" ChannelNo="11" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000280" SignalName="红外" SignalCategory="2" SignalType="1" ChannelNo="19" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:有告警;1:无告警" />
<Signal SignalId="510000350" SignalName="继电器2状态" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
<Signal SignalId="510000370" SignalName="继电器4状态" SignalCategory="2" SignalType="1" ChannelNo="27" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
<Signal SignalId="510000270" SignalName="门磁" SignalCategory="2" SignalType="1" ChannelNo="18" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004007001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:有告警;1:无告警" />
<Signal SignalId="510000061" SignalName="门锁状态-CH6" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000360" SignalName="门锁状态-DO3" SignalCategory="2" SignalType="1" ChannelNo="26" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:失电;1:得电" />
<Signal SignalId="510000011" SignalName="模拟输入01值" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000021" SignalName="模拟输入02值" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000031" SignalName="模拟输入03值" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000041" SignalName="模拟输入04值" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000051" SignalName="模拟输入05值" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000071" SignalName="模拟输入07值" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000081" SignalName="模拟输入08值" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000091" SignalName="模拟输入09值" SignalCategory="1" SignalType="1" ChannelNo="8" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="510000101" SignalName="模拟输入10值" SignalCategory="1" SignalType="1" ChannelNo="9" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
<Signal SignalId="-3" SignalName="设备通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004999001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:通讯异常;1:通讯正常" />
<Signal SignalId="510000260" SignalName="水浸" SignalCategory="2" SignalType="1" ChannelNo="17" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
<Signal SignalId="510000250" SignalName="烟感" SignalCategory="2" SignalType="1" ChannelNo="16" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无告警;1:有告警" />
</Signals>
<Events Name="模板事件">
<Event EventId="-3" EventName="设备通讯状态" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="通讯异常" EquipmentState="" BaseTypeId="1004999001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000250" EventName="烟感" EventCategory="5" StartType="1" EndType="3" StartExpression="[-1,510000250]" SuppressExpression="" SignalId="510000250" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="1004006001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000260" EventName="水浸" EventCategory="37" StartType="1" EndType="3" StartExpression="[-1,510000260]" SuppressExpression="" SignalId="510000260" Enable="True" Visible="True" Description="" DisplayIndex="17" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="1004005001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000270" EventName="门磁" EventCategory="35" StartType="1" EndType="3" StartExpression="[-1,510000270]" SuppressExpression="" SignalId="510000270" Enable="True" Visible="True" Description="" DisplayIndex="18" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="1004007001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000280" EventName="红外" EventCategory="36" StartType="1" EndType="3" StartExpression="[-1,510000280]" SuppressExpression="" SignalId="510000280" Enable="True" Visible="True" Description="" DisplayIndex="19" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="2" BaseTypeId="1004008001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000281" EventName="电池1总电压" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000111]" SuppressExpression="" SignalId="510000111" Enable="True" Visible="True" Description="" DisplayIndex="20" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过高" EquipmentState="" BaseTypeId="1101370001" StandardName="" />
<EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过低" EquipmentState="" BaseTypeId="1101170001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000282" EventName="电池2总电压" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000121]" SuppressExpression="" SignalId="510000121" Enable="True" Visible="True" Description="" DisplayIndex="21" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过高" EquipmentState="" BaseTypeId="1101370002" StandardName="" />
<EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="电压过低" EquipmentState="" BaseTypeId="1101170002" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000283" EventName="I2C温度" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000131]" SuppressExpression="" SignalId="510000131" Enable="True" Visible="True" Description="" DisplayIndex="22" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="32" StartDelay="0" EndOperation="&gt;" EndCompareValue="36" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="温度过高" EquipmentState="" BaseTypeId="1004001001" StandardName="" />
<EventCondition EventConditionId="1" EventSeverity="3" StartOperation="&gt;" StartCompareValue="36" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="温度超高" EquipmentState="" BaseTypeId="1004307001" StandardName="" />
<EventCondition EventConditionId="2" EventSeverity="1" StartOperation="&lt;" StartCompareValue="16" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="温度过低" EquipmentState="" BaseTypeId="1004002001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000284" EventName="I2C湿度" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000141]" SuppressExpression="" SignalId="510000141" Enable="True" Visible="True" Description="" DisplayIndex="23" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="75" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="湿度过高" EquipmentState="" BaseTypeId="1004003001" StandardName="" />
<EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&lt;" StartCompareValue="20" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="湿度过低" EquipmentState="" BaseTypeId="1004004001" StandardName="" />
</Conditions>
</Event>
<Event EventId="510000285" EventName="门锁状态-CH6" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000061]" SuppressExpression="" SignalId="510000061" Enable="True" Visible="True" Description="" DisplayIndex="24" ModuleNo="0">
<Conditions>
<EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&lt;" StartCompareValue="5" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="有告警" EquipmentState="" BaseTypeId="1004308001" StandardName="" />
</Conditions>
</Event>
</Events>
<Controls Name="模板控制">
<Control ControlId="510000340" ControlName="灯控" ControlCategory="1" CmdToken="10,0" BaseTypeId="1004303001" ControlSeverity="1" SignalId="510000340" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:关灯;1:开灯" />
<Control ControlId="510000350" ControlName="继电器2状态" ControlCategory="1" CmdToken="11,0" BaseTypeId="" ControlSeverity="1" SignalId="510000350" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
<Control ControlId="510000360" ControlName="远程开门-DO3" ControlCategory="1" CmdToken="12,250" BaseTypeId="1004010001" ControlSeverity="1" SignalId="510000360" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="3" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
<Control ControlId="510000370" ControlName="继电器4状态" ControlCategory="1" CmdToken="13,0" BaseTypeId="" ControlSeverity="1" SignalId="510000370" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="4" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:失电;1:得电" />
</Controls>
</EquipmentTemplate>
<Samplers>
<Sampler SamplerId="755000015" SamplerName="IDUIO设备" SamplerType="18" ProtocolCode="IDU-IO6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="IDUIO.so" Setting="9600,N,8,1" Description="" />
</Samplers>
</EquipmentTemplates>