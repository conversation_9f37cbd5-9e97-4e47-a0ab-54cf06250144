spring:
  datasource:
    url: jdbc:dm://10.163.96.61:5236/SITEWEB
    username: SITEWEB
    password: siteweb@2024
    driver-class-name: dm.jdbc.driver.DmDriver
# mybatis-plus相关配置
mybatis-plus:
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置），mapper中包含不通用的sql时，把mapper xml复制出各个数据库包下，然后对应数据库修改sql
  mapper-locations: classpath:mapper/*.xml, classpath:mapper/dameng/*.xml
  configuration:
    database-id: dameng
  # 打印sql日志，开发环境使用
#  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl








