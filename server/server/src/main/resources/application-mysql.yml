spring:
  #  datasource:
  #    url: ***********************************************************************************************************************************************************************************
  #    username: root
  #    password: siteweb1!
  #    driver-class-name: com.mysql.cj.jdbc.Driver
  datasource:
    url: *************************************************************************************************************************************************************************************
    username: root
    password: siteweb1!
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver

# mybatis-plus相关配置
mybatis-plus:
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置），mapper中包含不通用的sql时，把mapper xml复制出各个数据库包下，然后对应数据库修改sql
  mapper-locations: classpath:mapper/*.xml, classpath:mapper/mysql/*.xml
  global-config:
    database-id: mysql
    dbConfig:
      columnFormat: "`%s`"







