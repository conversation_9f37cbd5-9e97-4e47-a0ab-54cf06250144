greeting.message=Hello, {0}!
uploadFile.notEmpty=The uploaded file cannot be empty
uploadFile.Illegal=Illegal file
uploadFile.exist=File already exists
uploadFile.notExist=File not found
uploadFile.fileName.noEmpty=File name cannot be empty
uploadFile.exist.changeName=File already exists, please change the file name
uploadFile.protocolType.notExist=The protocol type does not exist
center.name.empty=Please enter the monitoring center name.
ungrouped.site=Ungrouped Site
managed.site=-Managed Site
equipment.MUHOST.equipment=RMU-MUHOST Equipment
add=Add
update=UPDATE
port=Port
monitor.equipmentTemplate.name=Equipment Template Name
monitor.signal.id=Signal ID
monitor.signal.name=Signal Name
monitor.sampler.name=Sampler Name
monitor.sampler.soPath=Sampler So Path
working.state=Working State
monitor.communicationStateSignal.doNotAllowDelete=Equipment Communication Status Signal Cannot Be Deleted
monitor.equipment.communicationState=Equipment Communication State
monitor.equipment.communicationFail=Communication Fail
monitor.equipment.communicationOk=Communication Ok
event.eventName=Event Name
event.eventCategory=Event Category
event.startType=Start Type
event.endType=End Type
event.startExpression=Start Expression
event.suppressExpression=Suppress Expression
event.enable=Valid
event.visible=Visible
event.displayIndex=Display Index
event.associatedSignal=Associated Signal
event.affiliatedRectifier=Affiliated Rectifier
event.turnover=Turnover Time
event.id=Event ID
event.signalId=Event SignalId
worksite.name=Work Site Name
modify=Modify
work.site.ip=Work Site IP
work.site.work.state=Work Site Working State
monitor.equipmentTemplate.existsChild=Exists Child Template
monitor.equipmentTemplate.existsEquipmentTemplateReference=Exists Equipment Template Reference
monitor.equipmentTemplate.existsEquipmentReference=Exists Equipment Reference
monitor.equipmentTemplate.existsSamplerUnitReference=Exists Sampler Unit Reference
monitor.protocol.noExists=Protocol does not exist
delete=Delete
monitor.equipment.monitoringEquipment=Monitoring Equipment
space.alarm.state=Space Alarm State
insufficient.space.alarm=Insufficient Space Alarm
workstation.selfdiagnostic.monitor=Virtual MU for work site %s %s self-diagnosis
monitor.unit=Monitor Unit
monitor.unit.name=MU Name
port.name=Port Name
site.self.Diagnosis.Equip=Working Site Self-diagnosis Equipment
sampler.unit.name=Sampler Unit Name
diag.dacilities.room=Diagnostic Facilities'Room
equipment.name=Equipment Name
house.default.name=Default House
file.not.exist=File does not exist
invalid.structure.id=Invalid structureId\uFF1A{0}
parameter.cannot.be.null=Parameter {0} cannot be null
center.name=Center Name
center.name.special=The monitoring center name is not allowed to contain special characters
center.name.length=The monitoring center name is not allowed to exceed 128 characters
ip.format.error=The IP address format is incorrect 
center.id=Please enter the center ID
center.id.length=The center ID must be 2 or 3 digits
center.exist=The monitoring center already exists
control.add=Add Control
control.name=Control Name
existing.control.name=Original template already has control [{0}], please reselect.
not.existing.control.name=Original template does not have control [{0}], please reselect.
control.parameter.meaning=Control Parameter Meaning
control.meaning=Meanings
channel.number.exist=The channel number is already exist in the equipment,please enter a new number.
select.sampler.unit.reference=Please select a sampler unit reference
select.channel.number.reference=Please select a channel number reference
monitor.signal.expression.not.empty=Please enter signal expression.
error.duplicate.name={0} Name \u201C{1}\u201D is repeated.
error.duplicate.ipAddress={0} Ip address \u201C{1}\u201D is the same.
error.duplicate.portNo={0}Port No\u201C{1}\u201D is the same\u3002
monitor.signal.expression.across.site=Only signal of channel No. -2 can be set across site signal.
monitor.signal.expression.already.configured=Signal expression has been configured, please do not need to configure again!
monitor.signal=signal
monitor.event=event
monitor.control=control
monitor.equipment=equipment
monitor.equipmentBaseType=equipment base type
monitor.templateSignal.delete=After replacing the template, the device does not have this signal, signal ID: {0}
monitor.templateEvent.delete=After replacing the template, the device does not have this event, event ID: {0}
monitor.templateControl.delete=After replacing the template, the device does not have this control, control ID: {0}
monitor.templateSignal.add=After replacing the template, the device add this signal, signal ID: {0}
monitor.templateSignal.settingInstance=After template replacement, the device has a new signal (ID: {0}) that requires instance configuration to function
monitor.templateSignal.checkInstance=After template replacement, the device has a new signal (ID: {0}). Please verify the instance configuration is correct
monitor.templateEvent.add=After replacing the template, the device add this event, event ID: {0}
monitor.templateControl.add=After replacing the template, the device add this control, control ID: {0}
monitor.templateSignal.originalSetting=After template replacement, the original instance configuration will be used. Signal ID: {0} Original configuration.
monitor.templateEquipment.resetting=After replacing the template, the alarm filter expression needs to be reconfigured
monitor.templateSignal.signalReference=Unable to associate with the selected device template as it includes deleted signals referenced by other devices
monitor.equipmentTemplate.name.exist=The equipment template name already exists
monitor.xml.station.info=StationId:Station ID, StationName: Station Name
monitor.xml.app.name=APP Configuration
ftp.username.and.password=ftp username and password
site.web.communication=Communicate with SiteWeb
equipment.communication=Communicate with Intelligent Equipment
save.local.file=Local File Saving
controls=Control
monitor.equipmentTemplateList=Equipment Template List
monitor.xml.channel.maps=Sampler Unit Channel No
equipment.list=Equipment List
equipment.event=Equipment Event
equipment.signal=Equipment Signal
equipment.control=Equipment Control
signal.id=Signal ID
signal.signalName=Signal Name
signal.signalCategory=Signal Category
signal.signalType=Signal Type
signal.channelNo=Channel No
signal.channelType=Channel Type
signal.expression=Expression
signal.dataType=Data Type
signal.showPrecision=Show Precision
signal.unit=Unit
signal.storeInterval=Store Interval
event.absValueThreshold=Abs Value Threshold
event.percentThreshold=Percent Threshold
signal.staticsPeriod=Statics Period
signal.baseTypeId=Base Type ID
signal.enable=Enable
signal.visible=Visible
signal.chargeStoreInterVal=Charge Store InterVal
signal.chargeAbsValue=charge Abs Value
signal.displayIndex=Display Index
alarm.linkage=Alarm Linkage
business.expression.configuration=Business Expression Configuration
equipmentTemplate.equipmentTemplateName=Equipment Template Name
equipmentTemplate.memo=memo
equipmentTemplate.equipmentCategory=Equipment Category
equipmentTemplate.equipmentType=Equipment Type
equipmentTemplate.property=Property
equipmentTemplate.equipmentStyle=Equipment Style
equipmentTemplate.unit=unit
equipmentTemplate.vendor=vendor
equipmentTemplate.stationCategory=Station Category
equipmentTemplate.equipmentBaseType=equipment BaseType
monitor.equipmentInstance=Equipment Instance
monitor.equipment.doesNotExist=Equipment Info Not Found
wildcard.format=Name wildcards must contain {0}
signalBase.baseNameExt.format=An extension signal name that is not empty must contain {0}
signalBase.baseTypeId.overflow=The baseTypeId cannot be empty, the extensible signal has been used up
signalBase.isSystem.delete=The built-in signal base class cannot be deleted
signalBase.isSystem.update=The built-in signal base class cannot be updated
signalBase.baseTypeId.delete=The base class signal has been referenced by the signal and cannot be deleted
signalBase.baseTypeId.update=The base class signal has been referenced by the signal and cannot be updated
eventBase.baseNameExt.format=An extension event name that is not empty must contain {0}
eventBase.baseTypeId.overflow=The baseTypeId cannot be empty, the extensible event has been used up
eventBase.isSystem.delete=The built-in event base class cannot be deleted
eventBase.isSystem.update=The built-in event base class cannot be updated
eventBase.baseTypeId.delete=The base class event condition is already referenced by the event and cannot be deleted
eventBase.baseTypeId.update=The base class event condition is already referenced by the event and cannot be updated
commandBase.baseNameExt.format=An extension command name that is not empty must contain {0}
commandBase.baseTypeId.overflow=The baseTypeId cannot be empty, the extensible command has been used up
commandBase.isSystem.delete=The built-in command base class cannot be deleted
commandBase.isSystem.update=The built-in command base class cannot be updated
commandBase.baseTypeId.delete=Base class control has been referenced by control and cannot be deleted
commandBase.baseTypeId.update=Base class control has been referenced by control and cannot be updated
abortNumber.format=The closing number should be an integer from 1 to 999
baseNameExt.batchAdd=The baseNameExt is empty and cannot be added in batch
baseNameExt.batchUpdate=The baseNameExt is empty and cannot be update in batch
error.conflic.portno=Port {0} conflict
error.conflic.port={0}_{1}_{2}
error.conflic.portsetting=Port configuration {0} conflict
error.conflic.port.setting={0}_{1}_{2}
error.conflic.port.setting.notfound=RMU Bottom-port is not founded or port address conflict.
control.controlName=Control Name
control.controlCategory=Control Category
control.cmdToken=Command Token
control.baseTypeId=Base Control ID
control.controlSeverity=Control Severity
control.signalId=Associated Signal
control.timeOut=Timeout
control.retry=Retry
control.enable=Enable
control.visible=Visible
control.displayIndex=Display Order
control.commandType=Command Type
control.controlType=Control Type
control.dataType=Data Type
control.maxValue=Max Value
control.minValue=Min Value
control.defaultValue=Default Value
control.moduleNo=Module Number
manage.room.name=Manage Room
error.duplicate.structure.name=The same level has the same name
error.structure.has.children=The structure has children and cannot be deleted
error.structure.has.equipment=The structure has equipment and cannot be deleted
error.monitor.unit.has.equipment=The monitoring unit has equipment and cannot be deleted
monitor.signal.not.exist=Signal does not exist
monitor.signal.expression.empty=Signal expression is empty
master=Master
slave=Slave
shutdown=Shutdown
running=Running
error.structure.not.create.station=The station has not been created on the structure
batchTool.driverTemplateType.NotExists=Driver Template Type NotExists
batchTool.driverTemplate.NotExists=Driver Template NotExists
batchTool.driverTemplate.DefaultTemplateProhibitDeletion=Default Template Prohibit Deletion
batchTool.driverTemplate.DefaultTemplateFileNotExists=Default Template {0} File Not Exists

# æ¹éå·¥å·
virtualEquipment.notExist.virtualSignal=Virtual Signal Not Exits
virtualEquipment.notExist.originChannel=Origin Channel Not Exits
virtualEquipment.notExist.virtualEquipmentStation=Virtual Equipment Station Not Exits
virtualEquipment.notExist.virtualMonitUnit=Virtual MonitUnit Not Exits
virtualEquipment.notExist.sampleUnit=Virtual SampleUnit Not Exits
virtualEquipment.notExist.virtualEquipmentHouse=Virtual Equipment House Not Exits
virtualEquipment.notExist.virtualTemplate=Virtual Template Not Exits
virtualEquipment.notExist.virtualEquipmentCategory=Virtual Equipment Category Not Exits
virtualEquipment.notExist.originEquipment=Origin Equipment Category Not Exits
virtualEquipment.cannotAcross.monitUnit=Virtual devices and source devices cannot cross monitoring units
virtualEquipment.quantityTooLarge=Too many imports, please import in batches, maximum support {0}
common.msg.equipmentExists=Device name duplicate, please replace and try again
virtualEquipment.virtualTemplate=Virtual template
virtualEquipment.crossSiteVirtualTemplate=Cross-site virtual template

# è®¾å¤æä½æ¥å¿
equipment.stationId=Station
equipment.equipmentName=Equipment Name
equipment.equipmentNo=Equipment No
equipment.equipmentModule=Equipment Module
equipment.equipmentStyle=Equipment Style
equipment.assetState=Asset State
equipment.price=Price
equipment.usedLimit=Used Limit
equipment.usedDate=Used Date
equipment.buyDate=Buy Date
equipment.vendor=Vendor
equipment.unit=Unit
equipment.equipmentCategory=Equipment Category
equipment.equipmentType=Equipment Type
equipment.equipmentClass=Equipment Class
equipment.equipmentState=Equipment State
equipment.eventExpression=Event Expression
equipment.startDelay=Start Delay
equipment.endDelay=End Delay
equipment.property=Property
equipment.equipmentTemplateId=Equipment Template Id
equipment.houseId=House Id
equipment.monitorUnitId=Monitor Unit Id
equipment.workStationId=Work Station Id
equipment.parentEquipmentId=Parent Equipment Id
equipment.ratedCapacity=Rated Capacity
equipment.installedModule=Installed Module
equipment.projectName=Project Name
equipment.contractNo=Contract No
equipment.resourceStructureId=Resource Structure Id
station.name=Station Name
resource.structure.name=Structure Name
resourcestructure.resourceStructureName=Structure Name
resourcestructure.structureTypeId=Structure Type Id
station.stationName=Station Name
station.latitude=Latitude
station.longitude=Longitude
station.companyId=Company Id
station.stationCategory=Station Category
station.stationGrade=Station Grade
station.stationState=Station State
station.projectName=Project Name
station.contractNo=Contract No
monitorunit.monitorUnitName=Monitor Unit Name
monitorunit.monitorUnitCategory=Monitor Unit Category
monitorunit.ipAddress=IP Address
monitorunit.workStationId=Work Station Id
monitorunit.rdsServer=RDS Server
monitorunit.dataServer=Data Server
monitorunit.projectName=Project Name
monitorunit.contractNo=Contract No
port.portName=Port Name
port.portNo=Port No
port.portType=Port Type
port.setting=Port Setting
port.phoneNumber=Phone Number
samplerunit.samplerUnitName=Sampler Unit Name
samplerunit.address=Address
samplerunit.dllPath=Sampler Unit Dynamic Library
samplerunit.spUnitInterval=Sampling Period
samplerunit.phoneNumber=Phone Number
monitor.unit.category.empty=Monitor Unit Category cannot be empty
error.username.or.password=Username or password error
monitor.equipment.repeatName=Equipment name duplicate

# æ ååå­å¸
#ä¿¡å·æ åè¯å­å¸
standardDic.remoteControl=Remote Control
standardDic.remoteSignal=Remote Signal
standardDic.remoteMeasure=Remote Measure
standardDic.eventText=Event
standardDic.mapping.existsError=This dictionary is linked and can't be deleted
standardDic.signal=Signal Normalization Dictionary
standardDic.signalId.length=The signal ID must be a 6-digit number
StandardDicSig.equipmentLogicClass=Equipment Type
StandardDicSig.signalLogicClass=Signal Type
StandardDicSig.signalStandardName=Signal Standard Name
StandardDicSig.storeInterval=Storage Interval
StandardDicSig.absValueThreshold=Absolute Value Threshold
StandardDicSig.statisticsPeriod=Statistics Period
StandardDicSig.percentThreshold=Percentage Threshold
StandardDicSig.stationCategory=Station Category
StandardDicSig.description=Description
StandardDicSig.extendFiled1=Unit
StandardDicSig.extendFiled2=Equipment Subtype

#åè­¦æ ååå­å¸
standardDic.eventId.length=Alarm ID must be 6 digits
standardDic.event=Event Normalization Dictionary
StandardDicEvent.equipmentLogicClass=Equipment Type
StandardDicEvent.eventLogicClass=Alarm Logic Class
StandardDicEvent.eventStandardName=Standard Event Name
StandardDicEvent.signalStandardName=Alarm Standard Name
StandardDicEvent.eventClass=Alarm Logic Subclass
StandardDicEvent.eventSeverity=Alarm Severity
StandardDicEvent.compareValue=Alarm Threshold
StandardDicEvent.startDelay=Alarm Delay
StandardDicEvent.meanings=Alarm Explanation
StandardDicEvent.equipmentAffect=Impact on Equipment
StandardDicEvent.businessAffect=Impact on Business
StandardDicEvent.description=Basis for Proposal
StandardDicEvent.extendFiled1=Node Alarm Level
StandardDicEvent.extendFiled2=Base Station Alarm Level
StandardDicEvent.extendFiled3=Data Center Alarm Level
StandardDicEvent.stationCategory=Station Category

standardDic.controlId.length=Control ID must be 6 digits
standardDic.control=Control Normalization Dictionary
StandardDicControl.equipmentLogicClass=Equipment Type
StandardDicControl.controlLogicClass=Control Type
StandardDicControl.controlStandardName=Control Standard Name
StandardDicControl.stationCategory=Station Category
StandardDicControl.description=Note
StandardDicControl.extendFiled1=Unit
StandardDicControl.extendFiled2=Equipment Subtype

#ç§»å¨å®¢æ·ç±»å
cmccCustomerType.china=China Mobile
cmccCustomerType.guangDong=Guangdong Mobile
cmccCustomerType.jiangsuDong=Jiangsu Mobile

standardized.dictionary.script.sql=Standardized Dictionary Script.sql
standardized.dictionary.xml=Standardized Dictionary.xml
standardized.dictionary.table=Standardized Dictionary Table
standardized.control=Standardized Control
standardized.event=Standardized Event
standardized.signal=Standardized Signal
baseStation.house.name=Base Station
important.signal.house.name=Important Signal
house.houseName=House Name
scene.id.empty=Scene cannot be empty
monitor.equipmentTemplate.name.length=The equipment template name cannot exceed 128 characters
monitor.equipment.houseIdIsNull=Equipment house cannot be empty
monitor.equipment.stationIdIsNull=Equipment station cannot be empty
monitor.equipment.monitorUnitIdIsNull=Equipment monitoring unit cannot be empty
site.self.diagnosis.equipment=Working Site Self-diagnosis Equipment
io.equipment=IO Equipment
import.template.error=Import template error
monitor.equipment.uncategorized=Uncategorized
resource.structure.root.delete.error=Root structure cannot be deleted
dictionary.id.exists=The dictionary ID already exists
base.station=Base Station
equipment.room=Equipment Room
error.device.template.notFound=Device template not found