greeting.message=ä½ å¥½, {0}!
uploadFile.notEmpty=ä¸ä¼ çæä»¶ä¸è½ä¸ºç©º
uploadFile.Illegal=éæ³çæä»¶
uploadFile.exist=æä»¶å·²ç»å­å¨
uploadFile.notExist=æä»¶ä¸å­å¨
uploadFile.fileName.noEmpty=æä»¶åä¸è½ä¸ºç©º
uploadFile.exist.changeName=æä»¶{0}å·²ç»å­å¨,è¯·æ´æ¢æä»¶åç§°
uploadFile.protocolType.notExist=åè®®ç±»åä¸å­å¨
center.name.empty=è¯·è¾å¥çæ§ä¸­å¿åç§°
ungrouped.site=æªåç»å±ç«
managed.site=-ç®¡çå±ç«
equipment.MUHOST.equipment=RMU-MUHOSTè®¾å¤
add=æ°å¢
update=æ´æ°
port=ç«¯å£
monitor.equipmentTemplate.name=è®¾å¤æ¨¡æ¿åç§°
monitor.signal.id=ä¿¡å·id
monitor.signal.name=ä¿¡å·åç§°
monitor.sampler.name=ééå¨åç§°
monitor.sampler.soPath=ééå¨soåº
monitor.communicationStateSignal.doNotAllowDelete=è®¾å¤éè®¯ç¶æä¿¡å·ä¸åè®¸å é¤
working.state=å·¥ä½ç¶æ
monitor.equipment.communicationState=è®¾å¤éè®¯ç¶æ
monitor.equipment.communicationFail=éè®¯å¼å¸¸
monitor.equipment.communicationOk=éè®¯æ­£å¸¸
event.eventName=äºä»¶åç§°
event.eventCategory=äºä»¶ç±»å«
event.startType=å¼å§ç±»å
event.endType=ç»æç±»å
event.startExpression=å¼å§è¡¨è¾¾å¼
event.suppressExpression=æå¶è¡¨è¾¾å¼
event.enable=ææ
event.visible=å¯è§
event.displayIndex=æ¾ç¤ºé¡ºåº
event.associatedSignal=å³èä¿¡å·
event.affiliatedRectifier=æå±æ¨¡å
event.turnover=ç¿»è½¬æ¶é´
event.id=äºä»¶id
event.signalId=äºä»¶å³èä¿¡å·id
worksite.name=å·¥ä½ç«åç§°
modify=ä¿®æ¹
work.site.ip=å·¥ä½ç«IP
work.site.work.state=å·¥ä½ç«å¯ç¨ç¶æ
monitor.equipmentTemplate.existsChild=å­å¨å­æ¨¡æ¿
monitor.equipmentTemplate.existsEquipmentTemplateReference={0}å­å¨è®¾å¤æ¨¡æ¿å¼ç¨
monitor.equipmentTemplate.existsEquipmentReference={0}å­å¨è®¾å¤å¼ç¨
monitor.equipmentTemplate.existsSamplerUnitReference={0}å­å¨ééååå¼ç¨
monitor.protocol.noExists=åè®®ä¸å­å¨
delete=å é¤
monitor.equipment.monitoringEquipment=çæ§è®¾å¤
space.alarm.state=ç©ºé´åè­¦ç¶æ
insufficient.space.alarm=ç©ºé´ä¸è¶³åè­¦
workstation.selfdiagnostic.monitor=%s%så·¥ä½ç«èªè¯æ­èæçæ§åå
monitor.unit=çæ§åå
monitor.unit.name=çæ§åååç§°
port.name=ç«¯å£åç§°
site.self.Diagnosis.Equip=å·¥ä½ç«èªè¯æ­è®¾å¤
sampler.unit.name=ééåååç§°
diag.dacilities.room=èªè¯æ­è®¾å¤å®¤
equipment.name=è®¾å¤åç§°
house.default.name=é»è®¤å±æ¿
file.not.exist=æä»¶ä¸å­å¨
invalid.structure.id=æ æçå±çº§IDï¼{0}
parameter.cannot.be.null=åæ° {0} ä¸è½ä¸ºç©º
center.name=ä¸­å¿åç§°
center.name.special=çæ§ä¸­å¿åç§°ä¸åè®¸åå«ç¹æ®å­ç¬¦
center.name.length=çæ§ä¸­å¿åç§°é¿åº¦ä¸åè®¸è¶è¿128ä½
ip.format.error=IPå°åæ ¼å¼éè¯¯
center.id=è¯·è¾å¥ä¸­å¿ID
center.id.length=ä¸­å¿IDå¿é¡»ä¸º2ä½æ3ä½æ°å­
center.exist=ä¸­å¿IDå·²å­å¨
control.add=æ°å¢æ§å¶
control.name=æ§å¶åç§°
existing.control.name=åæ¨¡æ¿å·²ç»å·ææ§å¶[{0}]ï¼è¯·éæ°éæ©
not.existing.control.name=åæ¨¡æ¿ä¸å·ææ§å¶[{0}]ï¼è¯·éæ°éæ©
control.parameter.meaning=æ§å¶åæ°å«ä¹
control.meaning=å«ä¹
channel.number.exist=ééå·å¨è¯¥è®¾å¤ä¸­å·²ç»å­å¨ï¼è¯·è¾å¥æ°çééå·!
select.sampler.unit.reference=è¯·éæ©å¼ç¨çééåå
select.channel.number.reference=è¯·éæ©å¼ç¨çééå·
monitor.signal.expression.not.empty=è¯·è¾å¥ä¿¡å·è¡¨è¾¾å¼
error.duplicate.name={0}åç§°â{1}âéå¤ã
error.duplicate.ipAddress={0}Ipå°åâ{1}âéå¤ã
error.duplicate.portNo={0}ç«¯å£å·â{1}âéå¤ã
monitor.signal.expression.across.site=åªæééä¸º-2çä¿¡å·å¯ä»¥è®¾ç½®è·¨ç«ä¿¡å·
monitor.signal.expression.already.configured=å·²éç½®äºè¡¨è¾¾å¼å¼ï¼ç¡®å®éè¦éç½®è¯·åæ¸é¤ï¼
monitor.signal=ä¿¡å·
monitor.event=äºä»¶
monitor.control=æ§å¶
monitor.equipment=è®¾å¤
monitor.equipmentBaseType=è®¾å¤åºç±»
monitor.templateSignal.delete=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ æ­¤ä¿¡å·ï¼ä¿¡å·ID:{0}
monitor.templateEvent.delete=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ æ­¤äºä»¶ï¼äºä»¶ID:{0}
monitor.templateControl.delete=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ æ­¤æ§å¶ï¼æ§å¶ID:{0}
monitor.templateSignal.add=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ°å¢ä¿¡å·ï¼ä¿¡å·ID:{0}
monitor.templateSignal.settingInstance=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ°å¢ä¿¡å·ï¼å¿é¡»éç½®å®ä¾æè½ä½¿ç¨ï¼ä¿¡å·ID:{0}
monitor.templateSignal.checkInstance=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ°å¢ä¿¡å·ï¼è¯·æ£æ¥éç½®å®ä¾æ¯å¦æ­£ç¡®ï¼ä¿¡å·ID:{0}
monitor.templateEvent.add=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ°å¢äºä»¶ï¼äºä»¶ID:{0}
monitor.templateControl.add=æ¿æ¢æ¨¡æ¿åï¼è®¾å¤æ°å¢æ§å¶ï¼æ§å¶ID:{0}
monitor.templateSignal.originalSetting=æ¿æ¢æ¨¡æ¿åï¼å°ä¼ä½¿ç¨åæå®ä¾éç½®ï¼ä¿¡å·ID:{0} åå§éç½®
monitor.templateEquipment.resetting=æ¿æ¢æ¨¡æ¿åï¼åè­¦è¿æ»¤è¡¨è¾¾å¼ééæ°éç½®
monitor.templateSignal.signalReference=æéè®¾å¤æ¨¡æ¿ä¸­è¢«å é¤çä¿¡å·è¢«å«çè®¾å¤æå¼ç¨ï¼ä¸åè®¸å³èå°è¯¥æ¨¡æ¿
monitor.equipmentTemplate.name.exist=è®¾å¤æ¨¡æ¿åç§°å·²å­å¨
monitor.xml.station.info=StationId:å±ç«IDï¼StationName: å±ç«åç§°
monitor.xml.app.name=APPéç½®
ftp.username.and.password=ftpç¨æ·ååå¯ç 
site.web.communication=ä¸SiteWebéè®¯
equipment.communication=ä¸æºè½è®¾å¤éè®¯
save.local.file=æ¬å°æä»¶å­å¨
controls=æ§å¶
monitor.equipmentTemplateList=è®¾å¤æ¨¡æ¿åè¡¨
monitor.xml.channel.maps=ééååééå·
equipment.list=è®¾å¤åè¡¨
equipment.event=è®¾å¤äºä»¶
equipment.signal=è®¾å¤ä¿¡å·
equipment.control=è®¾å¤æ§å¶
signal.id=ä¿¡å·id
signal.signalName=ä¿¡å·åç§°
signal.signalCategory=ä¿¡å·ç§ç±»
signal.signalType=ä¿¡å·åç±»
signal.channelNo=ééå·
signal.channelType=ééç±»å
signal.expression=è¡¨è¾¾å¼
signal.dataType=æ°æ®ç±»å
signal.showPrecision=ç²¾åº¦
signal.unit=åä½
signal.storeInterval=å­å¨å¨æ
signal.absValueThreshold=ç»å¯¹å¼éå¼
signal.percentThreshold=ç¾åæ¯éå¼
signal.staticsPeriod=ç»è®¡å¨æ
signal.baseTypeId=åºç±»ä¿¡å·ID
signal.enable=ææ
signal.visible=å¯è§
signal.chargeStoreInterVal=çµæ± å­å¨å¨æ
signal.chargeAbsValue=çµæ± å­çéå¼
signal.displayIndex=æ¾ç¤ºé¡ºåº
alarm.linkage=åè­¦èå¨
business.expression.configuration=ä¸å¡è¡¨è¾¾å¼éç½®
equipmentTemplate.equipmentTemplateName=è®¾å¤æ¨¡æ¿åç§°
equipmentTemplate.memo=å¤æ³¨
equipmentTemplate.equipmentCategory=è®¾å¤ç§ç±»
equipmentTemplate.equipmentType=è®¾å¤ç±»å
equipmentTemplate.property=å±æ§
equipmentTemplate.equipmentStyle=è®¾å¤åå·
equipmentTemplate.unit=åä½
equipmentTemplate.vendor=åå
equipmentTemplate.stationCategory=å±ç«ç±»å
equipmentTemplate.equipmentBaseType=è®¾å¤åºç±»
monitor.equipmentInstance=è®¾å¤å®ä¾å
monitor.equipment.doesNotExist=è®¾å¤ä¿¡æ¯ä¸å­å¨
wildcard.format=åç§°ééç¬¦å¿é¡»åå«{0}
signalBase.baseNameExt.format=ä¸ç©ºçæ©å±ä¿¡å·åå¿é¡»åå«{0}
signalBase.baseTypeId.overflow=åºç±»ä¿¡å·IDä¸è½ä¸ºç©º,å¯æ©å±ä¿¡å·å·²ç»ç¨å®
signalBase.isSystem.delete=ç³»ç»åç½®ä¿¡å·åºç±»ï¼ä¸åè®¸å é¤
signalBase.isSystem.update=ç³»ç»åç½®ä¿¡å·åºç±»ï¼ä¸åè®¸ä¿®æ¹
signalBase.baseTypeId.delete=åºç±»ä¿¡å·å·²ç»è¢«ä¿¡å·å¼ç¨,ä¸å¯å é¤
signalBase.baseTypeId.update=åºç±»ä¿¡å·å·²ç»è¢«ä¿¡å·å¼ç¨,ä¸å¯ä¿®æ¹
eventBase.baseNameExt.format=ä¸ç©ºçæ©å±äºä»¶åå¿é¡»åå«{0}
eventBase.baseTypeId.overflow=åºç±»äºä»¶IDä¸è½ä¸ºç©º,å¯æ©å±äºä»¶å·²ç»ç¨å®
eventBase.isSystem.delete=ç³»ç»åç½®äºä»¶åºç±»ï¼ä¸åè®¸å é¤
eventBase.isSystem.update=ç³»ç»åç½®äºä»¶åºç±»ï¼ä¸åè®¸ä¿®æ¹
eventBase.baseTypeId.delete=åºç±»äºä»¶æ¡ä»¶å·²ç»è¢«äºä»¶å¼ç¨,ä¸å¯å é¤
eventBase.baseTypeId.update=åºç±»äºä»¶æ¡ä»¶å·²ç»è¢«äºä»¶å¼ç¨,ä¸å¯ä¿®æ¹
commandBase.baseNameExt.format=ä¸ç©ºçæ©å±æ§å¶åå¿é¡»åå«{0}
commandBase.baseTypeId.overflow=åºç±»æ§å¶IDä¸è½ä¸ºç©º,å¯æ©å±æ§å¶å·²ç»ç¨å®
commandBase.isSystem.delete=ç³»ç»åç½®æ§å¶åºç±»ï¼ä¸åè®¸å é¤
commandBase.isSystem.update=ç³»ç»åç½®æ§å¶åºç±»ï¼ä¸åè®¸ä¿®æ¹
commandBase.baseTypeId.delete=åºç±»æ§å¶å·²ç»è¢«æ§å¶å¼ç¨,ä¸å¯å é¤
commandBase.baseTypeId.update=åºç±»æ§å¶å·²ç»è¢«æ§å¶å¼ç¨,ä¸å¯ä¿®æ¹
abortNumber.format=æªæ­¢åºå·å¿é¡»æ¯1å°999ä¹é´çæ´æ°
baseNameExt.batchAdd=åç§°æ©å±è¡¨è¾¾å¼ä¸ºç©ºçä¸è½æ¹éæ°å¢
baseNameExt.batchUpdate=åç§°æ©å±è¡¨è¾¾å¼ä¸ºç©ºçä¸è½æ¹éä¿®æ¹
error.conflic.portno=ç«¯å£ {0} å²çª:
error.conflic.port={0}_{1}_{2}
error.conflic.portsetting=ç«¯å£è®¾ç½® {0} å²çª:
error.conflic.port.setting={0}_{1}_{2}
error.conflic.port.setting.notfound=æ²¡æåç°RMUä¸ç«¯å£æç«¯å£è®¾ç½®å²çª
control.controlName=æ§å¶åç§°
control.controlCategory=æ§å¶ç§ç±»
control.cmdToken=å½ä»¤æ è¯
control.baseTypeId=åºç±»æ§å¶ID
control.controlSeverity=æ§å¶éè¦æ§
control.signalId=å³èä¿¡å·
control.timeOut=è¶æ¶æ¶é´
control.retry=éè¯æ¬¡æ°
control.enable=ææ
control.visible=å¯è§
control.displayIndex=æ¾ç¤ºé¡ºåº
control.commandType=å½ä»¤ç±»å
control.controlType=æ§å¶ç±»å
control.dataType=æ°æ®ç±»å
control.maxValue=æå¤§å¼
control.minValue=æå°å¼
control.defaultValue=é»è®¤å¼
control.moduleNo=æ¨¡æ°
manage.room.name=ç®¡çæ¿é´
error.duplicate.structure.name=åä¸å±çº§ä¸å­å¨ååçå±çº§
error.structure.has.children=å±çº§ä¸å­å¨å­å±çº§ï¼ä¸åè®¸å é¤
error.structure.has.equipment=å±çº§ä¸å­å¨è®¾å¤ï¼ä¸åè®¸å é¤
error.monitor.unit.has.equipment=çæ§ååä¸å­å¨è®¾å¤ï¼ä¸åè®¸å é¤
monitor.signal.not.exist=ä¿¡å·ä¸å­å¨
monitor.signal.expression.empty=ä¿¡å·è¡¨è¾¾å¼ä¸ºç©º
master=ä¸»
slave=ä»
shutdown=åæº
running=è¿è¡
error.structure.not.create.station=å±çº§ä¸æªåå»ºå±ç«
batchTool.driverTemplateType.NotExists=é©±å¨æ¨¡æ¿ç±»åä¸å­å¨
batchTool.driverTemplate.NotExists=é©±å¨æ¨¡æ¿ä¸å­å¨
batchTool.driverTemplate.DefaultTemplateProhibitDeletion=é»è®¤æ¨¡æ¿ç¦æ­¢å é¤
batchTool.driverTemplate.DefaultTemplateFileNotExists=é©±å¨æ¨¡æ¿{0}æä»¶ä¸å­å¨

# æ¹éå·¥å·
virtualEquipment.notExist.virtualSignal=èæè®¾å¤ä¿¡å·ä¸å­å¨
virtualEquipment.notExist.originChannel=æºè®¾å¤ééå·ä¸å­å¨
virtualEquipment.notExist.virtualEquipmentStation=èæè®¾å¤å±ç«ä¸å­å¨
virtualEquipment.notExist.virtualMonitUnit=èæçæ§ååä¸å­å¨
virtualEquipment.notExist.sampleUnit=èæééååä¸å­å¨
virtualEquipment.notExist.virtualEquipmentHouse=èæè®¾å¤å±æ¿ä¸å­å¨
virtualEquipment.notExist.virtualTemplate=èææ¨¡æ¿ä¸å­å¨
virtualEquipment.notExist.virtualEquipmentCategory=èæè®¾å¤ç±»åä¸å­å¨
virtualEquipment.notExist.originEquipment=æºè®¾å¤ä¸å­å¨
virtualEquipment.cannotAcross.monitUnit=èæè®¾å¤ä¸æºè®¾å¤ä¸è½è·¨çæ§åå
virtualEquipment.quantityTooLarge=å¯¼å¥çèæè®¾å¤è¿å¤ï¼è¯·åæ¹å¯¼å¥ï¼æå¤§æ¯æ{0}ä¸ªèæè®¾å¤å¯¼å¥
common.msg.equipmentExists=ç³»ç»ä¸­å·²å­å¨ååè®¾å¤ï¼è¯·æ´æ¢åç§°éè¯
virtualEquipment.virtualTemplate=èææ¨¡æ¿
virtualEquipment.crossSiteVirtualTemplate=è·¨ç«èææ¨¡æ¿

# è®¾å¤æä½æ¥å¿
equipment.stationId=å±ç«
equipment.equipmentName=è®¾å¤åç§°
equipment.equipmentNo=è®¾å¤ç¼ç 
equipment.equipmentModule=è®¾å¤æ¨¡å
equipment.equipmentStyle=è®¾å¤åå·
equipment.assetState=èµäº§ç¶æ
equipment.price=èµäº§ä»·æ ¼
equipment.usedLimit=èµäº§å¯¿å½
equipment.usedDate=å¯ç¨æ¶é´
equipment.buyDate=è´­ä¹°æ¶é´
equipment.vendor=è®¾å¤åå
equipment.unit=åä½
equipment.equipmentCategory=è®¾å¤ç±»å
equipment.equipmentType=è®¾å¤åç±»
equipment.equipmentClass=è®¾å¤å¤§ç±»
equipment.equipmentState=è®¾å¤ç¶æ
equipment.eventExpression=åè­¦è¿æ»¤è¡¨è¾¾å¼
equipment.startDelay=å¼å§å»¶æ¶
equipment.endDelay=ç»æå»¶æ¶
equipment.property=è®¾å¤å±æ§
equipment.equipmentTemplateId=è®¾å¤æ¨¡æ¿Id
equipment.houseId=å±æ¿Id
equipment.monitorUnitId=çæ§ååId
equipment.workStationId=çæ§ä¸»æºId
equipment.parentEquipmentId=ä¸ä¸çº§è®¾å¤
equipment.ratedCapacity=é¢å®å®¹é
equipment.installedModule=å·²å®è£æ¨¡å
equipment.projectName=å·¥ç¨å
equipment.contractNo=ååå·
equipment.resourceStructureId=å±çº§ID
station.name=å±ç«åç§°
resource.structure.name=å±çº§åç§°
resourcestructure.resourceStructureName=å±çº§åç§°
resourcestructure.structureTypeId=å±çº§ç±»å
station.stationName=å±ç«åç§°
station.latitude=çº¬åº¦
station.longitude=ç»åº¦
station.companyId=ä»£ç»´å¬å¸ID
station.stationCategory=å±ç«ç±»å
station.stationGrade=å±ç«ç­çº§
station.stationState=å±ç«ç¶æ
station.projectName=å·¥ç¨å
station.contractNo=ååå·
monitorunit.monitorUnitName=çæ§åååç§°
monitorunit.monitorUnitCategory=çæ§ååç±»å
monitorunit.ipAddress=IPå°å
monitorunit.workStationId=å·¥ä½ç«ID
monitorunit.rdsServer=RDSæå¡å¨
monitorunit.dataServer=æ°æ®æå¡å¨
monitorunit.projectName=é¡¹ç®åç§°
monitorunit.contractNo=ååå·
port.portName=ç«¯å£åç§°
port.portNo=ç«¯å£å·
port.portType=ç«¯å£ç±»å
port.setting=ç«¯å£è®¾ç½®
port.phoneNumber=çµè¯å·ç 
samplerunit.samplerUnitName=ééåååç§°
samplerunit.address=å°å
samplerunit.dllPath=ééååå¨æåº
samplerunit.spUnitInterval=ééå¨æ
samplerunit.phoneNumber=çµè¯å·ç 
monitor.unit.category.empty=çæ§ååç±»åä¸è½ä¸ºç©º
error.username.or.password=ç¨æ·åæå¯ç éè¯¯
monitor.equipment.repeatName=è®¾å¤åç§°éå¤

# æ ååå­å¸
standardDic.remoteControl=é¥æ§
standardDic.remoteSignal=é¥ä¿¡
standardDic.remoteMeasure=é¥æµ
standardDic.eventText=åè­¦
standardDic.mapping.existsError=æ­¤æ ååå­å¸å·²ç»è¢«æ å°ä¸åè®¸å é¤
standardDic.signal=ä¿¡å·æ ååå­å¸
standardDic.signalId.length=ä¿¡å·idå¿é¡»ä¸º6ä½æ°
StandardDicSig.equipmentLogicClass=è®¾å¤ç±»å
StandardDicSig.signalLogicClass=ä¿¡å·éç±»å
StandardDicSig.signalStandardName=ä¿¡å·æ åå
StandardDicSig.storeInterval=å­å¨å¨æ
StandardDicSig.absValueThreshold=ç»å¯¹å¼éå¼
StandardDicSig.statisticsPeriod=ç»è®¡å¨æ
StandardDicSig.percentThreshold=ç¾åæ¯éå¼
StandardDicSig.stationCategory=ç«ç¹ç±»å«
StandardDicSig.description=æè¿°
StandardDicSig.extendFiled1=åä½
StandardDicSig.extendFiled2=è®¾å¤å­ç±»

standardDic.eventId.length=åè­¦idå¿é¡»ä¸º6ä½æ°
standardDic.event=åè­¦æ ååå­å¸
StandardDicEvent.equipmentLogicClass=è®¾å¤ç±»å
StandardDicEvent.eventLogicClass=åè­¦é»è¾åç±»ç±»
StandardDicEvent.eventStandardName=äºä»¶æ ååç§°
StandardDicEvent.signalStandardName=åè­¦æ åå
StandardDicEvent.eventClass=åè­¦é»è¾å­ç±»
StandardDicEvent.eventSeverity=åè­¦ç­çº§
StandardDicEvent.compareValue=åè­¦é¨é
StandardDicEvent.startDelay=åè­¦å»¶è¿
StandardDicEvent.meanings=åè­¦è§£é
StandardDicEvent.equipmentAffect=å¯¹è®¾å¤å½±å
StandardDicEvent.businessAffect=å¯¹ä¸å¡å½±å
StandardDicEvent.description=æåºä¾æ®
StandardDicEvent.extendFiled1=èç¹åè­¦çº§å«
StandardDicEvent.extendFiled2=åºç«åè­¦çº§å«
StandardDicEvent.extendFiled3=æ°æ®ä¸­å¿åè­¦çº§å«
StandardDicEvent.stationCategory=ç«ç¹ç±»å«

standardDic.controlId.length=æ§å¶idå¿é¡»ä¸º6ä½æ°
standardDic.control=æ§å¶æ ååå­å¸
StandardDicControl.equipmentLogicClass=è®¾å¤ç±»å
StandardDicControl.controlLogicClass=æ§å¶éç±»å
StandardDicControl.controlStandardName=æ§å¶æ åå
StandardDicControl.stationCategory=ç«ç¹ç±»å«
StandardDicControl.description=å¤æ³¨
StandardDicControl.extendFiled1=åä½
StandardDicControl.extendFiled2=è®¾å¤å­ç±»

#ç§»å¨å®¢æ·ç±»å
cmccCustomerType.china=ä¸­å½ç§»å¨
cmccCustomerType.guangDong=å¹¿ä¸ç§»å¨
cmccCustomerType.jiangsuDong=æ±èç§»å¨

standardized.dictionary.script.sql=æ ååå­å¸èæ¬.sql
standardized.dictionary.xml=æ ååå­å¸.xml
standardized.dictionary.table=æ ååå­å¸è¡¨
standardized.control=æ ååæ§å¶
standardized.event=æ åååè­¦
standardized.signal=æ ååä¿¡å·
baseStation.house.name=åºç«
important.signal.house.name=éè¦ä¿¡å·
house.houseName=å±æ¿åç§°
error.structure.has.swatch.station=å·²è¢«è®¾ç½®ä¸ºæ ·æ¿ç«
scene.id.empty=åºæ¯ä¸è½ä¸ºç©º
monitor.equipmentTemplate.name.length=è®¾å¤æ¨¡æ¿åç§°é¿åº¦ä¸åè®¸è¶è¿128ä½
monitor.equipment.houseIdIsNull=è®¾å¤å±æ¿ä¸è½ä¸ºç©º
monitor.equipment.stationIdIsNull=è®¾å¤å±ç«ä¸è½ä¸ºç©º
monitor.equipment.monitorUnitIdIsNull=è®¾å¤çæ§ååä¸è½ä¸ºç©º
site.self.diagnosis.equipment=èªè¯æ­è®¾å¤
io.equipment=IOè®¾å¤
import.template.error=å¯¼å¥æ¨¡æ¿éè¯¯
monitor.equipment.uncategorized=æªåç±»è®¾å¤æ¨¡æ¿
resource.structure.root.delete.error=ä¸åè®¸å é¤æ ¹èç¹
dictionary.id.exists=è¯¥å­å¸idå·²ç»å­å¨
base.station=åºç«
equipment.room=æºæ¿
error.device.template.notFound=è®¾å¤æ¨¡æ¿ä¸å­å¨