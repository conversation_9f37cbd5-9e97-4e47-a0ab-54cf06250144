package org.siteweb.config.server;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;


@SpringBootApplication
@EnableScheduling
@MapperScan(basePackages = {"org.siteweb.**.mapper*"})
@ComponentScan("org.siteweb*")
public class Application {


    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}