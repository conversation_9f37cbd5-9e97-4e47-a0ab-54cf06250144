package org.siteweb.config.toolkit.utils;

import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * mybatis批量处理工具
 *
 * <AUTHOR>
 * Creation Date: 2024/4/30
 */
@Component
@RequiredArgsConstructor
public class MybatisBatchUtils {

    private final SqlSessionFactory sqlSessionFactory;
    private static final int BATCH_SIZE = 1000;


    public <T, U> int batchUpdateOrInsert(List<T> data, Class<U> mapperClass, BiConsumer<T, U> function) {
        SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
        int i = 1;
        try {
            U mapper = batchSqlSession.getMapper(mapperClass);
            int size = data.size();
            for (T element : data) {
                function.accept(element, mapper);
                if ((i % BATCH_SIZE == 0) || i == size) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            // 非事务情况下提交，事务情况下不生效
            batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
        } catch (Exception e) {
            batchSqlSession.rollback();
            throw new BusinessException("batchUpdateOrInsert error ", e);
        } finally {
            batchSqlSession.close();
        }
        return i - 1;
    }
}
