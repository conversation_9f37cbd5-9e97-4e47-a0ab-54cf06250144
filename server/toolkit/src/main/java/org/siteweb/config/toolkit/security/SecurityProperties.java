//package org.siteweb.config.toolkit.security;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR> (2024-03-04)
// **/
//@Component
//@ConfigurationProperties(prefix = "security")
//@Data
//public class SecurityProperties {
//
//    private TokenProperties token;
//
//    // api 认证白名单
//    private String[] whiteList;
//}
//
//
