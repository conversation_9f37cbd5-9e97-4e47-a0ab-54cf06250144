package org.siteweb.config.toolkit.service;

import org.siteweb.config.common.entity.SystemConfig;


public interface SystemConfigService {

    SystemConfig findBySystemConfigKey(String systemConfigKey);

    SystemConfig findById(Integer systemConfigId);

    /**
     * 获取自定义token的key
     * @return {@link SystemConfig}
     */
    String findTokenHeader();


    // 新增
    void insert(SystemConfig systemConfig);

}
