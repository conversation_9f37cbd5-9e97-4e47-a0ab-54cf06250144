package org.siteweb.config.toolkit.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.utils.RestfulCode;
import org.siteweb.config.toolkit.I18n.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPOutputStream;


/**
 * Restful API 返回值帮助类
 * 当业务失败时，请保持HttpStatus为OK(200)
 *
 * <AUTHOR> (2024/3/12)
 */
@Component
public class ResponseHelper {


    private static I18n staticI18n;

    private static ObjectMapper objectMapper;

    /**
     * 构建一条成功的业务消息
     *
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> successful() {
        ResponseResult result = new ResponseResult();
        result.setCode(RestfulCode.Successful.IntValue());
        result.setData("");
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


//    public static ResponseEntity<String> compress(Object data) throws IOException {
//        ResponseResult result = new ResponseResult();
//        result.setCode(RestfulCode.Successful.IntValue());
//        result.setData(data);
//        result.setTimestamp(System.currentTimeMillis());
//        String input = objectMapper.writeValueAsString(result);
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(outputStream)) {
//            gzipOutputStream.write(input.getBytes());
//        }
//        String content = outputStream.toString(StandardCharsets.UTF_8);
//        HttpHeaders headers = new HttpHeaders();
//        headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
//        headers.add(HttpHeaders.CONTENT_ENCODING, "gzip");
//        return new ResponseEntity<>(content, headers, HttpStatus.OK);
//    }


    /**
     * 构建一条成功的业务消息
     *
     * @param data 业务数据
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> successful(Object data) {
        ResponseResult result = new ResponseResult();
        result.setCode(RestfulCode.Successful.IntValue());
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 构建一条使用I18n翻译的业务成功的消息
     * 消息仅包含Message消息但不包含DATA数据
     *
     * @param code   I18n keycode
     * @param params 格式化参数（可选）
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> successfulT(String code, Object... params) {
        ResponseResult result = new ResponseResult();
        result.setCode(RestfulCode.Successful.IntValue());
        result.setMessage(staticI18n.T(code, params));
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 构建一条使用I18n翻译的业务失败消息
     * 该消息返回HttpStatus=200
     *
     * @param code   I18n keycode
     * @param params 格式化参数（可选）
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> failedT(String code, Object... params) {
        return failed(staticI18n.T(code, params));
    }

    /***
     * 构建一条业务失败消息（将Exception填充至Message）
     * 该消息返回HttpStatus=200
     * <AUTHOR> (2024/3/12)
     * @param ex 异常对象
     */
    public static ResponseEntity<ResponseResult> failed(Exception ex) {
        return failed(ex.getMessage());
    }

    /**
     * 返回一条业务的失败消息
     * 该消息返回HttpStatus=200
     *
     * @param message 失败消息内容
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> failed(String message) {
        ResponseResult result = new ResponseResult();
        result.setCode(RestfulCode.Failed.IntValue());
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 返回一条业务的失败消息
     * @param code   自定义错误码
     * @param message 自定义错误消息
     * @return ResponseEntity<ResponseResult>
     */
    public static ResponseEntity<ResponseResult> failed(Integer code, String message) {
        ResponseResult result = new ResponseResult();
        result.setCode(code);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 构建一条自定义消息,该消息需自定义Http状态码
     * 业务层内不建议使用此方法以免造成混淆
     *
     * @param httpStatus 返回Http状态码
     * @param result     返回内容
     * <AUTHOR> (2024/3/12)
     */
    public static ResponseEntity<ResponseResult> custom(HttpStatus httpStatus, ResponseResult result) {
        return new ResponseEntity<>(result, httpStatus);
    }

    @Autowired
    private void setI18nMessageSource(I18n i18n) {
        staticI18n = i18n;
    }


    @Autowired
    private void setObjectMapperSource(ObjectMapper mapper) {
        objectMapper = mapper;
    }


}