package org.siteweb.config.toolkit.security;


import cn.hutool.core.exceptions.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.utils.RestfulCode;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2024-03-08)
 **/

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<ResponseResult> myExceptionHandler(Exception ex) {
        if (ex instanceof NoResourceFoundException) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        ResponseResult result = new ResponseResult();
        result.setCode(RestfulCode.Failed.IntValue());
        result.setMessage(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        if (ex instanceof InvalidParameterException) {
            status = HttpStatus.OK;
            // log.error(ex.getMessage());
        } else {
            log.error("发生服务异常原因:{}\r\n{}", ex.getCause(), ExceptionUtil.stacktraceToString(ex));
        }
        return ResponseHelper.custom(status, result);
    }


    /**
     * 处理自定义的业务异常
     *
     * @param e 异常
     */
    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public ResponseEntity<ResponseResult> businessExceptionHandler(BusinessException e) {
        log.error("发生业务异常！原因是：{}\r\n{} ", e.getCause(), ExceptionUtil.stacktraceToString(e));
        return ResponseHelper.failed(e);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity<ResponseResult> handleValidationExceptions(MethodArgumentNotValidException ex) {
        List<String> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)                .collect(Collectors.toList());
        String message = String.join(", ", errors);
        return ResponseHelper.failed(new BusinessException(message));
    }

}
