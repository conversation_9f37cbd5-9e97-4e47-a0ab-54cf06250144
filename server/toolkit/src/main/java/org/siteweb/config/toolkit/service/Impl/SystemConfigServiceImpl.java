package org.siteweb.config.toolkit.service.Impl;

import cn.hutool.core.collection.CollUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.config.GlobalConstants;
import org.siteweb.config.common.entity.SystemConfig;
import org.siteweb.config.common.enums.SystemConfigEnum;
import org.siteweb.config.common.mapper.SystemConfigMapper;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class SystemConfigServiceImpl implements SystemConfigService, InitializingBean {


    public String tokenHeader;

    @Autowired
    SystemConfigMapper systemConfigMapper;

    @Override
    public SystemConfig findBySystemConfigKey(String systemConfigKey) {
        return systemConfigMapper.selectOne(new QueryWrapper<SystemConfig>().eq("SystemConfigKey", systemConfigKey));
    }

    public List<SystemConfig> findBySystemConfigKeys(List<String> systemConfigKeys) {
        if (CollUtil.isEmpty(systemConfigKeys)) {
            return Collections.emptyList();
        }
        return systemConfigMapper.selectList(new QueryWrapper<SystemConfig>().in("SystemConfigKey", systemConfigKeys));
    }


    @Override
    public SystemConfig findById(Integer systemConfigId) {
        return systemConfigMapper.selectById(systemConfigId);
    }




    @Override
    public String findTokenHeader() {
        return tokenHeader;
    }

    @Override
    public void insert(SystemConfig systemConfig) {
        systemConfigMapper.insert(systemConfig);
    }

    @Override
    public void afterPropertiesSet(){
        SystemConfig systemConfig = this.findBySystemConfigKey(SystemConfigEnum.TOKEN_HEADER.getSystemConfigKey());
        if (Objects.isNull(systemConfig)) {
            tokenHeader = GlobalConstants.TOKEN_HEADER;
            return;
        }
        tokenHeader = systemConfig.getSystemConfigValue();
    }

}
