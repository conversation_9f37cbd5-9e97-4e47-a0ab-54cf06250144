//package org.siteweb.config.toolkit.config;
//
//import org.siteweb.config.toolkit.annotation.Token;
//import org.siteweb.config.toolkit.types.UserToken;
//import org.springframework.core.MethodParameter;
//import org.springframework.web.bind.support.WebDataBinderFactory;
//import org.springframework.web.context.request.NativeWebRequest;
//import org.springframework.web.context.request.RequestAttributes;
//import org.springframework.web.method.support.HandlerMethodArgumentResolver;
//import org.springframework.web.method.support.ModelAndViewContainer;
//import org.springframework.web.multipart.support.MissingServletRequestPartException;
//
//public class TokenMethodArgumentResolver implements HandlerMethodArgumentResolver {
//    @Override
//    public boolean supportsParameter(MethodParameter methodParameter) {
//        return methodParameter.getParameterType().isAssignableFrom(UserToken.class)
//                && methodParameter.hasParameterAnnotation(Token.class);
//    }
//
//    @Override
//    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
//                                  NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
//        Object value = nativeWebRequest.getAttribute("USER_TOKEN", RequestAttributes.SCOPE_REQUEST);
//        if (value != null && value.equals("")) {
//            return null;
//        }
//        if (value instanceof UserToken currentUserInfo) {
//            return currentUserInfo;
//        }
//        // 如果当前用户信息为null 则抛出异常
//        throw new MissingServletRequestPartException("currentUserInfo");
//    }
//}