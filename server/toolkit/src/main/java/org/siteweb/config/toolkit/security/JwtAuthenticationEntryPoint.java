//package org.siteweb.config.toolkit.security;
//
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.security.core.AuthenticationException;
//import org.springframework.security.web.AuthenticationEntryPoint;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//
///**
// * <AUTHOR> (2024-02-22)
// **/
//@Component
//public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {
//
//    @Override
//    public void commence(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) throws IOException, ServletException {
//
//        httpServletResponse.setContentType("application/json;charset=UTF-8");
//        httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//        httpServletResponse.getWriter().write("{'error': 'Unauthorized', 'message': 'Authentication failed'}");
////        ResultDTO resultDTO = ResultDTO.error("请先登录");
//
//        // ServletOutputStream outputStream = httpServletResponse.getOutputStream();
////        outputStream.write(JSONUtil.toJsonStr(resultDTO).getBytes(StandardCharsets.UTF_8));
//        // outputStream.flush();
//        // outputStream.close();
//    }
//}