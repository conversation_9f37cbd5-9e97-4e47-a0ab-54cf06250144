package org.siteweb.config.toolkit.security;


import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.servlet.http.HttpServletRequest;
import org.siteweb.config.common.dto.AccountDTO;
import org.siteweb.config.common.entity.SystemConfig;
import org.siteweb.config.common.enums.SystemConfigEnum;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@Service
public class TokenUtil {
    private static final String LOGIN_USER_TOKEN = "LoginUserToken";

    @Autowired
    RedisTemplate<String, String> redisTemplate;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    PrivateKey privateKey;

    @Autowired
    PublicKey publicKey;

    public Map<Integer, Authentication> verifyToken(HttpServletRequest request) {
        Map<Integer, Authentication> hashMap = new HashMap<>();
        final String token = request.getHeader(systemConfigService.findTokenHeader());
        if (token != null && !token.isEmpty()) {
            final TokenUser user = parseUserFromToken(token.replace("Bearer", "").trim());
            if (user != null) {
                hashMap.put(1, new UserAuthentication(user));
            } else {
                hashMap.put(-1, null);
            }
            return hashMap;
        }
        hashMap.put(0, null);
        return hashMap;
    }

    //Get User Info from the Token
    public TokenUser parseUserFromToken(String token) {
        Claims claims = Jwts.parser()
                            .setSigningKey(publicKey)
                            .parseClaimsJws(token)
                            .getBody();
        //不是uicode的token则需要验证reids中的token
        if (!isUiCoreToken(claims) && !validateToken(token)) {
            return null;
        }
        AccountDTO user = new AccountDTO();
        if ("system".equals(ClaimsUtil.getType(claims))) {
            if (Boolean.FALSE.equals(redisTemplate.opsForSet().isMember("uiApiToken", "\"" + token + "\""))) { // 这么写是因为redis序列化使用的GenericJackson2JsonRedisSerializer
                return null;
            }
            user.setUserId(-6);
            user.setLogonId("uicore");
            user.setRoleIds("-1");
        } else {
            user.setUserId(ClaimsUtil.getUserId(claims));
            user.setLogonId(ClaimsUtil.getLogonId(claims));
            user.setRoleIds(ClaimsUtil.getRoles(claims));
        }
        user.setThemeName(ClaimsUtil.getTheme(claims));
        user.setLoginType(ClaimsUtil.getLoginType(claims));
        return new TokenUser(user);
    }

    /**
     * 是否开启用户并发登录
     * @return boolean
     */
    private boolean enableConcurrentLogin() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.MAX_CONCURRENT_LOGIN_COUNT.getSystemConfigKey());
        return systemConfig != null && Objects.equals("1", systemConfig.getSystemConfigValue().trim());
    }

    private boolean validateToken(String token) {
        if (!enableConcurrentLogin()) {
            return true;
        }
        Map<Object, Object> loginTokenMap = redisTemplate.opsForHash().entries(LOGIN_USER_TOKEN);
        return loginTokenMap.containsValue(token);
    }

    public String createTokenForUser(TokenUser tokenUser, String loginType) {
        return createTokenForUser(tokenUser.getUser(), loginType);
    }

    private String createTokenForUser(AccountDTO user, String loginType) {
        String loginUserKey = user.getLogonId() + ":" + loginType;
        Object objOldToken = redisTemplate.opsForHash().get(LOGIN_USER_TOKEN, loginUserKey);
        if (objOldToken != null) {
            redisTemplate.opsForHash().delete(LOGIN_USER_TOKEN, loginUserKey);
        }
        String token = Jwts.builder()
                .setSubject(user.getUserName())
                .claim("generateTime",System.currentTimeMillis())
                .claim("userId", user.getUserId())
                .claim("logonId", user.getLogonId())
                .claim("role", user.getRoleIds())
                .claim("personId", user.getUserId())
                .claim("themeName", user.getThemeName())
                .claim("needResetPwd", user.getNeedResetPwd())
                .claim("loginType", loginType)
                .signWith(SignatureAlgorithm.RS256, privateKey)
                .compact();
        redisTemplate.opsForHash().put(LOGIN_USER_TOKEN, loginUserKey, token);
        return token;
    }
    public boolean isUiCoreToken(Claims claims){
        return Objects.nonNull(claims.get("roles"));
    }
}
