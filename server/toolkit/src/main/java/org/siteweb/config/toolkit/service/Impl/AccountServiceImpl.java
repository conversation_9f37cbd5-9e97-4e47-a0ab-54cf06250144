package org.siteweb.config.toolkit.service.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.AccountDTO;
import org.siteweb.config.common.entity.Account;
import org.siteweb.config.common.entity.UserRole;
import org.siteweb.config.common.mapper.AccountMapper;
import org.siteweb.config.common.mapper.UserRoleMapper;
import org.siteweb.config.toolkit.service.AccountService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountServiceImpl implements AccountService {

    @Autowired
    AccountMapper accountMapper;

    @Autowired
    UserRoleMapper userRoleMapper;

//    @Autowired
//    AccountAliasService accountAliasService;


    @Override
    public List<AccountDTO> findByLogonId(String logonId) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.selectList(new QueryWrapper<Account>().eq("LogonId", logonId));
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public List<AccountDTO> findByMobile(String mobile) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.findByMobile(mobile);
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public AccountDTO findByUserId(Integer userId) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return null;
        }
        AccountDTO accountDTO = new AccountDTO();
        BeanUtils.copyProperties(account, accountDTO);
        accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
//        AccountAlias accountAlias = accountAliasService.currentAccountAlias(userId);
//        accountDTO.setAlias(accountAlias != null ? accountAlias.getAlias() : null);
        return accountDTO;
    }

    @Override
    public List<AccountDTO> findAll() {
        List<Account> accounts = accountMapper.selectList(null);
        if (CollUtil.isEmpty(accounts)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(accounts, AccountDTO.class);
    }

    @Override
    public void updateCenterIdForNegativeUserId(int centerId) {
        accountMapper.updateCenterIdForNegativeUserId(centerId);
    }
}
