//package org.siteweb.config.toolkit.security;
//
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.security.access.AccessDeniedException;
//import org.springframework.security.web.access.AccessDeniedHandler;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//
///**
// * <AUTHOR> (2024-02-22)
// **/
//@Component
//public class JwtAccessDeniedHandler implements AccessDeniedHandler {
//
//    @Override
//    public void handle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AccessDeniedException e) throws IOException, ServletException {
//        httpServletResponse.setContentType("application/json;charset=UTF-8");
//        httpServletResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
//        httpServletResponse.getWriter().write("{'error': 'AccessDenied', 'message': 'Access Denied'}");
////        ResultDTO resultDTO = ResultDTO.error(e.getMessage());
//        // ServletOutputStream outputStream = httpServletResponse.getOutputStream();
////        outputStream.write(JSONUtil.toJsonStr(resultDTO).getBytes(StandardCharsets.UTF_8));
//        // outputStream.flush();
//        // outputStream.close();
//    }
//}