package org.siteweb.config.toolkit.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (2024-03-04)
 **/
@Component
@ConfigurationProperties(prefix = "static")
@Data
public class StaticServeProperties {

    /* 静态文件目录 */
    private String[] dir = {
            "file:/home/<USER>/", // 部署目录
            "file:../wwwroot/",           // 调试目录
            "classpath:/wwwroot/"         // 内置资源目录
    };

    /* 默认首页文件 */
    private String defaultIndex = "forward:/index.html";

}
