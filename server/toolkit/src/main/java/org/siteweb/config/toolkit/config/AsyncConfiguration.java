package org.siteweb.config.toolkit.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfiguration {


    /**
     * 默认线程池
     *
     * <AUTHOR> (2024/3/15)
     */
    @Lazy
    @Bean()
    public TaskExecutor defaultExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(15);
        // 设置最大线程数
        executor.setMaxPoolSize(60);
        // 设置队列容量
        executor.setQueueCapacity(100);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(10);
        // 设置默认线程名称
        executor.setThreadNamePrefix("Async-");
        // 设置拒绝策略  不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        // 允许核心线程超时
        executor.getThreadPoolExecutor().allowCoreThreadTimeOut(true);
        return executor;
    }

    @Lazy
    @Bean(name = "monitorUnitTaskExecutor")
    public TaskExecutor monitorUnitTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 配置核心线程数为可用处理器数量
        int processors = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(processors);
        // 配置最大线程数
        executor.setMaxPoolSize(processors * 2);
        // 配置队列大小
        executor.setQueueCapacity(50);
        // 配置线程池中线程的名称前缀
        executor.setThreadNamePrefix("MonitorUnit-");
        // 配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }
}