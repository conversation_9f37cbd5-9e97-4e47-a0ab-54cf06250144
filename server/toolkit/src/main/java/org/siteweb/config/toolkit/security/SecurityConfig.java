package org.siteweb.config.toolkit.security;

import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.CaptchaUtil;
import lombok.RequiredArgsConstructor;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.siteweb.config.toolkit.service.UserConfigService;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final TokenUtil tokenUtil;
    private final ProdAntPatternServiceImpl prodAntPatternService;
    private final SystemConfigService systemConfigService;
    private final SecurityProperties securityProperties;
//    private final RedisUtil redisUtil;
    private final CaptchaUtil captchaUtil;
//    private final LocaleMessageSourceUtil messageSourceUtil;
//    private final AccountPasswordErrRecordService accountPasswordErrRecordService;
//    private final AccountTimeSpanService accountTimeSpanService;
//    private final IpFilterPolicyService ipFilterPolicyService;
    private final UserAuthenticationManager authenticationManager;
//    private final SmsCodeAuthenticationManager smsCodeAuthenticationManager;
//    private final AccountService accountService;
//    private final SecurityAuditManager securityAuditManager;
    private final UserConfigService userConfigService;
//    private final SecurityFileIntegrityService securityFileIntegrityService;
    private final I18n i18n;


    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (web) -> web.ignoring()
                // Spring Security should completely ignore URLs starting with / resources/
                .requestMatchers(prodAntPatternService.getAntPatterns());
    }





    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                .exceptionHandling(exceptionHandling ->
                        exceptionHandling.authenticationEntryPoint(authenticationEntryPoint())
                )
                .anonymous(withDefaults())
                // Disable Cross site references
                .csrf(AbstractHttpConfigurer::disable)
                // Custom Token based authentication based on the header previously given to the client
                .addFilterBefore(new VerifyTokenFilter(tokenUtil), UsernamePasswordAuthenticationFilter.class)
                // custom JSON based authentication by POST of {"username":"<name>","password":"<password>"} which sets the token header upon authentication
                .addFilterBefore(new GenerateTokenForUserFilter(authenticationManager, tokenUtil, systemConfigService, captchaUtil, userConfigService, i18n), UsernamePasswordAuthenticationFilter.class)
                // .addFilterBefore(new SmsCodeGenerateTokenForUserFilter(smsCodeAuthenticationManager, tokenUtil), UsernamePasswordAuthenticationFilter.class)
                // .addFilterBefore(new SignatureFilter(securityProperties, redisUtil, securityAuditManager, messageSourceUtil), ChannelProcessingFilter.class)
//                .addFilterBefore(new AccountHeartbeatFilter(securityProperties, accountService, tokenUtil, systemConfigService), ChannelProcessingFilter.class)
//                .addFilterBefore(new LanguageFilter(), WebAsyncManagerIntegrationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/api/**").authenticated()
                        .anyRequest().permitAll()
                );

        return httpSecurity.build();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED); // or any other custom behavior
    }

}
