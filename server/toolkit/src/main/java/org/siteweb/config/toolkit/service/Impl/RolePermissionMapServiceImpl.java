package org.siteweb.config.toolkit.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.siteweb.config.common.entity.RolePermissionMap;
import org.siteweb.config.common.mapper.RolePermissionMapMapper;
import org.siteweb.config.toolkit.service.RolePermissionMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class RolePermissionMapServiceImpl implements RolePermissionMapService {

    @Autowired
    private RolePermissionMapMapper rolePermissionMapMapper;

    @Override
    @Transactional
    public Boolean create(RolePermissionMap rolePermissionMap) {
        return rolePermissionMapMapper.insert(rolePermissionMap) > 0;
    }

    @Override
    @Transactional
    public Boolean update(RolePermissionMap rolePermissionMap) {
        return rolePermissionMapMapper.updateById(rolePermissionMap) > 0;
    }

    @Override
    @Transactional
    public Boolean deleteById(Integer rolePermissionMapId) {
        return rolePermissionMapMapper.deleteById(rolePermissionMapId) > 0;
    }

    @Override
    public RolePermissionMap findById(Integer rolePermissionMapId) {
        return rolePermissionMapMapper.selectById(rolePermissionMapId);
    }

    @Override
    public List<RolePermissionMap> findAll() {
        return rolePermissionMapMapper.selectList(null);
    }

    @Override
    public List<RolePermissionMap> findByRoleId(Integer roleId) {
        LambdaQueryWrapper<RolePermissionMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RolePermissionMap::getRoleId, roleId);
        return rolePermissionMapMapper.selectList(queryWrapper);
    }

    @Override
    public List<RolePermissionMap> findByRoleIdAndCategory(Integer roleId, Integer category) {
        LambdaQueryWrapper<RolePermissionMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RolePermissionMap::getRoleId, roleId);
        queryWrapper.eq(RolePermissionMap::getPermissionCategoryId, category);
        return rolePermissionMapMapper.selectList(queryWrapper);
    }
}