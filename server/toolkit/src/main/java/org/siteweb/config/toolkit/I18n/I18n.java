package org.siteweb.config.toolkit.I18n;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * I18N 多语言翻译器
 *
 * <AUTHOR> (2024-02-22)
 **/
@Component
public class I18n {

    @Autowired
    public I18nMessageSource messageSource;

    @Autowired
    Environment environment;

    /**
     * 从本地化字典中获取指定元素返回，
     *
     * @param code   本地化字典中的Key
     * @param params 格式化参数（可选项）
     * <AUTHOR> (2024/3/15)
     */
    public String T(String code, Object... params) {
        //默认Locale为zh_CN
        String localeProperty = environment.getProperty("spring.web.locale");
        Locale locale = Locale.SIMPLIFIED_CHINESE;
        if (localeProperty != null) {
            String[] splitArray = localeProperty.split("_");
            if (splitArray.length == 2) {
                locale = new Locale(splitArray[0], splitArray[1]);
            }
        }
        return messageSource.getMessage(code, params, locale);
    }


}
