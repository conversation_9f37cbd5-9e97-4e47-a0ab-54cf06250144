package org.siteweb.config.toolkit.service;

import org.siteweb.config.common.entity.RolePermissionMap;

import java.util.List;

public interface RolePermissionMapService {
    Boolean create(RolePermissionMap rolePermissionMap);
    Boolean update(RolePermissionMap rolePermissionMap);
    Boolean deleteById(Integer rolePermissionMapId);
    RolePermissionMap findById(Integer rolePermissionMapId);
    List<RolePermissionMap> findAll();
    List<RolePermissionMap> findByRoleId(Integer roleId);

    List<RolePermissionMap> findByRoleIdAndCategory(Integer roleId, Integer category);

}