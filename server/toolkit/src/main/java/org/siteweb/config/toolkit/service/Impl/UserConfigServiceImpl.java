package org.siteweb.config.toolkit.service.Impl;


import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import org.siteweb.config.common.entity.SystemConfig;
import org.siteweb.config.common.entity.UserConfig;
import org.siteweb.config.common.enums.SystemConfigEnum;
import org.siteweb.config.common.enums.UserConfigEnum;
import org.siteweb.config.common.mapper.UserConfigMapper;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.siteweb.config.toolkit.service.UserConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class UserConfigServiceImpl implements UserConfigService {
    /**
     * 默认主题
     */
    private static final String DEFAULT_THEME = "default";
    @Autowired
    UserConfigMapper userConfigMapper;
    @Autowired
    SystemConfigService systemConfigService;


    @Override
    public String findUserTheme(Integer userId) {
        UserConfig themeConfig = findByUserIdAndKey(userId, UserConfigEnum.THEME.getConfigKey());
        //用户没有配置主题
        if (Objects.isNull(themeConfig) || CharSequenceUtil.isBlank(themeConfig.getConfigValue())) {
            //使用全局主题
            SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.THEME.getSystemConfigKey());
            return Optional.ofNullable(systemConfig).map(SystemConfig::getSystemConfigValue).orElse(DEFAULT_THEME);
        }
        //使用用户自定义主题
        return themeConfig.getConfigValue();
    }

    /**
     * 获取用户配置信息
     * @param userId     用户id
     * @param configKey 配置键
     * @return {@link UserConfig}
     */
    private UserConfig findByUserIdAndKey(Integer userId, String configKey) {
        return userConfigMapper.selectOne(Wrappers.lambdaQuery(UserConfig.class)
                .eq(UserConfig::getUserId, userId)
                .eq(UserConfig::getConfigKey, configKey));
    }







}
