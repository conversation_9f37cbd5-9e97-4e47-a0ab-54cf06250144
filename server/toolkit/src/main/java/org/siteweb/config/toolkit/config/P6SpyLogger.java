package org.siteweb.config.toolkit.config;

import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

import java.time.LocalDateTime;


// org.siteweb.config.db.config.P6SpyLogger
public class P6SpyLogger implements MessageFormattingStrategy {

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category, String prepared, String sql, String s4) {
        if (sql.trim().isEmpty()) return "";
        return "[ " + LocalDateTime.now() + " ] --- | took " + elapsed + "ms | " + category + " | connection " + connectionId + "\n " + sql + ";";
    }

}