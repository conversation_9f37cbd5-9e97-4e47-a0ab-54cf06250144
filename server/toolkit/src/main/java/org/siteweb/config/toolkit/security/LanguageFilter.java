package org.siteweb.config.toolkit.security;

import cn.hutool.core.text.CharSequenceUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.siteweb.config.common.utils.LanguageUtil;
import org.springframework.web.filter.GenericFilterBean;


import java.io.IOException;

public class LanguageFilter extends GenericFilterBean {
    public static final ThreadLocal<String> LanguageThreadLocal = new ThreadLocal<>();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            LanguageThreadLocal.set(httpRequest.getHeader(LanguageUtil.LANGUAGE_HEADER));
            chain.doFilter(request, response);
        } finally {
            LanguageThreadLocal.remove();
        }
    }
    public static String getCurrentThreadLanguage(){
        String language = LanguageThreadLocal.get();
        if (CharSequenceUtil.isBlank(language)){
            return LanguageUtil.ZH_CN;
        }
        return language;
    }
}
