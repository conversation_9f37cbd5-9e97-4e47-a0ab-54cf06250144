package org.siteweb.config.toolkit.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.siteweb.config.common.config.InsertBatchSqlInjector;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import javax.sql.DataSource;


@Configuration
//@MapperScan(basePackages = {"org.siteweb.**.mapper*"}, sqlSessionTemplateRef = "myPluginSqlSessionTemplate")
public class MybatisPlusConfig {


    @Bean
    @Lazy
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        //添加乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }

    @Bean
    @Lazy
    public InsertBatchSqlInjector insertBatchSqlInjector() {
        return new InsertBatchSqlInjector();
    }



//
//    @Bean(name = "myPluginDataSourceProperties")
//    // TODO ConfigurationProperties 修改插件读取配置文件的数据源路径
//    @ConfigurationProperties(prefix = "plugins.test.datasource" /* 从Yaml文件的哪个地方读取数据库连接配置 */)
//    public DataSourceProperties myPluginDataSourceProperties() {
//        return new DataSourceProperties();
//    }
//
//    @Bean(name = "myPluginDataSource")
//    public DataSource myPluginDataSource(@Qualifier("myPluginDataSourceProperties") DataSourceProperties sitewebDataSourceProperties) {
//        return sitewebDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
//    }
//
//    @Bean(name = "myPluginTransactionManager")
//    public DataSourceTransactionManager myPluginTransactionManager(@Qualifier("myPluginDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//
//
//
//
//    @Bean(name = "myPluginSqlSessionFactory")
//    public SqlSessionFactory myPluginSqlSessionFactory(@Qualifier("myPluginDataSource") DataSource primaryDataSource, @Qualifier("classLoader") ClassLoader classLoader) throws Exception {
//        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
//        sessionFactory.setDataSource(primaryDataSource);
//        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(classLoader);
//        // TODO 下方路径中的 tsc-north-test 应在 resources/mapper 目录下 并保持全局唯一
//        var reources = resolver.getResources("classpath*:mapper/*.xml");
//        sessionFactory.setMapperLocations(reources);
//        return sessionFactory.getObject();
//    }
//
//    @Bean(name = "myPluginSqlSessionTemplate")
//    public SqlSessionTemplate myPluginSqlSessionTemplate(@Qualifier("myPluginSqlSessionFactory") SqlSessionFactory primarySqlSessionFactory) {
//        return new SqlSessionTemplate(primarySqlSessionFactory);
//    }


}