package org.siteweb.config.toolkit.utils;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 验证码校验
 * @Author: lzy
 * @Date: 2022/4/21 15:53
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CaptchaUtil {

    private final RedisUtil redisUtil;
//    private final CaptchaConfig captchaConfig;
//    private final LocaleMessageSourceUtil messageSourceUtil;

    /**
     * 验证码校验
     * @param code
     * @param codeKey
     */
//    public Boolean verifyCaptcha(String code, String codeKey) {
//        if (StrUtil.isBlank(code) || StrUtil.isBlank(codeKey)) {
//            log.warn("verify param.[code={}, codeKey={}]", code, codeKey);
//            return Boolean.FALSE;
//        }
//        Object data = redisUtil.get(captchaConfig.getCaptchaImgKey() + codeKey);
//        if (ObjectUtil.isEmpty(data)) {
//            log.warn("login captcha codeexpiredOrNonexistent");
//            return Boolean.FALSE;
//        }
//        redisUtil.deleteByKey(captchaConfig.getCaptchaImgKey() + codeKey);
//        return StringUtils.equals(code, data.toString());
//    }
}
