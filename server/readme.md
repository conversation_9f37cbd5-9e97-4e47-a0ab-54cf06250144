# 项目简介

## Restful API 规范

- 所有接口只要进入到Controller内， HttpCode既返回200
- HttpCode 返回非200 只有 401、404、500 等
- ResponseResult.Code 业务执行成功返回0 ， 失败返回非0

## 多语言 I18N

- 前端在 `HttpHeader` 中 `Accept-Language` 项增加  `zh-CN` 或 `en-US`

`后端多语言字典目录`

- `src/main/resources/i18n/`
- `messages.properties`     中文字典（默认）
- `messages_en.properties`  英文字典

## 前端build时的输出目录

后端Http服务提供的静态文件服务目录
`/server/server/src/main/resources/www/`

## common 模块

此模块为公共模块，将被client和server引用

内部提供基础数据model和数据库访问的方法

## client模块

此模块为SDK模块，生成jar包，供其他app引用。

- 此模块提供基础数据的API模块及数据Provider，

- 此模块内部不能直接提供访问数据库进行写改操作

提供 cache、MQTT通知机制逻辑。

## toolkit模块

此模块为server模块提供安全配置、工具包、Token验签等服务

## server模块

配置服务模块，内部提供数据缓存、clientAPI、基础数据的增删改查等操作

## 数据库表模型及Mapper

In `src/main/java/org/siteweb/config/common/entity/`

- ✅采集结构
- ✅管理结构
- ✅设备模板（❎ControlParameter）
- ✅数据字典
- ❎基类管理
- ❎工作站管理