package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.MonitorUnitSignalDTO;
import org.siteweb.config.common.entity.TslMonitorUnitSignal;
import org.siteweb.config.common.vo.MonitorUnitSignalVO;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/11
 */

public interface TslMonitorUnitSignalService {

    List<MonitorUnitSignalVO> getMonitorUnitSignals(int equipmentId);

    void deleteMonitorUnitSignal(Integer equipmentId, List<Integer> signalIds);

    void createOrUpdate(TslMonitorUnitSignal tslMonitorUnitSignal);

    void create(TslMonitorUnitSignal monitorUnitSignal);

    List<TslMonitorUnitSignal> findByStationIdAndChannelAndSamplerUnitId(Integer stationId, Integer channel, Integer samplerUnitId);

    List<TslMonitorUnitSignal> findByMonitorUnitIdExcludingEquipmentId(Integer monitorUnitId, Integer equipmentId);
    List<TslMonitorUnitSignal> findByMonitorUnitId(Integer monitorUnitId);

    MonitorUnitSignalDTO findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId);

    boolean existsByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId);

    void deleteByEquipmentId(Integer equipmentId);

    TslMonitorUnitSignal findByStationIdAndEquipmentIdAndSignalId(Integer stationId, Integer equipmentId, Integer signalId);

    void deleteByStationId(Integer stationId);
}
