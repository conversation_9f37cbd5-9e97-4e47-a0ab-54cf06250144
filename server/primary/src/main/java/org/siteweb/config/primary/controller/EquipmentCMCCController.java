package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblEquipmentCMCC;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EquipmentCMCCService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/equipmentcmcc")
public class EquipmentCMCCController {
    @Autowired
    private EquipmentCMCCService equipmentCMCCService;

    @GetMapping
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(equipmentCMCCService.findAllVO());
    }

    @PutMapping
    public ResponseEntity<ResponseResult> put(@RequestBody TblEquipmentCMCC tblEquipmentCMCC) {
        return ResponseHelper.successful(equipmentCMCCService.update(tblEquipmentCMCC));
    }
}
