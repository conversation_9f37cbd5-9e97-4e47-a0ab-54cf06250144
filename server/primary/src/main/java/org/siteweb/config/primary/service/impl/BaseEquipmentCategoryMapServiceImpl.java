package org.siteweb.config.primary.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.BaseEquipmentCategoryMapDTO;
import org.siteweb.config.common.entity.TblBaseEquipmentCategoryMap;
import org.siteweb.config.common.entity.TblDataItem;
import org.siteweb.config.common.entity.TblStandardType;
import org.siteweb.config.common.entity.TblSysConfig;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.mapper.TblBaseEquipmentCategoryMapMapper;
import org.siteweb.config.primary.enums.SysConfigEnum;
import org.siteweb.config.primary.service.BaseEquipmentCategoryMapService;
import org.siteweb.config.primary.service.DataItemService;
import org.siteweb.config.primary.service.StandardTypeService;
import org.siteweb.config.primary.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/28
 */
@Service
public class BaseEquipmentCategoryMapServiceImpl implements BaseEquipmentCategoryMapService {

    @Autowired
    private TblBaseEquipmentCategoryMapMapper tblBaseEquipmentCategoryMapMapper;


    @Autowired
    private DataItemService dataItemService;

    @Autowired
    StandardTypeService standardTypeService;

    @Autowired
    SysConfigService sysConfigService;

    @Override
    public int deleteAll() {
        return tblBaseEquipmentCategoryMapMapper.delete(new QueryWrapper<>());
    }

    @Override
    public int insert(TblBaseEquipmentCategoryMap tblBaseEquipmentCategoryMap) {
        return tblBaseEquipmentCategoryMapMapper.insert(tblBaseEquipmentCategoryMap);
    }

    @Override
    public void batchInsertYiDongEquipmentCategoryMap() {
        tblBaseEquipmentCategoryMapMapper.batchInsertYiDongEquipmentCategoryMap();
    }

    @Override
    public void batchInsertDianXinEquipmentCategoryMap() {
        tblBaseEquipmentCategoryMapMapper.batchInsertDianXinEquipmentCategoryMap();
    }

    @Override
    public BaseEquipmentCategoryMapDTO findByBaseEquipmentID(Integer baseEquipmentId) {
        List<TblBaseEquipmentCategoryMap> tblBaseEquipmentCategoryMaps = tblBaseEquipmentCategoryMapMapper.selectList(Wrappers.lambdaQuery(TblBaseEquipmentCategoryMap.class).eq(TblBaseEquipmentCategoryMap::getBaseEquipmentId, baseEquipmentId));
        List<TblDataItem> equipmentCategorys = dataItemService.findByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);
        TblSysConfig byKey = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
        if (byKey == null) {
            throw new RuntimeException("未查询到标准化类型");
        }
        if (equipmentCategorys != null && !equipmentCategorys.isEmpty()) {
            for (TblBaseEquipmentCategoryMap tblBaseEquipmentCategoryMap : tblBaseEquipmentCategoryMaps) {
                for (TblDataItem equipmentCategory : equipmentCategorys) {
                    if (tblBaseEquipmentCategoryMap.getEquipmentCategory().equals(equipmentCategory.getItemId())) {
                        tblBaseEquipmentCategoryMap.setEquipmentCategoryName(equipmentCategory.getItemValue());
                    }
                }
            }
        }
        BaseEquipmentCategoryMapDTO baseEquipmentCategoryMapDTO = new BaseEquipmentCategoryMapDTO();
        if (StrUtil.isNotEmpty(byKey.getConfigValue()) && byKey.getConfigValue().equals("0")) {
            // 维谛标准化不需要设置
            baseEquipmentCategoryMapDTO.setBaseEquipmentCategoryMapList(null);
        } else {
            if (tblBaseEquipmentCategoryMaps == null || tblBaseEquipmentCategoryMaps.isEmpty()) {
                baseEquipmentCategoryMapDTO.setBaseEquipmentCategoryMapList(null);
            } else {
                baseEquipmentCategoryMapDTO.setBaseEquipmentCategoryMapList(tblBaseEquipmentCategoryMaps);
            }
        }
        TblStandardType byStandardId = standardTypeService.findByStandardId(Integer.parseInt(byKey.getConfigValue()));
        baseEquipmentCategoryMapDTO.setStandardName(byStandardId.getStandardName());
        return baseEquipmentCategoryMapDTO;
    }
}
