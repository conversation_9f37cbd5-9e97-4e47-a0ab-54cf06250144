package org.siteweb.config.primary.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblBusinessType;
import org.siteweb.config.common.mapper.TblBusinessTypeMapper;
import org.siteweb.config.common.vo.BusinessExpressionVO;
import org.siteweb.config.common.vo.BusinessTypeExpressionVO;
import org.siteweb.config.primary.service.TblBusinessTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/18
 */
@Slf4j
@RestController
@RequestMapping("/TblBusinessTypeServiceImpl")
public class TblBusinessTypeServiceImpl implements TblBusinessTypeService {

    @Autowired
    private TblBusinessTypeMapper tblBusinessTypeMapper;

    @Override
    public List<TblBusinessType> findTblBusinessTypeByMonitorUnitId(Integer monitorUnitId) {
        return tblBusinessTypeMapper.findTblBusinessTypeByMonitorUnitId(monitorUnitId);
    }

    @Override
    public List<BusinessTypeExpressionVO> findDistinctBusinessTypesAndExpressionsForMonitorUnit(Integer monitorUnitId) {
        return tblBusinessTypeMapper.findDistinctBusinessTypesAndExpressionsForMonitorUnit(monitorUnitId);
    }

    @Override
    public List<BusinessExpressionVO> findDistinctBusinessExpressionsForMonitorUnit(Integer monitorUnitId) {
        return tblBusinessTypeMapper.findDistinctBusinessExpressionsForMonitorUnit(monitorUnitId);
    }
}
