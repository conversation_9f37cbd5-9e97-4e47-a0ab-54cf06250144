package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.constants.EquipmentTemplateConstant;
import org.siteweb.config.common.constants.SignalConstant;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EquipmentSignalDTO;
import org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.*;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.enums.*;
import org.siteweb.config.common.dto.excel.SignalExcel;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SignalServiceImpl implements SignalService {
    @Autowired
    TblEquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    TblSignalMapper signalMapper;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    SignalPropertyService signalPropertyService;
    @Autowired
    SignalMeaningsService signalMeaningsService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    I18n i18n;
    @Autowired
    private TslMonitorUnitSignalMapper monitorUnitSignalMapper;
    @Autowired
    private TblSignalPropertyMapper signalPropertyMapper;
    @Autowired
    private TblSignalMeaningsMapper signalMeaningsMapper;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private TblStatusBaseDicMapper statusBaseDicMapper;
    @Autowired
    TblSignalBaseConfirmMapper signalBaseConfirmMapper;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    SignalBaseDicService signalBaseDicService;
    @Autowired
    TblStandardBackMapper standardBackMapper;
    @Autowired
    TblSignalBaseDicMapper signalBaseDicMapper;
    @Autowired
    TblEquipmentBaseTypeMapper equipmentBaseTypeMapper;

    @Autowired
    TslAcrossMonitorUnitSignalMapper acrossMonitorUnitSignalMapper;

    @Autowired
    @Lazy
    StandardPointService standardPointService;

    @Override
    public void createSignal(TblSignal signal) {
        if (Objects.isNull(signal.getSignalId()) || Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
            List<SignalConfigItem> signalConfigItems = signalMapper.findSignalItemByEquipmentTemplateId(signal.getEquipmentTemplateId());
            // 信号名称不能重复、通道号 channelNo 不可为空值 大于0 时、-3 不可重复，小于等于0 可重复
            if (CollUtil.isNotEmpty(signalConfigItems)) {
                for (SignalConfigItem signalConfigItem : signalConfigItems) {
                    if (Objects.equals(signalConfigItem.getSignalName(), signal.getSignalName())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("monitor.signal"), signal.getSignalName()));
                    }
                    if ((signal.getChannelNo() > 0 || signal.getChannelNo() == -3) && Objects.equals(signalConfigItem.getChannelNo(), signal.getChannelNo())) {
                        throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("signal.channelNo"), signal.getChannelNo()));
                    }
                }
            }
            Integer signalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
            signal.setSignalId(signalId);
        }
        signalMapper.insert(signal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertSignal(List<TblSignal> signalList) {
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        Integer maxSignalId = null;
        for (TblSignal signal : signalList) {
            // insertBatchSomeColumn不允许字段为null，如果ModuleNo为null，默认设置为0。
            if (Objects.isNull(signal.getModuleNo())) {
                signal.setModuleNo(0);
            }
            if (Objects.nonNull(signal.getSignalId()) && !Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
                continue;
            }
            if (Objects.isNull(maxSignalId)) {
                maxSignalId = findMaxSignalIdByEquipmentTemplateId(signal.getEquipmentTemplateId());
                signal.setSignalId(maxSignalId);
                continue;
            }
            signal.setSignalId(++maxSignalId);
        }
        signalMapper.insertBatchSomeColumn(signalList);
    }


    @Override
    public void createCommunicationStateSignal(Integer equipmentTemplateId) {
        if (existCommunicationStateSignal(equipmentTemplateId)) {
            return;
        }
        TblSignal communicationStateSignal = TblSignal.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .signalId(SignalConstant.COMMUNICATION_STATE_SIGNAL)
                .signalName(i18n.T("monitor.equipment.communicationState"))
                .signalCategory(SignalCategoryEnum.SWITCH_SIGNAL.getValue())
                .signalType(SignalTypeEnum.VIRTUAL_SIGNAL.getValue())
                .channelNo(SignalConstant.COMMUNICATION_STATE_SIGNAL)
                .channelType(ChannelTypeEnum.ANALOG.getValue())
                .dataType(DataTypeEnum.FLOAT.getValue())
                .showPrecision("0")
                .enable(true)
                .visible(true)
                .moduleNo(0)
                .displayIndex(findCurrentDisplayIndexByEquipmentTemplateId(equipmentTemplateId))
                .build();
        createSignal(communicationStateSignal);
        //设备通信状态的信号属性
        signalPropertyService.createSignalProperty(new TblSignalProperty(equipmentTemplateId, SignalConstant.COMMUNICATION_STATE_SIGNAL, 27));
        //设备通信状态的信号含义
        signalMeaningsService.communicationStateSignalMeaning(equipmentTemplateId);
    }

    /**
     * 是否存在设备通信状态信号
     * 注：设备通信状态信号id是固定的一直是 -3
     *
     * @return true是  false否
     */
    private boolean existCommunicationStateSignal(Integer equipmentTemplateId) {
        return signalMapper.exists(Wrappers.lambdaQuery(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, equipmentTemplateId)
                .eq(TblSignal::getSignalId, SignalConstant.COMMUNICATION_STATE_SIGNAL));
    }

    @Override
    public List<TblSignal> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public Map<Integer, List<TblSignal>> findSignalMaps(Collection<Integer> equipmentTemplateIds) {
        if (CollUtil.isEmpty(equipmentTemplateIds)) {
            return Collections.emptyMap();
        }
        List<TblSignal> signals = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class).in(TblSignal::getEquipmentTemplateId, equipmentTemplateIds));
        return signals.stream().collect(Collectors.groupingBy(TblSignal::getEquipmentTemplateId));
    }

    @Override
    public SignalConfigItem findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        return signalMapper.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
    }

    @Override
    public List<SignalConfigItem> findItemByEquipmentTemplateIdAndEquipmentId(Integer equipmentTemplateId, Integer equipmentId) {
        // 获取跨站信号配置
        List<TslAcrossMonitorUnitSignal> acrossMonitorUnitSignals = acrossMonitorUnitSignalMapper.selectList(Wrappers.lambdaQuery(TslAcrossMonitorUnitSignal.class)
                .eq(TslAcrossMonitorUnitSignal::getEquipmentId, equipmentId));
        // 获取信号配置项并排序
        List<SignalConfigItem> signalConfigItems = Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId))
                .orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparingInt(SignalConfigItem::getDisplayIndex))
                .collect(Collectors.toList());

        // 创建一个 Map 来存储跨站信号的 signalId
        Map<Integer, TslAcrossMonitorUnitSignal> acrossSignalMap = acrossMonitorUnitSignals.stream()
                .collect(Collectors.toMap(TslAcrossMonitorUnitSignal::getSignalId, Function.identity()));

        // 设置 acrossSignal 字段
        for (SignalConfigItem item : signalConfigItems) {
            item.setAcrossSignal(acrossSignalMap.containsKey(item.getSignalId()));
        }

        return signalConfigItems;
    }

    @Override
    public TblSignal findBySignalId(Integer signalId) {
        return signalMapper.selectOne(new QueryWrapper<TblSignal>().lambda().eq(TblSignal::getSignalId, signalId));
    }

    @Override
    public void updateSignalName(String signalName, Integer signalId) {
        signalMapper.update(null, new UpdateWrapper<TblSignal>()
                .set("SignalName", signalName)
                .eq("SignalId", signalId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateSignal(SignalConfigItem signalConfigItem) {
        // 信号名称不能重复、通道号不可重复 channelNo 不可为空值 大于0 时、-3 不可重复，小于等于0 可重复
        List<SignalConfigItem> signalConfigItems = signalMapper.findSignalItemByEquipmentTemplateId(signalConfigItem.getEquipmentTemplateId());
        for (SignalConfigItem sc : signalConfigItems) {
            if (Objects.equals(sc.getSignalId(), signalConfigItem.getSignalId())) {
                continue;
            }
            if (Objects.equals(sc.getSignalName(), signalConfigItem.getSignalName())) {
                throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("monitor.signal"), signalConfigItem.getSignalName()));
            }
            if ((signalConfigItem.getChannelNo() > 0 || signalConfigItem.getChannelNo() == -3) && Objects.equals(sc.getChannelNo(), signalConfigItem.getChannelNo())) {
                throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("signal.channelNo"), signalConfigItem.getChannelNo()));
            }
        }
        //记录变更日志
        SignalConfigItem oldSignal = signalMapper.findByEquipmentTemplateIdAndSignalId(signalConfigItem.getEquipmentTemplateId(), signalConfigItem.getSignalId());
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldSignal, signalConfigItem);
        signalMeaningsService.updateSignalMeanings(signalConfigItem.getEquipmentTemplateId(), signalConfigItem.getSignalId(), signalConfigItem.getSignalMeaningsList());
        signalPropertyService.updateSignalProperty(signalConfigItem.getEquipmentTemplateId(), signalConfigItem.getSignalId(), signalConfigItem.getSignalPropertyList());
        TblSignal signal = BeanUtil.copyProperties(signalConfigItem, TblSignal.class);
        int result = signalMapper.update(signal, Wrappers.lambdaUpdate(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, signal.getEquipmentTemplateId())
                .eq(TblSignal::getSignalId, signal.getSignalId()));
        sendUpdateMsgByEquipmentTemplateId(signalConfigItem.getEquipmentTemplateId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSignal(Integer equipmentTemplateId, Integer signalId) {
        //设备通讯状态信号(ID为-3)不允许删除
        if (Objects.equals(signalId, SignalConstant.COMMUNICATION_STATE_SIGNAL)) {
            return 0;
        }
        SignalConfigItem signalConfigItem = findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
        if (Objects.isNull(signalConfigItem)) {
            return 0;
        }
        signalMapper.deleteSignal(equipmentTemplateId, signalId);
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), equipmentTemplateId + "." + signalId, OperationObjectTypeEnum.SIGNAL, i18n.T("monitor.signal.id"), i18n.T("delete"), String.valueOf(signalId), "");
        sendUpdateMsgByEquipmentTemplateId(equipmentTemplateId);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteSingal(int equipmentTemplateId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            throw new InvalidParameterException("signalIds can't be empty");
        }
        // 设备通讯状态信号(ID为-3)不允许删除
        if (signalIds.contains(SignalConstant.COMMUNICATION_STATE_SIGNAL)) {
            throw new BusinessException(i18n.T("monitor.communicationStateSignal.doNotAllowDelete"));
        }
        monitorUnitSignalMapper.batchDeleteBySignal(equipmentTemplateId, signalIds);
        signalPropertyMapper.batchDeleteBySignal(equipmentTemplateId, signalIds);
        signalMeaningsMapper.batchDeleteBySignal(equipmentTemplateId, signalIds);
        signalMapper.delete(Wrappers.lambdaUpdate(TblSignal.class).eq(TblSignal::getEquipmentTemplateId, equipmentTemplateId)
                .in(TblSignal::getSignalId, signalIds));
        // 记录操作日志
        signalIds.forEach(signalId -> operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), equipmentTemplateId + "." + signalId, OperationObjectTypeEnum.SIGNAL, i18n.T("monitor.signal.id"), i18n.T("delete"), String.valueOf(signalId), ""));
        sendUpdateMsgByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    public Double getSignalProgress(Integer equipmentTemplateId, Map<Integer, List<SignalProgressDTO>> signalProgressMap) {
        List<SignalProgressDTO> signalProgressList = signalProgressMap.get(equipmentTemplateId);
        if (CollUtil.isEmpty(signalProgressList)) {
            return 100.0;
        }
        int count = getSignalProgressCount(signalProgressList);
        return NumberUtil.div(Math.multiplyExact(count, 100), signalProgressList.size(), 2);
    }

    @Override
    public Map<Integer, List<SignalProgressDTO>> findSignalProgressMap() {
        List<SignalProgressDTO> signalProgressList = signalMapper.findSignalProgressList();
        List<TblSignalBaseConfirm> signalBaseConfirms = signalBaseConfirmMapper.selectList(Wrappers.emptyWrapper());
        Map<String, String> confirmMap = signalBaseConfirms.stream()
                .collect(Collectors.toMap(
                        confirm -> buildConfirmKey(confirm.getEquipmentTemplateId(), confirm.getSignalId(), confirm.getStateValue()),
                        TblSignalBaseConfirm::getSubState,
                        (existing, replacement) -> existing
                ));
        return signalProgressList.parallelStream().peek(m->
                m.setSubState(confirmMap.getOrDefault(buildConfirmKey(m.getEquipmentTemplateId(), m.getSignalId(), m.getStateValue()), null)))
                .collect(Collectors.groupingBy(SignalProgressDTO::getEquipmentTemplateId));
    }

    private int getSignalProgressCount(List<SignalProgressDTO> signalProgressList) {
        int count = 0;
        for (SignalProgressDTO signalProgressDTO : signalProgressList) {
            // 开关量
            if (signalProgressDTO.getSignalCategory() == 2) {
                if ((Objects.nonNull(signalProgressDTO.getBaseTypeId()) && Objects.isNull(signalProgressDTO.getStateValue()))
                        || ((Objects.nonNull(signalProgressDTO.getStateValue())) && (Objects.nonNull(signalProgressDTO.getBaseCondId())))
                        || EquipmentTemplateConstant.CONFIRM_FLAG.equals(signalProgressDTO.getSubState())) {
                    count++;
                }
            } else {
                if (Objects.nonNull(signalProgressDTO.getBaseTypeId()) || EquipmentTemplateConstant.CONFIRM_FLAG.equals(signalProgressDTO.getSubState())) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public BaseClassStatisticsDTO<SignalBaseClassDTO> findSignalBaseClassList(Integer equipmentBaseType) {
        List<SignalBaseClassDetailDTO> signalBaseClass = signalMapper.findSignalBaseClass(equipmentBaseType);
        buildSignalBaseClassDetail(signalBaseClass);
        Map<String, List<SignalBaseClassDetailDTO>> collectMap = signalBaseClass.stream()
                .collect(Collectors.groupingBy(g -> String.format("%s%s%s", g.getEquipmentBaseType(), g.getSignalName(), g.getMeanings())));
        AtomicInteger confirmCount = new AtomicInteger(0);
        AtomicInteger sumCount = new AtomicInteger(0);
        List<SignalBaseClassDTO> dataList = collectMap.values().parallelStream().map(signalBaseClassList -> {
                    // 基于模板名称排序，取第一条数据拼接
                    signalBaseClassList.sort(Comparator.comparing(SignalBaseClassDetailDTO::getEquipmentTemplateName, Comparator.nullsLast(Comparator.naturalOrder())));
                    Optional<SignalBaseClassDetailDTO> baseClassOptional = signalBaseClassList.stream()
                            .filter(item -> Objects.nonNull(item.getBaseTypeId())).findFirst();
                    SignalBaseClassDetailDTO signalBaseClassDetailDTO = baseClassOptional.orElseGet(() -> signalBaseClassList.stream().findFirst().orElse(new SignalBaseClassDetailDTO()));
                    SignalBaseClassDTO signalBaseClassDTO = BeanUtil.copyProperties(signalBaseClassDetailDTO, SignalBaseClassDTO.class);
                    sumCount.getAndAdd(signalBaseClassList.size());
                    signalBaseClassDTO.setSignalNumber(signalBaseClassList.size());
                    // 获取子项情况
                    signalBaseClassDTO.setChildState(getChildState(signalBaseClassList, confirmCount));
                    return signalBaseClassDTO;
                })
                .sorted(Comparator.comparing(SignalBaseClassDTO::getEquipmentBaseType).thenComparing(SignalBaseClassDTO::getSignalName, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(SignalBaseClassDTO::getMeanings, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
        BaseClassStatisticsDTO<SignalBaseClassDTO> result = new BaseClassStatisticsDTO<>();
        result.setConfirmCount(confirmCount.get());
        result.setSumCount(sumCount.get());
        result.setDataList(dataList);
        return result;
    }

    /**
     * 获取子项情况
     * 子类下面一个没填就是‘全部空’，如果有部分检查的数据就是‘已检查Part’，全是检查的数据就是‘已检查ALL’。
     * 子类下面有填的数据，如果全部都填了且数据一样就是‘全相同’，有不一样就是‘有不同’，如果有没填的就是‘有遗漏’，如果没填的都检查了则是‘有遗漏OK’
     *
     * @param confirmCount 确认数量 已检查和已填基类的数据，开关信号还需要填含义
     */
    private String getChildState(List<SignalBaseClassDetailDTO> signalBaseClassList, AtomicInteger confirmCount) {
        if (signalBaseClassList.stream().anyMatch(signalBaseClass -> Objects.nonNull(signalBaseClass.getBaseTypeId()))) {
            boolean omitJudge = false;
            boolean omitOkJudge = true;
            boolean sameJudge = true;
            for (SignalBaseClassDetailDTO baseClassDTO : signalBaseClassList) {
                if (Objects.isNull(baseClassDTO.getBaseTypeId()) || (CharSequenceUtil.isNotBlank(baseClassDTO.getMeanings()) && Objects.isNull(baseClassDTO.getBaseCondId()))) {
                    if (CharSequenceUtil.isBlank(baseClassDTO.getSubState())) {
                        omitOkJudge = false;
                    }
                    omitJudge = true;
                }
                if (baseClassDTO.getSignalCategory() == 2) {
                    // 开关量
                    if (Objects.nonNull(baseClassDTO.getBaseTypeId())
                            && (Objects.isNull(baseClassDTO.getStateValue()) || Objects.nonNull(baseClassDTO.getBaseCondId()))
                            || CharSequenceUtil.isNotBlank(baseClassDTO.getSubState())) {
                        confirmCount.getAndIncrement();
                    }
                } else {
                    if (Objects.nonNull(baseClassDTO.getBaseTypeId()) || CharSequenceUtil.isNotBlank(baseClassDTO.getSubState())) {
                        confirmCount.getAndIncrement();
                    }
                }
                SignalBaseClassDetailDTO signalBaseClass1 = signalBaseClassList.get(0);
                sameJudge = sameJudge && (Objects.equals(baseClassDTO.getBaseTypeId(), signalBaseClass1.getBaseTypeId()) && Objects.equals(baseClassDTO.getBaseCondId(), signalBaseClass1.getBaseCondId()));
            }
            if (omitOkJudge && omitJudge) {
                return "有遗漏OK";
            }
            if (omitJudge) {
                return "有遗漏";
            }
            if (sameJudge) {
                return "全相同";
            } else {
                return "有不同";
            }
        } else {
            // 数据全为空的
            boolean emptyJudge = true;
            boolean confirmJudge = true;
            for (SignalBaseClassDetailDTO baseClassDTO : signalBaseClassList) {
                emptyJudge = emptyJudge && CharSequenceUtil.isBlank(baseClassDTO.getSubState());
                confirmJudge = confirmJudge && CharSequenceUtil.isNotBlank(baseClassDTO.getSubState());
                if (CharSequenceUtil.isNotBlank(baseClassDTO.getSubState())) {
                    // 存在检查
                    confirmCount.getAndIncrement();
                }
            }
            if (emptyJudge) {
                return "全部空";
            }
            if (confirmJudge) {
                return "已检查ALL";
            }
            return "已检查Part";

        }
    }

    private List<SignalBaseClassDetailDTO> findSignalBaseClassDetailList(Integer equipmentBaseType, String signalName, String meanings) {
        List<SignalBaseClassDetailDTO> signalBaseClassDetails = signalMapper.findSignalBaseClassDetails(equipmentBaseType, signalName, meanings);
        buildSignalBaseClassDetail(signalBaseClassDetails);
        return signalBaseClassDetails;
    }

    private void buildSignalBaseClassDetail(List<SignalBaseClassDetailDTO> signalBaseClassDetails) {
        Map<Integer, String> signalCategoryNameMap = dataItemService.findMapByEntryId(DataEntryEnum.SIGNAL_CATEGORY);
        Map<Integer, String> signalTypeNameMap = dataItemService.findMapByEntryId(DataEntryEnum.SIGNAL_TYPE);
        List<SignalBaseStatusDTO> signalBaseStatusList = signalBaseDicMapper.findSignalBaseStatusList();
        // 将列表转换为 Map，Key 为 baseTypeId 和 baseCondId 组合，Value 为 baseMeaning
        Map<String, String> statusMap = signalBaseStatusList.stream()
                .collect(Collectors.toMap(
                        status -> buildBaseMeanKey(status.getBaseTypeId(), status.getBaseCondId()), // 组合 key
                        SignalBaseStatusDTO::getBaseMeaning,
                        (existing, replacement) -> existing // 处理重复 key，保留第一个
                ));
        List<TblEquipmentBaseType> equipmentBaseTypes = equipmentBaseTypeMapper.selectList(Wrappers.emptyWrapper());
        Map<Integer, String> equipmentBaseTypeMap = equipmentBaseTypes.stream()
                .collect(Collectors.toMap(TblEquipmentBaseType::getBaseEquipmentId, TblEquipmentBaseType::getBaseEquipmentName, ((v1, v2) -> v1)));
        List<TblSignalBaseConfirm> signalBaseConfirms = signalBaseConfirmMapper.selectList(Wrappers.emptyWrapper());
        Map<String, String> confirmMap = signalBaseConfirms.stream()
                .collect(Collectors.toMap(
                        confirm -> buildConfirmKey(confirm.getEquipmentTemplateId(), confirm.getSignalId(), confirm.getStateValue()),
                        TblSignalBaseConfirm::getSubState,
                        (existing, replacement) -> existing
                ));
        signalBaseClassDetails.forEach(bs -> {
            bs.setSignalCategoryName(signalCategoryNameMap.get(bs.getSignalCategory()));
            bs.setSignalTypeName(signalTypeNameMap.get(bs.getSignalType()));
            bs.setBaseMeaning(statusMap.getOrDefault(buildBaseMeanKey(bs.getBaseTypeId(), bs.getBaseCondId()), null));
            bs.setEquipmentBaseTypeName(equipmentBaseTypeMap.get(bs.getEquipmentBaseType()));
            bs.setSubState(confirmMap.getOrDefault(buildConfirmKey(bs.getEquipmentTemplateId(), bs.getSignalId(), bs.getStateValue()), null));
        });
        signalBaseClassDetails.sort(Comparator.comparing(SignalBaseClassDetailDTO::getEquipmentBaseType, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SignalBaseClassDetailDTO::getSignalName, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SignalBaseClassDetailDTO::getMeanings, Comparator.nullsLast(Comparator.naturalOrder())));
    }

    private String buildBaseMeanKey(Long baseTypeId, Integer baseCondId) {
        return baseTypeId + "_" + Objects.toString(baseCondId, "");
    }

    private String buildConfirmKey(Integer equipmentTemplateId, Integer signalId, Integer stateValue) {
        return equipmentTemplateId + "_" + signalId + "_" + Objects.toString(stateValue, "");
    }

    @Override
    public List<SignalBaseClassDetailDTO> findSignalBaseClassDetails(Integer equipmentBaseType, String signalName, String meanings) {
        List<SignalBaseClassDetailDTO> signalBaseClassDetails = findSignalBaseClassDetailList(equipmentBaseType, signalName, meanings);
        if (CollUtil.isEmpty(signalBaseClassDetails)) {
            return Collections.emptyList();
        }
        signalBaseClassDetails.sort(Comparator.comparing(SignalBaseClassDetailDTO::getEquipmentTemplateName, Comparator.nullsLast(Comparator.naturalOrder())));
        return signalBaseClassDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearSignalBaseTypeAndCondId(List<SignalBaseTypeConditionDTO> signalBaseTypeDTOList) {
        if (CollUtil.isEmpty(signalBaseTypeDTOList)) {
            return;
        }
        List<SignalBaseClassDetailDTO> signalBaseClass = signalMapper.findSignalBaseClass(signalBaseTypeDTOList.get(0).getEquipmentBaseType());
        List<SignalConditionDTO> signalConditionDTOList = signalBaseClass.stream()
                .filter(detail -> signalBaseTypeDTOList.stream().anyMatch(condition ->
                        (Objects.equals(condition.getEquipmentBaseType(), detail.getEquipmentBaseType())) &&
                                (Objects.equals(condition.getSignalName(), detail.getSignalName())) &&
                                (Objects.equals(condition.getMeanings(), detail.getMeanings()))
                ))
                .map(m -> {
                    SignalConditionDTO signalConditionDTO = new SignalConditionDTO();
                    signalConditionDTO.setEquipmentTemplateId(m.getEquipmentTemplateId());
                    signalConditionDTO.setSignalId(m.getSignalId());
                    signalConditionDTO.setSignalName(m.getSignalName());
                    signalConditionDTO.setStateValue(m.getStateValue());
                    signalConditionDTO.setBaseTypeId(null);
                    signalConditionDTO.setBaseCondId(null);
                    return signalConditionDTO;
                })
                .toList();
        if (CollUtil.isEmpty(signalConditionDTOList)) {
            return;
        }
        // 清除基类id以及含义
        updateBaseTypeAndCond(signalConditionDTOList, Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSignalBaseTypeAndCondId(BaseTypeCondIdDTO<SignalConditionDTO> baseTypeCondIdDTO) {
        List<SignalConditionDTO> signalConditions = baseTypeCondIdDTO.getConditionIds();
        if (CollUtil.isEmpty(signalConditions)) {
            return;
        }
        if (Boolean.TRUE.equals(baseTypeCondIdDTO.getStateFlag())) {
            // 修改检查确认情况
            updateConfirm(baseTypeCondIdDTO);
        } else {
            signalConditions.forEach(signalCondition -> {
                signalCondition.setBaseTypeId(baseTypeCondIdDTO.getBaseTypeId());
                signalCondition.setBaseCondId(baseTypeCondIdDTO.getBaseCondId());
            });
            // 修改基类id以及含义
            updateBaseTypeAndCond(signalConditions, Boolean.TRUE.equals(baseTypeCondIdDTO.getCoverFlag()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disposeSimilarSignal(SimilarDataDTO similarSignalDTO) {
        Integer startNumber = similarSignalDTO.getStartNumber();
        Integer abortNumber = similarSignalDTO.getAbortNumber();
        if (Objects.isNull(startNumber) || startNumber < 0 || startNumber > abortNumber) {
            throw new InvalidParameterException("startNumber Exception");
        }
        if (abortNumber > 999) {
            throw new InvalidParameterException(i18n.T("abortNumber.format"));
        }
        String wildcard = similarSignalDTO.getWildcard();
        if (StringUtils.isBlank(wildcard) || !wildcard.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("wildcard.format"));
        }
        startNumber = Math.max(startNumber, 1);
        abortNumber = Math.max(abortNumber, 1);
        List<SignalConditionDTO> signalConditionList;
        if (Boolean.TRUE.equals(similarSignalDTO.getChildFlag())) {
            // 子类相似信号参数设置 找同模板同含义递增的数据
            signalConditionList = Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(similarSignalDTO.getEquipmentTemplateId())).orElseGet(ArrayList::new).stream()
                    .flatMap(signalConfigItem -> signalConfigItem.getNotEmptySignalMeaningsList().stream()
                            .filter(signalMeaning -> Objects.equals(signalMeaning.getMeanings(), similarSignalDTO.getMeanings()))
                            .map(signalMeaning -> {
                                SignalConditionDTO signalConditionDTO = new SignalConditionDTO();
                                signalConditionDTO.setEquipmentTemplateId(signalConfigItem.getEquipmentTemplateId());
                                signalConditionDTO.setSignalId(signalConfigItem.getSignalId());
                                signalConditionDTO.setSignalName(signalConfigItem.getSignalName());
                                signalConditionDTO.setStateValue(signalMeaning.getStateValue());
                                return signalConditionDTO;
                            })).toList();
        } else {
            // 父类相似信号参数设置
            // 通过基类设备类型和含义从缓存拿到对应信号
            signalConditionList = findSignalBaseClassDetailList(similarSignalDTO.getEquipmentBaseType(), null, similarSignalDTO.getMeanings())
                    .stream()
                    .map(tblSignal -> {
                        SignalConditionDTO signalConditionDTO = new SignalConditionDTO();
                        signalConditionDTO.setEquipmentTemplateId(tblSignal.getEquipmentTemplateId());
                        signalConditionDTO.setSignalId(tblSignal.getSignalId());
                        signalConditionDTO.setSignalName(tblSignal.getSignalName());
                        signalConditionDTO.setStateValue(tblSignal.getStateValue());
                        return signalConditionDTO;
                    })
                    .toList();
        }

        if (CollUtil.isEmpty(signalConditionList)) {
            return;
        }
        List<SignalConditionDTO> signalConditionDTOList = new ArrayList<>();
        long sourceBaseTypeId = (similarSignalDTO.getBaseTypeId() / 1000) * 1000;
        int abortNumberMax = 0;
        for (; startNumber <= abortNumber; startNumber++) {
            // 找名字递增的数据
            String signalName = CharSequenceUtil.indexedFormat(similarSignalDTO.getWildcard(), startNumber);
            String signalName2 = CharSequenceUtil.indexedFormat(similarSignalDTO.getWildcard(), String.format("%02d", startNumber));
            String signalName3 = CharSequenceUtil.indexedFormat(similarSignalDTO.getWildcard(), String.format("%03d", startNumber));
            List<SignalConditionDTO> signalConditions = signalConditionList.stream().filter(f ->
                    Objects.equals(f.getSignalName(), signalName) || Objects.equals(f.getSignalName(), signalName2) || Objects.equals(f.getSignalName(), signalName3)
            ).toList();
            if (CollUtil.isEmpty(signalConditions)) {
                continue;
            }
            abortNumberMax = Math.max(abortNumberMax, startNumber);
            // BaseTypeId递增
            long handleBaseTypeId = sourceBaseTypeId + startNumber;
            for (SignalConditionDTO signalCondition : signalConditions) {
                signalCondition.setBaseTypeId(handleBaseTypeId);
                signalCondition.setBaseCondId(similarSignalDTO.getBaseCondId());
            }
            signalConditionDTOList.addAll(signalConditions);
        }
        if (CollUtil.isEmpty(signalConditionDTOList)) {
            return;
        }
        // BaseTypeId会递增，不存在则会新增，只根据匹配到的最大名称数字来新增
        signalBaseDicService.batchAddSignalBaseDic(similarSignalDTO.getBaseTypeId(), similarSignalDTO.getStartNumber(), abortNumberMax);
        updateBaseTypeAndCond(signalConditionDTOList, Boolean.TRUE.equals(similarSignalDTO.getCoverFlag()));
    }

    @Override
    public void clearBaseTypeByEquipmentTemplate(Integer equipmentTemplateId) {
        List<SignalConfigItem> signalConfigItems = signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId);
        if (CollUtil.isEmpty(signalConfigItems)) {
            return;
        }
        mybatisBatchUtils.batchUpdateOrInsert(signalConfigItems, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignal::getSignalId, item.getSignalId())
                        .set(TblSignal::getBaseTypeId, null)
                ));
        mybatisBatchUtils.batchUpdateOrInsert(signalConfigItems, TblSignalMeaningsMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignalMeanings.class)
                        .eq(TblSignalMeanings::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignalMeanings::getSignalId, item.getSignalId())
                        .set(TblSignalMeanings::getBaseCondId, null)
                ));
    }


    @Override
    public SignalConfigItem findMaxSignalByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalMapper.findMaxSignalByEquipmentTemplateId(equipmentTemplateId);
    }

    /**
     * 修改基类id以及含义
     *
     * @param coverFlag 覆盖标志 true则覆盖已有值的  false不覆盖
     */
    private void updateBaseTypeAndCond(List<SignalConditionDTO> signalConditions, boolean coverFlag) {
        List<SignalConditionDTO> emptyConditionIds = new ArrayList<>();
        List<SignalConditionDTO> noCoverConditionIds = new ArrayList<>();
        List<TblSignal> tblSignals = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class)
                .in(TblSignal::getEquipmentTemplateId, signalConditions.stream().map(SignalConditionDTO::getEquipmentTemplateId).collect(Collectors.toSet())));
        for (SignalConditionDTO signalConditionId : signalConditions) {
            TblSignal signalConfigItem = tblSignals.stream()
                    .filter(tblSignal -> Objects.equals(tblSignal.getEquipmentTemplateId(), signalConditionId.getEquipmentTemplateId())
                            && Objects.equals(tblSignal.getSignalId(), signalConditionId.getSignalId()))
                    .findFirst()
                    .orElseGet(TblSignal::new);
            // 当baseTypeId与原来不同时，需要清空tbl_signalmeanings之前的BaseCondId
            if (!Objects.equals(signalConfigItem.getBaseTypeId(), signalConditionId.getBaseTypeId())) {
                emptyConditionIds.add(SignalConditionDTO.builder()
                        .equipmentTemplateId(signalConditionId.getEquipmentTemplateId())
                        .signalId(signalConditionId.getSignalId())
                        .build());
            }
            // 不覆盖，仅赋值未设置的基类ID的数据
            if (Boolean.FALSE.equals(coverFlag) && Objects.isNull(signalConfigItem.getBaseTypeId())) {
                noCoverConditionIds.add(SignalConditionDTO.builder()
                        .equipmentTemplateId(signalConditionId.getEquipmentTemplateId())
                        .signalId(signalConditionId.getSignalId())
                        .stateValue(signalConditionId.getStateValue())
                        .baseTypeId(signalConditionId.getBaseTypeId())
                        .baseCondId(signalConditionId.getBaseCondId())
                        .build());
            }
        }
        if (Boolean.FALSE.equals(coverFlag)) {
            // 不覆盖
            if (CollUtil.isNotEmpty(noCoverConditionIds)) {
                updateBaseType(noCoverConditionIds);
            }
        } else {
            // 全部覆盖
            if (CollUtil.isNotEmpty(emptyConditionIds)) {
                // 当baseTypeId与原来不同时，需要清空tbl_signalmeanings之前的BaseCondId
                mybatisBatchUtils.batchUpdateOrInsert(emptyConditionIds, TblSignalMeaningsMapper.class, (item, mapper) ->
                        mapper.update(Wrappers.lambdaUpdate(TblSignalMeanings.class)
                                .eq(TblSignalMeanings::getEquipmentTemplateId, item.getEquipmentTemplateId())
                                .eq(TblSignalMeanings::getSignalId, item.getSignalId())
                                .set(TblSignalMeanings::getBaseCondId, null)
                        ));
            }
            // 设置基类
            updateBaseType(signalConditions);
        }
    }

    /**
     * 修改检查确认情况
     */
    private void updateConfirm(BaseTypeCondIdDTO<SignalConditionDTO> baseTypeCondIdDTO) {
        List<SignalConditionDTO> signalConditions = baseTypeCondIdDTO.getConditionIds();
        mybatisBatchUtils.batchUpdateOrInsert(signalConditions, TblSignalBaseConfirmMapper.class, (item, mapper) ->
                mapper.delete(Wrappers.lambdaUpdate(TblSignalBaseConfirm.class)
                        .eq(TblSignalBaseConfirm::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignalBaseConfirm::getSignalId, item.getSignalId())
                        .eq(Objects.nonNull(item.getStateValue()), TblSignalBaseConfirm::getStateValue, item.getStateValue())
                ));
        // 设置基类情况为已检查
        if (CharSequenceUtil.isNotBlank(baseTypeCondIdDTO.getSubState())) {
            mybatisBatchUtils.batchUpdateOrInsert(signalConditions, TblSignalBaseConfirmMapper.class, (item, mapper) ->
                    mapper.insert(TblSignalBaseConfirm.builder()
                            .equipmentTemplateId(item.getEquipmentTemplateId())
                            .signalId(item.getSignalId())
                            .stateValue(item.getStateValue())
                            .subState(baseTypeCondIdDTO.getSubState())
                            .build())
            );
        }
    }

    /**
     * 修改基类
     */
    private void updateBaseType(List<SignalConditionDTO> signalConditions) {
        mybatisBatchUtils.batchUpdateOrInsert(signalConditions, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignal::getSignalId, item.getSignalId())
                        .set(TblSignal::getBaseTypeId, item.getBaseTypeId())
                ));
        // 设置基类含义ID，仅开关信号有
        mybatisBatchUtils.batchUpdateOrInsert(signalConditions, TblSignalMeaningsMapper.class, (item, mapper) -> {
            if (Objects.nonNull(item.getStateValue())) {
                mapper.update(Wrappers.lambdaUpdate(TblSignalMeanings.class)
                        .eq(TblSignalMeanings::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignalMeanings::getSignalId, item.getSignalId())
                        .eq(TblSignalMeanings::getStateValue, item.getStateValue())
                        .set(TblSignalMeanings::getBaseCondId, item.getBaseCondId())
                );
            }
        });
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 通知缓存
                List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.selectBatchIds(signalConditions
                        .stream().map(SignalConditionDTO::getEquipmentTemplateId).collect(Collectors.toSet()));
                changeEventService.sendBatchUpdate(equipmentTemplateList);
            }
        });

    }

    @Override
    public List<EquipmentSignalDTO> findSignalsByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return Collections.emptyList();
        }
        return signalMapper.findSignalsByEquipmentIdAndSignalIds(equipmentId, signalIds);
    }

    @Override
    public List<SimpleEventSignalDTO> findSignalEvent(Integer equipmentId) {
        return signalMapper.findSignalEvent(equipmentId);
    }

    @Override
    public SignalConfigItem getSignalInfo(Integer equipmentTemplateId, Integer signalId) {
        if (ObjectUtil.isEmpty(equipmentTemplateId) || ObjectUtil.isEmpty(signalId)) {
            return null;
        }
        QueryWrapper<TblSignal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId);
        List<TblSignal> tblSignals = signalMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(tblSignals)) {
            return null;
        }
        List<TblSignalProperty> tblSignalPropertyList = signalPropertyService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
        List<TblSignalMeanings> tblSignalMeaningsList = signalMeaningsService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
        SignalConfigItem signalConfigItem = new SignalConfigItem();
        BeanUtil.copyProperties(tblSignals.get(0), signalConfigItem);
        signalConfigItem.setSignalPropertyList(tblSignalPropertyList);
        signalConfigItem.setSignalMeaningsList(tblSignalMeaningsList);
        return signalConfigItem;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // 标准化操作前备份
        standardBackMapper.delete(Wrappers.lambdaUpdate(TblStandardBack.class).eq(TblStandardBack::getEntryCategory, 1));
        standardBackMapper.backupSignalStandard(standardId, equipmentTemplateIds);
        // 标准化操作
        List<SignalApplyStandardDTO> applyStandards = signalMapper.getApplyStandards(standardId, equipmentTemplateIds);
        if (CollUtil.isEmpty(applyStandards)) {
            return 0L;
        }
        mybatisBatchUtils.batchUpdateOrInsert(applyStandards, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .eq(TblSignal::getId, item.getId())
                        .set(TblSignal::getSignalName, item.getSignalName())
                        .set(TblSignal::getStoreInterval, item.getStoreInterval())
                        .set(TblSignal::getAbsValueThreshold, item.getAbsValueThreshold())
                        .set(TblSignal::getPercentThreshold, item.getPercentThreshold())
                ));
        return (long) applyStandards.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long restoreStandard() {
        long count = signalMapper.restoreStandard();
        standardBackMapper.delete(Wrappers.lambdaUpdate(TblStandardBack.class).eq(TblStandardBack::getEntryCategory, 1));
        return count;
    }

    @Override
    public List<StandardSignalCompareDTO> getStandardCompareData() {
        return signalMapper.getStandardCompareData();
    }

    @Override
    public List<StandardApplySignalCheckDTO> getSignalStandardApplyCheckData(Integer standardId) {
        return signalMapper.getSignalStandardApplyCheckData(standardId);
    }

    @Override
    public List<StandardMappingSignalCheckDTO> getSignalStandardMappingCheck(Integer standardId, Integer equipmentCategory) {
        return signalMapper.getSignalStandardMappingCheck(standardId, equipmentCategory);
    }

    @Override
    public void applySignalPointMapping(StandardPointMappingDTO mappingDTO) {
        String desLike = String.format("%d-%d", mappingDTO.getEquipmentCategoryId(), mappingDTO.getPointId());
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(mappingDTO.getEquipmentTemplateId());
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("设备模板不存在");
        }
        if (!Objects.equals(equipmentTemplate.getEquipmentCategory(), mappingDTO.getEquipmentCategoryId())) {
            throw new BusinessException("不同设备类型的数据不能绑定");
        }
        if (Objects.isNull(mappingDTO.getExtendStartNum())) {
            Integer mappingId = mappingDTO.getMappingId();
            TblSignal signal = signalMapper.selectOne(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .eq(TblSignal::getSignalId, mappingId));
            if (Objects.isNull(signal)) {
                throw new BusinessException("绑定信号不存在");
            }
            Long count = signalMapper.selectCount(Wrappers.lambdaQuery(TblSignal.class)
                    .likeRight(TblSignal::getDescription, desLike)
                    .eq(TblSignal::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .ne(TblSignal::getId, signal.getId()));
            if (count > 0) {
                throw new BusinessException("同一标准测点不能绑定多个信号");
            }
            String description = String.format("%s-%03d", desLike, 0);
            signalMapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                    .set(TblSignal::getDescription, description)
                    .eq(TblSignal::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .eq(TblSignal::getSignalId, mappingId));
        } else {
            // 有扩展数字，可以绑定多个字段
            List<Integer> mappingIds = mappingDTO.getMappingIds();
            List<TblSignal> signalList = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .in(TblSignal::getSignalId, mappingIds).orderByAsc(TblSignal::getDisplayIndex));
            if (CollUtil.isEmpty(signalList)) {
                throw new BusinessException("绑定信号不存在");
            }
            int extendStartNum = Math.max(mappingDTO.getExtendStartNum(), 1);
            if (extendStartNum > 999) {
                throw new BusinessException("起始序号必须是1到999之间的整数");
            }
            int endNum = extendStartNum + signalList.size();
            if (endNum > 999) {
                throw new BusinessException("绑定的信号数量会导致id超过999，请重新检查绑定");
            }

            List<String> dataDesList = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class)
                            .select(TblSignal::getDescription)
                            .likeRight(TblSignal::getDescription, desLike))
                    .stream().map(TblSignal::getDescription).toList();
            int startNumber = extendStartNum;
            List<TblSignal> updateSignals = new ArrayList<>();
            List<String> desList = new ArrayList<>();
            for (TblSignal signal : signalList) {
                String description = String.format("%s-%03d", desLike, startNumber++);
                signal.setDescription(description);
                desList.add(description);
                updateSignals.add(signal);
            }
            Set<String> containDes = CollUtil.intersectionDistinct(dataDesList, desList);
            if (CollUtil.isNotEmpty(containDes)) {
                throw new BusinessException("请重新检查绑定，增加的序号已被绑定：" + String.join(",", containDes));
            }
            mybatisBatchUtils.batchUpdateOrInsert(updateSignals, TblSignalMapper.class, (item, mapper) ->
                    mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                            .set(TblSignal::getDescription, item.getDescription())
                            .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                            .eq(TblSignal::getSignalId, item.getSignalId()))
            );
        }

    }

    @Override
    public void unbindSignalPointMapping(StandardPointUnbindDTO standardPointUnbindDTO) {
        String mappingIds = standardPointUnbindDTO.getMappingIds();
        List<String> mappingIdList = CharSequenceUtil.split(mappingIds, ",");
        signalMapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                .set(TblSignal::getDescription, "")
                .eq(TblSignal::getEquipmentTemplateId, standardPointUnbindDTO.getEquipmentTemplateId())
                .in(TblSignal::getSignalId, mappingIdList));
    }

    @Override
    public void updateStandardPointCategory(StandardPointCategoryDTO standardPointCategoryDTO) {
        List<TblSignal> signalList = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, standardPointCategoryDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        List<TblSignal> updateSignals = new ArrayList<>();
        for (TblSignal signal : signalList) {
            String description = signal.getDescription();
            if (CharSequenceUtil.isNotBlank(description) && description.length() >= 12) {
                String desSub = description.substring(description.indexOf('-') + 1);
                signal.setDescription(String.format("%s-%s", standardPointCategoryDTO.getEquipmentCategoryId(), desSub));
                updateSignals.add(signal);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateSignals, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .set(TblSignal::getDescription, item.getDescription())
                        .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignal::getSignalId, item.getSignalId()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        List<TblSignal> signalList = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, standardUnmappedPointsDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        List<TblSignal> updateSignals = new ArrayList<>();
        for (TblSignal signal : signalList) {
            String description = signal.getDescription();
            if (CharSequenceUtil.isBlank(description)) {
                String lastSix = signal.getSignalId() <= 0 ? String.format("%06d", 999999) : CharSequenceUtil.subSufByLength(String.valueOf(signal.getSignalId()), 6);
                signal.setDescription(String.format("%s-%d-%s", standardUnmappedPointsDTO.getEquipmentCategoryId(), 991, lastSix));
                updateSignals.add(signal);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateSignals, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .set(TblSignal::getDescription, item.getDescription())
                        .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignal::getSignalId, item.getSignalId()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        List<TblSignal> signalList = signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, standardUnmappedPointsDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        List<TblSignal> updateSignals = new ArrayList<>();
        for (TblSignal signal : signalList) {
            String description = signal.getDescription();
            if (CharSequenceUtil.isNotBlank(description) && description.contains("-991-")) {
                signal.setDescription("");
                updateSignals.add(signal);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateSignals, TblSignalMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblSignal.class)
                        .set(TblSignal::getDescription, item.getDescription())
                        .eq(TblSignal::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblSignal::getSignalId, item.getSignalId()))
        );
    }

    /**
     * 批量复制信号字段，支持普通字段更新及特殊字段（属性、含义）复制
     *
     * @param dtoList DTO 列表
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean fieldCopy(List<SignalFieldCopyDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) return true;
        List<TblSignal> signalList = new ArrayList<>(dtoList.size());
        for (SignalFieldCopyDTO dto : dtoList) {
            Integer equipmentTemplateId = dto.getEquipmentTemplateId();
            Integer signalId = dto.getSignalId();
            String fieldName = dto.getFieldName();
            String fieldValue = dto.getFieldValue();
            // 复制 signalPropertyList 字段
            if ("signalPropertyList".equals(fieldName)) {
                handleSignalProperty(equipmentTemplateId, signalId, fieldValue);
                continue;
            }
            // 复制 signalMeaningsList 字段
            if ("signalMeaningsList".equals(fieldName)) {
                handleSignalMeanings(equipmentTemplateId, signalId, fieldValue);
                continue;
            }
            // 普通字段处理：反射赋值
            TblSignal signal = ReflectUtil.newInstance(TblSignal.class);
            signal.setEquipmentTemplateId(equipmentTemplateId);
            signal.setSignalId(signalId);
            ReflectUtil.setFieldValue(signal, fieldName, fieldValue);
            signalList.add(signal);
        }
        // 批量更新主表字段
        if (!signalList.isEmpty()) {
            signalMapper.batchUpdateField(signalList);
        }
        return true;
    }

    @Override
    public List<SignalExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalMapper.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
    }

    /**
     * 处理 signalPropertyList 字段：复制信号属性
     */
    private void handleSignalProperty(Integer srcTemplateId, Integer srcSignalId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destSignalId = Integer.parseInt(dest[1]);
        List<TblSignalProperty> properties = signalPropertyService.findByEquipmentTemplateIdAndSignalId(srcTemplateId, srcSignalId);
        List<TblSignalProperty> copied = properties.stream()
                                                   .map(p -> {
                                                       p.setId(null);
                                                       p.setEquipmentTemplateId(destTemplateId);
                                                       p.setSignalId(destSignalId);
                                                       return p;
                                                   }).toList();
        signalPropertyService.updateSignalProperty(destTemplateId, destSignalId, copied);
    }
    /**
     * 处理 signalMeaningsList 字段：复制信号含义
     */
    private void handleSignalMeanings(Integer srcTemplateId, Integer srcSignalId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destSignalId = Integer.parseInt(dest[1]);
        List<TblSignalMeanings> meanings = signalMeaningsService.findByEquipmentTemplateIdAndSignalId(srcTemplateId, srcSignalId);
        List<TblSignalMeanings> copied = meanings.stream()
                                                 .map(m -> {
                                                     m.setId(null);
                                                     m.setEquipmentTemplateId(destTemplateId);
                                                     m.setSignalId(destSignalId);
                                                     return m;
                                                 }).toList();
        signalMeaningsService.updateSignalMeanings(destTemplateId, destSignalId, copied);
    }

    @Override
    public List<TblSignal> findByEquipmentCategory(Integer equipmentCategory) {
        return signalMapper.findByEquipmentCategory(equipmentCategory);
    }

    @Override
    public List<SignalConfigPointDTO> findSignalPoints(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("设备模板不存在");
        }
        List<SignalConfigItem> signalConfigItemList = signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId);
        if (CollUtil.isEmpty(signalConfigItemList)) {
            return List.of();
        }
        List<StandardPoint> standardPoints = standardPointService.getSignalStandardPoints(equipmentTemplate.getEquipmentCategory());
        Map<String, String> pointMap = standardPoints.stream()
                .collect(Collectors.toMap(
                        s -> String.format("%d-%d", s.getEquipmentCategoryId(), s.getPointId()),
                        StandardPoint::getPointName
                ));
        return signalConfigItemList.stream().map(s -> {
            SignalConfigPointDTO signalConfigPoint = BeanUtil.copyProperties(s, SignalConfigPointDTO.class);
            signalConfigPoint.setMappingPointName(CharSequenceUtil.isBlank(s.getDescription()) ? null : pointMap.get(CharSequenceUtil.sub(s.getDescription(), 0, s.getDescription().lastIndexOf('-'))));
            return signalConfigPoint;
        }).sorted(Comparator.comparingInt(SignalConfigPointDTO::getDisplayIndex)).toList();
    }

    @Override
    public List<SignalConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId) {
        return Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparingInt(SignalConfigItem::getDisplayIndex))
                .toList();
    }

    @Override
    public void updateWorkStationSignalName(String prefixName, int equipmentTemplateId) {
        signalMapper.updateWorkStationSignalName(prefixName, equipmentTemplateId);
    }

    @Override
    public void updateDBWorkStationSignalName(String signalName, Integer equipmentTemplateId, int signalId) {
        signalMapper.update(null, new UpdateWrapper<TblSignal>()
                .set("SignalName", signalName)
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSignal(SignalConfigItem signalConfigItem) {
        TblSignal signal = BeanUtil.copyProperties(signalConfigItem, TblSignal.class, "id", "signalId");
        createSignal(signal);
        if (CollUtil.isNotEmpty(signalConfigItem.getSignalMeaningsList())) {
            signalConfigItem.getSignalMeaningsList().forEach(e -> e.setSignalId(signal.getSignalId()));
            signalMeaningsService.batchCreateSignalMeanings(signalConfigItem.getSignalMeaningsList());
        }
        if (CollUtil.isNotEmpty(signalConfigItem.getSignalPropertyList())) {
            signalConfigItem.getSignalPropertyList().forEach(e -> e.setSignalId(signal.getSignalId()));
            signalPropertyService.batchCreateSignalProperty(signalConfigItem.getSignalPropertyList());
        }
        sendUpdateMsgByEquipmentTemplateId(signalConfigItem.getEquipmentTemplateId());
        signalConfigItem.setId(signal.getId());
        signalConfigItem.setSignalId(signal.getSignalId());
    }

    @Override
    public void updateSelfDiagnosisSignal(int equipmentTemplateId, int centerId) {
        signalMapper.updateSelfDiagnosisSignal(equipmentTemplateId, centerId);
    }

    @Override
    public void batchInsertLianTongSignal() {
        signalMapper.batchInsertLianTongSignal();
    }

    @Override
    public SignStatusMeaningGroupDTO findEquipmentBaseTypeMeaningsBySignal(Integer equipmentTemplateId, Integer signalId) {
        List<SignStatusMeaningDTO> equipmentBaseTypeMeaningsBySignal = statusBaseDicMapper.findEquipmentBaseTypeMeaningsBySignal(equipmentTemplateId, signalId);
        if (CollUtil.isEmpty(equipmentBaseTypeMeaningsBySignal)) {
            return null;
        }
        Map<String, List<SignStatusMeaningDTO>> signStatusMeaningMap = equipmentBaseTypeMeaningsBySignal.stream().collect(Collectors.groupingBy(SignStatusMeaningDTO::getBaseStatusName));
        Set<String> meanMapKeys = signStatusMeaningMap.keySet();
        if (meanMapKeys.size() > 1) {
            throw new BusinessException("baseStatusName greater than one exception");
        }
        String meanMapKey = meanMapKeys.stream().findFirst().orElse(CharSequenceUtil.EMPTY);
        SignStatusMeaningGroupDTO signStatusMeaningGroupDTO = new SignStatusMeaningGroupDTO();
        signStatusMeaningGroupDTO.setBaseStatusName(meanMapKey);
        signStatusMeaningGroupDTO.setStatusMeanings(signStatusMeaningMap.get(meanMapKey));
        return signStatusMeaningGroupDTO;
    }

    @Override
    public List<TblSignal> findByEquipmentTemplateIdAndChannelNo(Integer equipmentTemplateId, Integer channelNo) {
        return signalMapper.selectList(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getEquipmentTemplateId, equipmentTemplateId).eq(TblSignal::getChannelNo, channelNo));
    }

    @Override
    public List<SimplifySignalDTO> findSimplifySignal(Integer equipmentTemplateId) {
        return Optional.ofNullable(signalMapper.findSignalItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(ArrayList::new)
                .stream()
                .map(signalConfigItem -> SimplifySignalDTO
                        .builder()
                        .signalId(signalConfigItem.getSignalId())
                        .signalName(signalConfigItem.getSignalName())
                        .baseTypeName(signalConfigItem.getBaseTypeName())
                        .build())
                .sorted(CompareUtil.comparingPinyin(SimplifySignalDTO::getSignalName))
                .toList();
    }

    @Override
    public List<TblSignal> diffSignal(Integer originTemplateId, Integer destTemplateId) {
        return signalMapper.diffSignal(originTemplateId, destTemplateId);
    }

    @Override
    public List<TblSignal> findSameVirtualSignals(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId) {
        return signalMapper.findSameVirtualSignals(oldEquipmentTemplateId, newEquipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copySignal(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<TblSignal> signalList = findByEquipmentTemplateId(originEquipmentTemplateId);
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        signalList.forEach(signal -> signal.setEquipmentTemplateId(destEquipmentTemplateId));
        signalMapper.insertBatchSomeColumn(signalList);
        signalMeaningsService.copySignalMeanings(originEquipmentTemplateId, destEquipmentTemplateId);
        signalPropertyService.copySignalProperty(originEquipmentTemplateId, destEquipmentTemplateId);
    }

    @Override
    public Integer findMaxSignalIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxSignalId = signalMapper.findMaxSignalIdByEquipmentTemplateId(equipmentTemplateId);
        if (Objects.nonNull(maxSignalId)) {
            return ++maxSignalId;
        }
        return primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_SIGNAL, 0);
    }

    /**
     * 根据设备模板取得信号当前的DisplayIndex
     *
     * @param equipmentTemplateId 模板id
     * @return 该模板中最大的DisplayIndex
     */
    private Integer findCurrentDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxDisplayIndex = signalMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);
        if (Objects.isNull(maxDisplayIndex)) {
            return 1;
        }
        return ++maxDisplayIndex;
    }

    @Override
    public List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return signalMapper.findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        //信号相关
        signalMapper.delete(Wrappers.lambdaQuery(TblSignal.class)
                .eq(TblSignal::getEquipmentTemplateId, equipmentTemplateId));
        signalPropertyService.deleteByEquipmentTemplateId(equipmentTemplateId);
        signalMeaningsService.deleteByEquipmentTemplateId(equipmentTemplateId);
    }

    public void sendUpdateMsgByEquipmentTemplateId(Integer equipmentTemplateId) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
                if (Objects.isNull(equipmentTemplate)) {
                    return;
                }
                //信号的所有变更属于模板的更新
                changeEventService.sendUpdate(equipmentTemplate);
            }
        });
    }
}
