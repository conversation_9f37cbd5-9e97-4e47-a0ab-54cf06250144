package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblCommandBaseMap;
import org.siteweb.config.common.mapper.TblCommandBaseMapMapper;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.CommandBaseMapService;
import org.siteweb.config.primary.service.StandardTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommandBaseMapServiceImpl implements CommandBaseMapService {
    @Autowired
    TblCommandBaseMapMapper commandBaseMapMapper;
    @Autowired
    private StandardTypeService standardTypeService;
    @Override
    public long countByStandardDicIdAndStandardType(Integer standardDicId, Integer standardType){
        return commandBaseMapMapper.selectCount(Wrappers.lambdaQuery(TblCommandBaseMap.class)
                                                          .eq(TblCommandBaseMap::getStandardDicId, standardDicId)
                                                          .eq(TblCommandBaseMap::getStandardType, standardType));
    }

    @Override
    public int batchCreate(List<TblCommandBaseMap> commandBaseMapList) {
        if (CollUtil.isEmpty(commandBaseMapList)) {
            return 0;
        }
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        commandBaseMapList.forEach(e -> e.setStandardType(currentStandardType.getValue()));
        commandBaseMapMapper.insertBatchSomeColumn(commandBaseMapList);
        return commandBaseMapList.size();
    }

}
