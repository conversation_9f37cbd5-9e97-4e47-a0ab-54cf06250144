package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.BaseTypeTreeDTO;
import org.siteweb.config.common.dto.SignalBaseDicAndStatusDetail;
import org.siteweb.config.common.dto.SignalBaseDicsDTO;
import org.siteweb.config.common.entity.TblBaseSignalEventCode;
import org.siteweb.config.common.entity.TblSignal;
import org.siteweb.config.common.entity.TblSignalBaseDic;
import org.siteweb.config.common.entity.TblStatusBaseDic;
import org.siteweb.config.common.mapper.TblBaseSignalEventCodeMapper;
import org.siteweb.config.common.mapper.TblSignalBaseDicMapper;
import org.siteweb.config.common.mapper.TblSignalMapper;
import org.siteweb.config.common.mapper.TblStatusBaseDicMapper;
import org.siteweb.config.common.vo.BaseDicFilterVO;
import org.siteweb.config.primary.service.EquipmentBaseTypeService;
import org.siteweb.config.primary.service.SignalBaseDicService;
import org.siteweb.config.primary.service.StatusBaseDicService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class SignalBaseDicServiceImpl implements SignalBaseDicService {
    @Autowired
    private TblSignalBaseDicMapper signalBaseDicMapper;
    @Autowired
    I18n i18n;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    private TblStatusBaseDicMapper statusBaseDicMapper;
    @Autowired
    TblBaseSignalEventCodeMapper baseSignalEventCodeMapper;
    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;
    @Autowired
    StatusBaseDicService statusBaseDicService;
    @Autowired
    TblSignalMapper signalMapper;

    @Override
    public TblSignalBaseDic findByBaseTypeId(Long baseTypeId) {
        return signalBaseDicMapper.selectById(baseTypeId);
    }

    @Override
    public List<TblSignalBaseDic> findSignalBaseDic(BaseDicFilterVO baseDicFilterVO) {
        return signalBaseDicMapper.findSignalBaseDic(baseDicFilterVO);
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return signalBaseDicMapper.exists(Wrappers.lambdaQuery(TblSignalBaseDic.class)
                .eq(TblSignalBaseDic::getBaseTypeId, baseTypeId));
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        signalBaseDicMapper.generateSignalBaseDic(baseTypeId, sourceId);
        TblSignalBaseDic signalBaseDic = findByBaseTypeId(baseTypeId);
        if (Objects.isNull(signalBaseDic) || CharSequenceUtil.isBlank(signalBaseDic.getBaseNameExt())) {
            return;
        }
        Long moduleNo = baseTypeId % 1000;
        String baseTypeName = CharSequenceUtil.indexedFormat(signalBaseDic.getBaseNameExt(), moduleNo);
        signalBaseDic.setBaseTypeName(baseTypeName);
        signalBaseDicMapper.updateById(signalBaseDic);
    }

    @Override
    public List<SignalBaseDicsDTO> findSignalBaseDicList() {
        return signalBaseDicMapper.findSignalBaseDicList();
    }

    @Override
    public TblSignalBaseDic addSignalBaseDic(SignalBaseDicsDTO signalBaseDicsDTO) {
        if (Objects.isNull(signalBaseDicsDTO.getBaseEquipmentId())) {
            throw new InvalidParameterException("baseEquipmentId cannot be empty");
        }
        if (StringUtils.isBlank(signalBaseDicsDTO.getBaseTypeName())) {
            throw new InvalidParameterException("baseTypeName cannot be empty");
        }
        if (StringUtils.isNotBlank(signalBaseDicsDTO.getBaseNameExt()) && !signalBaseDicsDTO.getBaseNameExt().contains("{0}")) {
            throw new InvalidParameterException(i18n.T("signalBase.baseNameExt.format"));
        }
        Long baseTypeId = generateBaseTypeId(signalBaseDicsDTO.getBaseEquipmentId());
        TblSignalBaseDic tblSignalBaseDic = BeanUtil.copyProperties(signalBaseDicsDTO, TblSignalBaseDic.class, SignalBaseDicsDTO.Fields.baseTypeId);
        tblSignalBaseDic.setBaseTypeId(baseTypeId);
        tblSignalBaseDic.setIsSystem(Boolean.TRUE.equals(signalBaseDicsDTO.getIsSystem()));
        signalBaseDicMapper.insert(tblSignalBaseDic);
        return tblSignalBaseDic;
    }

    @Override
    public int batchAddSignalBaseDic(Long baseTypeId, Integer startNumber, Integer abortNumber) {
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("batchAdd Signal baseTypeId cannot be empty");
        }
        if (Objects.isNull(startNumber) || startNumber < 0 || startNumber > abortNumber) {
            throw new InvalidParameterException("startNumber Exception");
        }
        if (abortNumber > 999) {
            throw new InvalidParameterException(i18n.T("abortNumber.format"));
        }
        long sourceId = ((baseTypeId / 1000) * 1000) + 1;
        TblSignalBaseDic tblSignalBaseDic = signalBaseDicMapper.selectById(sourceId);
        if (Objects.isNull(tblSignalBaseDic)) {
            throw new BusinessException("source signalBaseDic does not exist");
        }
        String baseNameExt = tblSignalBaseDic.getBaseNameExt();
        if (StringUtils.isBlank(baseNameExt)) {
            throw new InvalidParameterException(i18n.T("baseNameExt.batchAdd"));
        }
        if (!baseNameExt.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("signalBase.baseNameExt.format"));
        }
        List<Long> baseTypeIdList = signalBaseDicMapper.selectList(Wrappers.lambdaQuery(TblSignalBaseDic.class)
                        .select(TblSignalBaseDic::getBaseTypeId)
                        .eq(TblSignalBaseDic::getBaseEquipmentId, tblSignalBaseDic.getBaseEquipmentId()))
                .stream().map(TblSignalBaseDic::getBaseTypeId).toList();
        List<TblSignalBaseDic> signalBaseDicList = new ArrayList<>();
        long sourceBaseTypeId = (tblSignalBaseDic.getBaseTypeId() / 1000) * 1000;
        startNumber = Math.max(startNumber, 1);
        abortNumber = Math.max(abortNumber, 1);
        for (; startNumber <= abortNumber; startNumber++) {
            long handleBaseTypeId = sourceBaseTypeId + startNumber;
            if (!baseTypeIdList.contains(handleBaseTypeId)) {
                TblSignalBaseDic signalBaseDic = BeanUtil.copyProperties(tblSignalBaseDic, TblSignalBaseDic.class);
                signalBaseDic.setBaseTypeId(handleBaseTypeId);
                signalBaseDic.setBaseTypeName(CharSequenceUtil.indexedFormat(baseNameExt, startNumber));
                signalBaseDic.setIsSystem(false);
                signalBaseDicList.add(signalBaseDic);
            }
        }
        if (CollUtil.isEmpty(signalBaseDicList)) {
            return 0;
        }
        return signalBaseDicMapper.insertBatchSomeColumn(signalBaseDicList);
    }

    @Override
    public void updateSignalBaseDic(SignalBaseDicsDTO signalBaseDicsDTO) {
        Long baseTypeId = signalBaseDicsDTO.getBaseTypeId();
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("baseTypeId cannot be empty");
        }
        TblSignalBaseDic tblSignalBaseDic = signalBaseDicMapper.selectById(baseTypeId);
        if (Objects.isNull(tblSignalBaseDic)) {
            throw new BusinessException("signalBaseDic does not exist");
        }
        if (Boolean.TRUE.equals(tblSignalBaseDic.getIsSystem())) {
            throw new BusinessException(i18n.T("signalBase.isSystem.update"));
        }
        if (signalMapper.exists(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getBaseTypeId, baseTypeId))) {
            throw new BusinessException(i18n.T("signalBase.baseTypeId.update"));
        }
        TblSignalBaseDic signalBaseDic = BeanUtil.copyProperties(signalBaseDicsDTO, TblSignalBaseDic.class);
        signalBaseDicMapper.updateById(signalBaseDic);
    }

    @Override
    public void deleteSignalBaseDic(Long baseTypeId) {
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("baseTypeId cannot be empty");
        }
        TblSignalBaseDic tblSignalBaseDic = signalBaseDicMapper.selectById(baseTypeId);
        if (Objects.isNull(tblSignalBaseDic)) {
            return;
        }
        if (Boolean.TRUE.equals(tblSignalBaseDic.getIsSystem())) {
            throw new BusinessException(i18n.T("signalBase.isSystem.delete"));
        }
        if (signalMapper.exists(Wrappers.lambdaQuery(TblSignal.class).eq(TblSignal::getBaseTypeId, baseTypeId))) {
            throw new BusinessException(i18n.T("signalBase.baseTypeId.delete"));
        }
        signalBaseDicMapper.deleteById(baseTypeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSignalBaseDic(SignalBaseDicsDTO signalBaseDicsDTO) {
        // 该方法前端暂未使用，后续可删除
        String baseNameExt = signalBaseDicsDTO.getBaseNameExt();
        if (StringUtils.isBlank(baseNameExt)) {
            throw new InvalidParameterException(i18n.T("baseNameExt.batchUpdate"));
        }
        if (!baseNameExt.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("signalBase.baseNameExt.format"));
        }
        long prefixSourceId = signalBaseDicsDTO.getBaseTypeId() / 1000;
        long sourceId = (prefixSourceId * 1000) + 1;
        TblSignalBaseDic sourceSignalBaseDic = signalBaseDicMapper.selectById(sourceId);
        if (Objects.isNull(sourceSignalBaseDic)) {
            throw new BusinessException("source signalBaseDic does not exist");
        }
        List<TblSignalBaseDic> tblSignalBaseDics = signalBaseDicMapper.selectList(Wrappers.lambdaQuery(TblSignalBaseDic.class)
                .likeRight(TblSignalBaseDic::getBaseTypeId, prefixSourceId));
        List<TblSignalBaseDic> signalBaseDicList = new ArrayList<>();
        for (TblSignalBaseDic tblSignalBaseDic : tblSignalBaseDics) {
            if (Boolean.TRUE.equals(tblSignalBaseDic.getIsSystem())) {
                continue;
            }
            TblSignalBaseDic signalBaseDic = BeanUtil.copyProperties(signalBaseDicsDTO, TblSignalBaseDic.class);
            signalBaseDic.setBaseTypeId(tblSignalBaseDic.getBaseTypeId());
            signalBaseDic.setBaseEquipmentId(tblSignalBaseDic.getBaseEquipmentId());
            signalBaseDic.setUnitId(tblSignalBaseDic.getUnitId());
            signalBaseDic.setBaseStatusId(tblSignalBaseDic.getBaseStatusId());
            signalBaseDic.setBaseTypeName(CharSequenceUtil.indexedFormat(baseNameExt, signalBaseDic.getBaseTypeId() % 1000));
            signalBaseDic.setIsSystem(tblSignalBaseDic.getIsSystem());
            signalBaseDicList.add(signalBaseDic);
        }
        mybatisBatchUtils.batchUpdateOrInsert(signalBaseDicList, TblSignalBaseDicMapper.class, (item, mapper) -> mapper.updateById(item));
    }

    @Override
    public List<BaseTypeTreeDTO> findSignalBaseDicTree(Integer equipmentBaseType, Integer signalCategory, Boolean isSystem) {
        // 通过baseEquipmentId找到对应的子类
        List<TblSignalBaseDic> signalBaseDics = signalBaseDicMapper.selectList(Wrappers.lambdaQuery(TblSignalBaseDic.class)
                .orderByAsc(TblSignalBaseDic::getBaseEquipmentId).orderByAsc(TblSignalBaseDic::getBaseTypeName));
        // 通过BaseStatusId找到底层节点
        List<TblStatusBaseDic> statusBaseDics = statusBaseDicMapper.selectList(Wrappers.emptyWrapper());
        return equipmentBaseTypeService.buildBaseDicTree(equipmentBaseType, baseEquipmentId ->
                getSignalBaseTypeTreeDTOS(baseEquipmentId, signalCategory, signalBaseDics, statusBaseDics, isSystem));
    }

    /**
     * 获取基类信号树子节点
     *
     * @param baseEquipmentId baseEquipmentId
     * @param signalCategory  信号种类
     * @param signalBaseDics  通过baseEquipmentId找到对应的子类
     * @param statusBaseDics  通过BaseStatusId找到底层节点
     * @param isSystem  系统内置的不需要显示最底层节点
     */
    private List<BaseTypeTreeDTO> getSignalBaseTypeTreeDTOS(Integer baseEquipmentId, Integer signalCategory, List<TblSignalBaseDic> signalBaseDics, List<TblStatusBaseDic> statusBaseDics, Boolean isSystem) {
        return signalBaseDics.stream()
                .filter(signalBaseDic -> judgeTreeCategore(signalCategory, baseEquipmentId, signalBaseDic, isSystem))
                .map(signalBaseDic -> {
                    BaseTypeTreeDTO signalBaseChild = new BaseTypeTreeDTO();
                    signalBaseChild.setKey(String.valueOf(signalBaseDic.getBaseTypeId()));
                    signalBaseChild.setValue(signalBaseDic.getBaseTypeId());
                    signalBaseChild.setLabel(signalBaseDic.getBaseTypeName());
                    signalBaseChild.setBaseNameExt(signalBaseDic.getBaseNameExt());
                    signalBaseChild.setIndex(2);
                    // 基类含义子节点
                    signalBaseChild.setChildren(Boolean.TRUE.equals(isSystem) ? Collections.emptyList() : statusBaseDicService.getStatusBaseTreeChildren(statusBaseDics, signalBaseDic.getBaseStatusId(), signalBaseDic.getBaseTypeId()));
                    return signalBaseChild;
                })
                .toList();
    }

    @Override
    public SignalBaseDicAndStatusDetail findSignalBaseAndStatusDetail(Long baseTypeId, Integer baseCondId) {
        SignalBaseDicsDTO signalBaseDic = signalBaseDicMapper.findSignalBaseDicByBaseTypeId(baseTypeId);
        if (Objects.isNull(signalBaseDic)) {
            return null;
        }
        SignalBaseDicAndStatusDetail signalBasedetail = BeanUtil.copyProperties(signalBaseDic, SignalBaseDicAndStatusDetail.class);
        signalBasedetail.setSignalStatus(Objects.nonNull(signalBasedetail.getBaseStatusId()) ? "开关量" : "模拟量");
        String baseTypeIdSting = String.valueOf(signalBasedetail.getBaseTypeId());
        String code = CharSequenceUtil.subWithLength(baseTypeIdSting, baseTypeIdSting.length() - 6, 3);
        TblBaseSignalEventCode tblBaseSignalEventCode = baseSignalEventCodeMapper.selectById(Integer.valueOf(code));
        if (Objects.nonNull(tblBaseSignalEventCode)) {
            signalBasedetail.setSignalCode(code + (CharSequenceUtil.isNotBlank(tblBaseSignalEventCode.getSignal()) ? " - " + tblBaseSignalEventCode.getSignal() : ""));
        } else {
            signalBasedetail.setSignalCode(code);
        }
        signalBasedetail.setSignalCodeCategory(getSignalCodeCategory(Integer.parseInt(code)));
        if (Objects.isNull(baseCondId) || Objects.isNull(signalBaseDic.getBaseStatusId())) {
            return signalBasedetail;
        }
        TblStatusBaseDic statusBaseDic = statusBaseDicMapper.selectOne(Wrappers.lambdaQuery(TblStatusBaseDic.class)
                .eq(TblStatusBaseDic::getBaseStatusId, signalBaseDic.getBaseStatusId())
                .eq(TblStatusBaseDic::getBaseCondId, baseCondId)
        );
        signalBasedetail.fetchStatusBaseDic(statusBaseDic);
        return signalBasedetail;
    }

    /**
     * 获取信号类别
     */
    public static String getSignalCodeCategory(int code) {
        if (code >= 1 && code <= 300) {
            return "重要基类信号";
        } else if (code >= 301 && code <= 800) {
            return "兼容电信和移动等标准化而引入的信号";
        } else if (code >= 801 && code <= 900) {
            return "现场自定义基类信号";
        } else if (code == 999) {
            return "设备通讯状态信号";
        } else {
            return "无法分类,信号基类ID定义有问题";
        }
    }

    /**
     * 判断基类信号种类
     *
     * @param signalCategory 2 开关量 则 BaseStatusId is not null 反之为空
     * @param isSystem       是否系统内置
     */
    private boolean judgeTreeCategore(Integer signalCategory, Integer baseEquipmentId, TblSignalBaseDic signalBaseDic, Boolean isSystem) {
        boolean baseEquipmentIdEqual = Objects.equals(signalBaseDic.getBaseEquipmentId(), baseEquipmentId);
        if (Boolean.FALSE.equals(baseEquipmentIdEqual)) {
            return false;
        }
        if (Boolean.TRUE.equals(isSystem)) {
            return Boolean.TRUE.equals(signalBaseDic.getIsSystem());
        }
        if (Objects.nonNull(signalCategory)) {
            // 开关量
            if (signalCategory == 2) {
                return Objects.nonNull(signalBaseDic.getBaseStatusId());
            } else {
                return Objects.isNull(signalBaseDic.getBaseStatusId());
            }
        }
        return baseEquipmentIdEqual;
    }

    /**
     * 生成baseTypeId
     *
     * @param baseEquipmentId 基类设备ID
     */
    private Long generateBaseTypeId(Integer baseEquipmentId) {
        List<Long> baseTypeIdList = signalBaseDicMapper.selectList(Wrappers.lambdaQuery(TblSignalBaseDic.class)
                        .select(TblSignalBaseDic::getBaseTypeId)
                        .eq(TblSignalBaseDic::getBaseEquipmentId, baseEquipmentId))
                .stream().map(TblSignalBaseDic::getBaseTypeId).toList();
        Long baseTypeId = null;
        for (int i = 899; i >= 101; i--) {
            String tempBaseTypeId = String.format("%d%d001", baseEquipmentId, i);
            // 避免信号ID重复
            if (!baseTypeIdList.contains(Long.valueOf(tempBaseTypeId))) {
                baseTypeId = Long.valueOf(tempBaseTypeId);
                break;
            }
        }
        if (Objects.isNull(baseTypeId)) {
            throw new BusinessException(i18n.T("signalBase.baseTypeId.overflow"));
        }
        return baseTypeId;
    }
}
