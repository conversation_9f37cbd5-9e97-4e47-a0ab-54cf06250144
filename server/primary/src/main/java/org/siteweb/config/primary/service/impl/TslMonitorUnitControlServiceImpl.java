package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TslMonitorUnitControl;
import org.siteweb.config.common.mapper.TslMonitorUnitControlMapper;
import org.siteweb.config.primary.service.TslMonitorUnitControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/17
 */
@Slf4j
@Service
public class TslMonitorUnitControlServiceImpl implements TslMonitorUnitControlService {

    @Autowired
    private TslMonitorUnitControlMapper tslMonitorUnitControlMapper;

    @Override
    public List<TslMonitorUnitControl> findByMonitorUnitId(Integer monitorUnitId) {
        return tslMonitorUnitControlMapper.selectList(new LambdaQueryWrapper<>(TslMonitorUnitControl.class)
                .eq(TslMonitorUnitControl::getMonitorUnitId, monitorUnitId));
    }
}
