package org.siteweb.config.primary.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.StOrgCuccEquip;
import org.siteweb.config.common.entity.TblStandardDicControl;
import org.siteweb.config.common.enums.ModifyTypeEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.StOrgCuccEquipMapper;
import org.siteweb.config.common.mapper.TblStandardDicControlMapper;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class StandardDicControlServiceImpl implements StandardDicControlService {
    @Autowired
    TblStandardDicControlMapper standardDicControlMapper;
    @Autowired
    StandardTypeService standardTypeService;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    SysConfigService sysConfigService;
    @Autowired
    I18n i18n;
    @Autowired
    StandardDicSigService standardDicSigService;
    @Autowired
    StOrgCuccEquipMapper stOrgCuccEquipMapper;
    @Override
    public List<TblStandardDicControl> findCurrentStandardDicControl() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return standardDicControlMapper.selectList(Wrappers.lambdaQuery(TblStandardDicControl.class)
                                                           .eq(TblStandardDicControl::getStandardType, currentStandardType.getValue()));
    }

    @Override
    public TblStandardDicControl create(TblStandardDicControl tblStandardDicControl) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        String netManageId = getNetManageId(tblStandardDicControl.getNetManageId(),tblStandardDicControl.getStandardDicId(), tblStandardDicControl.getEquipmentLogicClassId(), currentStandardType);
        tblStandardDicControl.setNetManageId(netManageId);
        Integer standardDicId = getStandardDicId(tblStandardDicControl.getStandardDicId(), currentStandardType);
        tblStandardDicControl.setStandardDicId(standardDicId);
        if(standardDicControlMapper.exists(Wrappers.lambdaQuery(TblStandardDicControl.class).eq(TblStandardDicControl::getStandardDicId, tblStandardDicControl.getStandardDicId()))) {
            throw new BusinessException("控制id重复");
        }
        cuccExtendFiledHandler(tblStandardDicControl,currentStandardType);
        tblStandardDicControl.setModifyType(ModifyTypeEnum.Add.getValue());
        tblStandardDicControl.setStandardType(currentStandardType.getValue());
        standardDicControlMapper.insert(tblStandardDicControl);
        //记录操作日志
        String objectId = String.valueOf(tblStandardDicControl.getStandardDicId());
        String oldValue = tblStandardDicControl.getEquipmentLogicClass() + "/" + tblStandardDicControl.getControlStandardName();
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), objectId, OperationObjectTypeEnum.CONTROL_STANDARD, i18n.T("standardDic.control"), i18n.T("add"), oldValue, "");
        sysConfigService.updateBDicVersion();
        return tblStandardDicControl;
    }

    private void cuccExtendFiledHandler(TblStandardDicControl tblStandardDicControl, StandardCategoryEnum currentStandardType) {
        if (!Objects.equals(currentStandardType, StandardCategoryEnum.UNICOM)) {
            return;
        }
        tblStandardDicControl.setExtendFiled2(i18n.T("standardDic.remoteControl"));
        StOrgCuccEquip stOrgCuccEquip = stOrgCuccEquipMapper.selectOne(Wrappers.lambdaQuery(StOrgCuccEquip.class)
                                                                               .select(StOrgCuccEquip::getClazz)
                                                                               .eq(StOrgCuccEquip::getType, tblStandardDicControl.getEquipmentLogicClass()));
        tblStandardDicControl.setExtendFiled1(stOrgCuccEquip.getClazz());
    }

    @Override
    public TblStandardDicControl update(TblStandardDicControl standardDicControl) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        TblStandardDicControl oldStandardDIcControl = findByStandardDIcIdAndStandardType(standardDicControl.getStandardDicId(), currentStandardType.getValue());
        if (Objects.isNull(oldStandardDIcControl)) {
            log.error("没有查询到对应的控制基类标准化字典项,{}",standardDicControl);
            return null;
        }
        standardDicControl.setModifyType(ModifyTypeEnum.Modify.getValue());
        //记录操作日志
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldStandardDIcControl, standardDicControl);
        standardDicControlMapper.updateEntity(standardDicControl);
        return standardDicControl;
    }


    @Override
    public int deleteByStandardDicIdAndStandardType(Integer standardDicId, Integer standardTypeId) {
        TblStandardDicControl standardDicControl = findByStandardDIcIdAndStandardType(standardDicId, standardTypeId);
        if (Objects.isNull(standardDicControl)) {
            return 0;
        }
        standardDicControlMapper.delete(Wrappers.lambdaQuery(TblStandardDicControl.class)
                                              .eq(TblStandardDicControl::getStandardDicId, standardDicId)
                                              .eq(TblStandardDicControl::getStandardType, standardTypeId));
        //记录操作日志
        String objectId = String.valueOf(standardDicControl.getStandardDicId());
        String oldValue = standardDicControl.getEquipmentLogicClass() + "/" + standardDicControl.getControlStandardName();
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), objectId, OperationObjectTypeEnum.CONTROL_STANDARD, i18n.T("standardDic.control"), i18n.T("delete"), oldValue, "");
        sysConfigService.updateBDicVersion();
        return 1;
    }

    @Override
    public List<TblStandardDicControl> findByStandardType(Integer standardCategory) {
        return standardDicControlMapper.selectList(Wrappers.lambdaQuery(TblStandardDicControl.class)
                                                           .eq(TblStandardDicControl::getStandardType, standardCategory));
    }

    private TblStandardDicControl findByStandardDIcIdAndStandardType(Integer standardDicId, Integer standardTypeId) {
        return standardDicControlMapper.selectOne(Wrappers.lambdaQuery(TblStandardDicControl.class)
                                                        .eq(TblStandardDicControl::getStandardDicId, standardDicId)
                                                        .eq(TblStandardDicControl::getStandardType, standardTypeId));
    }

    private String getNetManageId(String netManageId, Integer standardDicId, Integer equipmentLogicClassId, StandardCategoryEnum currentStandardType) {
        //移动的需要计算，其他的是多少则是多少
        if (Objects.equals(currentStandardType, StandardCategoryEnum.MOBILE)) {
            String strTemp = CharSequenceUtil.padPre(String.valueOf(equipmentLogicClassId), 3, "0");
            return "0500-002-" + strTemp + "-00-" + standardDicId;
        }
        return netManageId;
    }
    private Integer getStandardDicId(Integer standardDicId, StandardCategoryEnum currentStandardType) {
        if (Objects.equals(currentStandardType, StandardCategoryEnum.MOBILE)) {
            return standardDicId;
        }
        return standardDicSigService.getMaxStandardDicId();
    }
}
