package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.SwatchStationDTO;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.entity.TblSwatchStation;

import java.util.List;

public interface TblSwatchStationService {

    // 查询所有
    List<TblSwatchStation> selectAll();

    // 修改
    boolean update(TblSwatchStation tblSwatchStation);
    // delete
    boolean delete(Integer swatchStationId);

    // add
    boolean add(TblSwatchStation tblSwatchStation);

    TblSwatchStation selectByStationId(Integer stationId);

    List<TblStation> selectStationBySwatchStationId(Integer swatchStationId);

    boolean addStation(SwatchStationDTO swatchStationDTO);

    boolean addStationV3(SwatchStationDTO swatchStationDTO);

    void deleteByStationId(Integer stationId);
}
