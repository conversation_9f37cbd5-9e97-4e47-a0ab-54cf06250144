package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblHouse;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.service.HouseService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/8/14
 */
@Slf4j
@RestController
@RequestMapping("/house")
public class HouseController {

    @Autowired
    private HouseService houseService;


    @Autowired
    private EquipmentService equipmentService;


    // 新增
    @PostMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createConfig(@RequestBody @Validated TblHouse house) {
        return ResponseHelper.successful(houseService.createHouse(house));
    }

    // 修改
    @PutMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateConfig(@RequestBody @Validated TblHouse house) {
        return ResponseHelper.successful(houseService.updateHouse(house));
    }

    // 删除
    @DeleteMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteConfig(@RequestParam("houseId") Integer houseId, @RequestParam("stationId") Integer stationId) {
        // 判断如果房间底下有设备，提示错误
        if (!equipmentService.findByHouseIdAndStationId(stationId, houseId).isEmpty()) {
            return ResponseHelper.failed("房间下有设备");
        }
        return ResponseHelper.successful(houseService.deleteHouse(houseId, stationId));
    }

    // 根据局站ID查询
    @GetMapping(value = "/config/{stationId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findStationDefaultHouse(@PathVariable("stationId") Integer stationId) {
        return ResponseHelper.successful(houseService.findStationDefaultHouse(stationId));
    }

    // 根据局站ID查询
    @GetMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findHouseByStationId(@RequestParam("stationId") Integer stationId) {
        return ResponseHelper.successful(houseService.findHouseByStationId(stationId));
    }
}
