package org.siteweb.config.primary.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.DoorCardBackup;
import org.siteweb.config.common.entity.TblDoor;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.mapper.TblDoorMapper;
import org.siteweb.config.common.mapper.TblEquipmentMapper;
import org.siteweb.config.primary.enums.EquipmentCategoryEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.DoorCardBackupService;
import org.siteweb.config.primary.service.DoorCardService;
import org.siteweb.config.primary.service.DoorService;
import org.siteweb.config.primary.service.PrimaryKeyValueService;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class DoorServiceImpl implements DoorService {
    /**
     * 默认门密码
     */
    private static final String DEFAULT_PASSWORD = "00000";
    @Autowired
    TblDoorMapper doorMapper;
    @Autowired
    DoorCardService doorCardService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    TblEquipmentMapper equipmentMapper;
    @Autowired
    DoorCardBackupService doorCardBackupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDoor(Integer equipmentId) {
        TblEquipment equipment = equipmentMapper.selectById(equipmentId);
        if (Objects.isNull(equipment) || !Objects.equals(equipment.getEquipmentCategory(), EquipmentCategoryEnum.DOOR.getValue())) {
            log.info("创建门信息失败，设备信息为空或者不是门设备,equipmentId:{}", equipmentId);
            return;
        }
        doorCardService.deleteByEquipmentId(equipmentId);
        Integer doorControlId = doorMapper.findDoorControlId(equipmentId);
        List<Integer> doorNoList = getDoorNoList(equipmentId, doorControlId);
        if (CollUtil.isEmpty(doorNoList)) {
            log.info("门号获取为空,{}",equipment);
            return;
        }
        for (Integer doorNo : doorNoList) {
            String doorName = equipment.getEquipmentName();
            if (Objects.nonNull(doorNo)) {
                doorName = doorName  + "#" + doorNo;
            }
            int doorId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_DOOR, 0);
            TblDoor door = TblDoor.builder()
                                  .doorId(doorId)
                                  .doorNo(doorNo)
                                  .doorName(doorName)
                                  .stationId(equipment.getStationId())
                                  .equipmentId(equipmentId)
                                  .samplerUnitId(equipment.getSamplerUnitId())
                                  .category(doorControlId)
                                  .workMode(1)
                                  .doorControlId(doorControlId)
                                  .password(DEFAULT_PASSWORD).build();
            doorMapper.insert(door);
            log.info("添加门设备成功,{}", door);
        }
    }

    /**
     * 2024-9-21
     * xsx
     * 创建门设备
     * @param tblDoor
     */
    @Override
    public void createDoor(TblDoor tblDoor) {
        if(ObjectUtil.isEmpty(tblDoor)){
            log.info("信息为空");
            throw new BusinessException("door info does not exist");
        }
        int doorId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_DOOR, 0);
        tblDoor.setDoorId(doorId);
        tblDoor.setCategory(tblDoor.getDoorControlId());
        tblDoor.setPassword(DEFAULT_PASSWORD);
        tblDoor.setWorkMode(1);
        doorMapper.insert(tblDoor);
        log.info("添加门设备成功,{}", tblDoor);
    }

    @Override
    public void deleteByEquipmentId(Integer equipmentId, String equipmentName) {
        if (!doorMapper.exists(Wrappers.lambdaQuery(TblDoor.class).eq(TblDoor::getEquipmentId, equipmentId))) {
            log.info("该设备不是门禁设备，删除无需处理备份逻辑");
            return;
        }
        //删除之前需要备份
        doorCardBackupService.backupCard(equipmentId, equipmentName);
        doorMapper.delete(Wrappers.lambdaQuery(TblDoor.class)
                                  .eq(TblDoor::getEquipmentId, equipmentId));
    }

    @Override
    public boolean deleteDoor(Integer equipmentId, Integer doorNo) {
        if(ObjectUtil.isEmpty(equipmentId) || ObjectUtil.isEmpty(doorNo)){
            log.info("信息为空");
            throw new BusinessException("door info does not exist");
        }
        //todo xsx 2024-9-21 备份业务
        return  doorMapper.delete(Wrappers.lambdaQuery(TblDoor.class)
                .eq(TblDoor::getEquipmentId,equipmentId)
                .eq(TblDoor::getDoorNo,doorNo)
        )>0;
    }

    @Override
    public TblDoor updateDoor(TblDoor tblDoor) {
        if(ObjectUtil.isEmpty(tblDoor)){
            log.info("信息为空");
            throw new BusinessException("door info does not exist");
        }
        LambdaQueryWrapper<TblDoor> queryWrapper = Wrappers.lambdaQuery(TblDoor.class)
                .eq(TblDoor::getEquipmentId, tblDoor.getEquipmentId())
                .eq(TblDoor::getDoorNo, tblDoor.getDoorNo());
        boolean exists = doorMapper.exists(queryWrapper);
        if(!exists){
            throw new BusinessException("door info does not exist");
        }
        LambdaUpdateWrapper<TblDoor> updateWrapper = Wrappers.lambdaUpdate();
        if(ObjectUtil.isNotEmpty(tblDoor.getDoorControlId())){
            updateWrapper.set(TblDoor::getDoorControlId,tblDoor.getDoorControlId());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getDoorName())){
            updateWrapper.set(TblDoor::getDoorName,tblDoor.getDoorName());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getDoorInterval())){
            updateWrapper.set(TblDoor::getDoorInterval,tblDoor.getDoorInterval());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getAddress())){
            updateWrapper.set(TblDoor::getAddress,tblDoor.getAddress());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getCategory())){
            updateWrapper.set(TblDoor::getCategory,tblDoor.getCategory());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getInfrared())){
            updateWrapper.set(TblDoor::getInfrared,tblDoor.getInfrared());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getOpenDelay())){
            updateWrapper.set(TblDoor::getOpenDelay,tblDoor.getOpenDelay());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getOpenMode())){
            updateWrapper.set(TblDoor::getOpenMode,tblDoor.getOpenMode());
        }
        if(ObjectUtil.isNotEmpty(tblDoor.getPassword())){
            updateWrapper.set(TblDoor::getPassword,tblDoor.getPassword());
        }
        updateWrapper.eq(TblDoor::getEquipmentId, tblDoor.getEquipmentId())
                .eq(TblDoor::getDoorNo, tblDoor.getDoorNo());
        List<TblDoor> tblDoorList = doorMapper.selectList(queryWrapper);
        return tblDoorList.get(0);
    }

    @Override
    public List<TblDoor> getDoorByEquipmentId(Integer equipmentId) {
        if(ObjectUtil.isEmpty(equipmentId)){
            log.info("信息为空");
            throw new BusinessException("door info does not exist");
        }
        LambdaQueryWrapper<TblDoor> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TblDoor::getEquipmentId,equipmentId);
        List<TblDoor> tblDoorList = doorMapper.selectList(queryWrapper);
        return tblDoorList;
    }

    public List<Integer> getDoorNoList(Integer equipmentId, Integer doorControlId) {
        List<Integer> doorNoList = null;
        if (Objects.equals(doorControlId, 4)) {
            doorNoList = doorMapper.findDoorNoByEquipmentIdAndFour(equipmentId);
        } else if (Objects.equals(doorControlId, 12)) {
            //twelve
            doorNoList = doorMapper.findDoorNoByEquipmentIdAndTwelve(equipmentId);
        } else if (Objects.equals(doorControlId, 20)) {
            //twenty
            doorNoList = doorMapper.findDoorNoByEquipmentIdAndTwenty(equipmentId);
        } else {
            doorNoList = doorMapper.findDoorNoByEquipmentIdAndOther(equipmentId);
        }
        return doorNoList;
    }
}
