package org.siteweb.config.primary.manager;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.StructureTreeNodeDTO;
import org.siteweb.config.common.entity.ResourceStructure;
import org.siteweb.config.primary.cache.CacheComplete;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.service.ResourceStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 层级对象树管理服务
 *
 * <AUTHOR> (2024-03-04)
 **/
@Slf4j
@Component
public class StructureTreeManager implements CacheComplete {

    @Autowired
    private ResourceStructureService resourceStructureService;



    /**
     * 获取层级数根节点
     * 当前方法返回层级对象完整的树结构 包括设备列表
     * 优化: 一次性查询所有数据，减少数据库查询次数
     *
     * <AUTHOR> (2024/3/5)
     */
    public StructureTreeNodeDTO root() {
        // 1. 获取根节点
        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure == null) {
            return null;
        }

        // 2. 一次性查询所有结构数据
        List<ResourceStructure> allStructures = resourceStructureService.getAllStructures();

        // 3. 构建父子关系映射
        Map<Integer, List<ResourceStructure>> parentChildrenMap = new HashMap<>();
        for (ResourceStructure structure : allStructures) {
            parentChildrenMap
                    .computeIfAbsent(structure.getParentResourceStructureId(), k -> new ArrayList<>())
                    .add(structure);
        }

        // 4. 使用映射构建树
        return buildTreeFromMap(rootStructure, parentChildrenMap);
    }

    /**
     * 使用父子关系映射构建树
     *
     * @param structure 当前节点
     * @param parentChildrenMap 父子关系映射
     * @return 构建好的树节点
     */
    private StructureTreeNodeDTO buildTreeFromMap(
            ResourceStructure structure,
            Map<Integer, List<ResourceStructure>> parentChildrenMap
    ) {
        StructureTreeNodeDTO node = convertToTreeNode(structure);

        // 获取子节点列表
        List<ResourceStructure> children = parentChildrenMap.getOrDefault(
                structure.getResourceStructureId(),
                Collections.emptyList()
        );

        // 递归构建子树
        for (ResourceStructure child : children) {
            StructureTreeNodeDTO childNode = buildTreeFromMap(child, parentChildrenMap);
            node.getChildren().add(childNode);
        }

        return node;
    }

    /**
     * 枚举对象树的分支，可使用 @maxDeep 参数指定递归深度
     * 返回结果包含了层级中的设备列表
     *
     * @param resourceStructureID 对象树节点ID  （默认为根节点）
     * @param eqs                 是否包含设备列表 （默认不包含设备列表）
     * @param maxDepth            递归深度   （默认为最大深度）
     * <AUTHOR> (2024/3/5)
     */
    public StructureTreeNodeDTO enumTreeBranch(Integer resourceStructureID, Boolean eqs, Integer maxDepth) {
        if (eqs == null) eqs = false;
        if (maxDepth == null) maxDepth = 64;

        ResourceStructure structure;
        if (resourceStructureID == null) {
            structure = resourceStructureService.getRootStructure();
        } else {
            structure = resourceStructureService.getStructureByID(resourceStructureID);
        }

        if (structure == null) {
            return null;
        }

        return buildTreeWithDepth(structure, eqs, maxDepth);
    }

    // Helper methods
    private StructureTreeNodeDTO convertToTreeNode(ResourceStructure structure) {
        StructureTreeNodeDTO treeNode = StructureTreeNodeDTO.build(structure);
        setNodeIds(treeNode, structure);
        return treeNode;
    }

    private void setNodeIds(StructureTreeNodeDTO treeNode, ResourceStructure structure) {
        if (structure.getStructureTypeId() == 104) {
            treeNode.setOriginId(structure.getOriginId());
        } else if (structure.getStructureTypeId() == 103 ||
                structure.getStructureTypeId() == 102 ||
                structure.getStructureTypeId() == 101) {
            treeNode.setStructureId(structure.getOriginId());
            treeNode.setOriginId(null);
        } else if (structure.getStructureTypeId() == StructureTypeEnum.STATION_HOUSE.getValue()) {
            treeNode.setHouseId(structure.getOriginId());
            treeNode.setOriginId(null);
        } else {
            treeNode.setOriginId(structure.getOriginId());
        }
    }

    private StructureTreeNodeDTO buildTreeWithDepth(ResourceStructure structure, boolean includeEquipment, int maxDepth) {
        if (maxDepth <= 0) {
            return convertToTreeNode(structure);
        }

        StructureTreeNodeDTO node = convertToTreeNode(structure);

        if (includeEquipment) {
            // 这里需要添加获取设备列表的逻辑
            // node.setEquipmentList(equipmentService.getEquipmentsByStructureId(structure.getResourceStructureId()));
        }

        if (maxDepth > 0) {
            List<ResourceStructure> children = resourceStructureService.getChildrenByParentId(structure.getResourceStructureId());
            for (ResourceStructure child : children) {
                StructureTreeNodeDTO childNode = buildTreeWithDepth(child, includeEquipment, maxDepth - 1);
                node.getChildren().add(childNode);
            }
        }

        return node;
    }




    @Override
    public void cacheLoadCompleteAfter() {
        // TODO: 2023/3/29 此处需要实现
    }
}
