package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.dto.TypeItemDTO;
import org.siteweb.config.common.entity.TblDataItem;
import org.siteweb.config.common.enums.DataEntryEnum;

import java.util.List;
import java.util.Map;

public interface DataItemService {
    Map<Integer, String> findMapByEntryId(DataEntryEnum dataEntryEnum);

    Map<Integer, String> findMapByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer itemId);

    List<TblDataItem> findByEntryId(DataEntryEnum dataEntryEnum);

    TblDataItem findByEntryIdAndItemId(DataEntryEnum dataEntryEnum, Integer itemId);

    int createDataItem(TblDataItem dataItem);

    /**
     * 对应存储过程：PCT_InitDataEntryItem
     *
     * 新增dataentryitem，以及如果tbl_primarykeyvalue不存在记录则初始化
     */
    void initDataEntryItem(TblDataItem dataItem);


    List<TblDataItem> findEquipmentCategory();

    void deleteByEntryId(DataEntryEnum dataEntryEnum);

    /**
     * 通过entryItemId删除字典项
     *
     * @param entryItemIds 主键ids
     * @return
     */
    int deleteByEntryItemIds(List<Integer> entryItemIds);

    /**
     * 对应存储过程PIL_SaveDictionaryItemByEntry
     * @param dataItem
     */
    int saveDictionaryItemByEntry(TblDataItem dataItem);


    List<TypeItemDTO> findTypes(DataEntryEnum dataEntryEnum);

    List<TypeItemDTO> findPortTypesByMonitorUnitCategory(Integer monitorUnitCategory);

    List<IdValueDTO<Integer,String>> findValues(DataEntryEnum dataEntryEnum);


    /**
     * 获取对应字典map
     */
    Map<Integer, String> getDataItemMap(DataEntryEnum dataEntryEnum);

    int update(TblDataItem dataItem);


    int getIncrementMaxEntryItemId();

    /**
     * 字典项的最大值在自增1
     * @param entryId 字典项
     * @return int
     */
    int getIncrementMaxItemId(Integer entryId);

    int deleteByEntryIdAndItemId(Integer entryId, Integer itemId);

    int updatePortExtendField2();
}
