package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.excel.ControlExcel;
import org.siteweb.config.common.entity.TblControl;
import org.siteweb.config.common.vo.ControlVO;

import java.util.List;
import java.util.Map;

public interface ControlService {

    List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    void createControl(TblControl control);

    void createControl(ControlConfigItem controlConfigItem);

    int updateControl(TblControl control);

    void updateControlByControl(ControlConfigItem controlConfigItem);

    void deleteControl(int equipmentTemplateId, int controlId);

    void batchDeleteControl(int equipmentTemplateId, List<Integer> controlIds);

    /**
     * 复制原始模板中控制相关的内容至目标模板中
     *
     * @param originEquipmentTemplateId 原始设备模板id
     * @param destEquipmentTemplateId   目标设备模板id
     */
    void copyControl(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    int batchInsert(List<TblControl> controlList);

    List<TblControl> findByEquipmentTemplateId(Integer equipmentTemplateId);

    List<ControlConfigItem> findItemByEquipmentTemplateId(Integer equipmentTemplateId);

    List<ControlVO> findVoByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsertLianTongControls();


    Integer findMaxControlIdByEquipmentTemplateId(Integer equipmentTemplateId);

    int findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId);


    /**
     * 对比新旧模板中缺少的控制
     *
     * @param oldEquipmentTemplateId 旧设备模板id
     * @param newEquipmentTemplateId 新设备模板id
     * @return {@link List}<{@link TblControl}>
     */
    List<TblControl> diffControl(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId);


    SignStatusMeaningGroupDTO findEquipmentBaseTypeMeaningsByControl(Integer equipmentTemplateId, Integer controlId);

    /**
     * 获取控制进度数据
     */
    Map<Integer, List<ControlProgressDTO>> getControlProgressMap();

    /**
     * 获取控制处理进度
     */
    Double getControlProgress(Integer equipmentTemplateId, Map<Integer, List<ControlProgressDTO>> controlProgressMap);

    /**
     * 获取基类标准化控制列表
     */
    BaseClassStatisticsDTO<ControlBaseClassDTO> findControlBaseClassList(Integer equipmentBaseType);

    /**
     * 获取基类标准化控制列表详情
     */
    List<ControlBaseClassDetailDTO> findControlBaseClassDetails(Integer equipmentBaseType, String controlName, String meanings);

    /**
     * 基类控制相关修改
     */
    void updateControlBaseTypeAndCondId(BaseTypeCondIdDTO<ControlConditionDTO> baseTypeCondIdDTO);

    /**
     * 相似控制批量指定
     */
    void disposeSimilarControl(SimilarDataDTO similarControlDTO);

    /**
     * 清除基类
     */
    void clearBaseTypeByEquipmentTemplate(Integer equipmentTemplateId);

    ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId);

    /**
     * 控制标准化
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);

    /**
     * 还原标准化 PCT_RestoreControlStandard
     */
    Long restoreStandard();

    /**
     * 获取标准化控制比较数据
     */
    List<StandardControlCompareDTO> getStandardCompareData();

    /**
     * 获取控制应用标准化检查
     */
    List<StandardApplyControlCheckDTO> getControlStandardApplyCheckData(Integer standardId);

    /**
     * 获取控制映射标准化检查
     */
    List<StandardMappingControlCheckDTO> getControStandardMappingCheck(Integer standardId, Integer equipmentCategory);
    /**
     * 应用控制标准测点映射
     */
    void applyControlPointMapping(StandardPointMappingDTO mappingDTO);

    List<TblControl> findByEquipmentCategory(Integer equipmentCategory);

    /**
     * 获取控制以及绑定测点
     */
    List<ControlConfigPointDTO> findControlPoints(Integer equipmentTemplateId);
    /**
     * 解除测点绑定
     */
    void unbindControlPointMapping(StandardPointUnbindDTO standardPointUnbindDTO);
    /**
     * 批量更新设备类型
     */
    void updateStandardPointCategory(StandardPointCategoryDTO standardPointCategoryDTO);

    /**
     * 生成未映射点位
     */
    void generateUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO);

    /**
     * 解除生成的未映射点位
     */
    void unbindUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO);
    /**
     * 批量清除基类
     */
    void clearControlBaseTypeAndCondId(List<ControlBaseTypeConditionDTO> controlBaseTypeConditionDTOS);

    boolean fieldCopy(List<CommandFieldCopyDTO> commandFieldCopyDTOList);

    List<ControlExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId);
}
