package org.siteweb.config.primary.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.EquipmentBaseTypeClassDTO;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EquipmentBaseTypeService;
import org.siteweb.config.primary.utils.ExcelExportUtil;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
public class EquipmentBaseTypeController {

    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;

    /**
     * 导出设备类型字典表
     */
    @GetMapping(value = "/equipmentbasetype/baseclassdics/export")
    public void exportEquipmentBaseTypeAndClass(HttpServletResponse response) throws IOException {
        List<EquipmentBaseTypeClassDTO> equipmentBaseTypeAndClass = equipmentBaseTypeService.findEquipmentBaseTypeAndClass();
        ExcelExportUtil.exportExcel(response, equipmentBaseTypeAndClass, EquipmentBaseTypeClassDTO.class, "equipmentBaseTypeAndClass");
    }

    /**
     * 获取设备基类以及大类名称(获取设备类型字典表)
     */
    @GetMapping(value = "/equipmentbasetype/baseclassdics")
    public ResponseEntity<ResponseResult> findEquipmentBaseTypeAndClass() {
        return ResponseHelper.successful(equipmentBaseTypeService.findEquipmentBaseTypeAndClass());
    }

    /**
     * 获取基类设备类型树
     */
    @GetMapping(value = "/equipmentbasetype/tree")
    public ResponseEntity<ResponseResult> findEquipmentBaseTypeTree(Boolean spliceFlag) {
        return ResponseHelper.successful(equipmentBaseTypeService.findEquipmentBaseTypeTree(spliceFlag));
    }

    /**
     * 获取设备基类
     */
    @GetMapping(value = "/equipmentbasetype/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> list() {
        return ResponseHelper.successful(equipmentBaseTypeService.List());
    }

    /**
     * 获取设备子类
     *
     * @param equipmentTypeId 设备大类id
     */
    @GetMapping(value = "/equipmentsubtype/{eqTypeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSubTypeById(@PathVariable("eqTypeId") Integer equipmentTypeId) {
        return ResponseHelper.successful(equipmentBaseTypeService.findEquipmentSubTypeById(equipmentTypeId));
    }

    /**
     * 获取设备大类子类树
     */
    @GetMapping(value = "/equipmentsubtype/tree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSubTypeTree() {
        return ResponseHelper.successful(equipmentBaseTypeService.buildEquipmentSubTypeTree());
    }

    @GetMapping(value = "/equipmentbasetype/{baseEquipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentbasetypeBybaseEquipmentId(@PathVariable("baseEquipmentId") Integer baseEquipmentId) {
        return ResponseHelper.successful(equipmentBaseTypeService.findEquipmentBaseTypeByBaseEquipmentId(baseEquipmentId));
    }
}
