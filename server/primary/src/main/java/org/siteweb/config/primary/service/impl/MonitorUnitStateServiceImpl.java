package org.siteweb.config.primary.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TslSamplerUnit;
import org.siteweb.config.primary.enums.MonitorUnitStateEnum;
import org.siteweb.config.primary.manager.MonitorUnitStateManager;
import org.siteweb.config.primary.service.MonitorUnitStateService;
import org.siteweb.config.primary.service.SamplerUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> (2024-04-17)
 **/
@Slf4j

@Service
public class MonitorUnitStateServiceImpl implements MonitorUnitStateService {

    @Autowired
    private MonitorUnitStateManager monitorUnitStateManager;

    @Autowired
    private SamplerUnitService samplerUnitService;



    @Override
    public void updateMonitorUnit(Integer monitorUnitId) {
//        MonitorUnitDTO monitorUnit = monitorUnitCache.get(monitorUnitId);
//        if (monitorUnit == null) return;
//        monitorUnitCache.referenceCacheByMonitorUnitId(monitorUnitId);
        monitorUnitStateManager.updateMonitorUnitStatus(monitorUnitId, MonitorUnitStateEnum.PENDING);
//        log.info("MonitorUnit {} 已修改，请及时下发配置。", monitorUnit.getMonitorUnitName());
    }



    public void updateSampler(Integer samplerId) {
        List<Integer> muIds = samplerUnitService.findBySamplerId(samplerId).stream()
                .map(TslSamplerUnit::getMonitorUnitId)
                .distinct().toList();
        if (muIds.isEmpty()) return;
        muIds.forEach(this::updateMonitorUnit);
    }


}
