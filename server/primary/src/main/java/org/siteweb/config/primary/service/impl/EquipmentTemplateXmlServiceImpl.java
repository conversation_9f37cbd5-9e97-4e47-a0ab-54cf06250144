package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.siteweb.config.common.constants.SignalConstant;
import org.siteweb.config.common.dto.EventConfigItem;
import org.siteweb.config.common.dto.SignalConfigItem;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.mapper.TblControlMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.utils.StrSplitUtil;
import org.siteweb.config.common.vo.ControlVO;
import org.siteweb.config.primary.enums.EquipmentCategoryEnum;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;


import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class EquipmentTemplateXmlServiceImpl implements EquipmentTemplateXmlService {
    @Autowired
    EquipmentTemplateService equipmentTemplateService;
    @Autowired
    SignalService signalService;
    @Autowired
    SamplerService samplerService;
    @Autowired
    EventService eventService;
    @Autowired
    ControlService controlService;
    @Autowired
    CategoryIdMapService categoryIdMapService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    SysConfigService sysConfigService;
    @Autowired
    OriginBussinessCategoryMapService originBussinessCategoryMapService;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    @Qualifier("baseDicServiceImpl")
    BaseDicService baseDicService;
    @Autowired
    SignalPropertyService signalPropertyService;
    @Autowired
    SignalMeaningsService signalMeaningsService;
    @Autowired
    EventConditionService eventConditionService;
    @Autowired
    TblControlMapper controlMapper;
    @Autowired
    ControlMeaningsService controlMeaningsService;
    @Autowired
    I18n i18n;

    @Value("${bytedance.generate-signal-standard:false}")
    private Boolean generateSignalStandard;

    @Override
    public String exportEquipmentTemplate(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);

        // Create document using dom4j
        Document document = DocumentHelper.createDocument();

        // Create root element
        Element equipmentTemplates = document.addElement("EquipmentTemplates");
        equipmentTemplates.addAttribute("Name", "设备模板列表");

        // 获取采集器节点
        Element samplers = getSamplersElement(document, equipmentTemplate.getProtocolCode());
        equipmentTemplates.add(samplers);

        // 获取设备模板节点
        Element equipmentTemplateElement = getEquipmentTemplateElement(equipmentTemplate);
        equipmentTemplates.add(equipmentTemplateElement);

        // Output XML with pretty format
        OutputFormat format = OutputFormat.createPrettyPrint();
        StringWriter stringWriter = new StringWriter();
        XMLWriter writer = new XMLWriter(stringWriter, format);

        try {
            writer.write(document);
            return stringWriter.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to export equipment template", e);
        }
    }

    @Override
    public Element exportEquipmentTemplateElement(List<Integer> equipmentTemplateIds) {
        // Create the equipment templates element using DocumentHelper instead of doc.addElement
        Element equipmentTemplates = DocumentHelper.createElement("EquipmentTemplates");
        equipmentTemplates.addAttribute("Name", i18n.T("monitor.equipmentTemplateList"));

        for (Integer equipmentTemplateId : equipmentTemplateIds) {
            TblEquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);

            // Get equipment template node
            Element equipmentTemplateElement = getEquipmentTemplateElement(equipmentTemplate);
            equipmentTemplates.add(equipmentTemplateElement);
        }

        return equipmentTemplates;
    }

    @Override
    public TblEquipmentTemplate importTemplate(Element equipmentTemplatesElement) {
        Element equipmentTemplateElement = equipmentTemplatesElement.element("EquipmentTemplate");
        TblEquipmentTemplate tblEquipmentTemplate = generateTemplateFromXml(equipmentTemplateElement);
        if (Objects.isNull(tblEquipmentTemplate)) {
            return null;
        }

        // 生成采集器(协议)
        samplerService.createSamplersFromXml(tblEquipmentTemplate, equipmentTemplatesElement.element("Samplers"));

        // 产生设备模板信号
        generateSignalsFromXml(tblEquipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Signals"));

        // 产生设备模板事件
        generateEventsFromXml(tblEquipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Events"));

        // 产生设备控制
        generateCommandsFromXml(tblEquipmentTemplate.getEquipmentTemplateId(), equipmentTemplateElement.element("Controls"));

        // 升级基类标准化字典
        baseDicService.updateBaseClassStandardDictionary(tblEquipmentTemplate.getEquipmentTemplateId());
        changeEventService.sendCreate(tblEquipmentTemplate);
        return tblEquipmentTemplate;
    }

    /**
     * 生成控制从xml
     *
     * @param equipmentTemplateId 设备模板id
     * @param controlsElement     控制的XML元素
     */
    private void generateCommandsFromXml(Integer equipmentTemplateId, Element controlsElement) {
        if (Objects.isNull(controlsElement)) {
            return;
        }

        List<Element> controlElementList = controlsElement.elements("Control");
        List<TblControl> batchInsertControlList = new ArrayList<>(controlElementList.size());
        List<TblControlMeanings> batchInsertControlMeaningsList = new ArrayList<>(controlElementList.size());

        for (Element element : controlElementList) {
            TblControl control = parseControlFromXml(equipmentTemplateId, element);
            batchInsertControlList.add(control);

            String controlMeaningsStr = element.attributeValue("ControlMeanings");
            List<TblControlMeanings> controlMeaningsList = createControlMeaningsFormXml(
                    equipmentTemplateId,
                    control.getControlId(),
                    controlMeaningsStr.split(";")
            );
            batchInsertControlMeaningsList.addAll(controlMeaningsList);
        }

        controlService.batchInsert(batchInsertControlList);
        controlMeaningsService.batchInsert(batchInsertControlMeaningsList);
    }

    /**
     * 从xml创建控制含义
     *
     * @param equipmentTemplateId 设备模板id
     * @param controlId           控制主键id
     * @param controlMeaningsArr  控制含义arr
     * @return
     */
    private List<TblControlMeanings> createControlMeaningsFormXml(Integer equipmentTemplateId, Integer controlId, String[] controlMeaningsArr) {
        List<TblControlMeanings> batchInsertControlMeanings = new ArrayList<>(controlMeaningsArr.length);
        for (String controlMeaningStr : controlMeaningsArr) {
            String[] meaningArr = controlMeaningStr.split(":");
            if (!NumberUtil.isNumber(meaningArr[0])) {
                continue;
            }
            TblControlMeanings controlMeanings = new TblControlMeanings();
            controlMeanings.setEquipmentTemplateId(equipmentTemplateId);
            controlMeanings.setControlId(controlId);
            controlMeanings.setParameterValue(Integer.valueOf(meaningArr[0]));
            controlMeanings.setMeanings(meaningArr[1]);
            if (meaningArr.length == 3) {
                controlMeanings.setBaseCondId(Integer.valueOf(meaningArr[2]));
            }
            batchInsertControlMeanings.add(controlMeanings);
        }
        return batchInsertControlMeanings;
    }
    /**
     * 解析控制从xml中
     *
     * @param equipmentTemplateId 设备模板id
     * @param element             元素
     * @return 返回控制
     */
    private TblControl parseControlFromXml(Integer equipmentTemplateId, Element element) {
        Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
        String description = element.attributeValue("Description");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
        } else {
            // 处理 description 为 null 的情况
            // 可以设置一个默认值，或者记录日志，或者其他适当的处理
            description = ""; // 例如，设置为空字符串
        }
        return TblControl.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .controlId(getAttributeAsInteger(element, "ControlId"))
                .controlName(element.attributeValue("ControlName"))
                .controlCategory(getAttributeAsInteger(element, "ControlCategory"))
                .cmdToken(element.attributeValue("CmdToken"))
                .baseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId) //如果基类id等于0，则赋值为null,因为模板的错误，需要上层来补
                .controlSeverity(getAttributeAsInteger(element, "ControlSeverity"))
                .signalId(getAttributeAsInteger(element, "SignalId"))
                .timeOut(getAttributeAsDouble(element, "TimeOut"))
                .retry(getAttributeAsInteger(element, "Retry"))
                .description(description)
                .enable(getAttributeAsBoolean(element, "Enable"))
                .visible(getAttributeAsBoolean(element, "Visible"))
                .displayIndex(getAttributeAsInteger(element, "DisplayIndex"))
                .commandType(getAttributeAsInteger(element, "CommandType"))
                .controlType(getAttributeAsShort(element, "ControlType"))
                .dataType(getAttributeAsShort(element, "DataType"))
                .maxValue(getAttributeAsDouble(element, "MaxValue"))
                .minValue(getAttributeAsDouble(element, "MinValue"))
                .defaultValue(getAttributeAsDouble(element, "DefaultValue"))
                .moduleNo(getAttributeAsInteger(element, "ModuleNo", 0))
                .build();
    }

    // Helper methods to replace XmlElementUtil functionality
    private Integer getAttributeAsInteger(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Integer.parseInt(value) : null;
    }

    private Integer getAttributeAsInteger(Element element, String name, Integer defaultValue) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Integer.parseInt(value) : defaultValue;
    }

    private Long getAttributeAsLong(Element element, String name, Long defaultValue) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Long.parseLong(value) : defaultValue;
    }

    private Double getAttributeAsDouble(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Double.parseDouble(value) : null;
    }

    private Boolean getAttributeAsBoolean(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Boolean.parseBoolean(value) : null;
    }

    private Short getAttributeAsShort(Element element, String name) {
        String value = element.attributeValue(name);
        return (value != null && !value.isEmpty()) ? Short.parseShort(value) : null;
    }

    /**
     * 通过xml解析信号
     *
     * @param equipmentTemplateId 添加好的模板信息
     * @param signalsElementList  xml中的信号元素集合
     */
    private void generateSignalsFromXml(Integer equipmentTemplateId, Element signalsElementList) {
        createSignalFormXml(equipmentTemplateId, signalsElementList);
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);
        //xiong 写死了99，记住，一一对应 【99 自诊断设备】
        if (Objects.equals(equipmentTemplate.getEquipmentCategory(), EquipmentCategoryEnum.SELF_DIAGNOSTICS.getValue())) {
            return;
        }
        //生成设备通信状态相关信号
        signalService.createCommunicationStateSignal(equipmentTemplateId);
    }

    /**
     * 通过xml解析事件
     * @param equipmentTemplateId 模板id
     * @param eventsElement xml事件元素
     */
    private void generateEventsFromXml(Integer equipmentTemplateId, Element eventsElement) {
        createEventFormXml(equipmentTemplateId, eventsElement);
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);
        //xiong 写死了99，记住，一一对应 【99 自诊断设备】
        if (Objects.equals(equipmentTemplate.getEquipmentCategory(), EquipmentCategoryEnum.SELF_DIAGNOSTICS.getValue())) {
            return;
        }
        //生成通讯状态相关事件
        eventService.createCommunicationStateEvent(equipmentTemplateId);
    }

    /**
     * 创建事件从xml
     *
     * @param equipmentTemplateId 设备模板id
     * @param eventsElement       事件xml元素
     */
    private void createEventFormXml(Integer equipmentTemplateId, Element eventsElement) {
        List<Element> eventElementList = eventsElement.elements("Event");
        List<TblEvent> batchInsertEventList = new ArrayList<>(eventElementList.size());
        List<TblEventCondition> batchInsertEventConditionList = new ArrayList<>(eventElementList.size());

        for (Element element : eventElementList) {
            TblEvent event = parseEventXml(equipmentTemplateId, element);
            // insertBatchSomeColumn不允许字段为null，如果ModuleNo为null，默认设置为0。
            if (Objects.isNull(event.getModuleNo())) {
                event.setModuleNo(0);
            }
            batchInsertEventList.add(event);

            // 添加事件条件
            Element conditionsElement = element.element("Conditions");
            List<TblEventCondition> eventCondition = createEventConditionFormXml(
                    equipmentTemplateId,
                    event.getEventId(),
                    conditionsElement.elements("EventCondition")
            );
            batchInsertEventConditionList.addAll(eventCondition);
        }

        eventService.batchEvent(batchInsertEventList);
        eventConditionService.batchCreate(batchInsertEventConditionList);
    }
    private List<TblEventCondition> createEventConditionFormXml(Integer equipmentTemplateId, Integer eventId, List<Element> eventConditionElements) {
        List<TblEventCondition> eventConditionList = new ArrayList<>(eventConditionElements.size());

        for (Element element : eventConditionElements) {
            Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
            TblEventCondition eventCondition = TblEventCondition.builder()
                    .equipmentTemplateId(equipmentTemplateId)
                    .eventId(eventId)
                    .eventConditionId(getAttributeAsInteger(element, "EventConditionId"))
                    .eventSeverity(getAttributeAsInteger(element, "EventSeverity"))
                    .startOperation(element.attributeValue("StartOperation"))
                    .startCompareValue(getAttributeAsDouble(element, "StartCompareValue"))
                    .startDelay(getAttributeAsInteger(element, "StartDelay"))
                    .endOperation(element.attributeValue("EndOperation"))
                    .endCompareValue(getAttributeAsDouble(element, "EndCompareValue"))
                    .endDelay(getAttributeAsInteger(element, "EndDelay"))
                    .frequency(getAttributeAsInteger(element, "Frequency"))
                    .frequencyThreshold(getAttributeAsInteger(element, "FrequencyThreshold"))
                    .meanings(element.attributeValue("Meanings"))
                    .equipmentState(getAttributeAsInteger(element, "EquipmentState"))
                    .baseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId) //如果基类id等于0，则赋值为null,因为模板的错误，需要上层来补
                    .standardName(getAttributeAsInteger(element, "StandardName"))
                    .build();
            eventConditionList.add(eventCondition);
        }

        return eventConditionList;
    }
    private TblEvent parseEventXml(Integer equipmentTemplateId, Element element) {
        String description = element.attributeValue("Description");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
        } else {
            // 处理 description 为 null 的情况
            // 可以设置一个默认值，或者记录日志，或者其他适当的处理
            description = ""; // 例如，设置为空字符串
        }
        return TblEvent.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .eventId(getAttributeAsInteger(element, "EventId"))
                .eventName(element.attributeValue("EventName"))
                .signalId(getAttributeAsInteger(element, "SignalId"))
                .startExpression(element.attributeValue("StartExpression"))
                .eventCategory(getAttributeAsInteger(element, "EventCategory"))
                .startType(getAttributeAsInteger(element, "StartType"))
                .endType(getAttributeAsInteger(element, "EndType"))
                .suppressExpression(element.attributeValue("SuppressExpression"))
                .enable(getAttributeAsBoolean(element, "Enable"))
                .visible(getAttributeAsBoolean(element, "Visible"))
                .description(description)
                .displayIndex(getAttributeAsInteger(element, "DisplayIndex"))
                .moduleNo(getAttributeAsInteger(element, "ModuleNo"))
                .build();
    }

    private TblEquipmentTemplate generateTemplateFromXml(Element equipmentTemplateElement) {
        // 从XML中获取设备模板的基础属性
        TblEquipmentTemplate equipmentTemplate = parseEquipmentTemplateXml(equipmentTemplateElement);
        Map<Integer, String> businessCategoryFromOriginCategory = categoryIdMapService.findBusinessCategoryFromOriginCategory(equipmentTemplate.getEquipmentCategory());

        // 設備模板頁面增加了未分類，如果在系統中不存在，可以在未分類中重新修改設備類型（字節修改）
//        if (CollUtil.isEmpty(businessCategoryFromOriginCategory)) {
//            //
//            log.error("添加模板失败,设备种类在StandardCategoryEnum中没有对");
//            return null;
//        }

        if (CollUtil.size(businessCategoryFromOriginCategory) == 1) {
            // 直接设置
            equipmentTemplate.setEquipmentCategory(new ArrayList<>(businessCategoryFromOriginCategory.keySet()).get(0));
        } else {
            // 在页面上弹出选择框
            equipmentTemplate.setEquipmentCategory(equipmentTemplate.getEquipmentCategory());
        }

        int equipmentTemplateId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT_TEMPLATE, 0);
        if (equipmentTemplateService.existsByEquipmentTemplateName(equipmentTemplate.getEquipmentTemplateName())) {
            // 模板名称已存在，重置模板名称
            String equipmentTemplateName = equipmentTemplate.getEquipmentTemplateName() + equipmentTemplateId;
            equipmentTemplate.setEquipmentTemplateName(equipmentTemplateName);
        }

        equipmentTemplate.setEquipmentTemplateId(equipmentTemplateId);
        equipmentTemplateService.createEquipmentTemplate(equipmentTemplate);

        // xiong 记录维谛设备种类和电信移动联通种类对应
        StandardCategoryEnum standardCategoryEnum = sysConfigService.findStandardCategoryEnum();
        if (!Objects.equals(standardCategoryEnum, StandardCategoryEnum.EMR)) {
            Integer originEquipmentCategory = Integer.valueOf(equipmentTemplateElement.attributeValue("EquipmentCategory"));
            TblOriginBussinessCategoryMap originBussinessCategoryMap = new TblOriginBussinessCategoryMap(equipmentTemplateId, originEquipmentCategory);
            originBussinessCategoryMapService.create(originBussinessCategoryMap);
        }

        return equipmentTemplate;
    }

    private TblEquipmentTemplate parseEquipmentTemplateXml(Element equipmentTemplateElement) {
        TblEquipmentTemplate equipmentTemplate = TblEquipmentTemplate.builder()
                .parentTemplateId(0)
                .equipmentTemplateName(equipmentTemplateElement.attributeValue("EquipmentTemplateName"))
                .protocolCode(equipmentTemplateElement.attributeValue("ProtocolCode"))
                .equipmentCategory(getAttributeAsInteger(equipmentTemplateElement, "EquipmentCategory"))
                .equipmentType(getAttributeAsInteger(equipmentTemplateElement, "EquipmentType"))
                .memo(equipmentTemplateElement.attributeValue("Memo"))
                .property(equipmentTemplateElement.attributeValue("Property"))
                .description(equipmentTemplateElement.attributeValue("Decription"))
                .equipmentStyle(equipmentTemplateElement.attributeValue("EquipmentStyle"))
                .unit(equipmentTemplateElement.attributeValue("Unit"))
                .vendor(equipmentTemplateElement.attributeValue("Vendor"))
                .equipmentBaseType(getAttributeAsInteger(equipmentTemplateElement, "EquipmentBaseType"))
                .stationCategory(getAttributeAsInteger(equipmentTemplateElement, "StationCategory", 0))
                .build();

        if (equipmentTemplate.getEquipmentTemplateName().length() > 128) {
            throw new BusinessException("设备模板名称不能超过128个字符");
        }

        // 如果该模板基类模板不存在，将该模板设置为基类模板
        if (!equipmentTemplateService.existsByParentTemplateId(equipmentTemplate.getParentTemplateId())) {
            equipmentTemplate.setParentTemplateId(0);
        }

        return equipmentTemplate;
    }
    private Element getSamplersElement(Document document, String protocolCode) {
        TslSampler sampler = samplerService.findByProtocolCode(protocolCode);

        // Use DocumentHelper instead of document.addElement
        Element samplersElement = DocumentHelper.createElement("Samplers");

        if (Objects.isNull(sampler)) {
            log.warn("通过协议编码查询不到采集器:protocolCode:{}", protocolCode);
            return samplersElement;
        }

        samplersElement.addAttribute("Name", "采集器");
        Element samplerElement = samplersElement.addElement("Sampler");

        // 设置属性
        samplerElement.addAttribute("SamplerId", Convert.toStr(sampler.getSamplerId(), ""));
        samplerElement.addAttribute("SamplerName", sampler.getSamplerName());
        samplerElement.addAttribute("SamplerType", Convert.toStr(sampler.getSamplerType(), ""));
        samplerElement.addAttribute("ProtocolCode", sampler.getProtocolCode());
        samplerElement.addAttribute("DllCode", sampler.getDllCode());
        samplerElement.addAttribute("DLLVersion", sampler.getDllVersion());
        samplerElement.addAttribute("ProtocolFilePath", sampler.getProtocolFilePath());
        samplerElement.addAttribute("DLLFilePath", sampler.getDllFilePath());
        samplerElement.addAttribute("DllPath", sampler.getDllPath());
        samplerElement.addAttribute("Setting", sampler.getSetting());
        samplerElement.addAttribute("Description", sampler.getDescription());

        return samplersElement;
    }

    /**
     * 获取设备模板元素节点
     *
     * @param equipmentTemplate 设备模板
     * @return {@link Element}
     */
    private Element getEquipmentTemplateElement(TblEquipmentTemplate equipmentTemplate) {
        // Use DocumentHelper instead of document.addElement
        Element equipmentTemplateElement = DocumentHelper.createElement("EquipmentTemplate");

        equipmentTemplateElement.addAttribute("EquipmentTemplateId", Convert.toStr(equipmentTemplate.getEquipmentTemplateId()));
        equipmentTemplateElement.addAttribute("ParentTemplateId", "0");
        equipmentTemplateElement.addAttribute("EquipmentTemplateName", equipmentTemplate.getEquipmentTemplateName());
        equipmentTemplateElement.addAttribute("ProtocolCode", equipmentTemplate.getProtocolCode());
        equipmentTemplateElement.addAttribute("EquipmentCategory", Convert.toStr(equipmentTemplate.getEquipmentCategory()));
        equipmentTemplateElement.addAttribute("EquipmentType", Convert.toStr(equipmentTemplate.getEquipmentType()));
        equipmentTemplateElement.addAttribute("Memo", Convert.toStr(equipmentTemplate.getMemo(), ""));
        equipmentTemplateElement.addAttribute("Property", Convert.toStr(equipmentTemplate.getProperty(), ""));
        equipmentTemplateElement.addAttribute("Decription", Convert.toStr(equipmentTemplate.getDescription(), ""));
        equipmentTemplateElement.addAttribute("EquipmentStyle", Convert.toStr(equipmentTemplate.getEquipmentStyle(), ""));
        equipmentTemplateElement.addAttribute("Unit", Convert.toStr(equipmentTemplate.getUnit(), ""));
        equipmentTemplateElement.addAttribute("Vendor", Convert.toStr(equipmentTemplate.getVendor(), ""));
        equipmentTemplateElement.addAttribute("StationCategory", Convert.toStr(equipmentTemplate.getStationCategory(), "0"));
        equipmentTemplateElement.addAttribute("EquipmentBaseType", Convert.toStr(equipmentTemplate.getEquipmentBaseType(), ""));

        // 信号
        Element signalsElement = getTemplateSignalsElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(signalsElement);

        // 事件
        Element eventsElement = getEquipmentEventsElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(eventsElement);

        // 控制
        Element controlElement = getEquipmentControlElement(equipmentTemplate.getEquipmentTemplateId());
        equipmentTemplateElement.add(controlElement);

        return equipmentTemplateElement;
    }

    private Element getEquipmentControlElement(Integer equipmentTemplateId) {
        Element controlsElement = DocumentHelper.createElement("Controls");
        controlsElement.addAttribute("Name", "模板控制");

        List<ControlVO> controlList = controlService.findVoByEquipmentTemplateId(equipmentTemplateId);
        for (ControlVO control : controlList) {
            Element controlElement = controlsElement.addElement("Control");

            controlElement.addAttribute("ControlId", Convert.toStr(control.getControlId()));
            controlElement.addAttribute("ControlName", control.getControlName());
            controlElement.addAttribute("ControlCategory", Convert.toStr(control.getControlCategory()));
            controlElement.addAttribute("CmdToken", control.getCmdToken());
            controlElement.addAttribute("BaseTypeId", Convert.toStr(control.getBaseTypeId()));
            controlElement.addAttribute("ControlSeverity", Convert.toStr(control.getControlSeverity()));
            controlElement.addAttribute("SignalId", Convert.toStr(control.getSignalId()));
            controlElement.addAttribute("TimeOut", Convert.toStr(control.getTimeOut()));
            controlElement.addAttribute("Retry", Convert.toStr(control.getRetry()));
            if (generateSignalStandard && control.getDescription().length() > 9) {
                controlElement.addAttribute("Description", "bytedancebid=" + control.getDescription().replaceAll("-", ""));
            } else {
                controlElement.addAttribute("Description", control.getDescription());
            }
            controlElement.addAttribute("Enable", Convert.toStr(control.getEnable()));
            controlElement.addAttribute("Visible", Convert.toStr(control.getVisible()));
            controlElement.addAttribute("DisplayIndex", Convert.toStr(control.getDisplayIndex()));
            controlElement.addAttribute("CommandType", Convert.toStr(control.getCommandType()));
            controlElement.addAttribute("ControlType", Convert.toStr(control.getControlType()));
            controlElement.addAttribute("DataType", Convert.toStr(control.getDataType()));
            controlElement.addAttribute("MaxValue", Convert.toStr(control.getMaxValue()));
            controlElement.addAttribute("MinValue", Convert.toStr(control.getMinValue()));
            controlElement.addAttribute("DefaultValue", Convert.toStr(control.getDefaultValue()));
            controlElement.addAttribute("ModuleNo", Convert.toStr(control.getModuleNo()));

            StringJoiner sj = new StringJoiner(";");
            for (TblControlMeanings meanings : control.getControlMeaningsList()) {
                if (Objects.isNull(meanings.getParameterValue()) || Objects.isNull(meanings.getMeanings())) {
                    continue;
                }
                sj.add(meanings.getParameterValue() + ":" + meanings.getMeanings());
            }

            controlElement.addAttribute("ControlMeanings", sj.toString());
        }

        return controlsElement;
    }

    private Element getEquipmentEventsElement(Integer equipmentTemplateId) {
        Element eventsElement = DocumentHelper.createElement("Events");
        eventsElement.addAttribute("Name", "模板事件");

        List<EventConfigItem> eventList = eventService.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        for (EventConfigItem event : eventList) {
            Element eventElement = eventsElement.addElement("Event");

            eventElement.addAttribute("EventId", Convert.toStr(event.getEventId()));
            eventElement.addAttribute("EventName", event.getEventName());
            eventElement.addAttribute("EventCategory", Convert.toStr(event.getEventCategory()));
            eventElement.addAttribute("StartType", Convert.toStr(event.getStartType()));
            eventElement.addAttribute("EndType", Convert.toStr(event.getEndType()));
            eventElement.addAttribute("StartExpression", event.getStartExpression());
            eventElement.addAttribute("SuppressExpression", event.getSuppressExpression());
            eventElement.addAttribute("SignalId", Convert.toStr(event.getSignalId(), ""));
            eventElement.addAttribute("Enable", formatBooleanStr(event.getEnable(), ""));
            eventElement.addAttribute("Visible", formatBooleanStr(event.getVisible(), ""));
            if (generateSignalStandard && event.getDescription().length() > 9) {
                eventElement.addAttribute("Description", "bytedancebid=" + event.getDescription().replaceAll("-", ""));
            } else {
                eventElement.addAttribute("Description", event.getDescription());
            }
            eventElement.addAttribute("Turnover", Convert.toStr(event.getTurnover(), ""));
            eventElement.addAttribute("DisplayIndex", Convert.toStr(event.getDisplayIndex()));
            eventElement.addAttribute("ModuleNo", Convert.toStr(event.getModuleNo(), ""));

            Element conditionsElement = eventElement.addElement("Conditions");

            for (TblEventCondition eventCondition : event.getTblEventCondition()) {
                Element eventConditionElement = conditionsElement.addElement("EventCondition");

                eventConditionElement.addAttribute("EventConditionId", Convert.toStr(eventCondition.getEventConditionId()));
                eventConditionElement.addAttribute("EventSeverity", Convert.toStr(eventCondition.getEventSeverity()));
                eventConditionElement.addAttribute("StartOperation", Convert.toStr(eventCondition.getStartOperation(), ""));
                eventConditionElement.addAttribute("StartCompareValue", formatNumberStr(eventCondition.getStartCompareValue(), ""));
                eventConditionElement.addAttribute("StartDelay", Convert.toStr(eventCondition.getStartDelay(), ""));
                eventConditionElement.addAttribute("EndOperation", Convert.toStr(eventCondition.getEndOperation(), ""));
                eventConditionElement.addAttribute("EndCompareValue", Convert.toStr(eventCondition.getEndCompareValue(), ""));
                eventConditionElement.addAttribute("EndDelay", Convert.toStr(eventCondition.getEndDelay(), ""));
                eventConditionElement.addAttribute("Frequency", Convert.toStr(eventCondition.getFrequency(), ""));
                eventConditionElement.addAttribute("FrequencyThreshold", Convert.toStr(eventCondition.getFrequencyThreshold(), ""));
                eventConditionElement.addAttribute("Meanings", eventCondition.getMeanings());
                eventConditionElement.addAttribute("EquipmentState", Convert.toStr(eventCondition.getEquipmentState(), ""));
                eventConditionElement.addAttribute("BaseTypeId", Convert.toStr(eventCondition.getBaseTypeId(), ""));
                eventConditionElement.addAttribute("StandardName", Convert.toStr(eventCondition.getStandardName(), ""));
            }
        }

        return eventsElement;
    }

    /**
     * 将Boolean值转换为"True"或"False"的字符串形式，null值将返回指定的默认值
     *
     * @param value Boolean类型的值
     * @param defaultValue 当值为null时返回的默认值
     * @return 转换后的字符串，非null值转为"True"或"False"
     */
    private String formatBooleanStr(Boolean value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        return value ? "True" : "False";
    }

    private Element getTemplateSignalsElement(Integer equipmentTemplateId) {
        Element signalsElement = DocumentHelper.createElement("Signals");
        signalsElement.addAttribute("Name", "模板信号");

        List<SignalConfigItem> signalList = signalService.findItemByEquipmentTemplateId(equipmentTemplateId);
        for (SignalConfigItem signal : signalList) {
            Element signalElement = signalsElement.addElement("Signal");

            signalElement.addAttribute("SignalId", String.valueOf(signal.getSignalId()));
            signalElement.addAttribute("SignalName", signal.getSignalName());
            signalElement.addAttribute("SignalCategory", String.valueOf(signal.getSignalCategory()));
            signalElement.addAttribute("SignalType", String.valueOf(signal.getSignalType()));
            signalElement.addAttribute("ChannelNo", String.valueOf(signal.getChannelNo()));
            signalElement.addAttribute("ChannelType", String.valueOf(signal.getChannelType()));
            signalElement.addAttribute("Expression", Convert.toStr(signal.getExpression(), ""));
            signalElement.addAttribute("DataType", Convert.toStr(signal.getDataType(), ""));
            signalElement.addAttribute("ShowPrecision", Convert.toStr(signal.getShowPrecision(), ""));
            signalElement.addAttribute("Unit", Convert.toStr(signal.getUnit(), ""));
            signalElement.addAttribute("StoreInterval", formatNumberStr(signal.getStoreInterval(), ""));
            signalElement.addAttribute("AbsValueThreshold", formatNumberStr(signal.getAbsValueThreshold(), ""));
            signalElement.addAttribute("PercentThreshold", formatNumberStr(signal.getPercentThreshold(), ""));
            signalElement.addAttribute("StaticsPeriod", Convert.toStr(signal.getStaticsPeriod(), ""));
            signalElement.addAttribute("Enable", formatBooleanStr(signal.getEnable(), ""));
            signalElement.addAttribute("Visible", formatBooleanStr(signal.getVisible(), ""));
            if (generateSignalStandard && signal.getDescription().length() > 9) {
                signalElement.addAttribute("Discription", "bytedancebid=" + signal.getDescription().replaceAll("-", ""));
            } else {
                signalElement.addAttribute("Discription", String.valueOf(signal.getDescription()));
            }
            signalElement.addAttribute("BaseTypeId", Convert.toStr(signal.getBaseTypeId(), ""));
            signalElement.addAttribute("ChargeStoreInterVal", Convert.toStr(signal.getChargeStoreInterVal(), ""));
            signalElement.addAttribute("ChargeAbsValue", Convert.toStr(signal.getChargeAbsValue(), ""));
            signalElement.addAttribute("DisplayIndex", Convert.toStr(signal.getDisplayIndex()));
//            signalElement.addAttribute("MDBSignalId", Convert.toStr(signal.getMDBSignalId()));
            signalElement.addAttribute("SignalProperty", signal.getSignalPropertyXmlFormat());
            signalElement.addAttribute("SignalMeaning", signal.getSignalMeaningsXmlFormat());
            signalElement.addAttribute("ModuleNo", Convert.toStr(signal.getModuleNo(), ""));
        }

        return signalsElement;
    }

    /**
     * 格式化数字为字符串，null值将返回指定的默认值
     * @param value
     * @param defaultValue
     * @return
     */
    private String formatNumberStr(Double value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }

        if (value % 1 == 0) {
            // 如果是整数，使用不带小数点的格式
            return NumberUtil.decimalFormat("#", value);
        }
        return Convert.toStr(value);
    }

    private void createSignalFormXml(Integer equipmentTemplateId, Element signalsElement) {
        List<Element> signalElementList = signalsElement.elements("Signal");
        List<TblSignal> batchSignalList = new ArrayList<>(signalElementList.size());
        List<TblSignalProperty> batchSignalPropertyList = new ArrayList<>(signalElementList.size());
        List<TblSignalMeanings> batchSignalMeaningsList = new ArrayList<>(signalElementList.size());
        AtomicInteger atomicSignalId = new AtomicInteger(-1);

        for (Element signalElement : signalElementList) {
            TblSignal signal = parserSignalXml(equipmentTemplateId, signalElement, atomicSignalId);
            batchSignalList.add(signal);

            List<TblSignalProperty> signalPropertyList = parserSignalPropertyXml(equipmentTemplateId, signalElement, signal.getSignalId());
            batchSignalPropertyList.addAll(signalPropertyList);

            List<TblSignalMeanings> signalMeaningsList = parserSignalMeaningsXml(equipmentTemplateId, signalElement, signal.getSignalId());
            batchSignalMeaningsList.addAll(signalMeaningsList);
        }

        signalService.batchInsertSignal(batchSignalList);
        signalPropertyService.batchCreateSignalProperty(batchSignalPropertyList);
        signalMeaningsService.batchCreateSignalMeanings(batchSignalMeaningsList);
    }

    private List<TblSignalMeanings> parserSignalMeaningsXml(Integer equipmentTemplateId, Element signalsElement, Integer signalId) {
        String signalMeanings = signalsElement.attributeValue("SignalMeanings");
        if (signalMeanings == null) return new ArrayList<>();
        String[] meaningsArr = signalMeanings.split(";");
        return signalMeaningsService.parseSignalMeaningStr(equipmentTemplateId, signalId, meaningsArr);
    }

    private List<TblSignalProperty> parserSignalPropertyXml(Integer equipmentTemplateId, Element element, Integer signalId) {
        String attribute = element.attributeValue("SignalProperty");
        List<Integer> signalPropertyIdList = StrSplitUtil.splitToIntList(attribute);
        List<TblSignalProperty> signalPropertyList = new ArrayList<>(signalPropertyIdList.size());

        for (Integer propertyId : signalPropertyIdList) {
            TblSignalProperty signalProperty = new TblSignalProperty(null, equipmentTemplateId, signalId, propertyId);
            signalPropertyList.add(signalProperty);
        }

        return signalPropertyList;
    }

    private TblSignal parserSignalXml(Integer equipmentTemplateId, Element element, AtomicInteger atomicSignalId) {
        TblSignal signal = new TblSignal();
        signal.setEquipmentTemplateId(equipmentTemplateId);
        signal.setSignalId(getAttributeAsInteger(element, "SignalId"));
        signal.setSignalName(element.attributeValue("SignalName"));
        signal.setSignalCategory(getAttributeAsInteger(element, "SignalCategory"));
        signal.setSignalType(getAttributeAsInteger(element, "SignalType"));
        signal.setChannelNo(getAttributeAsInteger(element, "ChannelNo"));
        signal.setChannelType(getAttributeAsInteger(element, "ChannelType"));
        signal.setExpression(element.attributeValue("Expression"));
        signal.setDataType(getAttributeAsInteger(element, "DataType"));
        signal.setShowPrecision(element.attributeValue("ShowPrecision"));
        signal.setUnit(element.attributeValue("Unit"));
        signal.setStoreInterval(getAttributeAsDouble(element, "StoreInterval"));
        signal.setAbsValueThreshold(getAttributeAsDouble(element, "AbsValueThreshold"));
        signal.setPercentThreshold(getAttributeAsDouble(element, "PercentThreshold"));
        signal.setStaticsPeriod(getAttributeAsInteger(element, "StaticsPeriod"));
        signal.setEnable(getAttributeAsBoolean(element, "Enable"));
        signal.setVisible(getAttributeAsBoolean(element, "Visible"));
        String description = element.attributeValue("Discription");
        if (description != null) {
            if (description.contains("bytedancebid=")) {
                description = description.replace("bytedancebid=", "");
            }
            signal.setDescription(description);
        } else {
            // 处理 description 为 null 的情况
            // 可以设置一个默认值，或者记录日志，或者其他适当的处理
            signal.setDescription(""); // 例如，设置为空字符串
        }
        // 如果基类id等于0，则赋值为null,因为模板的错误，需要上层来补
        Long baseTypeId = getAttributeAsLong(element, "BaseTypeId", 0L);
        signal.setBaseTypeId(Objects.equals(baseTypeId, 0L) ? null : baseTypeId);

        signal.setChargeStoreInterVal(getAttributeAsDouble(element, "ChargeStoreInterVal"));
        signal.setChargeAbsValue(getAttributeAsDouble(element, "ChargeAbsValue"));
        signal.setDisplayIndex(getAttributeAsInteger(element, "DisplayIndex"));
        signal.setMDBSignalId(getAttributeAsInteger(element, "MDBSignalId"));
        signal.setModuleNo(getAttributeAsInteger(element, "ModuleNo"));

        // 需要自己生成信号id
        if (Objects.isNull(signal.getSignalId()) || Objects.equals(signal.getSignalId(), SignalConstant.GENERATE_SIGNAL_ID_FLAG)) {
            if (atomicSignalId.get() == -1) {
                atomicSignalId.set(signalService.findMaxSignalIdByEquipmentTemplateId(equipmentTemplateId));
                signal.setSignalId(atomicSignalId.get());
            } else {
                signal.setSignalId(atomicSignalId.incrementAndGet());
            }
        }

        return signal;
    }
}
