package org.siteweb.config.primary.controller;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblDoor;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.DoorService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: DoorControl
 * @descriptions: 门禁设备操作控制器
 * @author: xsx
 * @date: 2024/9/20 18:10
 **/
@Slf4j
@RestController
@RequestMapping("/door")
public class DoorControl {

    /**
     * 门禁控制器操作控制器，用于单独创建门禁设备
     * S6之前逻辑是根据设备所属模板的39通道，如果有，并存在通道号为20的实时信号，即创建门禁设备
     * 后续TCS计划将门禁接口统一，所有的设备全实例化，并且定义统一的门禁模板
     * 将门禁设备单独建立，不再与配置强绑定
     */

    @Autowired
    I18n i18n;

    @Autowired
    private DoorService doorService;

    @PostMapping(value = "/config", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> addDoor(@RequestBody TblDoor tblDoor){
        if(ObjectUtil.isEmpty(tblDoor)){
            return ResponseHelper.failed("request body cannot be null");
        }
        if(ObjectUtil.isEmpty(tblDoor.getEquipmentId()) ){
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null","equipmentId"));
        }
        if(ObjectUtil.isEmpty(tblDoor.getSamplerUnitId()) ){
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null","samplerUnitId"));
        }
        if(ObjectUtil.isEmpty(tblDoor.getDoorControlId()) ) {
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null", "doorControlId"));
        }
        if(ObjectUtil.isEmpty(tblDoor.getDoorNo()) ) {
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        doorService.createDoor(tblDoor);
        return ResponseHelper.successful(tblDoor);
    }

    @DeleteMapping(value = "/config/{equipmentId}/{doorNo}",produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> deleteDoor(@PathVariable(value = "equipmentId")Integer equipmentId,@PathVariable(value = "doorNo")Integer doorNo){
        if(ObjectUtil.isEmpty(equipmentId)){
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null","equipmentId"));
        }
        if(ObjectUtil.isEmpty(doorNo) ) {
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        boolean delFlag = doorService.deleteDoor(equipmentId,doorNo);
        ResponseEntity responseEntity = delFlag?ResponseHelper.successful(true):ResponseHelper.failed("delete door fail");
        return responseEntity;
    }

    @PutMapping(value = "/config",produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> updateDoor(@RequestBody TblDoor tblDoor){
        if(ObjectUtil.isEmpty(tblDoor.getEquipmentId())){
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null","equipmentId"));
        }
        if(ObjectUtil.isEmpty(tblDoor.getDoorNo()) ) {
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null", "doorNo"));
        }
        TblDoor res = doorService.updateDoor(tblDoor);
        return ResponseHelper.successful(res);
    }

    @GetMapping(value = "/config/{equipmentId}",produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> getDoor(@PathVariable(value = "equipmentId") Integer equipmentId){
        if(ObjectUtil.isEmpty(equipmentId)){
            return ResponseHelper.failed(i18n.T("parameter.cannot.be.null","equipmentId"));
        }
        List<TblDoor> tblDoorList = doorService.getDoorByEquipmentId(equipmentId);
        return ResponseHelper.successful(tblDoorList);
    }

}
