package org.siteweb.config.primary.service.impl;

import org.siteweb.config.common.entity.TblFaceData;
import org.siteweb.config.common.mapper.TblFaceDataMapper;
import org.siteweb.config.primary.service.FaceDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (2024-09-11)
 **/
@Service
public class FaceDataServiceImpl implements FaceDataService {

    @Autowired
    private TblFaceDataMapper faceDataMapper;


    @Override
    public TblFaceData findDataById(Integer bioId) {
        return faceDataMapper.selectById(bioId);
    }
}
