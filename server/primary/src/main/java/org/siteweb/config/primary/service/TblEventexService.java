package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblEventex;

import java.util.List;

public interface TblEventexService {

    /***
     * 事件修改翻转时间
     *
     * @param equipmentTemplateId 设备模板id
     * @param eventId 事件id
     * @param turnover 翻转时间
     * <AUTHOR> (2024/4/15)
     */
    void updateEventx(Integer equipmentTemplateId, Integer eventId, Integer turnover);

    void deleteByEvent(Integer equipmentTemplateId, Integer eventId);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchUpdate(List<TblEventex> eventexList);
}
