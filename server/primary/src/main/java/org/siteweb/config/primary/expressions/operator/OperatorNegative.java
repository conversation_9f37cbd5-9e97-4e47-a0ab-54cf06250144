package org.siteweb.config.primary.expressions.operator;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;

/**
 * 运算符负数
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorNegative extends OperatorBase {
    @Override
    public String operatorSymbol() {
        return "-";
    }

    @Override
    public String operatorName() {
        return "取负号";
    }

    @Override
    public int priority() {
        return 15;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.RIGHT_TO_LEFT;
    }

    @Override
    public int operandCount() {
        return 1;
    }

    @Override
    public double onCalculate(double[] operands) {
        return -operands[0];
    }
}
