package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblBusinessType;
import org.siteweb.config.common.vo.BusinessExpressionVO;
import org.siteweb.config.common.vo.BusinessTypeExpressionVO;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/18
 */

public interface TblBusinessTypeService {
    List<TblBusinessType> findTblBusinessTypeByMonitorUnitId(Integer monitorUnitId);

    List<BusinessTypeExpressionVO> findDistinctBusinessTypesAndExpressionsForMonitorUnit(Integer monitorUnitId);

    List<BusinessExpressionVO> findDistinctBusinessExpressionsForMonitorUnit(Integer monitorUnitId);


}
