package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.BaseTypeTreeDTO;
import org.siteweb.config.common.dto.EventBaseDetailDTO;
import org.siteweb.config.common.dto.EventBaseDicDTO;
import org.siteweb.config.common.dto.EventBaseDictionaryDTO;
import org.siteweb.config.common.entity.TblBaseSignalEventCode;
import org.siteweb.config.common.entity.TblEventBaseDic;
import org.siteweb.config.common.entity.TblEventCondition;
import org.siteweb.config.common.mapper.TblBaseSignalEventCodeMapper;
import org.siteweb.config.common.mapper.TblEventBaseDicMapper;
import org.siteweb.config.common.mapper.TblEventConditionMapper;
import org.siteweb.config.common.vo.BaseDicFilterVO;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.primary.service.DataItemService;
import org.siteweb.config.primary.service.EquipmentBaseTypeService;
import org.siteweb.config.primary.service.EventBaseDicService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/14
 */
@Service
public class EventBaseDicServiceImpl implements EventBaseDicService {

    @Autowired
    TblEventBaseDicMapper eventBaseDicMapper;
    @Autowired
    I18n i18n;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    TblBaseSignalEventCodeMapper baseSignalEventCodeMapper;
    @Autowired
    TblEventConditionMapper eventConditionMapper;

    @Override
    public List<TblEventBaseDic> list(EventBaseDicDTO eventBaseDicDTO) {
        return eventBaseDicMapper.selectList(new QueryWrapper<TblEventBaseDic>()
                .eq("BaseEquipmentId", eventBaseDicDTO.getBaseEquipmentId()));
    }

    @Override
    public TblEventBaseDic findByBaseTypeId(Long baseTypeId) {
        return eventBaseDicMapper.selectById(baseTypeId);
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return eventBaseDicMapper.exists(Wrappers.lambdaQuery(TblEventBaseDic.class)
                .eq(TblEventBaseDic::getBaseTypeId, baseTypeId));
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        eventBaseDicMapper.generateEventBaseDic(baseTypeId, sourceId);
        TblEventBaseDic eventBaseDic = findByBaseTypeId(baseTypeId);
        if (Objects.isNull(eventBaseDic) || CharSequenceUtil.isBlank(eventBaseDic.getBaseNameExt())) {
            return;
        }
        Long moduleNo = baseTypeId % 1000;
        String baseTypeName = CharSequenceUtil.indexedFormat(eventBaseDic.getBaseNameExt(), moduleNo);
        eventBaseDic.setBaseTypeName(baseTypeName);
        eventBaseDicMapper.updateById(eventBaseDic);
    }

    @Override
    public List<TblEventBaseDic> findEventBaseDic(BaseDicFilterVO baseDicFilterVO) {
        return eventBaseDicMapper.findEventBaseDic(baseDicFilterVO);
    }

    @Override
    public void deleteLianTongByBaseTypeId(int baseTypeId) {
        eventBaseDicMapper.deleteLianTongByBaseTypeId(baseTypeId);
    }

    @Override
    public void createEventBaseDic(TblEventBaseDic eventBaseDic) {
        eventBaseDicMapper.insert(eventBaseDic);
    }

    @Override
    public void createLianTongEventBaseDic() {
        eventBaseDicMapper.createLianTongEventBaseDic();
    }

    @Override
    public List<EventBaseDictionaryDTO> findEventBaseDicList() {
        List<EventBaseDictionaryDTO> eventBaseDicList = eventBaseDicMapper.findEventBaseDicList();
        // 获取事件等级
        Map<Integer, String> eventLevels = dataItemService.getDataItemMap(DataEntryEnum.EVENT_LEVEL);
        eventBaseDicList.forEach(eventBaseDictionary -> eventBaseDictionary.setEventSeverityName(eventLevels.get(eventBaseDictionary.getEventSeverityId())));
        return eventBaseDicList;
    }

    @Override
    public void addEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO) {
        if (Objects.isNull(eventBaseDictionaryDTO.getBaseEquipmentId())) {
            throw new InvalidParameterException("add EventBaseDic baseEquipmentId cannot be empty");
        }
        if (StringUtils.isBlank(eventBaseDictionaryDTO.getBaseTypeName())) {
            throw new InvalidParameterException("add EventBaseDic baseTypeName cannot be empty");
        }
        if (StringUtils.isNotBlank(eventBaseDictionaryDTO.getBaseNameExt()) && !eventBaseDictionaryDTO.getBaseNameExt().contains("{0}")) {
            throw new InvalidParameterException(i18n.T("eventBase.baseNameExt.format"));
        }
        Long baseTypeId = generateBaseTypeId(eventBaseDictionaryDTO.getBaseEquipmentId());
        TblEventBaseDic tblEventBaseDic = BeanUtil.copyProperties(eventBaseDictionaryDTO, TblEventBaseDic.class);
        tblEventBaseDic.setBaseTypeId(baseTypeId);
        tblEventBaseDic.setIsSystem(Boolean.TRUE.equals(eventBaseDictionaryDTO.getIsSystem()));
        eventBaseDicMapper.insert(tblEventBaseDic);
    }

    @Override
    public void updateEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO) {
        Long baseTypeId = eventBaseDictionaryDTO.getBaseTypeId();
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("update EventBaseDic baseTypeId cannot be empty");
        }
        TblEventBaseDic tblEventBaseDic = eventBaseDicMapper.selectById(baseTypeId);
        if (Objects.isNull(tblEventBaseDic)) {
            throw new BusinessException("eventBaseDic does not exist");
        }
        if (Boolean.TRUE.equals(tblEventBaseDic.getIsSystem())) {
            throw new BusinessException(i18n.T("eventBase.isSystem.update"));
        }
        if (eventConditionMapper.exists(Wrappers.lambdaQuery(TblEventCondition.class).eq(TblEventCondition::getBaseTypeId, baseTypeId))) {
            throw new BusinessException(i18n.T("eventBase.baseTypeId.update"));
        }
        TblEventBaseDic eventBaseDic = BeanUtil.copyProperties(eventBaseDictionaryDTO, TblEventBaseDic.class);
        eventBaseDicMapper.updateById(eventBaseDic);
    }

    @Override
    public void deleteEventBaseDic(Long baseTypeId) {
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("delete EventBaseDic baseTypeId cannot be empty");
        }
        TblEventBaseDic tblEventBaseDic = eventBaseDicMapper.selectById(baseTypeId);
        if (Objects.isNull(tblEventBaseDic)) {
            return;
        }
        if (Boolean.TRUE.equals(tblEventBaseDic.getIsSystem())) {
            throw new BusinessException(i18n.T("eventBase.isSystem.delete"));
        }
        if (eventConditionMapper.exists(Wrappers.lambdaQuery(TblEventCondition.class).eq(TblEventCondition::getBaseTypeId, baseTypeId))) {
            throw new BusinessException(i18n.T("eventBase.baseTypeId.delete"));
        }
        eventBaseDicMapper.deleteById(baseTypeId);
    }

    @Override
    public void batchAddEventBaseDic(Long baseTypeId, Integer startNumber, Integer abortNumber) {
        if (Objects.isNull(baseTypeId)) {
            throw new InvalidParameterException("batchAdd EventBaseDic baseTypeId cannot be empty");
        }
        if (Objects.isNull(startNumber) || startNumber < 0 || startNumber > abortNumber) {
            throw new InvalidParameterException("startNumber Exception");
        }
        if (abortNumber > 999) {
            throw new InvalidParameterException(i18n.T("abortNumber.format"));
        }
        long sourceId = ((baseTypeId / 1000) * 1000) + 1;
        TblEventBaseDic tblEventBaseDic = eventBaseDicMapper.selectById(sourceId);
        if (Objects.isNull(tblEventBaseDic)) {
            throw new BusinessException("source eventBaseDic does not exist");
        }
        String baseNameExt = tblEventBaseDic.getBaseNameExt();
        if (StringUtils.isBlank(baseNameExt)) {
            throw new InvalidParameterException(i18n.T("baseNameExt.batchAdd"));
        }
        if (!baseNameExt.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("eventBase.baseNameExt.format"));
        }
        List<Long> baseTypeIdList = eventBaseDicMapper.selectList(Wrappers.lambdaQuery(TblEventBaseDic.class)
                        .select(TblEventBaseDic::getBaseTypeId)
                        .eq(TblEventBaseDic::getBaseEquipmentId, tblEventBaseDic.getBaseEquipmentId()))
                .stream().map(TblEventBaseDic::getBaseTypeId).toList();
        List<TblEventBaseDic> eventBaseDicList = new ArrayList<>();
        long sourceBaseTypeId = (tblEventBaseDic.getBaseTypeId() / 1000) * 1000;
        startNumber = Math.max(startNumber, 1);
        abortNumber = Math.max(abortNumber, 1);
        for (; startNumber <= abortNumber; startNumber++) {
            long handleBaseTypeId = sourceBaseTypeId + startNumber;
            if (!baseTypeIdList.contains(handleBaseTypeId)) {
                TblEventBaseDic eventBaseDic = BeanUtil.copyProperties(tblEventBaseDic, TblEventBaseDic.class);
                eventBaseDic.setBaseTypeId(handleBaseTypeId);
                eventBaseDic.setBaseTypeName(CharSequenceUtil.indexedFormat(baseNameExt, startNumber));
                eventBaseDic.setIsSystem(false);
                eventBaseDicList.add(eventBaseDic);
            }
        }
        if (CollUtil.isEmpty(eventBaseDicList)) {
            return;
        }
        eventBaseDicMapper.insertBatchSomeColumn(eventBaseDicList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO) {
        // 该方法前端暂未用，后续可删除
        String baseNameExt = eventBaseDictionaryDTO.getBaseNameExt();
        if (StringUtils.isBlank(baseNameExt)) {
            throw new InvalidParameterException(i18n.T("baseNameExt.batchUpdate"));
        }
        if (!baseNameExt.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("eventBase.baseNameExt.format"));
        }
        long prefixSourceId = eventBaseDictionaryDTO.getBaseTypeId() / 1000;
        long sourceId = (prefixSourceId * 1000) + 1;
        TblEventBaseDic sourceEventBaseDic = eventBaseDicMapper.selectById(sourceId);
        if (Objects.isNull(sourceEventBaseDic)) {
            throw new BusinessException("source eventBaseDic does not exist");
        }
        List<TblEventBaseDic> tblEventBaseDics = eventBaseDicMapper.selectList(Wrappers.lambdaQuery(TblEventBaseDic.class)
                .likeRight(TblEventBaseDic::getBaseTypeId, prefixSourceId));
        List<TblEventBaseDic> eventBaseDicList = new ArrayList<>();
        for (TblEventBaseDic tblEventBaseDic : tblEventBaseDics) {
            if (Boolean.TRUE.equals(tblEventBaseDic.getIsSystem())) {
                continue;
            }
            TblEventBaseDic eventBaseDic = BeanUtil.copyProperties(eventBaseDictionaryDTO, TblEventBaseDic.class);
            eventBaseDic.setBaseTypeId(tblEventBaseDic.getBaseTypeId());
            eventBaseDic.setBaseEquipmentId(tblEventBaseDic.getBaseEquipmentId());
            eventBaseDic.setBaseTypeName(CharSequenceUtil.indexedFormat(baseNameExt, eventBaseDic.getBaseTypeId() % 1000));
            eventBaseDic.setIsSystem(tblEventBaseDic.getIsSystem());
            eventBaseDicList.add(eventBaseDic);
        }
        return mybatisBatchUtils.batchUpdateOrInsert(eventBaseDicList, TblEventBaseDicMapper.class, (item, mapper) -> mapper.updateById(item));
    }

    @Override
    public List<BaseTypeTreeDTO> findEventBaseDicTree(Integer equipmentBaseType, Boolean isSystem) {
        List<TblEventBaseDic> eventBaseDicList = eventBaseDicMapper.selectList(Wrappers.lambdaQuery(TblEventBaseDic.class)
                .orderByAsc(TblEventBaseDic::getBaseEquipmentId, TblEventBaseDic::getBaseTypeName));
        return equipmentBaseTypeService.buildBaseDicTree(equipmentBaseType, baseEquipmentId -> getEventBaseTypeTreeDTOS(baseEquipmentId, eventBaseDicList, isSystem));
    }

    @Override
    public EventBaseDetailDTO findEventBaseDicDetail(Long baseTypeId) {
        EventBaseDictionaryDTO eventBaseDic = eventBaseDicMapper.findEventBaseDicDetail(baseTypeId);
        if (Objects.isNull(eventBaseDic)) {
            return null;
        }
        EventBaseDetailDTO eventBaseTreeDetail = BeanUtil.copyProperties(eventBaseDic, EventBaseDetailDTO.class);
        // 获取事件等级
        Map<Integer, String> eventLevels = dataItemService.getDataItemMap(DataEntryEnum.EVENT_LEVEL);
        Integer eventSeverityId = eventBaseTreeDetail.getEventSeverityId();
        eventBaseTreeDetail.setEventSeverityName(String.format("%s[%d]", eventLevels.get(eventSeverityId), eventSeverityId));
        // 获取事件条件编码
        String baseTypeIdSting = String.valueOf(eventBaseTreeDetail.getBaseTypeId());
        String code = CharSequenceUtil.subWithLength(baseTypeIdSting, baseTypeIdSting.length() - 6, 3);
        TblBaseSignalEventCode tblBaseSignalEventCode = baseSignalEventCodeMapper.selectById(Integer.valueOf(code));
        if (Objects.nonNull(tblBaseSignalEventCode)) {
            eventBaseTreeDetail.setEventConditionCode(code + (CharSequenceUtil.isNotBlank(tblBaseSignalEventCode.getEvent()) ? " - " + tblBaseSignalEventCode.getEvent() : ""));
        } else {
            eventBaseTreeDetail.setEventConditionCode(code);
        }
        eventBaseTreeDetail.setEventConditionCategory(getEventCodeCategory(Integer.parseInt(code)));
        return eventBaseTreeDetail;
    }

    /**
     * 获取事件条件类别
     */
    @Override
    public String getEventCodeCategory(int code) {
        if (code >= 1 && code <= 300) {
            return "重要基类事件条件";
        } else if (code >= 301 && code <= 800) {
            return "兼容电信和移动等标准化而引入的事件条件";
        } else if (code >= 801 && code <= 900) {
            return "现场自定义基类事件条件";
        } else if (code == 998) {
            return "其他告警";
        } else if (code == 999) {
            return "设备通讯状态告警";
        } else {
            return "无法分类,事件条件基类ID定义有问题";
        }
    }

    /**
     * 获取基类事件树子节点
     */
    private List<BaseTypeTreeDTO> getEventBaseTypeTreeDTOS(Integer baseEquipmentId, List<TblEventBaseDic> eventBaseDicList, Boolean isSystem) {
        return eventBaseDicList.stream().filter(eventBaseDic -> judgeTreeCondition(baseEquipmentId, eventBaseDic, isSystem))
                .map(eventBaseDic -> {
                    BaseTypeTreeDTO eventBaseChild = new BaseTypeTreeDTO();
                    eventBaseChild.setKey(String.valueOf(eventBaseDic.getBaseTypeId()));
                    eventBaseChild.setValue(eventBaseDic.getBaseTypeId());
                    eventBaseChild.setLabel(eventBaseDic.getBaseTypeName());
                    eventBaseChild.setBaseNameExt(eventBaseDic.getBaseNameExt());
                    eventBaseChild.setIndex(2);
                    eventBaseChild.setChildren(Collections.emptyList());
                    return eventBaseChild;
                }).toList();
    }

    private boolean judgeTreeCondition(Integer baseEquipmentId, TblEventBaseDic eventBaseDic, Boolean isSystem) {
        boolean baseEquipmentIdEqual = Objects.equals(eventBaseDic.getBaseEquipmentId(), baseEquipmentId);
        if (Boolean.FALSE.equals(baseEquipmentIdEqual)) {
            return false;
        }
        if (Boolean.TRUE.equals(isSystem)) {
            return Boolean.TRUE.equals(eventBaseDic.getIsSystem());
        }
        return baseEquipmentIdEqual;
    }

    private Long generateBaseTypeId(Integer baseEquipmentId) {
        List<Long> baseTypeIdList = eventBaseDicMapper.selectList(Wrappers.lambdaQuery(TblEventBaseDic.class)
                        .select(TblEventBaseDic::getBaseTypeId)
                        .eq(TblEventBaseDic::getBaseEquipmentId, baseEquipmentId))
                .stream().map(TblEventBaseDic::getBaseTypeId).toList();
        Long baseTypeId = null;
        for (int i = 899; i >= 101; i--) {
            String tempBaseTypeId = String.format("%d%d001", baseEquipmentId, i);
            // 避免ID重复
            if (!baseTypeIdList.contains(Long.valueOf(tempBaseTypeId))) {
                baseTypeId = Long.valueOf(tempBaseTypeId);
                break;
            }
        }
        if (Objects.isNull(baseTypeId)) {
            throw new BusinessException(i18n.T("eventBase.baseTypeId.overflow"));
        }
        return baseTypeId;
    }


}
