package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.DriveStructureTemplateVO;
import org.siteweb.config.common.dto.batchtool.*;
import org.siteweb.config.common.entity.DriveStructureTemplate;
import org.siteweb.config.common.entity.EquipmentExt;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.mapper.EquipmentExtMapper;
import org.siteweb.config.common.vo.batchtool.EquipmentSettingVO;
import org.siteweb.config.primary.batchtool.BuildConfigRunnable;
import org.siteweb.config.primary.batchtool.PortTypeEnum;
import org.siteweb.config.primary.batchtool.warning.WarningApplication;
import org.siteweb.config.primary.service.DriveStructureTemplateService;
import org.siteweb.config.primary.service.DriveTemplateService;
import org.siteweb.config.primary.service.EquipmentExtService;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.utils.ZipUtil;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.config.FileServerProperty;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2022/6/28 10:55
 */
@Service
@Slf4j
public class EquipmentExtServiceImpl implements EquipmentExtService {

    @Autowired
    DriveStructureTemplateService driveStructureTemplateService;

    @Autowired
    EquipmentExtMapper equipmentExtMapper;

    @Autowired
    DriveTemplateService driveTemplateService;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    WarningApplication warningApplication;
    @Autowired
    FileServerProperty fileServerProperty;
    @Autowired
    I18n i18n;

    @Qualifier("defaultExecutor")
    @Autowired
    TaskExecutor taskExecutor;
    public static final Integer YES = 1;
    public static final Integer NO = 0;

    public static final String buildFileRootPath = "buildConfig";

    @Override
    public void buildConfig(BuildConfigDTO buildConfigDTO, HttpServletResponse response) {
        long dbStartTime = System.currentTimeMillis();
        // 1. 根据设备id查询设备相关信息
        List<EquipmentSettingVO> dealEquipments = equipmentExtMapper.findDestSettingListByEquipmentIds(buildConfigDTO.getPortType(), buildConfigDTO.getEquipmentIds());
        if (CollUtil.isEmpty(dealEquipments)) {
            return;
        }

        // 2. 获取设备使用的驱动模板
        Set<Integer> driveTemplateIds = dealEquipments.stream().map(EquipmentSettingVO::getDriveTemplateId).collect(Collectors.toSet());

        // 3. 根据驱动模板id查询驱动结构
        List<DriveStructureTemplateVO> driveStructureTemplateList = driveStructureTemplateService.findByDriveTemplateIds(driveTemplateIds);
        if (CollUtil.isEmpty(driveStructureTemplateList)) {
            log.error("请求生成配置参数：{}, 不存在的驱动模板ids，{}", buildConfigDTO, driveTemplateIds);
            throw new BusinessException(i18n.T("batchTool.driverTemplate.NotExists"));
        }

        // 4. 获取该些设备中上传了的文件id（后续需要加载到内存）
        Set<Integer> fileIds = dealEquipments.stream().map(EquipmentSettingVO::getFileId).collect(Collectors.toSet());

        // 4.1 获取驱动模板中需要用到的文件id
        for (DriveStructureTemplateVO driveStructureTemplate : driveStructureTemplateList) {
            // 需要上传并且是创建模板时上传的文件
            if (Objects.equals(driveStructureTemplate.getIsUpload(), 1) && Objects.equals(driveStructureTemplate.getUploadTiming(), 0)) {
                // 创建模板时就需要上传的文件
                fileIds.add(driveStructureTemplate.getFileId());
            }
        }

        // 5. 提前加载文件资源 key => fileId， value => fileByteData

        Map<Integer, File> resourceMap = driveStructureTemplateService.getResourceByFileIds(fileIds);
        long dbEndTime = System.currentTimeMillis();
        // 6. 将驱动结构整合成驱动模板
        driveStructureTemplateService.buildTree(driveStructureTemplateList);
        Map<Integer, DriveStructureTemplateVO> driveTemplateMap = driveStructureTemplateList.stream().filter(e -> Objects.equals(e.getPid(), 0)).collect(Collectors.toMap(DriveStructureTemplate::getDriveTemplateId, e -> e));
        // to [driveTemplateId(驱动模板id) => DriveTemplate(驱动模板， 包含当前模板下的所有驱动结构)]

        // 7. 生成随机目录明（后续用于压缩和创建文件）
        String rootPath = fileServerProperty.getRootPath();
        String tempPath = UUID.randomUUID().toString();
        long fileHandlerStarTime = System.currentTimeMillis();
        List<CompletableFuture<?>> tasks = new ArrayList<>(dealEquipments.size());
        for (EquipmentSettingVO equipmentSettingVo : dealEquipments) {
            // 获取要处理的驱动模板
            DriveStructureTemplateVO driveTemplate = driveTemplateMap.get(equipmentSettingVo.getDriveTemplateId());
            if (driveTemplate == null) {
                continue;
            }
            tasks.add(CompletableFuture.runAsync(new BuildConfigRunnable(rootPath + File.separator + tempPath, driveTemplate, equipmentSettingVo, resourceMap, i18n)));
        }
        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();

        long fileHandlerEndTime = System.currentTimeMillis();
        long zipStartTime = System.currentTimeMillis();
        String packagePath = rootPath + File.separator + tempPath;
        String zipPath = packagePath + ".zip";

        // 3.压缩
        ZipUtil.compress(packagePath, zipPath, true);
        long zipEndTime = System.currentTimeMillis();

        long toWebStartTime = System.currentTimeMillis();
        // 4.输出web
        ZipUtil.toWebDownLoad(zipPath, DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".zip", response);
        // 5.删除压缩包
        PathUtil.del(Paths.get(zipPath));

        log.info("[{}]生成配置，数据操作相关耗时:{}ms, 文件创建耗时:{}ms, 压缩文件耗时:{}ms, 文件输出web耗时:{}ms, 总耗时:{}ms",
                buildConfigDTO,
                dbEndTime - dbStartTime,
                fileHandlerEndTime - fileHandlerStarTime,
                zipEndTime - zipStartTime,
                System.currentTimeMillis() - toWebStartTime,
                System.currentTimeMillis() - dbStartTime);

        if (CollUtil.isNotEmpty(dealEquipments)) {
            updateTemplateByEquipment(dealEquipments, 0);
        }
    }

    @Override
    public void updateTemplateByEquipment(List<EquipmentSettingVO> equipmentSettingVOList, Integer isReset) {
        List<EquipmentExt> collect = equipmentSettingVOList.stream().map(e -> {
            EquipmentExt equipmentExt = new EquipmentExt();
            equipmentExt.setFieldHash(e.fieldHashValue());
            equipmentExt.setIsReset(isReset);
            equipmentExt.setIsUpload(e.getIsUpload());
            equipmentExt.setFileId(e.getFileId());
            equipmentExt.setEquipmentId(e.getEquipmentId());
            equipmentExt.setDriveTemplateId(e.getDriveTemplateId());
            return equipmentExt;
        }).toList();
        mybatisBatchUtils.batchUpdateOrInsert(collect, EquipmentExtMapper.class, (item, mapper) -> mapper.updateById(item));
    }

    @Override
    public List<EquipmentSettingVO> getSettingManager(EquipmentSettingDTO equipmentSettingDTO) {
        List<EquipmentSettingVO> settingManager = equipmentExtMapper.getSettingManagerList(equipmentSettingDTO.getPortType());
        // 判断原集合中是否与目标集合中的有冲突,需警告或提醒
        warningApplication.isWarningOrRemind(settingManager, settingManager);
        // 判断客户端工具是否有所修改
        this.existsClientUpdate(settingManager);
        return settingManager;
    }

    @Override
    public void initExt(List<Integer> equipmentIds, Integer portType) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        Set<Integer> notInitEquipmentIds = this.getNotInitEquipmentIds(equipmentIds);
        if (CollUtil.isEmpty(notInitEquipmentIds)) {
            return;
        }
        // 查询默认模板
        Integer defaultTemplateId = this.getDefaultTemplateId(portType);
        // 初始化并保存ids
        List<EquipmentExt> equipmentSaveList = new ArrayList<>(notInitEquipmentIds.size());
        for (Integer notInitEquipmentId : notInitEquipmentIds) {
            equipmentSaveList.add(new EquipmentExt(notInitEquipmentId, YES, NO, defaultTemplateId));
        }
        equipmentExtMapper.batchInsert(equipmentSaveList);
    }

    @Override
    public EquipmentSettingVO getSettingManagerById(Integer id) {
        return equipmentExtMapper.findSettingManagerById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEquipment(EquipmentSettingUpdateDTO equipmentSettingUpdateDto) {
        // 模板重新生成
        this.resetTemplateByEquipmentId(equipmentSettingUpdateDto.getEquipmentId());
        // 更新设备端口信息
        equipmentSettingUpdateDto.setSetting(CharSequenceUtil.concat(true, equipmentSettingUpdateDto.getIpAddress(), "/", equipmentSettingUpdateDto.getPortAttribute()));
        this.equipmentExtMapper.updatePort(equipmentSettingUpdateDto);
        // 更新采集单元信息
        this.equipmentExtMapper.updateSamplerUnit(equipmentSettingUpdateDto);
        // 更新驱动模板
        this.equipmentExtMapper.updateTemplateByEquipmentId(equipmentSettingUpdateDto.getEquipmentId(), equipmentSettingUpdateDto.getDriveTemplateId());
        return true;
    }

    @Override
    public Boolean uploadSetting(EquipmentExtUpdateDTO equipmentExtUpdateDto) {
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentExtUpdateDto.getEquipmentId());
        // 全部上传
        if (equipmentExtUpdateDto.getIsAll() == YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return equipmentExtMapper.updateTemplateFileById(equipment.getEquipmentTemplateId(), equipmentExtUpdateDto.getFileId(), YES) > 0;
        }
        // 上传一个
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return equipmentExtMapper.updateFileIdByEquipmentId(equipment.getEquipmentId(), equipmentExtUpdateDto.getFileId(), YES) > 0;
    }

    @Override
    public Boolean deleteSetting(EquipmentExtUpdateDTO equipmentExtUpdateDto) {
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentExtUpdateDto.getEquipmentId());
        // 全部删除
        if (equipmentExtUpdateDto.getIsAll() == YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return this.equipmentExtMapper.updateTemplateFileById(equipment.getEquipmentTemplateId(), null, NO) > 0;
        }
        // 单单删除这一个
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return this.equipmentExtMapper.updateFileIdByEquipmentId(equipment.getEquipmentId(), null, NO) > 0;
    }

    @Override
    public Boolean updateTemplateByEquipmentId(EquipmentExtUpdateDTO equipmentExtUpdateDto) {
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentExtUpdateDto.getEquipmentId());
        // 如果修改了驱动模板，对于引用统一模板的设备，弹框，手动选择是否要批量切换(是)
        if (equipmentExtUpdateDto.getIsAll() == YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return this.equipmentExtMapper.updateTemplateByTemplateId(equipment.getEquipmentTemplateId(), equipmentExtUpdateDto.getDriveTemplateId()) > 0;
        }
        // 否
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return this.equipmentExtMapper.updateTemplateByEquipmentId(equipmentExtUpdateDto.getEquipmentId(), equipmentExtUpdateDto.getDriveTemplateId()) > 0;
    }

    @Override
    public Integer initSampler(BuildConfigDTO buildConfigDTO) {
        if (CollUtil.isEmpty(buildConfigDTO.getEquipmentIds())) {
            return 0;
        }
        String portName = PortTypeEnum.getPortNameByType(buildConfigDTO.getPortType());
        if (CharSequenceUtil.isBlank(portName)) {
            log.error("端口类型错误:{}, ", buildConfigDTO);
            return 0;
        }
        List<NeedSamplerDTO> portList = this.equipmentExtMapper.getNeedInitSampler(buildConfigDTO.getEquipmentIds());
        for (NeedSamplerDTO dto : portList) {
            dto.setAddress(dto.getPortNo());
            dto.setDllPath(CharSequenceUtil.concat(true, portName, dto.getPortNo().toString(), ".so"));
        }
        // 批量初始化采集单元地址与采集动态库地址
        equipmentExtMapper.batchInit(portList);
        return portList.size();
    }

    /**
     * 重新生成模板(设备id)
     *
     * @param equipmentId 设备id
     */
    private void resetTemplateByEquipmentId(Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId)) {
            return;
        }
        LambdaUpdateWrapper<EquipmentExt> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(EquipmentExt::getIsReset, YES)
                .eq(EquipmentExt::getEquipmentId, equipmentId);
        equipmentExtMapper.update(null, lambdaUpdateWrapper);
    }

    private Integer getDefaultTemplateId(Integer portType) {
        Integer driveTemplateType = PortTypeEnum.getDriveTemplateTypeByType(portType);
        return driveTemplateService.getDefaultTemplateByType(driveTemplateType);
    }

    private Set<Integer> getNotInitEquipmentIds(List<Integer> equipmentIds) {
        // 获取已初始化的id,并将其移除
        List<Integer> existsEquipmentIds = equipmentExtMapper.existsEquipmentIds(equipmentIds);
        equipmentIds.removeIf(existsEquipmentIds::contains);
        return new HashSet<>(equipmentIds);
    }

    /**
     * 判断客户端工具是否有所修改
     *
     * @param content 分页数据
     */
    private void existsClientUpdate(List<EquipmentSettingVO> content) {
        if (CollUtil.isEmpty(content)) {
            return;
        }

        for (EquipmentSettingVO setting : content) {
            // 字段hash为空说明无重新生成过,必重新生成
            if (ObjectUtil.isNull(setting.getFieldHash())) {
                setting.setIsReset(YES);
                continue;
            }
            if (!ObjectUtil.equals(setting.getFieldHash(), setting.fieldHashValue())) {
                setting.setIsReset(YES);
            }
        }
    }

    /**
     * 重新生成模板根据 设备引用模板id
     * 相同模板的设备全部需要重新生成配置
     *
     * @param equipmentTemplateId 引用模板id
     */
    private void resetTemplateByTemplateId(Integer equipmentTemplateId) {
        if (ObjectUtil.isNull(equipmentTemplateId)) {
            return;
        }
        equipmentExtMapper.resetTemplateByTemplateId(equipmentTemplateId);
    }


}
