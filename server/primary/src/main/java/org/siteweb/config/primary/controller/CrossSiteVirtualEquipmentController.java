package org.siteweb.config.primary.controller;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.utils.RestfulCode;
import org.siteweb.config.common.vo.batchtool.CrossSiteSplitEquipmentVO;
import org.siteweb.config.common.vo.batchtool.VirtualEquipmentSettingVO;
import org.siteweb.config.primary.service.CrossSiteVirtualEquipmentService;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.service.StationService;
import org.siteweb.config.primary.service.VirtualEquipmentService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/crosssitevirtualequipment")
public class CrossSiteVirtualEquipmentController {
    /**
     * 生成的最大虚拟设备的数量
     */
    private static final int MAX_VIRTUAL_EQUIPMENT_COUNT = 100;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    StationService stationService;
    @Autowired
    I18n i18n;
    @Autowired
    CrossSiteVirtualEquipmentService crossSiteVirtualEquipmentService;
    @Autowired
    VirtualEquipmentService virtualEquipmentService;

    @PostMapping("/splitequipment")
    public ResponseEntity<ResponseResult> buildSplitEquipment(@RequestBody CrossSiteSplitEquipmentVO crossSiteSplitEquipmentVO) {
        TblEquipment equipment = equipmentService.findEquipmentById(crossSiteSplitEquipmentVO.getEquipmentId());
        TblStation station = stationService.findByStationId(equipment.getStationId());
        if (equipmentService.findEquipmentByStationNameAndEquipmentName(station.getStationName(), crossSiteSplitEquipmentVO.getVirtualEquipmentName()) != null) {
            return ResponseHelper.failed(RestfulCode.MonitorUnitHasEquipment.IntValue(), i18n.T("common.msg.equipmentExists"));
        }
        return ResponseHelper.successful(crossSiteVirtualEquipmentService.splitEquipment(crossSiteSplitEquipmentVO));
    }

    /**
     * 跨站虚拟设备列表
     * @return
     */
    @GetMapping
    public ResponseEntity<ResponseResult> virtualEquipment(){
        return ResponseHelper.successful(crossSiteVirtualEquipmentService.virtualEquipmentList());
    }

    /**
     * 导出跨站虚拟设备配置
     * @param equipmentIds 设备ids
     * @return
     */
    @PostMapping("/export/equipmentsetting")
    public ResponseEntity<ResponseResult> exportVirtualEquipmentSetting(@RequestBody List<Integer> equipmentIds){
        return ResponseHelper.successful(crossSiteVirtualEquipmentService.exportVirtualEquipmentSetting(equipmentIds));
    }

    @GetMapping("/virtualequipment/{equipmentId}")
    public ResponseEntity<ResponseResult> buildSplitEquipment(@PathVariable("equipmentId") Integer equipmentId) {
        return ResponseHelper.successful(crossSiteVirtualEquipmentService.findVirtualEquipmentDetail(equipmentId));
    }

    /**
     * 导入跨站虚拟设备配置
     * @param virtualEquipmentSettingVo
     * @return
     */
    @PostMapping("/import/equipmentsetting")
    public ResponseEntity<ResponseResult> importVirtualEquipmentSetting(@RequestBody VirtualEquipmentSettingVO virtualEquipmentSettingVo){
        if (CollUtil.size(virtualEquipmentSettingVo.getVirtualEquipmentExcelList()) > MAX_VIRTUAL_EQUIPMENT_COUNT) {
            return ResponseHelper.failed(i18n.T("virtualEquipment.quantityTooLarge", MAX_VIRTUAL_EQUIPMENT_COUNT));
        }
        return ResponseHelper.successful(crossSiteVirtualEquipmentService.importVirtualEquipmentSetting(virtualEquipmentSettingVo));
    }

    @DeleteMapping("/{equipmentId}")
    public ResponseEntity<ResponseResult> deleteCrossSiteVirtualEquipment(@PathVariable("equipmentId") Integer equipmentId){
        crossSiteVirtualEquipmentService.deleteVirtualEquipment(equipmentId);
         return ResponseHelper.successful();
    }
}
