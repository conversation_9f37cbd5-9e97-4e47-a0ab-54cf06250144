package org.siteweb.config.primary.utils;

import cn.hutool.core.io.FileUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.ArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Objects;

public class ZipUtil {

    private static final Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    /**
     * 将文件压缩成zip
     *
     * @param sourceFile    源文件或目录，如：archive.tar
     * @param targetFile    目标文件，如：archive.tar.zip
     * @param delSourceFile 是否删除源文件
     */
    public static void compress(String sourceFile, String targetFile, boolean delSourceFile) {
        try (OutputStream fos = new FileOutputStream(targetFile);
             OutputStream bos = new BufferedOutputStream(fos);
             ArchiveOutputStream aos = new ZipArchiveOutputStream(bos);) {
            Path dirPath = Paths.get(sourceFile);
            Files.walkFileTree(dirPath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                    ArchiveEntry entry = new ZipArchiveEntry(dir.toFile(), dirPath.relativize(dir).toString());
                    aos.putArchiveEntry(entry);
                    aos.closeArchiveEntry();
                    return super.preVisitDirectory(dir, attrs);
                }

                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    ArchiveEntry entry = new ZipArchiveEntry(
                            file.toFile(), dirPath.relativize(file).toString());
                    aos.putArchiveEntry(entry);
                    FileInputStream fis = new FileInputStream(file.toFile());
                    IOUtils.copy(fis, aos);
                    aos.closeArchiveEntry();
                    fis.close();
                    return super.visitFile(file, attrs);
                }
            });
            if (delSourceFile) {
                File file = new File(sourceFile);
                if (file.exists()) {
                    FileUtil.del(file);
                }
            }
        } catch (IOException e) {
            logger.error("压缩失败，原因: ", e);
        }
    }


    /**
     * 将zip文件解压到指定目录
     *
     * @param zipPath 源文件，如：archive.zip
     * @param descDir 解压目录
     */
    public static void uncompress(String zipPath, String descDir) {
        long d1 = System.currentTimeMillis();
        try (InputStream fis = Files.newInputStream(Paths.get(zipPath));
             InputStream bis = new BufferedInputStream(fis);
             ArchiveInputStream ais = new ZipArchiveInputStream(bis);
        ) {
            ArchiveEntry entry;
            while (Objects.nonNull(entry = ais.getNextEntry())) {
                if (!ais.canReadEntryData(entry)) {
                    continue;
                }
                String name = descDir + File.separator + entry.getName();
                File f = new File(name);
                if (entry.isDirectory()) {
                    if (!f.isDirectory() && !f.mkdirs()) {
                        f.mkdirs();
                    }
                } else {
                    File parent = f.getParentFile();
                    if (!parent.isDirectory() && !parent.mkdirs()) {
                        throw new IOException("failed to create directory " + parent);
                    }
                    try (OutputStream o = Files.newOutputStream(f.toPath())) {
                        IOUtils.copy(ais, o);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("解压失败，原因：", e);
        }
    }


    public static void toWebDownLoad(File file, String outPutFileName, HttpServletResponse response) {
        if (!file.exists()) {
            logger.error("文件{}不存在", file.getAbsolutePath());
        } else {
            try (InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()))) {
                byte[] buffer = new byte[fis.available()];
                fis.read(buffer);
                fis.close();
                // 清空response
                response.reset();

                OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType("application/octet-stream");

                // 如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String(outPutFileName.getBytes("GB2312"), "ISO8859-1"));
                toClient.write(buffer);
                toClient.flush();
                toClient.close();
            } catch (Exception e) {
                throw new RuntimeException("zip toWebDownLoad error from ZipsUtils", e);
            }
        }
    }

    /**
     * 将指定文件输出到web端
     *
     * @param path     文件路径
     * @param response response 流
     */
    public static void toWebDownLoad(String path, String outPutFileName, HttpServletResponse response) {
        File file = new File(path);
        toWebDownLoad(file, outPutFileName, response);
    }
}
