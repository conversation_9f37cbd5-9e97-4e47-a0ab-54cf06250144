package org.siteweb.config.primary.expressions.validate;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.esotericsoftware.minlog.Log;
import org.siteweb.config.primary.expressions.calculate.ExpressionCalculate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式验证类
 * <AUTHOR>
 * @date 2024/03/22
 */
@Component
public class ExpressionValidation {
    /**
     * @param expression
     * @return boolean
     */
    public boolean validateExpression(String expression) {
        return validateExpressionFormat(expression);
    }

    /**
     * 样例：([-1,5210001]:53#45 >= 7) AND [-1,52100234]$32
     * 1 准备
     * 2 扩展表达式验证
     * 3 逻辑表达式验证
     * 4 四则表达式验证
     * @param source 表达式字符串
     * @return boolean 是否符合表达式书写规范
     */
    public boolean validateExpressionFormat(String source) {
        if (source == null || source.isEmpty()) return true;

        // 1 准备
        // 1.1 判断'['和']'是否个数对称相等，如果不等，返回false
        if (countMatches(source, "\\[") != countMatches(source, "\\]")) return false;

        // 1.2 判断'('和')'是否个数对称相等，如果不等，返回false
        if (countMatches(source, "\\(") != countMatches(source, "\\)")) return false;

        // 1.3 判断是否有非允许范围内字符，如果有，返回false
        String tmpStr = source;

        // 1.3.1 去除所有数字
        tmpStr = tmpStr.replaceAll("[0-9]", "");

        // 1.3.2 去除所有的运算符
        tmpStr = tmpStr.replaceAll("AND|OR|NOT", "");
        //去除+-*/
        tmpStr = tmpStr.replaceAll("[+\\-*/]", "");
        //去除== >= <= > < 符号
        tmpStr = tmpStr.replaceAll("==|>=|<=|>|<", "");
        //去除扩展表达运算符
        tmpStr = tmpStr.replaceAll("[():?#$@%^;]", "");
        //去除所有空格和所有的信号符号 '['和']'和','
        tmpStr = tmpStr.replaceAll("[ ,\\[\\].]", "");
        //去除max min avg符号
        tmpStr = tmpStr.replaceAll("max|min|avg", "");

        // 1.3.3 如果仍然不为空，则说明有特殊字符，返回false 【把该去除的都去除了还有别的玩意儿】
        if (!tmpStr.isEmpty()) return false;

        // 2 扩展表达式验证
        ExpressionValidationResult expressionValidationResult = validateExpressionExtension(source);
        if (!expressionValidationResult.isValid()) return false;

        // 3 逻辑表达式验证
        // 3.1 降级逻辑表达式为四则运算
        tmpStr = expressionValidationResult.getResultString();
        tmpStr = tmpStr.replaceAll("AND|OR", " + ");
        tmpStr = tmpStr.replaceAll("NOT", " - ");
        tmpStr = tmpStr.replaceAll("==|>=|<=|>|<|:", "-");

        // 4 四则表达式验证
        // 4.1 判断是否只是信号ID的罗列，如果是则返回false
        // 如果两个信号间有操作符，则四则运算可以处理，这里只考虑没有任何操作符的情况
        String ls = tmpStr.replace(" ", "");
        ls = ls.replaceAll("\\[-?[0-9]+,[0-9]+]", "TMP");
        //存在两个TMP说明多个变量粘合在一起了
        if (ls.contains("TMPTMP")) return false;

        // 4.2 判断ID是否有效（为模板ID）
        if (!validateExpressionId(source)) return false;

        // 4.3 替换所有的信号ID，为数字1
        tmpStr = tmpStr.replaceAll("\\[-?[0-9]+,-?[0-9]+]", " 1");

        // 4.4 如果仍然存在 '['或']'，说明信号表达式自身格式有问题
        if (tmpStr.contains("[") || tmpStr.contains("]")) return false;

        // 4.5真正调用计算逻辑 计算一下
        try {
            Pattern functionPattern = Pattern.compile("(max|min|avg)\\(\\s*( -?\\d+)(\\s*,\\s*-?\\d+)*\\s*\\)");
            Matcher matcherFunc = functionPattern.matcher(tmpStr);
            tmpStr = matcherFunc.replaceAll("1");

            ExpressionCalculate.calculate(tmpStr);
        } catch (Exception e) {
            Log.error("表达式计算错误:{}", ExceptionUtil.stacktraceToString(e));
            return false;
        }

        // 4.6 因为四则运算校验的不完善，这里需要多运算符重复再做校验

        // 4.6.1 去除负数情况
        String os = Pattern.compile("-\\d+").matcher(tmpStr).replaceAll("1");

        // 4.6.2 去除空格
        os = os.replace(" ", "");

        // 4.6.3 从四个运算符任意取两个的排列为12种,增加4种重复的为16种，如下：
        if (os.contains("--") || os.contains("++") || os.contains("**") || os.contains("//") ||
                os.contains("-+") || os.contains("-*") || os.contains("-/") || os.contains("+-") ||
                os.contains("*-") || os.contains("/-") || os.contains("+*") || os.contains("+/") ||
                os.contains("*+") || os.contains("/+") || os.contains("*/") || os.contains("/*")) {
            return false;
        }
        return true;
    }

    private int countMatches(String source, String regex) {
        Matcher matcher = Pattern.compile(regex).matcher(source);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    public ExpressionValidationResult validateExpressionExtension(String source) {
        List<String> exs = new ArrayList<>();
        String str = null;
        source = source.replace(":", "-");

        // 2.1 判断是否扩展运算符为第一个（边界条件）
        String extensionOperatorRex = "[:?#$@%^;]";
        if (Pattern.matches(extensionOperatorRex, source.substring(0, 1))) return new ExpressionValidationResult(false, null);

        // 2.2 遍历判断是否有扩展运算符，如果没有, 返回true
        String[] ss = source.split("]");
        for (String s : ss) {
            if (s.isEmpty()) continue;

            // 2.2.1 将扩展运算符所在存到临时的集合中
            if (Pattern.matches(extensionOperatorRex, s.substring(0, 1)))
                exs.add(s);
            else {
                // 2.2.2 如果第一个字母不是就说明有多余空格
                if (Pattern.compile(extensionOperatorRex).matcher(s).find())
                    return new ExpressionValidationResult(false, null);
            }
        }
        // 2.3 扩展表达式计算式进行验证
        if (exs.isEmpty()) {
            str = source;
            return new ExpressionValidationResult(true, str);
        }

        for (String t : exs) {
            // 2.3.1 以扩展运算符表达式进行分解,
            List<String> ts = split(t.trim(), "[:?#$@%^;]\\d+");
            //// 2.3.2 遍历分解后的值字符串
            int count = ts.size() - 1;
            // 2.3.3 如果不包含扩展运算表达式最后一个忽略
            if (Pattern.compile(extensionOperatorRex).matcher(ts.get(ts.size()-1)).find())
                count = ts.size();

            for (int i = 0; i < count; i++) {
                // 2.3.4 如果字符串中仍有扩展运算符，说明不符合规范，返回失败
                if (Pattern.compile(extensionOperatorRex).matcher(ts.get(i)).find())
                    return new ExpressionValidationResult(false, null);

                // 2.3.5 如果扩展预算表达式之间不是紧密挨着，则返回失败
                if (!ts.get(i).isEmpty()) return new ExpressionValidationResult(false, null);
            }
        }

        // 2.4 替换掉所有的扩展运算表达式为string.Empty,设置返回字符串
        str = source.replaceAll("[:?#$@%^;]\\d+", "");

        // 2.5 返回成功
        return new ExpressionValidationResult(true, str);
    }

    /**
     * 校验表达式中的信号ID是否有效
     * @param source 表达式
     * @return 是否有效
     */
    private boolean validateExpressionId(String  source){
        Pattern pattern = Pattern.compile("\\[-?[0-9]+,[0-9]+]");
        Matcher matcher = pattern.matcher(source);

        // 4.2.1 如果没有信号ID，返回true
        if (!matcher.find()) return true;

        // 重置matcher的状态，以便重新开始遍历匹配项
        matcher.reset();

        // 4.2.2 遍历所有的匹配项
        while (matcher.find()) {
            String[] tokens = matcher.group().split(",");

            // ******* 是否能从ID字符串获取2个ID: 设备ID和信号ID，如果不能返回失败
            if (tokens.length != 2) return false;

            String equipId = tokens[0];
            String signalId = tokens[1];

            // ******* 如果这两个ID字符串为空，返回失败
            if (equipId == null || equipId.isEmpty() || signalId == null || signalId.isEmpty()) return false;

            // ******* 如果这两个ID字符串长度不足，返回失败
            if (equipId.length() < 2 || signalId.length() < 2) return false;
            //下面这一大段是查询数据库是否存在该信号，但是配置工具中已将其注释。
            //int eid = Integer.parseInt(equipId.trim().substring(1, equipId.length() - 1));
            //int sid = Integer.parseInt(signalId.trim().substring(0, signalId.length() - 1));

            // 4.2.3 如果数据库连接有问题，则返回成功
            // 此部分代码依赖于具体的数据库连接实现，因此在这里不进行转换

            // 4.2.4 如果是设备模板，则查询设备模板
            //if (eid == -1) {
            // 数据库查询逻辑，需要根据实际的数据库连接和查询API进行转换
            //}
            // 4.2.4 如果是设备实例，则查询设备实例
            // 数据库查询逻辑，需要根据实际的数据库连接和查询API进行转换
        }

        return true;
    }


    /**
     * c#中的Regex.Split(()方法的平替
     *
     * @param input 输入
     * @param regex 正则表达式
     * @return {@link List}<{@link String}>
     */
    private static List<String> split(String input, String regex) {
        List<String> resultList = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        int start = 0;
        while (matcher.find()) {
            resultList.add(input.substring(start, matcher.start()));
            start = matcher.end();
        }
        resultList.add(input.substring(start));

        return resultList;
    }
}
