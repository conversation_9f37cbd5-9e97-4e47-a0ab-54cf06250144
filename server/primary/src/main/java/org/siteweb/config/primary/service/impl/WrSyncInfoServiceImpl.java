package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.WrSyncInfo;
import org.siteweb.config.common.mapper.WrSyncInfoMapper;
import org.siteweb.config.primary.service.WrSyncInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/2/15
 */
@Service
public class WrSyncInfoServiceImpl implements WrSyncInfoService {

    @Autowired
    private WrSyncInfoMapper wrSyncInfoMapper;


    @Override
    public List<WrSyncInfo> findWrSyncInfoList() {
        return wrSyncInfoMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<WrSyncInfo> findWrSyncInfoList(Integer syncFlag) {
        return wrSyncInfoMapper.selectList(Wrappers.lambdaQuery(WrSyncInfo.class).eq(WrSyncInfo::getSyncFlag, syncFlag));
    }

    @Override
    public boolean UpdateById(WrSyncInfo wrSyncInfo) {
        return wrSyncInfoMapper.updateById(wrSyncInfo) > 0;
    }


}
