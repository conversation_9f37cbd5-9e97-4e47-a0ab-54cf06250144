package org.siteweb.config.primary.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.siteweb.config.common.entity.DiskFile;

import java.io.File;
import java.util.Collection;
import java.util.List;

public interface DiskFileService {

    DiskFile findById(Integer id);
    /**
     * 上传文件到指定文件夹
     *
     * @param filePath 目录
     * @param name     名称
     * @param bytes    文件内容
     * @return boolean 是否成功
     */
    DiskFile upload(String filePath, String name, byte[] bytes);

    /**
     * 按目录查找里面的所有文件
     *
     * @param filePath 目录
     * @return {@link List }<{@link DiskFile }>
     */
    List<DiskFile> findByFilePath(String filePath);

    DiskFile findByFilePathAndFileName(String filePath, String fileName);

    /**
     * 指定目录下的文件是否存在(针对磁盘)
     *
     * @param filePath 目录
     * @param name      文件名称
     * @return boolean
     */
    boolean diskExist(String filePath, String name);

    /**
     * 直接写入磁盘
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param bytes    文件内容(字节)
     */
    void writeDisk(String filePath, String fileName, byte[] bytes);

    boolean deleteFilesFromDiskAndDatabase(String filePath, List<String> fileNameList);

    Page<DiskFile> findPageByFilePath(Page<DiskFile> page, String filePath);

    File getPath(String filePath);

    List<DiskFile> findByFileIds(Collection<Integer> ids);

    File getFile(String filePath, String fileName);

    /**
     * 重命名
     * @param filePath 文件路径
     * @param oldName  旧名称
     * @param newName  新名称
     */
    void rename(String filePath, String oldName, String newName);
}
