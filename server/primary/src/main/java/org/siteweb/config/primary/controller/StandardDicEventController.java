package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblStandardDicEvent;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.EventBaseMapService;
import org.siteweb.config.primary.service.StandardDicEventService;
import org.siteweb.config.primary.service.StandardTypeService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/standarddicevent")
public class StandardDicEventController {
    @Autowired
    private StandardDicEventService standardDicEventService;
    @Autowired
    StandardTypeService standardTypeService;
    @Autowired
    EventBaseMapService eventBaseMapService;
    @Autowired
    I18n i18n;
    @GetMapping
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(standardDicEventService.findCurrentStandardDicEvent());
    }

    @PostMapping
    public ResponseEntity<ResponseResult> create(@RequestBody TblStandardDicEvent tblStandardDicEvent){
        //if (CharSequenceUtil.length(String.valueOf(tblStandardDicEvent.getStandardDicId())) != 6) {
        //    return ResponseHelper.failed(i18n.T("standardDic.eventId.length"));
        //}
        return ResponseHelper.successful(standardDicEventService.create(tblStandardDicEvent));
    }


    @PutMapping
    public ResponseEntity<ResponseResult> updateStandardDicSig(@RequestBody TblStandardDicEvent standardDicEvent){

        return ResponseHelper.successful(standardDicEventService.update(standardDicEvent));
    }

    @DeleteMapping(params = "standardDicId")
    public ResponseEntity<ResponseResult> deleteByStandardId(Integer standardDicId){
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        if (eventBaseMapService.countByStandardDicIdAndStandardType(standardDicId, currentStandardType.getValue()) > 0) {
            return ResponseHelper.failed(i18n.T("standardDic.mapping.existsError"));
        }
        return ResponseHelper.successful(standardDicEventService.deleteByStandardDicIdAndStandardType(standardDicId,currentStandardType.getValue()));
    }
}
