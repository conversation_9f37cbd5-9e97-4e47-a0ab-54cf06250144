package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import org.siteweb.config.common.entity.DoorCardBackup;
import org.siteweb.config.common.mapper.DoorCardBackupMapper;
import org.siteweb.config.primary.service.DoorCardBackupService;
import org.siteweb.config.primary.service.DoorCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DoorCardBackupServiceImpl implements DoorCardBackupService {
    @Autowired
    DoorCardBackupMapper doorCardBackupMapper;
    @Autowired
    DoorCardService doorCardService;


    @Override
    public void backupCard(Integer equipmentId, String equipmentName) {
        List<Integer> cardList = doorCardService.findCardIdByEquipmentId(equipmentId);
        List<DoorCardBackup> backupList = DoorCardBackup.backupCardEntity(cardList,equipmentId,equipmentName);
        if (CollUtil.isEmpty(backupList)) {
            return;
        }
        doorCardBackupMapper.insertBatchSomeColumn(backupList);
    }
}
