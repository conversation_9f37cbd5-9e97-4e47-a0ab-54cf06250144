package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EquipmentEventDTO;
import org.siteweb.config.common.dto.batchtool.EventRequestBySignalId;
import org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO;
import org.siteweb.config.common.dto.excel.EventExcel;
import org.siteweb.config.common.entity.TblEvent;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface EventService {
    void batchEvent(List<TblEvent> eventList);

    List<EventConfigItem> findEventItemByEquipmentTemplateId(Integer equipmentTemplateId);

    TblEvent findByEventId(Integer eventId);

    void create(TblEvent event);

    void updateEventName(String eventName, Integer eventId);

    /**
     * 生成通讯状态相关事件
     * @param equipmentTemplateId 设备模板
     */
    void createCommunicationStateEvent(Integer equipmentTemplateId);

    List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    void createEvent(TblEvent event);

    int updateEvent(TblEvent event);

    int deleteEvent(Integer equipmentTemplateId, Integer eventId);

    void updateWorkStationEventName(String prefixName, int equipmentTemplateId);

    void updateDBWorkStationEventName(String eventName, Integer equipmentTemplateId, int EventId);

    /**
     * 复制原始模板中的告警至模板模板中
     * @param originEquipmentTemplateId 原始设备模板id
     * @param destEquipmentTemplateId 目标模板
     */
    void copyEvent(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    /**
     * 更新自诊断事件
     * @param equipmentTemplateId
     */
    void updateSelfDiagnosisEvent(int equipmentTemplateId, int centerId);

    void createEventByEventItem(EventConfigItem eventConfigItem);

    void batchInsertLianTongEvents();

    void updateByEventItem(EventConfigItem eventConfigItem);

    void batchUpdateByEventItem(BatchEventConfigItem batchEventConfigItem);

    /**
     * 对比新旧模板中事件缺少的问题
     * @param oldEquipmentTemplateId old设备样板主键id
     * @param newEquipmentTemplateId new设备样板主键id
     * @return {@link List}<{@link TblEvent}>
     */
    List<TblEvent> diffEvent(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId);

    /**
     * 获取精简的的事件列表和条件
     *
     * @param equipmentTemplateId 设备模板ID
     * <AUTHOR> (2024/4/18)
     */
    List<SimplifyEventAndConditionDTO> findSimplifyEventAndCondition(Integer equipmentTemplateId);


    /**
     * 获取事件处理进度
     */
    Double getEventProgress(Integer equipmentTemplateId, Map<Integer, List<EventProgressDTO>> eventProgressMap);

    /**
     * 获取基类事件进度列表
     */
    Map<Integer, List<EventProgressDTO>> findEventProgressMap();

    /**
     * 获取基类标准化事件
     */
    BaseClassStatisticsDTO<EventBaseClassDTO> findEventBaseClassList(Integer equipmentBaseType);

    /**
     * 获取基类标准化事件明细
     */
    List<EventBaseClassDetailDTO> findEventBaseClassDetails(Integer equipmentBaseType, String eventName, String meanings);

    /**
     * 基类事件相关修改
     */
    void updateEventBaseTypeAndConfirm(BaseTypeCondIdDTO<EventBaseCondDTO> eventBaseTypeCondDTO);

    /**
     * 处理相似事件
     */
    void disposeSimilarEvent(SimilarDataDTO similarEventDTO);

    /**
     * 清除基类
     */
    void clearBaseTypeByEquipmentTemplate(Integer equipmentTemplateId);

    EventConfigItem findMaxEventByEquipmentTemplateId(Integer equipmentTemplateId);

    List<EquipmentEventDTO> findEventsByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds);

    List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(EventRequestBySignalId eventRequestBySignalId);

    Boolean batchDeleteEvent(int equipmentTemplateId, List<Integer> eventIds);

    EventConfigItem getEventInfo(Integer equipmentTemplateId, Integer eventId);

    EventConfigItem findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId);

    List<TblEvent> findByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     * 应用标准化
     */
    Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds);

    /**
     * 还原标准化 PCT_RestoreEventStandard
     */
    Long restoreStandard();

    /**
     * 获取标准化告警比较
     */
    List<StandardEventCompareDTO> getStandardCompareData();

    /**
     * 获取告警应用标准化检查
     */
    List<StandardApplyEventCheckDTO> getEventStandardApplyCheckData(Integer standardId);

    /**
     * 获取告警映射标准化检查
     */
    List<StandardMappingEventCheckDTO> getEventStandardMappingCheck(Integer standardId, Integer equipmentCategory);

    @Transactional(rollbackFor = Exception.class)
    boolean linkEvent(Integer equipmentTemplateId, Integer signalId);

    /**
     * 应用事件标准测点映射
     */
    void applyEventPointMapping(StandardPointMappingDTO mappingDTO);

    List<TblEvent> findByEquipmentCategory(Integer equipmentCategory);

    /**
     * 获取事件以及绑定测点
     */
    List<EventConfigPointDTO> findEventPoints(Integer equipmentTemplateId);

    /**
     * 解除测点绑定
     */
    void unbindEventPointMapping(StandardPointUnbindDTO standardPointUnbindDTO);
    /**
     * 批量更新设备类型
     */
    void updateStandardPointCategory(StandardPointCategoryDTO standardPointCategoryDTO);

    /**
     * 生成未映射点位
     */
    void generateUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO);

    /**
     * 解除生成的未映射点位
     */
    void unbindUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO);
    /**
     * 批量清除基类
     */
    void clearEventBaseType(List<EventBaseTypeConditionDTO> eventBaseTypeConditionDTOS);

    boolean fieldCopy(List<EventFieldCopyDTO> eventFieldCopyDTOList);

    List<EventExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId);
}

