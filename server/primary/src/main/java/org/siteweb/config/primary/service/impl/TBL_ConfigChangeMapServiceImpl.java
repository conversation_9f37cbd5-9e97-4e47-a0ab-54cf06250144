package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblConfigChangeMap;
import org.siteweb.config.common.mapper.TblConfigChangeMapMapper;
import org.siteweb.config.primary.service.TBL_ConfigChangeMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TBL_ConfigChangeMapServiceImpl implements TBL_ConfigChangeMapService {
    @Autowired
    TblConfigChangeMapMapper configChangeMapMapper;

    @Override
    public TblConfigChangeMap findByConfigIdAndEditType(Integer configId, Integer editType) {
        List<TblConfigChangeMap> tblConfigChangeMaps = configChangeMapMapper.selectList(Wrappers.lambdaQuery(TblConfigChangeMap.class)
                                                                                                .eq(TblConfigChangeMap::getMicroConfigId, configId)
                                                                                                .eq(TblConfigChangeMap::getMicroEditType, editType));
        if (CollUtil.isEmpty(tblConfigChangeMaps)) {
            return null;
        }
        //存储过程中为 limit 0,1;
        return tblConfigChangeMaps.get(0);
    }
}
