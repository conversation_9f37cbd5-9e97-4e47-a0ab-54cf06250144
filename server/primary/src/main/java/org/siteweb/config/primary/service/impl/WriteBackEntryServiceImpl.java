package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblWriteBackEntry;
import org.siteweb.config.common.mapper.TblWriteBackEntryMapper;
import org.siteweb.config.primary.service.WriteBackEntryService;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WriteBackEntryServiceImpl implements WriteBackEntryService {
    @Autowired
    private TblWriteBackEntryMapper writeBackEntryMapper;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;

    @Override
    public List<TblWriteBackEntry> findWriteBackEntrys(Integer entryCategory) {
        return writeBackEntryMapper.selectList(Wrappers.lambdaQuery(TblWriteBackEntry.class).eq(TblWriteBackEntry::getEntryCategory, entryCategory));
    }

    @Override
    public void updateWriteBackEntrys(List<TblWriteBackEntry> tblWriteBackEntrys) {
        mybatisBatchUtils.batchUpdateOrInsert(tblWriteBackEntrys, TblWriteBackEntryMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblWriteBackEntry.class).set(TblWriteBackEntry::getEnable, item.getEnable())
                        .eq(TblWriteBackEntry::getEntryId, item.getEntryId())
                ));
    }
}
