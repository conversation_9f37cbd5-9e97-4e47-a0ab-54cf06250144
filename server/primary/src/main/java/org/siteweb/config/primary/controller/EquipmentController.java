package org.siteweb.config.primary.controller;


import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonView;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.ResourceStructure;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.entity.TblEquipmentTemplate;
import org.siteweb.config.common.entity.TblHouse;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.view.EquipmentView;
import org.siteweb.config.common.vo.EquipmentReferenceVO;
import org.siteweb.config.primary.enums.EquipmentPropertyEnum;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.manager.StructureTreeManager;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.primary.utils.ExcelExportUtil;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> (2024-02-22)
 **/
@Slf4j
@RestController
@RequestMapping("/equipment")
public class EquipmentController {
    @Autowired
    private HouseService houseService;
    @Autowired
    MonitorUnitService monitorUnitService;
    @Autowired
    public ResourceStructureService resourceStructureService;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private EquipmentTemplateService equipmentTemplateService;
    @Autowired
    private I18n i18n;


    /**
     * 获取单个层级的配置信息（完整配置）
     *
     * @param equipmentId 层级ID
     * <AUTHOR> (2024/3/7)
     */
    @GetMapping(value = "/config/{eqId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfig(@PathVariable("eqId") Integer equipmentId) {
        return ResponseHelper.successful(equipmentService.findEquipmentById(equipmentId));
    }


    @PostMapping(value = "/config", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createEquipment(@RequestBody CreateEquipmentDto info) {
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(null, info.getMonitorUnitId(), info.getEquipmentName());
        if (repeatName) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.repeatName"));
        }

        TblEquipment eq = info.toEntrty();
        TblEquipmentTemplate template = equipmentTemplateService.findById(info.getEquipmentTemplateId());
        ResourceStructure structure = resourceStructureService.getStructureByID(info.getResourceStructureId());
        if (structure.getStructureTypeId() == StructureTypeEnum.STATION_HOUSE.getValue()) {
            TblHouse byHouseId = houseService.findStationIdAndHouseId(structure.getOriginParentId(), structure.getOriginId());
            eq.setHouseId(byHouseId.getHouseId());
            eq.setStationId(byHouseId.getStationId());
        } else {
            TblHouse house = houseService.findStationDefaultHouse(structure.getOriginId());
            eq.setHouseId(house.getHouseId());
            eq.setStationId(structure.getOriginId());
        }
        eq.setUpdateTime(LocalDateTime.now());
        eq.setProperty(template.getProperty());
        eq.setEquipmentType(template.getEquipmentType());
        eq.setEquipmentStyle(template.getEquipmentStyle());
        eq.setEquipmentCategory(template.getEquipmentCategory());
        eq.setUnit(template.getUnit());
        eq.setVendor(template.getVendor());
        equipmentService.createEquipment(eq);

        // 实例化模板
        if (info.getInstantiated()) {
            int equipmentTemplateId = equipmentService.equipmentInstance(eq.getEquipmentId());
            eq.setEquipmentTemplateId(equipmentTemplateId);
        }
        return ResponseHelper.successful(eq);
    }

    @PostMapping(value = "/v3/config", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createEquipmentV3(@RequestBody CreateEquipmentDto info) {
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(null, info.getMonitorUnitId(), info.getEquipmentName());
        if (repeatName) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.repeatName"));
        }

        if (info.getHouseId() == null) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.houseIdIsNull"));
        }

        if (info.getStationId() == null) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.stationIdIsNull"));
        }

        if (info.getMonitorUnitId() == null) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.monitorUnitIdIsNull"));
        }
        TblEquipment eq = info.toEntrty();
        TblEquipmentTemplate template = equipmentTemplateService.findById(info.getEquipmentTemplateId());
        String property = getProperty(eq.getMonitorUnitId(), template.getProperty());
        eq.setProperty(property);
        eq.setUpdateTime(LocalDateTime.now());
        eq.setEquipmentType(template.getEquipmentType());
        eq.setEquipmentStyle(template.getEquipmentStyle());
        eq.setEquipmentCategory(template.getEquipmentCategory());
        eq.setUnit(template.getUnit());
        eq.setVendor(template.getVendor());
        equipmentService.createEquipment(eq);

        // 实例化模板
        if (Boolean.TRUE.equals(info.getInstantiated())) {
            int equipmentTemplateId = equipmentService.equipmentInstance(eq.getEquipmentId());
            eq.setEquipmentTemplateId(equipmentTemplateId);
        }
        return ResponseHelper.successful(eq);
    }

    /**
     * 获取设备属性，如果是跨站监控单元，则添加跨站虚拟设备属性
     *
     * @param monitorUnitId 监控单元ID
     * @param equipmentTemplateProperty 设备模板属性
     * @return 处理后的设备属性
     */
    private String getProperty(Integer monitorUnitId, String equipmentTemplateProperty) {
        return Optional.of(monitorUnitId)
                       .filter(id -> monitorUnitService.isCrossSiteMonitoringUnit(id))
                       .map(id -> {
                           // 获取跨站虚拟设备属性ID
                           String crossProperty = String.valueOf(EquipmentPropertyEnum.CROSS_VIRTUAL_EQUIPMENT.getId());
                           // 如果原属性为空，则直接使用跨站属性；否则，将跨站属性追加到原属性后
                           return CharSequenceUtil.isBlank(equipmentTemplateProperty) ? crossProperty : equipmentTemplateProperty + "/" + crossProperty;
                       })
                       .orElse(equipmentTemplateProperty); // 如果不是跨站监控单元，则返回原属性
    }


    @PutMapping(value = "/config")
    public ResponseEntity<ResponseResult> updateEquipment(@RequestBody EquipmentDetailDTO equipmentDetailDTO) {
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(equipmentDetailDTO.getEquipmentId(), equipmentDetailDTO.getMonitorUnitId(), equipmentDetailDTO.getEquipmentName());
        if (repeatName) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.repeatName"));
        }
        return ResponseHelper.successful(equipmentService.updateEquipment(equipmentDetailDTO));
    }

    @PutMapping(value = "/v3/config")
    public ResponseEntity<ResponseResult> updateEquipmentV3(@RequestBody EquipmentDetailDTO equipmentDetailDTO) {
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(equipmentDetailDTO.getEquipmentId(), equipmentDetailDTO.getMonitorUnitId(), equipmentDetailDTO.getEquipmentName());
        if (repeatName) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.repeatName"));
        }
        return ResponseHelper.successful(equipmentService.updateEquipment(equipmentDetailDTO));
    }


    /**
     * 删除设备
     *
     * @param eqId 设备ID
     * <AUTHOR> (2024/4/9)
     */
    @DeleteMapping(value = "/config/{eqId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEquipment(@PathVariable("eqId") Integer eqId) {
        return ResponseHelper.successful(equipmentService.deleteEquipment(eqId));
    }


    /**
     * 获取设备列表
     *
     * <AUTHOR> (2024/3/7)
     */
    @JsonView(EquipmentView.Simple.class)
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> list() {
        return ResponseHelper.successful(equipmentService.allEquipment().stream().toList());
    }


    /**
     * 单独为跨站信号设备列表提供接口
     *
     * <AUTHOR> (2024/3/7)
     */
    @GetMapping(value = "/across/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> simple() {
        return ResponseHelper.successful(equipmentService.findStationEquipmentList());
    }


    @GetMapping(value = "/search/{rid}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStructureByID(@PathVariable("rid") Integer structureID) {
        return ResponseHelper.successful(equipmentService.findByStructureId(structureID));
    }

    @GetMapping(value = "/v3/search/{rid}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStructureByIDV3(@PathVariable("rid") Integer structureID) {
        return ResponseHelper.successful(equipmentService.findEquipmentVOsByStructureId(structureID));
    }

    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStructureByStationID(@RequestParam("stationId") Integer stationId, @RequestParam(value = "houseId", required = false) Integer houseId) {
        return ResponseHelper.successful(equipmentService.findByHouseIdAndStationId(stationId, houseId));
    }

    @GetMapping(value = "/reference", params = "equipmentTemplateId")
    public ResponseEntity<ResponseResult> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return ResponseHelper.successful(equipmentService.findReferenceVoByEquipmentTemplateId(equipmentTemplateId));
    }

    @GetMapping(value = "/reference/export", params = "equipmentTemplateId")
    public void exportByEquipmentTemplateId(HttpServletResponse response, Integer equipmentTemplateId) throws IOException {
        List<EquipmentReferenceVO> equipmentReferenceVOS = equipmentService.findReferenceVoByEquipmentTemplateId(equipmentTemplateId);
        ExcelExportUtil.exportExcel(response, equipmentReferenceVOS, EquipmentReferenceVO.class, "equipmentReference");
    }

    /**
     * 获取设备信息
     *
     * @param equipmentId 设备ID
     */
    @GetMapping("/{equipmentId}")
    public ResponseEntity<ResponseResult> findEquipmentDetail(@PathVariable Integer equipmentId) {
        return ResponseHelper.successful(equipmentService.findEquipmentDetail(equipmentId));
    }

    /**
     * 获取上一级设备列表(拿此设备同局站的设备)
     */
    @GetMapping("/simplifyequipments")
    public ResponseEntity<ResponseResult> findSimplifyEquipmentsByStationId(Integer equipmentId) {
        return ResponseHelper.successful(equipmentService.findSimplifyEquipmentsByStationId(equipmentId));
    }

    @GetMapping("/simplifyequipments/{stationId}/{monitorUnitId}")
    public ResponseEntity<ResponseResult> findSimplifyEquipmentsByStationIdAndMonitorUnitId(@PathVariable Integer stationId, @PathVariable Integer monitorUnitId) {
        return ResponseHelper.successful(equipmentService.findSimplifyEquipmentsByStationIdAndMonitorUnitId(stationId, monitorUnitId));
    }

    @GetMapping("/simplifysplicelist")
    public ResponseEntity<ResponseResult> findSimplifyEquipmentsByMonitorUnitId(Integer monitorUnitId, Boolean spliceFlag) {
        return ResponseHelper.successful(equipmentService.findSimplifyEquipmentsByMonitorUnitId(monitorUnitId, spliceFlag));
    }


    @PostMapping(value = "/switchtemplate/checkchange")
    public ResponseEntity<ResponseResult> switchTemplateCheck(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        if (equipmentTemplateService.switchTemplateSignalCheck(switchTemplateDTO)) {
            //所选设备模板中被删除的信号被别的设备所引用，不允许关联到该模板
            return ResponseHelper.failed(i18n.T("monitor.templateSignal.signalReference"));
        }
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = equipmentTemplateService.changeCompare(switchTemplateDTO.getOriginTemplateId(), switchTemplateDTO.getDestTemplateId(), switchTemplateDTO.getEquipmentIds());
        return ResponseHelper.successful(equipTemplateChangeDTOList);
    }

    @PostMapping(value = "/switchtemplate/checkchange/export")
    public void switchTemplateCheckExport(HttpServletResponse response, @RequestBody SwitchTemplateDTO switchTemplateDTO) throws IOException {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = equipmentTemplateService.changeCompare(switchTemplateDTO.getOriginTemplateId(), switchTemplateDTO.getDestTemplateId(), switchTemplateDTO.getEquipmentIds());
        ExcelExportUtil.exportExcel(response, equipTemplateChangeDTOList, EquipTemplateChangeDTO.class, "checkchange");
    }

    @PostMapping(value = "/switchtemplate")
    public ResponseEntity<ResponseResult> switchTemplate(@RequestBody SwitchTemplateDTO switchTemplateDTO) {
        //切换设备模板
        return ResponseHelper.successful(equipmentService.switchEquipmentTemplate(switchTemplateDTO));
    }

    @PostMapping(value = "/instance")
    public ResponseEntity<ResponseResult> copyAndSwitchTemplate(Integer equipmentId) {
        return ResponseHelper.successful(equipmentService.equipmentInstance(equipmentId));
    }

    // 设备映射，post请求，需要传入stationId，houseId，equipmentIds
    @PostMapping(value = "/mapping")
    public ResponseEntity<ResponseResult> mappingEquipment(@RequestBody MappingEquipmentDTO mappingEquipmentDTO) {
        return ResponseHelper.successful(equipmentService.mappingEquipment(mappingEquipmentDTO));
    }

    // 移除设备映射，post请求，需要传入stationId，houseId，equipmentIds
    @PostMapping(value = "/unmapping")
    public ResponseEntity<ResponseResult> unMappingEquipment(@RequestBody MappingEquipmentDTO mappingEquipmentDTO) {
        return ResponseHelper.successful(equipmentService.unMappingEquipment(mappingEquipmentDTO));
    }

    // 修改设备的局房ID
    @PutMapping(value = "/updateHouseId")
    public ResponseEntity<ResponseResult> updateHouseId(@RequestBody UpdateHouseIdDTO updateHouseIdDTO) {
        EquipmentDTO equipmentDTO = new EquipmentDTO();
        equipmentDTO.setHouseId(updateHouseIdDTO.getHouseId());
        equipmentDTO.setEquipmentId(updateHouseIdDTO.getEquipmentId());
        equipmentDTO.setStationId(updateHouseIdDTO.getStationId());
        return ResponseHelper.successful(equipmentService.updateHouseId(equipmentDTO));
    }

    @PutMapping(value = "/generate-uuid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> generateUuid() {
        equipmentService.generateUuid();
        return ResponseHelper.successful(true);
    }
}
