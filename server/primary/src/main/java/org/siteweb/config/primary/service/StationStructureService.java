package org.siteweb.config.primary.service;

import org.apache.ibatis.annotations.Update;
import org.siteweb.config.common.entity.TblStationStructure;

import java.util.List;

/**
 * <AUTHOR> (2024-03-11)
 **/
public interface StationStructureService {

    <PERSON><PERSON><PERSON> create(TblStationStructure stationStructure);

    // 根据structureGroupId获取局站结构
    List<TblStationStructure> getStructureByStructureGroupId(Integer structureGroupId);

    TblStationStructure findStructureById(Integer id);

    List<TblStationStructure> findALL();

    // 修改
    Boolean update(TblStationStructure stationStructure);


    Boolean delete(Integer id);

    // tree
    TblStationStructure tree();



//    List<TblStationStructure> findPathsByStationIds(List<Integer> stationIds, TblStationStructure root);
    TblStationStructure findCommonParentByStationIds(List<Integer> stationIds, TblStationStructure root);

    // select * from  TBL_StationStructure a  where  a.StructureGroupId = 1
    //and not exists (SELECT 'x' FROM resourcestructure b WHERE b.OriginId = a.StructureId AND b.structureTypeId = 103);
    List<TblStationStructure> findStationStructureByStructureGroupIdAndNotExistsResourceStructure();


    List<TblStationStructure> findChildrenByStructureId(Integer structureId);

    // 查询isungroup为true的父节点
    TblStationStructure findUngroupedStructure(Integer structureGroupId);

}
