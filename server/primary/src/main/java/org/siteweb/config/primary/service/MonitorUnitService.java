package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.MonitorUnitAndStationNameDTO;
import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.siteweb.config.common.entity.TslMonitorUnit;
import org.siteweb.config.common.vo.MonitorUnitDetails;
import org.siteweb.config.common.vo.MonitorUnitStationVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> (2024-03-11)
 **/
public interface MonitorUnitService {

    MonitorUnitDTO findById(Integer monitorUnitId);


    List<MonitorUnitDTO> findByStationId(Integer stationId);

    List<MonitorUnitDTO> findByWorkStationId(Integer workStationId);


    /**
     * 创建监控单元对象
     * 根据传入的monitorUnitCategory 自动创建 端口采集单元和自诊断设备
     *
     * @param monitorUnit         监控单元信息
     * @param resourceStructureId 所在层级ID，自诊断设备挂载层级ID
     * <AUTHOR> (2024/4/10)
     */
    boolean createMonitorUnit(MonitorUnitDTO monitorUnit, Integer resourceStructureId);

    /**
     * 创建监控单元对象V3
     * @param monitorUnit
     * @return
     */
    boolean createMonitorUnitV3(MonitorUnitDTO monitorUnit);

    boolean createSwatchMonitorUnit(MonitorUnitDTO monitorUnit);

    boolean updateMonitorUnit(MonitorUnitDTO monitorUnit);


    boolean deleteMonitorUnit(Integer monitorUnitId);

    /**
     * 删除监控单元及其下的设备
     * @param monitorUnitId
     * @return
     */
    boolean deleteMonitorUnitAndEqs(Integer monitorUnitId);



    void verification(MonitorUnitDTO monitorUnit);

    /**
     * 判断监控单元是否报告给所有数据服务器
     * @param monitorUnitId
     * @return
     */
    boolean isMuReportToAllDS(Integer monitorUnitId);

    void updateMonitorUnitConfigFileCode(Integer monitorUnitId, String configFileCode);

    void updateMonitorUnitConfigFileCodeOld(Integer monitorUnitId, String configFileCode, LocalDateTime time);

    List<MonitorUnitStationVO> findByRmuId(Integer rmuId);

    List<MonitorUnitDetails> findMonitorUnitDetails(Integer workstationId);


    List<Double> findStartCompareValue(Integer monitorUnitId);


    List<MonitorUnitDTO> findByIds(List<Integer> monitorUnitIds);

    List<MonitorUnitDTO> findAllMonitorUnit();

    List<MonitorUnitDTO> findActiveMonitorUnit();

    List<MonitorUnitDTO> findActiveMonitorUnit(List<Integer> monitorUnitIds);

    String findMonitorUnitLog(Integer monitorUnitId);
    /**
     * 获取监控单元和局站名称
     */
    List<MonitorUnitAndStationNameDTO> getMonitorUnitAndStationName();

    MonitorUnitDTO findMonitorUnit(String stationName, String monitorUnitName);

    void deleteByStationId(Integer stationId);

    List<TslMonitorUnit> findByCategory(Integer category);

    /**
     * 根据IP地址查询监控单元列表
     * @param ipAddress IP地址
     * @return 监控单元列表
     */
    List<TslMonitorUnit> findByIpAddress(String ipAddress);

    /**
     * 是否是跨站监控单元
     * @param monitorUnitId 监控单元id
     * @return boolean true是 false 否
     */
    boolean isCrossSiteMonitoringUnit(Integer monitorUnitId);

    Integer getStationIdByMonitorUnitId(Integer monitorUnitId);
}
