package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.siteweb.config.common.entity.TblWorkStation;
import org.siteweb.config.common.entity.TslMonitorUnit;

import java.util.List;
import java.util.concurrent.CompletableFuture;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/14
 */

public interface MonitorUnitXmlService {

    /**
     * 生成监控单元配置文件
     *
     * @param tslMonitorUnits 监控单元
     * @param path
     */
    void createMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnits, String path);

    /**
     * 生成监控单元配置文件
     *
     * @param workStations 工作站
     * @param path
     */
    void createMonitorUnitConfigXML(TblWorkStation workStations, String path);


    /**
     * 通过websocket返回进度信息
     *
     * @param msg
     * @param uniqueId
     * @return
     */
    void createMonitorUnitConfigXMLAsync(String msg, String uniqueId);

    /**
     * 异步下发监控单元配置文件
     *
     * @param monitorUnitIds 监控单元ID，多个用逗号隔开
     * @param port           端口
     * @param username       用户名
     * @param password       密码
     * @param protocol       协议 ftp / sftp
     * @param uniqueId       唯一标识
     * @param skipSoFileDownload 是否跳过so文件下发
     */
    void sendMonitorUnitConfigXMLAsync(String monitorUnitIds, Integer port, String username, String password, String protocol, String uniqueId, boolean skipSoFileDownload);

    /**
     * 异步下发监控单元配置文件
     *
     * @param tslMonitorUnit 监控单元
     * @param uniqueId       唯一标识
     * @param protocol       协议 ftp / sftp
     * @param ftpPort        端口
     * @param ftpUsername    用户名
     * @param ftpPassword    密码
     * @param skipSoFileDownload 是否跳过so文件下发
     * @return 是否成功
     */
    boolean sendMonitorUnitConfigXML(MonitorUnitDTO tslMonitorUnit, String uniqueId, String protocol, Integer ftpPort, String ftpUsername, String ftpPassword, boolean skipSoFileDownload);

    byte[] zipMonitorUnitConfigXML(List<TblWorkStation> workStations);


    // 生成设备类型XML
     boolean generateBytedanceEquipmentCategoryTypeXml();

     boolean generateBytedanceSignalIdMapIniFile();

     boolean generateBytedanceInitListFile(TslMonitorUnit tslMonitorUnit, String path);


}
