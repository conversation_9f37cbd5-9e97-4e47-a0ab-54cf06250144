package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.StationDTO;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.entity.TblStationStructure;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.primary.service.DataItemService;
import org.siteweb.config.primary.service.StationService;
import org.siteweb.config.primary.service.StationStructureService;
import org.siteweb.config.primary.service.UpdateStationGroupService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR> (2024-03-25)
 **/


@Slf4j
@RestController
@RequestMapping("/station")
public class StationController {


    @Autowired
    private StationService stationService;
    @Autowired
    private DataItemService dataItemService;

    @Autowired
    private UpdateStationGroupService updateStationGroupService;

    @Autowired
    private StationStructureService stationStructureService;


    /**
     * <AUTHOR> (2024/3/25)
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> fullTree() {
        return ResponseHelper.successful(stationService.findAllStation());
    }


    /**
     * 获取层级的局站信息
     *
     * @param stationId 局站ID，层级的OriginID
     * <AUTHOR> (2024/3/25)
     */
    @GetMapping("config/{stationId}")
    public ResponseEntity<ResponseResult> getStructureStation(@PathVariable("stationId") Integer stationId) {
        TblStation station = stationService.findByStationId(stationId);
        if (station != null) {
            return ResponseHelper.successful(StationDTO.from(station));
        }
        return ResponseHelper.failed("");
    }


    /**
     * 层级修改API
     *
     * @param station 层级
     * <AUTHOR> (2024/3/7)
     */
    @PutMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> putStructureStation(@RequestBody @Validated StationDTO station) {
        boolean result = stationService.updateDTO(station);
        if (result) {
            return ResponseHelper.successful(station);
        }
        return ResponseHelper.failed("");
    }


    // 层级创建API
    @PostMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createStructureStation(@Validated @RequestBody StationDTO station) {
        TblStationStructure stationStructure = stationStructureService.findStructureById(station.getStationStructureId());
        if (stationStructure == null) {
            return ResponseHelper.failed("层级不存在");
        }
        // 如果是根节点不能创建局站，parentId为0
        if (stationStructure.getParentStructureId() == 0) {
            return ResponseHelper.failed("根节点不能创建局站");
        }
        boolean result = stationService.createDTO(station);
        if (result) {
            return ResponseHelper.successful(station);
        }
        return ResponseHelper.failed("");
    }

    // 层级删除API
    @DeleteMapping(value = "/config/{stationId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteStructureStation(@PathVariable("stationId") Integer stationId) {
        if (stationId < 0) {
            return ResponseHelper.failed("管理局站不允许删除");
        }
        boolean result = stationService.deleteByID(stationId);
        if (result) {
            return ResponseHelper.successful(stationId);
        }
        return ResponseHelper.failed("");
    }

    // 局站模板接口
    @GetMapping("/template")
    public ResponseEntity<ResponseResult> getStationTemplate() {
        return ResponseHelper.successful(stationService.findStationTemplateList());
    }


    @GetMapping("/category")
    public ResponseEntity<ResponseResult> getStationCategory() {
        return ResponseHelper.successful(stationService.findCategoryList());
    }


    @GetMapping("/types")
    public ResponseEntity<ResponseResult> getStationTypes() {
        return ResponseHelper.successful(dataItemService.findTypes(DataEntryEnum.STATION_CATEGORY));
    }

    @GetMapping("/grades")
    public ResponseEntity<ResponseResult> getStationGrades() {
        return ResponseHelper.successful(dataItemService.findValues(DataEntryEnum.STATION_GRADE));
    }

    // 查询所有未绑定的局站列表
//    @GetMapping("/unbound")
//    public ResponseEntity<ResponseResult> getUnboundStation() {
//        return ResponseHelper.successful(stationService.findUnboundStation());
//    }

    // stationCategory
    @GetMapping("/categorizedataitemsbytype")
    public ResponseEntity<ResponseResult> getStationCategoryMap() {
        return ResponseHelper.successful(stationService.categorizeDataItemsByType());
    }

    @PutMapping("/rename")
    public ResponseEntity<ResponseResult> rename(@RequestBody @Validated StationDTO station) {
        if (station == null || station.getStationId() == null || station.getStationName() == null) {
            return ResponseHelper.failed("参数错误");
        }
        // 局站名称不能重复
        TblStation tblStationExist = stationService.findStationByName(station.getStationName());
        if (tblStationExist != null) {
            if (Objects.equals(tblStationExist.getStationId(), station.getStationId()) && station.getStationName().equals(tblStationExist.getStationName())) {
                return ResponseHelper.successful(tblStationExist);
            }
            return ResponseHelper.failed("局站名称重复");
        }

        TblStation tblStation = stationService.findByStationId(station.getStationId());
        if (tblStation == null) {
            return ResponseHelper.failed("局站不存在");
        }
        tblStation.setStationName(station.getStationName());

        boolean result = stationService.update(tblStation);
        if (result) {
            return ResponseHelper.successful(station);
        }
        return ResponseHelper.failed("");
    }



    // 根据updatetime返回最新一条station
    @GetMapping("/latest")
    public ResponseEntity<ResponseResult> getLatestStation() {
        return ResponseHelper.successful(stationService.findLatestStation());
    }

    // 更改局站的局站分组
    @PutMapping("/group")
    public ResponseEntity<ResponseResult> updateGroup(@RequestBody StationDTO station) {
        return ResponseHelper.successful(updateStationGroupService.updateGroup(station.getStationIds(), station.getStationStructureId()));
    }

}
