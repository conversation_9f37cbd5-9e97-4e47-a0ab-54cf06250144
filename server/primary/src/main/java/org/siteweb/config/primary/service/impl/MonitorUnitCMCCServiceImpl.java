package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.SecureUtil;
import org.siteweb.config.common.entity.TslMonitorUnitCMCC;
import org.siteweb.config.common.enums.MonitorUnitTypeEnum;
import org.siteweb.config.common.mapper.TblStationCMCCMapper;
import org.siteweb.config.common.mapper.TslMonitorUnitCMCCMapper;
import org.siteweb.config.common.vo.binterface.MonitorUnitCMCCVO;
import org.siteweb.config.primary.service.MonitorUnitCMCCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MonitorUnitCMCCServiceImpl implements MonitorUnitCMCCService {
    /**
     * 默认的用户名与密码
     */
    private static final String DEFAULT_USERNAME_PASSWORD = "admin";
    @Autowired
    TblStationCMCCMapper stationCMCCMapper;
    @Autowired
    TslMonitorUnitCMCCMapper tslMonitorUnitCMCCMapper;

    @Override
    public void init(){
        List<TslMonitorUnitCMCC> monitorUnitCMCCList = tslMonitorUnitCMCCMapper.findMonitorUnitsByCategory(MonitorUnitTypeEnum.BInterface.getValue());
        if (CollUtil.isEmpty(monitorUnitCMCCList)) {
            return;
        }
        for (TslMonitorUnitCMCC tslMonitorUnitCMCC : monitorUnitCMCCList) {
            tslMonitorUnitCMCC.setFsuID("");
            tslMonitorUnitCMCC.setSiteId(stationCMCCMapper.findSiteIdByStationId(tslMonitorUnitCMCC.getStationId()));
            tslMonitorUnitCMCC.setRoomID("");
            tslMonitorUnitCMCC.setUserName(DEFAULT_USERNAME_PASSWORD);
            tslMonitorUnitCMCC.setPassWord(SecureUtil.md5().digestHex(DEFAULT_USERNAME_PASSWORD).toUpperCase());
            tslMonitorUnitCMCC.setFtpUserName(DEFAULT_USERNAME_PASSWORD);
            tslMonitorUnitCMCC.setFtpPassWord(DEFAULT_USERNAME_PASSWORD);
        }
        tslMonitorUnitCMCCMapper.insertBatchSomeColumn(monitorUnitCMCCList);
    }

    @Override
    public List<MonitorUnitCMCCVO> findAll() {
        return tslMonitorUnitCMCCMapper.findAll();
    }

    @Override
    public int update(TslMonitorUnitCMCC tslMonitorUnitCMCC) {
        //密码需要使用MD5加密
        tslMonitorUnitCMCC.setPassWord(SecureUtil.md5().digestHex(tslMonitorUnitCMCC.getPassWord()).toUpperCase());
        return tslMonitorUnitCMCCMapper.updateEntity(tslMonitorUnitCMCC);
    }
}
