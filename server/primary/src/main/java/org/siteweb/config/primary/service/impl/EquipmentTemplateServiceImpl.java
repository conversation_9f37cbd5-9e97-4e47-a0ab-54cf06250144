package org.siteweb.config.primary.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.constants.SignalConstant;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.excel.ControlExcel;
import org.siteweb.config.common.dto.excel.EventExcel;
import org.siteweb.config.common.dto.excel.SheetDataWrapper;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.ObjectChangeTypeEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblEquipmentMapper;
import org.siteweb.config.common.mapper.TblEquipmentTemplateMapper;
import org.siteweb.config.common.mapper.TslSamplerMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.utils.StrSplitUtil;
import org.siteweb.config.common.vo.EquipmentTemplateBaseClassVO;
import org.siteweb.config.common.vo.EquipmentTemplateVO;
import org.siteweb.config.common.vo.StationEquipment;
import org.siteweb.config.primary.enums.EquipmentCategoryEnum;
import org.siteweb.config.primary.enums.InstanceTypeEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.common.dto.excel.SignalExcel;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.primary.utils.ExcelExportUtil;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class EquipmentTemplateServiceImpl implements EquipmentTemplateService {
    @Autowired
    TblEquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    TslSamplerMapper samplerMapper;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    SignalService signalService;
    @Autowired
    EventService eventService;
    @Autowired
    I18n i18n;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    ControlService controlService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    TslMonitorUnitSignalService tslMonitorUnitSignalService;
    @Autowired
    TSLMonitorUnitEventService tslMonitorUnitEventService;
    @Autowired
    private TblEquipmentMapper equipmentMapper;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    StationService stationService;

    public TblEquipmentTemplate findById(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.selectById(equipmentTemplateId);
    }

    @Override
    public List<TblEquipmentTemplate> findByIds(List<Integer> equipmentTemplateIds) {
        return equipmentTemplateMapper.selectBatchIds(equipmentTemplateIds);
    }

    @Override
    public List<TblEquipmentTemplate> findAll(){
        return equipmentTemplateMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public EquipmentTemplateVO findVoById(Integer equipmentTemplateId) {
        if (equipmentTemplateId == null) {
            return null;
        }

        EquipmentTemplateVO equipmentTemplateVO = equipmentTemplateMapper.findVoByEquipmentTemplateId(equipmentTemplateId);
        if (equipmentTemplateVO != null && equipmentTemplateVO.getProperty() != null) {
            equipmentTemplateVO.setPropertyList(StrSplitUtil.splitToIntList(equipmentTemplateVO.getProperty(), "/"));
        }

        return equipmentTemplateVO;
    }

    @Override
    public TblEquipmentTemplate findByName(String equipmentTemplateName) {
        return equipmentTemplateMapper.findByName(equipmentTemplateName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(TblEquipmentTemplate equipmentTemplate) {
        TblEquipmentTemplate oldEquipmentTemplate = findById(equipmentTemplate.getEquipmentTemplateId());
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldEquipmentTemplate, equipmentTemplate);
        if (Objects.nonNull(equipmentTemplate.getEquipmentBaseType()) &&
                !Objects.equals(equipmentTemplate.getEquipmentBaseType(), oldEquipmentTemplate.getEquipmentBaseType())) {
            // 修改到equipmentBaseType设备基类时，要把模板下的信号、事件、控制绑定的基类id和含义id清空
            clearBaseTypeByEquipmentTemplate(equipmentTemplate.getEquipmentTemplateId());
        }
        boolean result = equipmentTemplateMapper.updateById(equipmentTemplate) > 0;
        this.sendUpdateEvent(equipmentTemplate.getEquipmentTemplateId());
        return result;
    }

    public void sendUpdateEvent(Integer equipmentTemplateId) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (isTransactionActive) {
            // 在事务中执行的逻辑
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    TblEquipmentTemplate tblEquipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
                    changeEventService.sendUpdate(tblEquipmentTemplate);
                }
            });
        } else {
            // 没有事务时的逻辑
            TblEquipmentTemplate tblEquipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
            changeEventService.sendUpdate(tblEquipmentTemplate);
        }
    }

    @Override
    public void updateEquipmentTemplateCategoryByCategoryIdMap(int businessId, int categoryTypeId) {
        equipmentTemplateMapper.updateEquipmentCategoryByCategoryIdMap(businessId, categoryTypeId);
    }

    @Override
    public void updateEquipmentBaseTypeToNull() {
        equipmentTemplateMapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                .set(TblEquipmentTemplate::getEquipmentBaseType, null));
    }

    @Override
    public List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(int equipmentTemplateIdDiv) {
        return equipmentTemplateMapper.findEquipmentTemplateIdByEquipmentTemplateIdDiv(equipmentTemplateIdDiv);
    }

    @Override
    public void batchInsertLianTongEquipmentTemplate() {
        equipmentTemplateMapper.batchInsertLianTongEquipmentTemplate();
    }

    @Override
    public boolean switchTemplateSignalCheck(SwitchTemplateDTO switchTemplateDTO) {
        //新模板中与当前模板比较、被删掉的信号的ID
        List<TblSignal> diffSignals = signalService.diffSignal(switchTemplateDTO.getOriginTemplateId(), switchTemplateDTO.getDestTemplateId());
        Set<Integer> channelNoSet = diffSignals.stream().map(TblSignal::getChannelNo).collect(Collectors.toSet());
        Set<Integer> signalIdSet = diffSignals.stream().map(TblSignal::getSignalId).collect(Collectors.toSet());
        //根据不同的实例化类型判断是否有删除信号被引用
        for (Integer equipmentId : switchTemplateDTO.getEquipmentIds()) {
            if (checkEquipmentSignalReference(equipmentId, channelNoSet, signalIdSet)) {
                return true;
            }
            if (checkEquipmentEventReference(equipmentId, signalIdSet)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<EquipTemplateChangeDTO> changeCompare(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId, List<Integer> equipmentIds) {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = new ArrayList<>();
        List<EquipTemplateChangeDTO> compareDeletedSectionList = compareDeletedSection(oldEquipmentTemplateId, newEquipmentTemplateId);
        List<EquipTemplateChangeDTO> compareAddSectionList = compareAddSection(oldEquipmentTemplateId, newEquipmentTemplateId);
        List<EquipTemplateChangeDTO> compareUpdateSectionList = compareUpdateSection(oldEquipmentTemplateId, newEquipmentTemplateId);
        equipTemplateChangeDTOList.addAll(compareDeletedSectionList);
        equipTemplateChangeDTOList.addAll(compareAddSectionList);
        equipTemplateChangeDTOList.addAll(compareUpdateSectionList);
        List<EquipTemplateChangeDTO> result = new ArrayList<>(equipmentIds.size() * equipTemplateChangeDTOList.size() + equipmentIds.size());
        List<StationEquipment> stationEquipmentList = equipmentMapper.findStationEquipmentListByEquipmentIds(equipmentIds);
        Map<Integer, StationEquipment> equipmentStationMap = stationEquipmentList.stream().collect(Collectors.toMap(StationEquipment::getEquipmentId, Function.identity()));
        for (Integer equipmentId : equipmentIds) {
            List<EquipTemplateChangeDTO> copyList = BeanUtil.copyToList(equipTemplateChangeDTOList, EquipTemplateChangeDTO.class);
            StationEquipment stationEquipment = equipmentStationMap.get(equipmentId);
            copyList.forEach(e -> {
                e.setEquipmentName(stationEquipment.getEquipmentName());
                e.setStationName(stationEquipment.getStationName());
            });
            result.addAll(copyList);
        }
        List<EquipTemplateChangeDTO> eventExpression = checkEventExpression(oldEquipmentTemplateId, newEquipmentTemplateId, equipmentIds, equipmentStationMap);
        result.addAll(eventExpression);
        return result;
    }

    @Override
    public List<String> findDLlPathByEquipmentTemplateIds(List<Integer> equipmentTemplateIdList) {
        if (CollUtil.isEmpty(equipmentTemplateIdList)) {
            return Collections.emptyList();
        }
        List<TslSampler> samplerList = samplerMapper.findDllPathByEquipmentTemplateIds(equipmentTemplateIdList);
        return samplerList.stream().map(TslSampler::getDllPath).toList();
    }

    /**
     * 检查事件表达式中引用的信号是否被删除
     *
     * @param oldEquipmentTemplateId 旧模板id
     * @param newEquipmentTemplateId 新模板id
     * @param equipmentIds           设备IDs
     * @param equipmentStationMap
     * @return {@link List}<{@link EquipTemplateChangeDTO}>
     */
    private List<EquipTemplateChangeDTO> checkEventExpression(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId, List<Integer> equipmentIds, Map<Integer, StationEquipment> equipmentStationMap) {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = new ArrayList<>();
        //检查设备的告警过滤表达式是否引用了被删除的信号
        List<TblSignal> tblSignals = signalService.diffSignal(oldEquipmentTemplateId, newEquipmentTemplateId);
        Set<Integer> signalIdSet = tblSignals.stream().map(TblSignal::getSignalId).collect(Collectors.toSet());

        List<TblEquipment> equipmentList = equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                                                                              .select(TblEquipment::getEquipmentId,TblEquipment::getEventExpression)
                                                                              .in(TblEquipment::getEquipmentId,equipmentIds));
        for (TblEquipment tblEquipment : equipmentList) {
            if (checkValidExpress(signalIdSet, tblEquipment.getEventExpression())) {
                StationEquipment stationEquipment = equipmentStationMap.get(tblEquipment.getEquipmentId());
                EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                     .equipmentName(stationEquipment.getEquipmentName())
                                                                     .stationName(stationEquipment.getStationName())
                                                                     .objectId(stationEquipment.getEquipmentId())
                                                                     .objectName(stationEquipment.getEquipmentName())
                                                                     .objectType(i18n.T("monitor.equipment"))
                                                                     .description(i18n.T("monitor.templateEquipment.resetting"))
                                                                     .objectChangeType(ObjectChangeTypeEnum.TO_DELETE.getValue())
                                                                     .build();
                equipTemplateChangeDTOList.add(build);
            }
        }
        return equipTemplateChangeDTOList;
    }

    private List<EquipTemplateChangeDTO> compareUpdateSection(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId) {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = new ArrayList<>();
        //新模板中与当前模板比较、信号ID相同的虚拟信号
        List<TblSignal> virtualSignals = signalService.findSameVirtualSignals(oldEquipmentTemplateId,newEquipmentTemplateId);
        virtualSignals.forEach(signal -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(signal.getSignalId())
                                                                 .objectName(signal.getSignalName())
                                                                 .objectType(i18n.T("monitor.signal"))
                                                                 .description(i18n.T("monitor.templateSignal.originalSetting", signal.getSignalId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_UPDATE.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        return equipTemplateChangeDTOList;
    }

    /**
     * 比较两个模板之间的新增部分【信号、事件、控制】
     *
     * @param oldEquipmentTemplateId 旧模板id
     * @param newEquipmentTemplateId 新模板id
     * @return {@link List}<{@link EquipTemplateChangeDTO}>
     */
    private List<EquipTemplateChangeDTO> compareAddSection(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId) {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = new ArrayList<>();
        List<TblSignal> tblSignals = signalService.diffSignal(newEquipmentTemplateId, oldEquipmentTemplateId);
        tblSignals.forEach(signal -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(signal.getSignalId())
                                                                 .objectName(signal.getSignalName())
                                                                 .objectType(i18n.T("monitor.signal"))
                                                                 .build();
            if (!Objects.equals(signal.getChannelNo(), SignalConstant.VIRTUAL_SIGNAL_CHANNEL_NO)) {
                build.setObjectChangeType(ObjectChangeTypeEnum.TO_ADD.getValue());
                build.setDescription(i18n.T("monitor.templateSignal.add", signal.getSignalId()));
            } else if (CharSequenceUtil.isNotBlank(signal.getExpression())) {
                build.setObjectChangeType(ObjectChangeTypeEnum.TO_CONFIG.getValue());
                build.setDescription(i18n.T("monitor.templateSignal.settingInstance", signal.getSignalId()));
            } else {
                build.setObjectChangeType(ObjectChangeTypeEnum.TO_ADD.getValue());
                build.setDescription(i18n.T("monitor.templateSignal.checkInstance"));
            }
            equipTemplateChangeDTOList.add(build);
        });
        List<TblEvent> tblEvents = eventService.diffEvent(newEquipmentTemplateId, oldEquipmentTemplateId);
        tblEvents.forEach(event -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(event.getEventId())
                                                                 .objectName(event.getEventName())
                                                                 .objectType(i18n.T("monitor.event"))
                                                                 .description(i18n.T("monitor.templateEvent.add", event.getEventId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_ADD.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        //新模板中与当前模板比较、被删掉的控制列表
        List<TblControl> tblControls =  controlService.diffControl(newEquipmentTemplateId, oldEquipmentTemplateId);
        tblControls.forEach(control -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(control.getControlId())
                                                                 .objectName(control.getControlName())
                                                                 .objectType(i18n.T("monitor.control"))
                                                                 .description(i18n.T("monitor.templateControl.add", control.getControlId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_ADD.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        return equipTemplateChangeDTOList;
    }

    /**
     * 比较两个模板之间的删除部分【信号、事件、控制】
     *
     * @param oldEquipmentTemplateId 旧模板id
     * @param newEquipmentTemplateId 新模板id
     * @return {@link List}<{@link EquipTemplateChangeDTO}>
     */
    private List<EquipTemplateChangeDTO> compareDeletedSection(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId) {
        List<EquipTemplateChangeDTO> equipTemplateChangeDTOList = new ArrayList<>();
        //新模板中与当前模板比较、被删掉的信号列表
        List<TblSignal> tblSignals = signalService.diffSignal(oldEquipmentTemplateId, newEquipmentTemplateId);
        tblSignals.forEach(signal -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(signal.getSignalId())
                                                                 .objectName(signal.getSignalName())
                                                                 .objectType(i18n.T("monitor.signal"))
                                                                 .description(i18n.T("monitor.templateSignal.delete", signal.getSignalId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_DELETE.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        //新模板中与当前模板比较、被删掉的事件列表
        List<TblEvent> tblEvents =  eventService.diffEvent(oldEquipmentTemplateId, newEquipmentTemplateId);
        tblEvents.forEach(event -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(event.getEventId())
                                                                 .objectName(event.getEventName())
                                                                 .objectType(i18n.T("monitor.event"))
                                                                 .description(i18n.T("monitor.templateEvent.delete", event.getEventId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_DELETE.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        //新模板中与当前模板比较、被删掉的控制列表
        List<TblControl> tblControls =  controlService.diffControl(oldEquipmentTemplateId, newEquipmentTemplateId);
        tblControls.forEach(control -> {
            EquipTemplateChangeDTO build = EquipTemplateChangeDTO.builder()
                                                                 .objectId(control.getControlId())
                                                                 .objectName(control.getControlName())
                                                                 .objectType(i18n.T("monitor.control"))
                                                                 .description(i18n.T("monitor.templateControl.delete", control.getControlId()))
                                                                 .objectChangeType(ObjectChangeTypeEnum.TO_DELETE.getValue())
                                                                 .build();
            equipTemplateChangeDTOList.add(build);
        });
        return equipTemplateChangeDTOList;
    }

    /**
     * 检查设备中的告警是否被别的设备引用
     *
     * @param equipmentId 设备主键id
     * @return boolean
     */
    private boolean checkEquipmentEventReference(Integer equipmentId, Set<Integer> signalIdSet) {
        TblEquipment equipment = equipmentMapper.selectById(equipmentId);
        List<TslMonitorUnitEvent> monitorUnitEventList = tslMonitorUnitEventService.findByMonitorUnitIdExcludingEquipmentId(equipment.getMonitorUnitId(),equipmentId);
        for (TslMonitorUnitEvent tslMonitorUnitEvent : monitorUnitEventList) {
            if (CharSequenceUtil.isNotBlank(tslMonitorUnitEvent.getStartExpression()) && checkValidExpress(signalIdSet,tslMonitorUnitEvent.getStartExpression())) {
                return true;
            }
            if (CharSequenceUtil.isNotBlank(tslMonitorUnitEvent.getSuppressExpression()) && checkValidExpress(signalIdSet,tslMonitorUnitEvent.getSuppressExpression())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查设备中的信号是否被别的设备引用
     *
     * @param equipmentId  设备主键id
     * @param channelNoSet 通道未设置
     * @param signalIdSet  信号主键id设置
     * @return boolean
     */
    private boolean checkEquipmentSignalReference(Integer equipmentId, Set<Integer> channelNoSet, Set<Integer> signalIdSet) {
        TblEquipment equipment = equipmentMapper.selectById(equipmentId);
        //获取设备的跨站表达式
        List<TslMonitorUnitSignal> tslMonitorUnitSignalList = tslMonitorUnitSignalService.findByMonitorUnitIdExcludingEquipmentId(equipment.getMonitorUnitId(), equipmentId);
        for (TslMonitorUnitSignal tslMonitorUnitSignal : tslMonitorUnitSignalList) {
            //检查采集单元引用
            if (checkSamplerUnitReference(channelNoSet, tslMonitorUnitSignal, equipment)){
                return true;
            }
            //检查表达式引用
            if (checkExpressionReference(signalIdSet, tslMonitorUnitSignal)) {
                return true;
            }
        }
        return false;
    }

    private boolean checkExpressionReference(Set<Integer> signalIdSet, TslMonitorUnitSignal tslMonitorUnitSignal) {
        //与表达式无关
        if (Objects.equals(tslMonitorUnitSignal.getInstanceType(), InstanceTypeEnum.REFERENCED_SAMPLER_UNIT_CONFIG.getValue())) {
            return false;
        }
        //表达式为空
        if (CharSequenceUtil.isBlank(tslMonitorUnitSignal.getExpression())) {
            return false;
        }
        return checkValidExpress(signalIdSet, tslMonitorUnitSignal.getExpression());
    }

    private boolean checkSamplerUnitReference(Set<Integer> channelNoSet, TslMonitorUnitSignal tslMonitorUnitSignal, TblEquipment equipment) {
        //与采集单元无关
        if (tslMonitorUnitSignal.getInstanceType() == InstanceTypeEnum.REFERENCED_SIGNAL_EXPRESSION_CONFIG.getValue()) {
            return false;
        }
        //采集单元id不一样
        if (!Objects.equals(tslMonitorUnitSignal.getReferenceSamplerUnitId(), equipment.getSamplerUnitId())) {
            return false;
        }
        return !Objects.equals(tslMonitorUnitSignal.getReferenceChannelNo(), SignalConstant.VIRTUAL_SIGNAL_CHANNEL_NO) && channelNoSet.contains(tslMonitorUnitSignal.getReferenceChannelNo());
    }

    private boolean checkValidExpress(Set<Integer> signalIdList, String expression) {
        if (CharSequenceUtil.isBlank(expression)) {
            return false;
        }
        String pattern = ",[\\d]+]";
        Pattern compiledPattern = Pattern.compile(pattern);
        Matcher matcher = compiledPattern.matcher(expression);
        while (matcher.find()) {
            String group = matcher.group();
            int signalId = Integer.parseInt(group.substring(1, group.length() - 1));
            if (signalIdList.contains(signalId)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean doesTemplateNameExist(String templateName) {
        TblEquipmentTemplate tblEquipmentTemplate = findByName(templateName);
        if (Objects.nonNull(tblEquipmentTemplate)) {
            throw new BusinessException(i18n.T("monitor.equipmentTemplate.name.exist"));
        }
        return false;
    }

    @Override
    public List<TblEquipmentTemplate> findByMonitorUnitId(Integer monitorUnitId) {
        List<TblEquipment> eqs = equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                                                                    .select(TblEquipment::getEquipmentTemplateId)
                                                                    .eq(TblEquipment::getMonitorUnitId, monitorUnitId));
        if (CollUtil.isEmpty(eqs)) {
            return Collections.emptyList();
        }
        return findByIds(eqs.stream().map(TblEquipment::getEquipmentTemplateId).collect(Collectors.toList()));
    }

    @Override
    public TblEquipmentTemplate findTopParentTemplate(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("equipmentTemplate info does not exist");
        }
        return equipmentTemplateMapper.selectOne(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                                                         .eq(TblEquipmentTemplate::getProtocolCode, equipmentTemplate.getProtocolCode())
                                                         .eq(TblEquipmentTemplate::getParentTemplateId, 0));
    }

    @Override
    public List<TblEquipmentTemplate> findByProtocolCode(String protocolCode) {
        return equipmentTemplateMapper.selectList(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                                                          .eq(TblEquipmentTemplate::getProtocolCode, protocolCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateChildrenEquipmentCategory(Integer equipmentTemplateId, Integer equipmentCategory) {
        if (ObjectUtil.hasNull(equipmentTemplateId, equipmentCategory)) {
            throw new InvalidParameterException("equipmentTemplateId, equipmentCategory cannot be empty");
        }
        // 拿到所有子equipmentTemplateId
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("equipmentTemplate info does not exist");
        }
        List<TblEquipmentTemplate> equipmentTemplateList = findByProtocolCode(equipmentTemplate.getProtocolCode());
        List<Integer> equipmentTemplateIds = equipmentTemplateList.stream()
                                                                  .map(TblEquipmentTemplate::getEquipmentTemplateId)
                                                                  .toList();
        int equipmentTemplateCount = equipmentTemplateMapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                .set(TblEquipmentTemplate::getEquipmentCategory, equipmentCategory)
                .in(TblEquipmentTemplate::getEquipmentTemplateId, equipmentTemplateIds));
        int equipmentCount = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getEquipmentCategory, equipmentCategory)
                .in(TblEquipment::getEquipmentTemplateId, equipmentTemplateIds));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 设备变更通知
                List<TblEquipment> equipments = equipmentMapper.selectBatchIds(CollUtil.newHashSet(equipmentTemplateIds));
                changeEventService.sendBatchUpdate(equipments);
                List<TblEquipmentTemplate> eqList = equipmentTemplateMapper.selectBatchIds(equipmentTemplateList.stream().map(TblEquipmentTemplate::getEquipmentTemplateId).toList());
                // 设备模板变更通知
                changeEventService.sendBatchUpdate(eqList);
            }
        });
        return equipmentTemplateCount + equipmentCount;
    }

    @Override
    public int updateEquipmentCategory(Integer equipmentTemplateId, Integer equipmentCategory) {
        if (ObjectUtil.hasNull(equipmentTemplateId, equipmentCategory)) {
            throw new InvalidParameterException("equipmentTemplateId, equipmentCategory cannot be empty");
        }
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("equipmentTemplate info does not exist");
        }
        int equipmentTemplateCount = equipmentTemplateMapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                .set(TblEquipmentTemplate::getEquipmentCategory, equipmentCategory)
                .eq(TblEquipmentTemplate::getEquipmentTemplateId, equipmentTemplateId));
        equipmentTemplate.setEquipmentCategory(equipmentCategory);
        changeEventService.sendUpdate(equipmentTemplate);
       return equipmentTemplateCount;

    }

    @Override
    public List<EquipmentTemplateBaseClassVO> findBaseClassAll() {
        List<EquipmentTemplateBaseClassVO> result = equipmentTemplateMapper.findBaseClassAll();
        if (CollUtil.isEmpty(result)) {
            return List.of();
        }
        // 使用 CompletableFuture 并发执行查询，提升性能
        CompletableFuture<Map<Integer, List<SignalProgressDTO>>> signalFuture =
                CompletableFuture.supplyAsync(signalService::findSignalProgressMap)
                        .handle((res, ex) -> {
                            if (ex != null) throw new BusinessException("findSignalProgressMap() 查询失败", ex);
                            return res;
                        });

        CompletableFuture<Map<Integer, List<EventProgressDTO>>> eventFuture =
                CompletableFuture.supplyAsync(eventService::findEventProgressMap)
                        .handle((res, ex) -> {
                            if (ex != null) throw new BusinessException("findEventProgressMap() 查询失败", ex);
                            return res;
                        });

        CompletableFuture<Map<Integer, List<ControlProgressDTO>>> controlFuture =
                CompletableFuture.supplyAsync(controlService::getControlProgressMap)
                        .handle((res, ex) -> {
                            if (ex != null) throw new BusinessException("getControlProgressMap() 查询失败", ex);
                            return res;
                        });
        // 等待所有任务完成
        return CompletableFuture.allOf(signalFuture, eventFuture, controlFuture).thenApply(v -> {
            // 获取执行结果
            Map<Integer, List<SignalProgressDTO>> signalProgressMap = signalFuture.join();
            Map<Integer, List<EventProgressDTO>> eventProgressMap = eventFuture.join();
            Map<Integer, List<ControlProgressDTO>> controlProgressMap = controlFuture.join();
            // 并行处理数据
            result.parallelStream().forEach(vo -> {
                vo.setSignalProgress(signalService.getSignalProgress(vo.getEquipmentTemplateId(), signalProgressMap));
                vo.setEventProgress(eventService.getEventProgress(vo.getEquipmentTemplateId(), eventProgressMap));
                vo.setControlProgress(controlService.getControlProgress(vo.getEquipmentTemplateId(), controlProgressMap));
            });
            return result;
        }).join();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int clearEquipmentBaseType(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            log.warn("清空基类设备类型模板id:{},设备模板不存在，", equipmentTemplateId);
            return 0;
        }
        // 修改到equipmentBaseType设备基类时，要把模板下的信号、事件、控制绑定的基类id和含义id清空
        clearBaseTypeByEquipmentTemplate(equipmentTemplate.getEquipmentTemplateId());
        int updateCount = equipmentTemplateMapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                .set(TblEquipmentTemplate::getEquipmentBaseType, null)
                .eq(TblEquipmentTemplate::getEquipmentTemplateId, equipmentTemplateId)
        );
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(equipmentTemplate.getEquipmentTemplateId()), OperationObjectTypeEnum.EQUIPMENT_TEMPLATE, i18n.T("monitor.equipmentTemplate.name"), i18n.T("update"), equipmentTemplate.getEquipmentTemplateName(), "");
        this.sendUpdateEvent(equipmentTemplate.getEquipmentTemplateId());
        return updateCount;
    }

    /**
     * 把模板下的信号、事件、控制绑定的基类id和含义id清空
     */
    private void clearBaseTypeByEquipmentTemplate(Integer equipmentTemplateId) {
        signalService.clearBaseTypeByEquipmentTemplate(equipmentTemplateId);
        eventService.clearBaseTypeByEquipmentTemplate(equipmentTemplateId);
        controlService.clearBaseTypeByEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoSetEquipmentBaseType() {
        List<AutoEquipmentBaseTypeDTO> autoSetEquipmentBaseTypeList = equipmentTemplateMapper.getAutoSetEquipmentBaseTypeList();
        if (CollUtil.isEmpty(autoSetEquipmentBaseTypeList)) {
            return 0;
        }
        // 修改到equipmentBaseType设备基类时，要把模板下的信号、事件、控制绑定的基类id和含义id清空
        for (AutoEquipmentBaseTypeDTO autoEquipmentBaseTypeDTO : autoSetEquipmentBaseTypeList) {
            clearBaseTypeByEquipmentTemplate(autoEquipmentBaseTypeDTO.getEquipmentTemplateId());
        }
        int updateCount = mybatisBatchUtils.batchUpdateOrInsert(autoSetEquipmentBaseTypeList, TblEquipmentTemplateMapper.class, (item, mapper) -> mapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                .set(TblEquipmentTemplate::getEquipmentBaseType, item.getBaseEquipmentID())
                .eq(TblEquipmentTemplate::getEquipmentTemplateId, item.getEquipmentTemplateId())));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 为了通知缓存，把模板数据查出来
                List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.selectBatchIds(autoSetEquipmentBaseTypeList.stream().map(AutoEquipmentBaseTypeDTO::getEquipmentTemplateId).toList());
                equipmentTemplateList.forEach(e -> operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(e.getEquipmentTemplateId()), OperationObjectTypeEnum.EQUIPMENT_TEMPLATE, i18n.T("monitor.equipmentBaseType"), i18n.T("update"), e.getEquipmentTemplateName(), ""));
                // 设备模板变更通知
                changeEventService.sendBatchUpdate(equipmentTemplateList);
            }
        });
        return updateCount;
    }

    @Override
    public boolean inheritUpdate(TblEquipmentTemplate entity) {
        List<Integer> allChildId = findAllChildId(entity.getEquipmentTemplateId());
        //批量更新
        List<TblEquipmentTemplate> equipmentTemplateList = findByIds(allChildId);
        mybatisBatchUtils.batchUpdateOrInsert(equipmentTemplateList, TblEquipmentTemplateMapper.class, (item, mapper) -> mapper.update(Wrappers.lambdaUpdate(TblEquipmentTemplate.class)
                                                                                                                                               .set(TblEquipmentTemplate::getEquipmentCategory, entity.getEquipmentCategory())
                                                                                                                                               .eq(TblEquipmentTemplate::getEquipmentTemplateId, item.getEquipmentTemplateId())));
        equipmentTemplateList.forEach(e -> {
            //记录日志
            TblEquipmentTemplate equipmentTemplate = BeanUtil.copyProperties(e, TblEquipmentTemplate.class);
            equipmentTemplate.setEquipmentCategory(entity.getEquipmentCategory());
            operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), e, equipmentTemplate);
            changeEventService.sendUpdate(equipmentTemplate);
        });
        return true;
    }

    public List<Integer> findAllChildId(Integer parentTemplateId){
        return equipmentTemplateMapper.findAllChildId(parentTemplateId);
    }

    @Override
    public Integer getBInterfaceDeviceTemplateRootId() {
        QueryWrapper<TblEquipmentTemplate> queryWrapper = new QueryWrapper();
        queryWrapper.eq("EquipmentCategory", EquipmentCategoryEnum.B_INTERFACE_DEVICE.getValue());
        queryWrapper.eq("ParentTemplateId",0);
        queryWrapper.select("EquipmentTemplateId");
        try {
            TblEquipmentTemplate tblEquipmentTemplate = equipmentTemplateMapper.selectOne(queryWrapper);
            return tblEquipmentTemplate.getEquipmentTemplateId();
        }catch (Exception e){
            log.error("query b interface device root template error, the reason is {}",e.getCause());
            return null;
        }
    }

    @Override
    public void deleteByProtocolCode(String protocolCode) {
        List<TblEquipmentTemplate> equipmentTemplateList = findByProtocolCode(protocolCode);
        equipmentTemplateList.forEach(e -> deleteById(e.getEquipmentTemplateId()));
    }

    @Override
    public List<IdValueDTO<String, String>> findNameByIds(List<Integer> equipmentTemplateIds) {
        if (CollUtil.isEmpty(equipmentTemplateIds)) {
            return Collections.emptyList();
        }
        return equipmentTemplateMapper.findNameByIds(equipmentTemplateIds);
    }

    @Override
    public List<TblEquipmentTemplate> queryTemplate(EquipmentTemplateVO equipmentTemplateVO) {
        LambdaQueryWrapper<TblEquipmentTemplate> tblEquipmentTemplateLambdaQueryWrapper = Wrappers.lambdaQuery(TblEquipmentTemplate.class);
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getEquipmentCategory())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getEquipmentCategory,equipmentTemplateVO.getEquipmentCategory());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getExtendField1())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getExtendField1,equipmentTemplateVO.getExtendField1());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getEquipmentType())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getEquipmentType,equipmentTemplateVO.getEquipmentType());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getProtocolCode())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getProtocolCode,equipmentTemplateVO.getProtocolCode());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getMemo())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getMemo,equipmentTemplateVO.getMemo());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getDescription())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getDescription,equipmentTemplateVO.getDescription());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getProperty())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getProperty,equipmentTemplateVO.getProperty());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getEquipmentBaseType())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getEquipmentBaseType,equipmentTemplateVO.getEquipmentBaseType());
        }
        if(ObjectUtil.isNotEmpty(equipmentTemplateVO.getEquipmentTemplateId())){
            tblEquipmentTemplateLambdaQueryWrapper.eq(TblEquipmentTemplate::getEquipmentTemplateId,equipmentTemplateVO.getEquipmentTemplateId());
        }
        List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.selectList(tblEquipmentTemplateLambdaQueryWrapper);
        return equipmentTemplateList;
    }

    @Override
    public List<StandardEquipmentTemplate> findStandardEquipmentTemplateList() {
        List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.selectList(Wrappers.emptyWrapper());
        List<IdValueDTO<Integer, String>> stationCategoryList = stationService.findCategoryList();
        Map<Integer, String> eqTypes = dataItemService.getDataItemMap(DataEntryEnum.EQUIPMENT_CATEGORY);
        return equipmentTemplateList.stream().map(eq -> {
            StandardEquipmentTemplate standardEquipmentTemplate = BeanUtil.copyProperties(eq, StandardEquipmentTemplate.class);
            standardEquipmentTemplate.setSelected(true);
            standardEquipmentTemplate.setStationBaseType(stationCategoryList.stream()
                    .filter(f -> Objects.equals(f.getId(), eq.getStationCategory()))
                    .findFirst().map(IdValueDTO::getValue)
                    .orElse(null));
            standardEquipmentTemplate.setEquipmentTemplateType(eqTypes.get(eq.getEquipmentCategory()));
            return standardEquipmentTemplate;
        }).toList();
    }

    @Override
    public String findProtocolCodeByEquipmentTemplateId(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectOne(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                                                                                           .select(TblEquipmentTemplate::getProtocolCode)
                                                                                           .eq(TblEquipmentTemplate::getEquipmentTemplateId, equipmentTemplateId));
        return equipmentTemplate.getProtocolCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upgradeToRootTemplate(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            log.error("升级设备模板成跟模板失败，设备模板id不存在：{}", equipmentTemplateId);
            return false;
        }
        TblEquipmentTemplate upgradeToTemplate = BeanUtil.copyProperties(equipmentTemplate, TblEquipmentTemplate.class);
        //升级成根模板 只需要将parentTemplateId更新成0
        upgradeToTemplate.setParentTemplateId(0);
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), equipmentTemplate, upgradeToTemplate);
        equipmentTemplateMapper.updateById(upgradeToTemplate);
        sendUpdateEvent(equipmentTemplateId);
        return true;
    }

    @Override
    public void exportExcel(HttpServletResponse response, Integer equipmentTemplateId) throws IOException {
        TblEquipmentTemplate tblEquipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
        if (Objects.isNull(tblEquipmentTemplate)) {
            log.error("设备模板不存在:{}", equipmentTemplateId);
            throw new BusinessException(i18n.T("error.device.template.notFound"));
        }
        List<SignalExcel> signalExcelList = signalService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
        List<EventExcel> eventExcelList = eventService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
        List<ControlExcel> controlExcelList = controlService.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
        Map<String, SheetDataWrapper> sheetDataMap = new LinkedHashMap<>();
        sheetDataMap.put(i18n.T("monitor.signal"), SheetDataWrapper.of(SignalExcel.class, signalExcelList));
        sheetDataMap.put(i18n.T("monitor.event"), SheetDataWrapper.of(EventExcel.class, eventExcelList));
        sheetDataMap.put(i18n.T("monitor.control"), SheetDataWrapper.of(ControlExcel.class, controlExcelList));
        ExcelExportUtil.exportExcelMultiSheet(response, sheetDataMap, tblEquipmentTemplate.getEquipmentTemplateName());
    }

    @Override
    public List<EquipmentTemplateVO> findVoAll() {
        List<TblEquipmentTemplate> equipmentTemplateList = findAll();
        if (CollUtil.isEmpty(equipmentTemplateList)) {
            return Collections.emptyList();
        }
        return equipmentTemplateList.stream().map(EquipmentTemplateVO::toVo).toList();
    }

    @Override
    public boolean existsChildTemplate(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.exists(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                .eq(TblEquipmentTemplate::getParentTemplateId, equipmentTemplateId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteById(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = findById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            log.warn("删除模板id:{},设备模板不存在，", equipmentTemplateId);
            return false;
        }
        equipmentTemplateMapper.deleteById(equipmentTemplateId);
        signalService.deleteByEquipmentTemplateId(equipmentTemplateId);
        controlService.deleteByEquipmentTemplateId(equipmentTemplateId);
        eventService.deleteByEquipmentTemplateId(equipmentTemplateId);
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(equipmentTemplate.getEquipmentTemplateId()), OperationObjectTypeEnum.EQUIPMENT_TEMPLATE, i18n.T("monitor.equipmentTemplate.name"), i18n.T("delete"), equipmentTemplate.getEquipmentTemplateName(), "");
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                changeEventService.sendDelete(equipmentTemplate);
            }
        });
        return true;
    }

    @Override
    public List<Tree<Integer>> findTree(Boolean hideDynamicConfigTemplate) {
        // 获取设备模板节点
        List<EquipmentTemplateTreeDTO> equipmenttemplateNodeList = equipmentTemplateMapper.findDynamicConfigTemplate(hideDynamicConfigTemplate)
                .stream()
                .map(EquipmentTemplateTreeDTO::new)
                .collect(Collectors.toList());

        // 获取设备种类节点
        List<EquipmentTemplateTreeDTO> equipmentCategoryNodeList = getEquipmentCategoryNodeList();

        // 创建一个包含已有设备种类ID的集合，以便快速查找
        Set<Integer> existingCategoryIds = equipmentCategoryNodeList.stream()
                .map(EquipmentTemplateTreeDTO::getId)
                .collect(Collectors.toSet());

        // 创建未分类节点，ID为-1000
        Integer uncategorizedNodeId = -1000;
        EquipmentTemplateTreeDTO uncategorizedNode = new EquipmentTemplateTreeDTO();
        uncategorizedNode.setName("未分类");
        uncategorizedNode.setId(uncategorizedNodeId);
        uncategorizedNode.setParentId(-999);
        uncategorizedNode.setTemplate(false);

        // 处理设备模板
        equipmenttemplateNodeList.forEach(equipmentTemplate -> {
            // 如果设备模板的父节点是0，则将其父节点设置为种类或未分类
            if (Objects.equals(equipmentTemplate.getParentId(), 0)) {
                // 如果种类存在于系统中，则使用它作为父节点
                if (existingCategoryIds.contains(equipmentTemplate.getEquipmentCategory())) {
                    equipmentTemplate.setParentId(equipmentTemplate.getEquipmentCategory());
                } else {
                    // 如果种类不存在，则将父节点设置为未分类节点
                    equipmentTemplate.setParentId(uncategorizedNodeId);
                }
            }
        });

        // 将"监控设备"设置为根节点
        Integer rootId = -9999;
        EquipmentTemplateTreeDTO rootNode = getEquipmentTemplateTreeRoot(rootId);

        // 添加所有节点到列表中
        equipmenttemplateNodeList.add(rootNode);
        equipmenttemplateNodeList.add(uncategorizedNode);
        equipmenttemplateNodeList.addAll(equipmentCategoryNodeList);

        // 构建并返回树
        return TreeUtil.build(equipmenttemplateNodeList, rootId, (node, tree) -> {
            tree.setId(node.getId());
            tree.setParentId(node.getParentId());
            tree.setName(node.getName());
            tree.setWeight(node.getName());
            tree.putExtra("template", node.isTemplate());
            tree.putExtra("equipmentCategory", node.getEquipmentCategory());
        });
    }

    /**
     * 获取设备种类节点
     *
     * @return {@link List}<{@link EquipmentTemplateTreeDTO}>
     */
    private List<EquipmentTemplateTreeDTO> getEquipmentCategoryNodeList() {
        List<TblDataItem> dataItemList = dataItemService.findEquipmentCategory();
        return dataItemList.stream()
                .map(EquipmentTemplateTreeDTO::new)
                .toList();
    }

    /**
     * 获取设备模板树根节点
     *
     * @return {@link EquipmentTemplateTreeDTO}
     */
    private EquipmentTemplateTreeDTO getEquipmentTemplateTreeRoot(Integer rootId) {
        EquipmentTemplateTreeDTO rootNode = new EquipmentTemplateTreeDTO();
        rootNode.setName(i18n.T("monitor.equipment.monitoringEquipment"));
        rootNode.setId(-999);
        rootNode.setParentId(rootId);
        rootNode.setTemplate(false);
        return rootNode;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TblEquipmentTemplate createEquipmentTemplate(TblEquipmentTemplate equipmentTemplate) {
        //传入的equipmentTemplateId为null时自己生成
        if (Objects.isNull(equipmentTemplate.getEquipmentTemplateId())) {
            int equipmentTemplateId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT_TEMPLATE, 0);
            equipmentTemplate.setEquipmentTemplateId(equipmentTemplateId);
        }
        // 如果模板名称超过mysql数据的varchar(128)则提示
        if (CharSequenceUtil.isNotBlank(equipmentTemplate.getEquipmentTemplateName()) && equipmentTemplate.getEquipmentTemplateName().length() > 128) {
            throw new BusinessException(i18n.T("monitor.equipmentTemplate.name.length"));
        }
        // 如果memo为空则设置为空字符串
        if (StrUtil.isEmpty(equipmentTemplate.getMemo())) {
            equipmentTemplate.setMemo("init");
        }
        // 如果protocolCode为空则设置为空字符串
        if (StrUtil.isEmpty(equipmentTemplate.getProtocolCode())) {
            equipmentTemplate.setProtocolCode("init");
        }
        equipmentTemplateMapper.insert(equipmentTemplate);
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(equipmentTemplate.getEquipmentTemplateId()), OperationObjectTypeEnum.EQUIPMENT_TEMPLATE, i18n.T("monitor.equipmentTemplate.name"), i18n.T("add"), "", equipmentTemplate.getEquipmentTemplateName());
        changeEventService.sendCreate(equipmentTemplate);
        return equipmentTemplate;
    }


    @Override
    public boolean existsByParentTemplateId(Integer parentTemplateId) {
        if (Objects.isNull(parentTemplateId)) {
            return false;
        }
        return equipmentTemplateMapper.exists(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                                                      .eq(TblEquipmentTemplate::getParentTemplateId,parentTemplateId));
    }

    @Override
    public boolean existsByEquipmentTemplateName(String equipmentTemplateName) {
        if (CharSequenceUtil.isBlank(equipmentTemplateName)) {
            return false;
        }
        return equipmentTemplateMapper.exists(Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                                                      .eq(TblEquipmentTemplate::getEquipmentTemplateName,equipmentTemplateName));
    }

    @Override
    public List<EquipmentTemplateTreeDTO> findTree(Integer equipmentCategory, String protocolCode, String equipmentTemplateName) {
        List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.findByEquipmentCategoryAndProtocolCode(equipmentCategory,protocolCode, equipmentTemplateName);
        List<EquipmentTemplateTreeDTO> list = equipmentTemplateList.stream().map(EquipmentTemplateTreeDTO::new).toList();
        return buildTree(list);
    }
    /**
     * 构建树结构并按name升序排序
     */
    public List<EquipmentTemplateTreeDTO> buildTree(List<EquipmentTemplateTreeDTO> list) {
        // 创建ID到节点的映射
        Map<Integer, EquipmentTemplateTreeDTO> nodeMap = list.stream()
                                                             .collect(Collectors.toMap(EquipmentTemplateTreeDTO::getId, node -> node, (v1, v2) -> v1));

        // 收集所有节点的子节点
        Map<Integer, List<EquipmentTemplateTreeDTO>> childrenMap = new HashMap<>();

        // 找出所有非根节点
        Set<Integer> nonRootIds = new HashSet<>();

        // 遍历所有节点，建立父子关系
        for (EquipmentTemplateTreeDTO node : list) {
            Integer parentId = node.getParentId();
            // 如果父节点ID存在于nodeMap中，则不是根节点
            if (nodeMap.containsKey(parentId)) {
                nonRootIds.add(node.getId());
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(node);
            }
        }

        // 找出所有根节点（不在nonRootIds集合中的节点）
        List<EquipmentTemplateTreeDTO> roots = list.stream()
                                                   .filter(node -> !nonRootIds.contains(node.getId()))
                                                   .collect(Collectors.toList());

        // 递归设置子节点
        roots.forEach(root -> setChildren(root, childrenMap));

        // 递归排序
        sortTree(roots);

        return roots;
    }

    /**
     * 递归设置子节点
     */
    private void setChildren(EquipmentTemplateTreeDTO node, Map<Integer, List<EquipmentTemplateTreeDTO>> childrenMap) {
        List<EquipmentTemplateTreeDTO> children = childrenMap.get(node.getId());
        if (children != null && !children.isEmpty()) {
            node.setChildren(children);
            // 递归设置子节点的子节点
            children.forEach(child -> setChildren(child, childrenMap));
        }
    }
    /**
     * 递归排序树的每个层级
     */
    private void sortTree(List<EquipmentTemplateTreeDTO> nodes) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }

        // 按name升序排序当前层级
        nodes.sort(Comparator.comparing(EquipmentTemplateTreeDTO::getName));

        // 递归排序每个节点的子节点
        nodes.forEach(node -> {
            if (CollUtil.isNotEmpty(node.getChildren())) {
                sortTree(node.getChildren());
            }
        });
    }
}
