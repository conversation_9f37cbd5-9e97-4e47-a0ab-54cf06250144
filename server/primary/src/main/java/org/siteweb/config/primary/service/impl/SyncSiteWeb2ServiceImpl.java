package org.siteweb.config.primary.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.mapper.SyncSiteWeb2Mapper;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class SyncSiteWeb2ServiceImpl implements SyncSiteWeb2Service {

    @Autowired
    private SyncSiteWeb2Mapper syncSiteWeb2Mapper;

    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private StationService stationService;

    @Autowired
    private StationStructureService stationStructureService;

    @Autowired
    private StationStructureMapService stationStructureMapService;

    @Autowired
    private TBL_ConfigChangeMacroLogService tblConfigChangeMacroLogService;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private EquipmentService equipmentService;

    /**
     * 同步站点资源结构
     *
     * <p>同步 SiteWeb2 的站点资源结构
     *
     * <p>1. 如果资源结构表为空，调用 {@link #SyncResourceStructureFromStationData()} 方法同步资源结构
     * <p>2. 如果站点表为空，直接返回
     * <p>3. 过滤掉站点表中 category 不是 1、105、106、107、108 的记录
     * <p>4. 通过 {@link StationStructureService#findCommonParentByStationIds(List, TblStationStructure)} 方法
     * 找到所有站点的公共父节点
     * <p>5. 通过 {@link #createResourceStructureList(TblStationStructure)} 方法根据公共父节点生成资源结构列表
     * <p>6. 通过 {@link ResourceStructureService#batchInsert(List)} 方法批量插入资源结构
     * <p>7. 通过 {@link ResourceStructureService#findResourceStructures()} 方法查询所有的资源结构
     * <p>8. 遍历资源结构，更新设备的resourceStructureId
     * <p>9. 通过 {@link TBL_ConfigChangeMacroLogService#configChangeLog(String, Integer, ChangeOperatorEnum)} 方法
     * 记录配置变更日志
     */
    @Transactional
    public void syncSiteWeb2Config() {
        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure == null) {
            SyncResourceStructureFromStationData();
            return;
        }

        List<TblStation> allStation = stationService.findAllStation();
        if (allStation == null || allStation.isEmpty()) {
            return;
        }
        // 查询未分组局站下的stationids
        // 查询未分组分组
        TblStationStructure ungroupedStructure = stationStructureService.findUngroupedStructure(1);
        List <Integer> ungroupedStationIds = stationStructureMapService.findStationIdsByStructureId(ungroupedStructure.getStructureId());

        List<TblStation> filteredStations = allStation.stream()
                .filter(station -> station.getBordNumber() != null && station.getBordNumber() == 0)
                .filter(station -> !ungroupedStationIds.contains(station.getStationId()))
                .toList();

        if (filteredStations.isEmpty()) {
            return;
        }

        TblStationStructure stationStructureTree = stationStructureService.tree();
        List<Integer> stationIds = filteredStations.stream().map(TblStation::getStationId).toList();
        TblStationStructure commonParentStructure = stationStructureService.findCommonParentByStationIds(stationIds, stationStructureTree);

        List<ResourceStructure> resourceStructureList = createResourceStructureList(commonParentStructure);

        resourceStructureService.batchInsert(resourceStructureList);


        List<ResourceStructure> resourceStructures = resourceStructureService.findResourceStructures();
        resourceStructures = resourceStructures.stream()
                .filter(resourceStructure -> resourceStructure.getStructureTypeId() == StructureTypeEnum.STATION_HOUSE.getValue())
                .toList();

        for (ResourceStructure resourceStructure : resourceStructures) {
            equipmentService.updateResourceStructureIdIfNotMapped(resourceStructure.getOriginId(), resourceStructure.getResourceStructureId());
        }
        tblConfigChangeMacroLogService.configChangeLog("-1", 27, ChangeOperatorEnum.CREATE);

        tblConfigChangeMacroLogService.configChangeLog("-1", 3, ChangeOperatorEnum.CREATE);

    }

    private void SyncResourceStructureFromStationData() {
        Integer isSC = syncSiteWeb2Mapper.getIsScValue();

        if (isSC == 1) {
            syncSiteWeb2Mapper.updateResourceStructureForSc();
        }

        syncSiteWeb2Mapper.updateInitialStructureType();
        syncSiteWeb2Mapper.insertCenterStructure();
        syncSiteWeb2Mapper.updateCenterStructureName();
        syncSiteWeb2Mapper.deleteCenterStructure();
        syncSiteWeb2Mapper.updateCenterStructurePath();
        syncSiteWeb2Mapper.insertSubStructure();
        syncSiteWeb2Mapper.updateSubStructureName();
        syncSiteWeb2Mapper.deleteSubStructure();
        syncSiteWeb2Mapper.updateSubStructureParentPath();
        syncSiteWeb2Mapper.updateSubStructureSecondLevel();
        syncSiteWeb2Mapper.insertStationStructure();
        syncSiteWeb2Mapper.updateStationStructure();
        syncSiteWeb2Mapper.deleteStationStructure();
        syncSiteWeb2Mapper.updateStationStructurePath();
        syncSiteWeb2Mapper.insertHouseStructure();
        syncSiteWeb2Mapper.updateHouseStructure();
        syncSiteWeb2Mapper.deleteHouseStructure();
        syncSiteWeb2Mapper.updateHouseStructurePath();
        syncSiteWeb2Mapper.updateEquipmentResourceStructure();

        if (isSC == 1) {
            syncSiteWeb2Mapper.revertResourceStructureForSc();
        }

        syncSiteWeb2Mapper.insertConfigChangeLog();
    }

    /**
     * 递归打印结构节点及其子节点
     * @param node 当前节点
     * @param prefix 前缀（用于缩进）
     * @param isLast 是否是最后一个节点
     */
    private void printStructureNode(TblStationStructure node, String prefix, boolean isLast) {
        // 打印当前节点基本信息
        System.out.println(prefix + (isLast ? "└── " : "├── ") + "结构信息:");
        String childPrefix = prefix + (isLast ? "    " : "│   ");

        System.out.println(childPrefix + "├── ID: " + node.getStructureId());
        System.out.println(childPrefix + "├── 名称: " + node.getStructureName());
        System.out.println(childPrefix + "├── 父节点ID: " + (node.getParentStructureId() == null ? "无" : node.getParentStructureId()));
        System.out.println(childPrefix + "├── 结构组ID: " + node.getStructureGroupId());

        // 打印关联的站点信息
        if (node.getStations() != null && !node.getStations().isEmpty()) {
            System.out.println(childPrefix + "├── 关联站点:");
            for (int i = 0; i < node.getStations().size(); i++) {
                TblStation station = node.getStations().get(i);
                boolean isLastStation = i == node.getStations().size() - 1;
                String stationPrefix = childPrefix + (isLastStation ? "    " : "│   ");

                System.out.println(childPrefix + (isLastStation ? "└── " : "├── ") + "站点 " + (i + 1) + ":");
                System.out.println(stationPrefix + "├── ID: " + station.getStationId());
                System.out.println(stationPrefix + "├── 名称: " + station.getStationName());

                // 打印站点关联的房屋信息
                if (station.getHouses() != null && !station.getHouses().isEmpty()) {
                    System.out.println(stationPrefix + "└── 关联房屋:");
                    for (int j = 0; j < station.getHouses().size(); j++) {
                        TblHouse house = station.getHouses().get(j);
                        boolean isLastHouse = j == station.getHouses().size() - 1;

                        System.out.println(stationPrefix + "    " + (isLastHouse ? "└── " : "├── ") + "房屋 " + (j + 1) + ":");
                        System.out.println(stationPrefix + "    " + "    ├── ID: " + house.getHouseId());
                        System.out.println(stationPrefix + "    " + "    └── 名称: " + house.getHouseName());
                    }
                }
            }
        }

        // 递归打印子节点
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            System.out.println(childPrefix + "└── 子节点:");
            for (int i = 0; i < node.getChildren().size(); i++) {
                printStructureNode(
                        node.getChildren().get(i),
                        childPrefix + "    ",
                        i == node.getChildren().size() - 1
                );
            }
        }
    }

    private void printResourceStructureList(List<ResourceStructure> resourceStructureList) {
        for (ResourceStructure resourceStructure : resourceStructureList) {
            System.out.println(resourceStructure.toString());
        }
    }

    public List<ResourceStructure> createResourceStructureList(TblStationStructure commonParentStructure) {
        List<ResourceStructure> resourceStructureList = new ArrayList<>();
        Integer existingRootResourceStructureId = null;

        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure != null) {
            existingRootResourceStructureId = rootStructure.getResourceStructureId();
        }


        if (existingRootResourceStructureId != null) {
            String levelPath = String.valueOf(existingRootResourceStructureId);
            if (commonParentStructure.getChildren() != null) {
                for (TblStationStructure child : commonParentStructure.getChildren()) {
                    createResourceStructureRecursive(child, existingRootResourceStructureId, levelPath, resourceStructureList);
                }
            }

            if (commonParentStructure.getStations() != null) {
                for (TblStation station : commonParentStructure.getStations()) {
                    createStationResourceStructure(station, existingRootResourceStructureId, levelPath, resourceStructureList);
                }
            }
        }

        return resourceStructureList;
    }

    private void createResourceStructureRecursive(
            TblStationStructure currentStructure,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(currentStructure.getStructureId(), 103);
        if (existingResourceStructureId != null) {
            String currentLevelPath = parentLevelPath + "." + existingResourceStructureId;
            if (currentStructure.getChildren() != null) {
                for (TblStationStructure child : currentStructure.getChildren()) {
                    createResourceStructureRecursive(child, existingResourceStructureId, currentLevelPath, resourceStructureList);
                }
            }
            if (currentStructure.getStations() != null) {
                for (TblStation station : currentStructure.getStations()) {
                    createStationResourceStructure(station, existingResourceStructureId, currentLevelPath, resourceStructureList);
                }
            }
            return;
        }

        int resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
        String currentLevelPath = parentLevelPath.isEmpty() ? String.valueOf(resourceStructureId) : parentLevelPath + "." + resourceStructureId;

        ResourceStructure resource = new ResourceStructure();
        resource.setResourceStructureId(resourceStructureId);
//        resource.setSceneId(2);
        resource.setDisplay(true);
        resource.setSortValue(1);
        resource.setParentResourceStructureId(parentResourceStructureId);
        resource.setLevelOfPath(currentLevelPath);
        resource.setResourceStructureName(currentStructure.getStructureName());
        resource.setOriginId(currentStructure.getStructureId());
        resource.setOriginParentId(currentStructure.getParentStructureId());
        resource.setStructureTypeId(103);

        resourceStructureList.add(resource);

        if (currentStructure.getChildren() != null) {
            for (TblStationStructure child : currentStructure.getChildren()) {
                createResourceStructureRecursive(child, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }

        if (currentStructure.getStations() != null) {
            for (TblStation station : currentStructure.getStations()) {
                createStationResourceStructure(station, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }
    }




    private void createStationResourceStructure(
            TblStation station,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(station.getStationId(), 104);

        // 如果 station 已经有对应的 ResourceStructure，获取其 ID 用于 house 的处理
        int resourceStructureId;
        String currentLevelPath;

        if (existingResourceStructureId != null) {
            resourceStructureId = existingResourceStructureId;
            currentLevelPath = parentLevelPath + "." + resourceStructureId;
        } else {
            // 如果不存在，则创建新的 ResourceStructure
            resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
            currentLevelPath = parentLevelPath + "." + resourceStructureId;

            ResourceStructure resource = new ResourceStructure();
            resource.setResourceStructureId(resourceStructureId);
//            resource.setSceneId(2);
            resource.setDisplay(true);
            resource.setSortValue(1);
            resource.setStructureTypeId(104);
            resource.setParentResourceStructureId(parentResourceStructureId);
            resource.setLevelOfPath(currentLevelPath);
            resource.setResourceStructureName(station.getStationName());
            resource.setOriginId(station.getStationId());
            TblStationStructureMap stationStructureMap = stationStructureMapService.findStationStructureMapByStationId(station.getStationId());
            resource.setOriginParentId(stationStructureMap.getStructureId());

            resourceStructureList.add(resource);
        }

        // 无论 station 是否已有对应的 ResourceStructure，处理 station 下的 house
        if (station.getHouses() != null) {
            for (TblHouse house : station.getHouses()) {
                createHouseResourceStructure(house, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }
    }


    private void createHouseResourceStructure(
            TblHouse house,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(house.getHouseId(), house.getStationId(), 105);
        if (existingResourceStructureId != null) {
            return;
        }

        int resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
        String currentLevelPath = parentLevelPath + "." + resourceStructureId;

        ResourceStructure resource = new ResourceStructure();
        resource.setResourceStructureId(resourceStructureId);
//        resource.setSceneId(2);
        resource.setStructureTypeId(105);
        resource.setDisplay(true);
        resource.setSortValue(1);
        resource.setParentResourceStructureId(parentResourceStructureId);
        resource.setLevelOfPath(currentLevelPath);
        resource.setResourceStructureName(house.getHouseName());
        resource.setOriginId(house.getHouseId());
        resource.setOriginParentId(house.getStationId());

        resourceStructureList.add(resource);
    }

    private Integer fetchExistingResourceStructureId(Integer originId, Integer structureTypeId) {
        ResourceStructure existingStructure = resourceStructureService.findByOriginIdAndStructureType(originId, structureTypeId);
        return existingStructure != null ? existingStructure.getResourceStructureId() : null;
    }

    private Integer fetchExistingResourceStructureId(Integer originId, Integer parentOriginId, Integer structureTypeId) {
        ResourceStructure existingStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(originId, parentOriginId, structureTypeId);
        return existingStructure != null ? existingStructure.getResourceStructureId() : null;
    }
}
