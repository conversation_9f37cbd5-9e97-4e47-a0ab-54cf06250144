package org.siteweb.config.primary.websocket.handler.impl;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.siteweb.config.primary.manager.TelnetConnectionManager;
import org.siteweb.config.primary.service.MonitorUnitXmlService;
import org.siteweb.config.primary.websocket.dto.WebSocketCommonMessageBody;
import org.siteweb.config.primary.websocket.enums.WebSocketBusinessTypeEnum;
import org.siteweb.config.primary.websocket.handler.WebSocketHandlerAdapter;
import org.siteweb.config.primary.websocket.manager.WebSocketSessionManager;
import org.siteweb.config.primary.websocket.manager.impl.CommonMsgWebSocketManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/25
 */
@Component
public class CommonMsgWebSocketHandler extends WebSocketHandlerAdapter {

    @Autowired
    CommonMsgWebSocketManager commonMsgWebSocketManager;

    @Autowired
    private MonitorUnitXmlService monitorUnitXmlService;

    @Autowired
    private TelnetConnectionManager telnetConnectionManager;

    @Override
    public WebSocketSessionManager getWebSocketManager() {
        return commonMsgWebSocketManager;
    }

    @Override
    protected void subHandleTextMessage(WebSocketSession session, TextMessage message) {
        // 收到消息后的处理逻辑，判断消息类型，然后做相应的处理
        JSONObject entries = JSONUtil.parseObj(message.getPayload());
        switch (entries.getInt("webSocketBusinessType")) {
            case 1:
                // 监控单元配置生成
                monitorUnitXmlService.createMonitorUnitConfigXMLAsync(entries.getStr("monitorUnitIds"), getUniqueId(session));
                break;
            case 2:
                // 监控单元配置下发
                String protocol = entries.getStr("protocol");
                if (StrUtil.isEmpty(protocol)) {
                    protocol = "ftp";
                }
                boolean skipSoFileDownload = entries.getBool("skipSoFileDownload", false);
                monitorUnitXmlService.sendMonitorUnitConfigXMLAsync(entries.getStr("monitorUnitIds"), entries.getInt("port"), entries.getStr("user"), entries.getStr("passWord"), protocol, getUniqueId(session), skipSoFileDownload);
                break;
            case 5:
                if (entries.getInt("monitorUnitId") == null) {
                    return;
                }
                boolean isConnectionAvailable = telnetConnectionManager.isConnectionAvailable(entries.getInt("monitorUnitId") + ":" + getUniqueId(session));
                if (isConnectionAvailable) {
                    // 发送一个空消息，用于telnet判断连接状态
                    telnetConnectionManager.sendCommandToMonitorUnit(entries.getInt("monitorUnitId") + ":" + getUniqueId(session), entries.getStr("cmd") == null ? "" : entries.getStr("cmd"));
                } else {
                    telnetConnectionManager.addConnection(entries.getInt("monitorUnitId"), 23, getUniqueId(session));
                }
                break;
            case 6:
                // 用于telnet判断连接状态
                boolean checkResult = telnetConnectionManager.isConnectionAvailable(getUniqueId(session));
                commonMsgWebSocketManager.sendMessage(getUniqueId(session), WebSocketCommonMessageBody.of(WebSocketBusinessTypeEnum.TELNET_AVAILABLE, checkResult, true));
                break;
            default:
                break;
        }

    }
}
