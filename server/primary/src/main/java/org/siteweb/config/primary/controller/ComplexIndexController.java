package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.ComplexIndexDTO;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.ComplexIndexService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/complexindex")
public class ComplexIndexController {
    @Autowired
    ComplexIndexService complexIndexService;

    @GetMapping(value = "/all")
    public ResponseEntity<ResponseResult> getAllComplexIndex() {
        List<ComplexIndexDTO> complexList = complexIndexService.findComplexList(null, null);
        return ResponseHelper.successful(complexList);
    }
    @GetMapping(value = "/list/{objectTypeId}")
    public ResponseEntity<ResponseResult> getComplexIndexList(@PathVariable("objectTypeId") Integer objectTypeId) {
        return ResponseHelper.successful(complexIndexService.findComplexList(null, objectTypeId));
    }
}
