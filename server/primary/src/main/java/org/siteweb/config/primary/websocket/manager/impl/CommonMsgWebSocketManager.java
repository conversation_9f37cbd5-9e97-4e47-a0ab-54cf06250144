package org.siteweb.config.primary.websocket.manager.impl;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.primary.websocket.dto.WebSocketCommonMessageBody;
import org.siteweb.config.primary.websocket.manager.WebSocketSessionManager;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/25
 */
@Component
@Slf4j
public class CommonMsgWebSocketManager extends WebSocketSessionManager {
    private final ConcurrentHashMap<String, WebSocketSession> commonMsgWebsocketStatePool = new ConcurrentHashMap<>();

    @Override
    public ConcurrentHashMap<String, WebSocketSession> getSessionStatePool() {
        return commonMsgWebsocketStatePool;
    }

    /**
     * 发送给指定客户端消息
     * @param uniqueId 客户端唯一会话id
     * @param webSocketCommonMessageBody 需要发送的消息体
     */
    public <T> void sendMessage(String uniqueId, WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        WebSocketSession session = commonMsgWebsocketStatePool.get(uniqueId);
        // 调用内部的、同步的发送方法
        sendMessageInternal(uniqueId, session, webSocketCommonMessageBody);
    }

    /**
     * 发送给所有在线客户端消息
     * @param webSocketCommonMessageBody webSocket需要接收的内容
     */
    public <T> void sendAllUserMessage(WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        // 复制entrySet以避免迭代时修改
        ArrayList<Map.Entry<String, WebSocketSession>> webSocketEntries = new ArrayList<>(commonMsgWebsocketStatePool.entrySet());
        for (Map.Entry<String, WebSocketSession> webSocketEntry : webSocketEntries) {
            // 调用内部的、同步的发送方法
            sendMessageInternal(webSocketEntry.getKey(), webSocketEntry.getValue(), webSocketCommonMessageBody);
        }
    }

    /**
     * 内部发送消息方法，增加了同步处理
     * @param uniqueId 客户端唯一会话id (用于日志)
     * @param webSocketSession 目标WebSocket会话
     * @param webSocketCommonMessageBody 需要发送的消息体
     */
    private <T> void sendMessageInternal(String uniqueId, WebSocketSession webSocketSession, WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        if (webSocketSession == null) {
            // 会话不存在，不尝试发送，仅记录警告（可选）
            // log.warn("Attempted to send message to null session for uniqueId: {}", uniqueId);
            return;
        }

        // 在发送前对当前会话对象进行同步锁定
        synchronized (webSocketSession) {
            // 在获取锁后再次检查会话是否仍然打开
            if (!webSocketSession.isOpen()) {
                log.error("uniqueId:{},消息内容:{},发送webSocket消息失败，获取锁后发现会话已经关闭", uniqueId, webSocketCommonMessageBody);
                // 可选：如果会话已关闭，则从池中移除
                // commonMsgWebsocketStatePool.remove(uniqueId, webSocketSession);
                return;
            }

            // --- 原有的发送逻辑开始 ---
            try {
                // 注意：JSONUtil.toJsonStr也可能抛出异常，但通常不在此处捕获，除非必要
                TextMessage textMessage = new TextMessage(JSONUtil.toJsonStr(webSocketCommonMessageBody));
                webSocketSession.sendMessage(textMessage);
                // 注意：保持原有的info级别日志
                log.info("uniqueId：{},发送消息成功,消息内容:{}", uniqueId, textMessage.getPayload()); // 使用getPayload获取实际发送的字符串
            } catch (IOException e) {
                // 保持原有的错误日志格式
                log.error("uniqueId:{}，内容：{},发送webSocket消息失败 {}", uniqueId, webSocketCommonMessageBody, ExceptionUtil.stacktraceToString(e));
                // 发生IO异常时，可以考虑关闭并移除会话
                // closeSessionWithError(uniqueId, webSocketSession);
            } catch (IllegalStateException e) {
                // 特别处理 IllegalStateException，虽然理论上同步后不应发生TEXT_PARTIAL_WRITING
                log.error("uniqueId:{}，内容：{},发送webSocket消息时发生IllegalStateException (可能是会话状态问题): {}", uniqueId, webSocketCommonMessageBody, ExceptionUtil.stacktraceToString(e));
                // 发生此异常也可能意味着会话有问题，考虑关闭并移除
                // closeSessionWithError(uniqueId, webSocketSession);
            } catch (Exception e) {
                // 捕获其他可能的运行时异常
                log.error("uniqueId:{}，内容：{},发送webSocket消息时发生未知异常: {}", uniqueId, webSocketCommonMessageBody, ExceptionUtil.stacktraceToString(e));
                // closeSessionWithError(uniqueId, webSocketSession);
            }
            // --- 原有的发送逻辑结束 ---

        } // 同步块结束
    }

    // 可选的辅助方法：处理发送错误时关闭并移除会话
    // private void closeSessionWithError(String uniqueId, WebSocketSession session) {
    //     try {
    //         if (session != null && session.isOpen()) {
    //              // 使用合适的状态码关闭
    //             session.close(CloseStatus.PROTOCOL_ERROR.withReason("Send message failed"));
    //         }
    //     } catch (IOException ex) {
    //         log.error("关闭出错的WebSocket会话 {} 时发生IO异常: {}", uniqueId, ex.getMessage());
    //     } finally {
    //         // 确保从池中移除
    //         commonMsgWebsocketStatePool.remove(uniqueId, session);
    //         log.warn("因发送错误移除会话: {}", uniqueId);
    //     }
    // }


    // --- 确保WebSocketSessionManager或此处的其他方法正确管理 commonMsgWebsocketStatePool 的会话添加和移除 ---

}
