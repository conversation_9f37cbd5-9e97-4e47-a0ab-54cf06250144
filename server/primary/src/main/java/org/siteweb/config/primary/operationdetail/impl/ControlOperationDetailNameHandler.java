package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblControlMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ControlOperationDetailNameHandler extends AbstractNameHandler {
    @Autowired
    TblControlMapper controlMapper;
    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Map<String, String>> controlUniqueIds = ids.stream()
                                                        .map(id -> {
                                                            String[] parts = id.split("\\.");
                                                            Map<String, String> map = new HashMap<>();
                                                            map.put("equipmentTemplateId", parts[0]);
                                                            map.put("controlId", parts[1]);
                                                            return map;
                                                        })
                                                        .toList();
        return controlMapper.findNamesByIds(controlUniqueIds);
    }

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.CONTROL;
    }
}
