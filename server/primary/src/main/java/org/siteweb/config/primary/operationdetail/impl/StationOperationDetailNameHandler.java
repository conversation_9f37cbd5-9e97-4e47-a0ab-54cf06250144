package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblStationMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StationOperationDetailNameHandler extends AbstractNameHandler {
    @Autowired
    TblStationMapper stationMapper;
    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Integer> stationIds = ids.stream().map(Integer::valueOf).toList();
        return stationMapper.findNamesByIds(stationIds);
    }

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.STATION;
    }
}
