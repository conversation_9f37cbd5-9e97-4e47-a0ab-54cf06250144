package org.siteweb.config.primary.manager;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.telnet.TelnetClient;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;


@Data
@Slf4j
public class TelnetConnection {
    private final TelnetClient telnet;
    private InputStream in;
    private OutputStream out;
    private Integer monitorUnitId;
    private long lastActivityTime;
    private TelnetMessageListener messageListener;

    public TelnetConnection(String server, int port, int monitorUnitId) {
        telnet = new TelnetClient();
        try {
            telnet.connect(server, port);
            this.in = telnet.getInputStream();
            this.out = telnet.getOutputStream();
            this.monitorUnitId = monitorUnitId;
            updateLastActivityTime();
        } catch (Exception e) {
            log.error("Failed to connect to server: {}, port: {}", server, port, e);
        }
    }

    public void sendCommand(String command) {
        try {
            PrintWriter writer = new PrintWriter(out, true, StandardCharsets.UTF_8);
            writer.println(command);
            writer.flush();
            updateLastActivityTime();
        } catch (Exception e) {
            log.error("Failed to send command: {}", command, e);
        }
    }

//    public void readResponse() {
//        try {
//            byte[] buffer = new byte[1024];
//            int bytesRead;
//            StringBuilder response = new StringBuilder();
//            while ((bytesRead = in.read(buffer)) != -1) {
//                for (int i = 0; i < bytesRead; i++) {
//                    char ch = (char) buffer[i];
//                    response.append(ch);
//                    System.out.print(ch); // 实时打印每个字符，确保实时输出
//                    updateLastActivityTime();
//                    if (isEndOfMessage(response)) {
//                        String completeMessage = response.toString();
//                        if (messageListener != null) {
//                            messageListener.onMessageReceived(completeMessage);
//                        }
//                        response.setLength(0); // 清空 StringBuilder 以准备下一条消息
//                    }
//                    if (response.toString().contains("\n")) { // 假设以换行符为完整消息的结束符，可以根据具体情况调整
//                        String completeMessage = response.toString();
//                        if (messageListener != null) {
//                            messageListener.onMessageReceived(completeMessage);
//                        }
//                        response.setLength(0); // 清空 StringBuilder 以准备下一条消息
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("Failed to read response from server", e);
//        }
//    }

    public void readResponse() {
        try {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                String message = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                updateLastActivityTime();
                if (messageListener != null) {
                    messageListener.onMessageReceived(message);
                }
            }
        } catch (Exception e) {
            log.error("Failed to read response from server", e);
        }
    }

    private boolean isEndOfMessage(StringBuilder response) {
        // 定义多个提示符，可以根据实际情况添加更多提示符
        String[] prompts = {"login", "Password"};
        int length = response.length();
        for (String prompt : prompts) {
            if (length >= prompt.length()) {
                String end = response.substring(length - prompt.length());
                if (end.contains(prompt)) {
                    return true;
                }
            }
        }
        return false;
    }


    public void close() {
        try {
            telnet.disconnect();
        } catch (Exception e) {
            log.error("Failed to close telnet connection", e);
        }
    }

    public void updateLastActivityTime() {
        lastActivityTime = System.currentTimeMillis();
    }

}
