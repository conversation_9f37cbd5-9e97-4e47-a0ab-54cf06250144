package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblSignalMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SignalOperationDetailNameHandler extends AbstractNameHandler {
    @Autowired
    TblSignalMapper signalMapper;
    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Map<String, String>> signalUniqueIds = ids.stream()
                                                       .map(id -> {
                                                           String[] parts = id.split("\\.");
                                                           Map<String, String> map = new HashMap<>();
                                                           map.put("equipmentTemplateId", parts[0]);
                                                           map.put("signalId", parts[1]);
                                                           return map;
                                                       })
                                                       .toList();
        return signalMapper.findNamesByIds(signalUniqueIds);
    }

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.SIGNAL;
    }
}
