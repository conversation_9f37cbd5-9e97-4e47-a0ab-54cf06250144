package org.siteweb.config.primary.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.siteweb.config.common.dto.OperationDetailDTO;
import org.siteweb.config.common.entity.TblOperationDetail;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.vo.OperationDetailVO;

import java.util.List;

public interface OperationDetailService {
    /**
     * 记录操作日志
     * TODO【暂时没有用户id，所有用户id暂且为-1】
     *
     * @param userId                  用户id
     * @param objectId                对象id
     * @param operationObjectTypeEnum 对象类型
     * @param propertyName            操作属性
     * @param operationType           操作
     * @param oldValue                旧值
     * @param newValue                新值
     */
    void recordOperationLog(Integer userId, String objectId, OperationObjectTypeEnum operationObjectTypeEnum, String propertyName, String operationType, String oldValue, String newValue);

    /**
     * 比较实体并记录操作日志【通过反射判断字段是否修改，然后添加操作详情日志】
     * 注意【只能用于更新操作】
     * @param userId    用户id TODO【暂时没有用户id，所有用户id暂且为-1】
     * @param oldEntity     旧实体
     * @param newEntity     新实体
     */
    <T> void compareEntitiesRecordLog(Integer userId, T oldEntity, T newEntity);

    /**
     * 获取所有操作日志
     *
     * @return {@link List }<{@link TblOperationDetail }>
     */
    Page<OperationDetailVO> findPage(Page<TblOperationDetail> page, OperationDetailDTO operationDetailDTO);

    Page<TblOperationDetail> findPageByObjectTypeAndObjectId(Page<TblOperationDetail> page, String objectType, String objectId);

    Page<OperationDetailVO> findEquipmentTemplateLogPage(Page<TblOperationDetail> page, OperationDetailDTO operationDetailDTO);

    Page<OperationDetailVO> findEquipmentLogPage(Page<TblOperationDetail> page, OperationDetailDTO operationDetailDTO);
}
