package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.ResourceStructureMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ResourceSturctureOperationDetailNameHandler extends AbstractNameHandler {

    @Autowired
    ResourceStructureMapper resourceStructureMapper;

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.RESOURCE_STRUCTURE;
    }

    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Integer> resourceStructureIds = ids.stream().map(Integer::valueOf).toList();
        return resourceStructureMapper.findNamesByIds(resourceStructureIds);
    }
}
