package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.StandardPointCategoryDTO;
import org.siteweb.config.common.dto.StandardPointMappingDTO;
import org.siteweb.config.common.dto.StandardPointUnbindDTO;
import org.siteweb.config.common.dto.StandardUnmappedPointsDTO;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.StandardPointService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/standardpoints")
public class StandardPointController {
    @Autowired
    StandardPointService standardPointService;

    @GetMapping
    public ResponseEntity<ResponseResult> listStandardPoints(Integer type, Integer equipmentCategoryId) {
        return ResponseHelper.successful(standardPointService.listStandardPoints(type, equipmentCategoryId));
    }

    @PostMapping("/applymapping")
    public ResponseEntity<ResponseResult> applyStandardPointMapping(@RequestBody StandardPointMappingDTO standardPointMappingDTO) {
        standardPointService.applyStandardPointMapping(standardPointMappingDTO);
        return ResponseHelper.successful();
    }

    @PostMapping("/unbindmapping")
    public ResponseEntity<ResponseResult> unbindStandardPointMapping(@RequestBody StandardPointUnbindDTO standardPointUnbindDTO) {
        standardPointService.unbindStandardPointMapping(standardPointUnbindDTO);
        return ResponseHelper.successful();
    }

    @PutMapping("/standardpointcategory")
    public ResponseEntity<ResponseResult> updateStandardPointCategory(@RequestBody StandardPointCategoryDTO standardPointCategoryDTO) {
        standardPointService.updateStandardPointCategory(standardPointCategoryDTO);
        return ResponseHelper.successful();
    }

    @PutMapping("/generateunmappedpoints")
    public ResponseEntity<ResponseResult> generateUnmappedPoints(@RequestBody StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        standardPointService.generateUnmappedPoints(standardUnmappedPointsDTO);
        return ResponseHelper.successful();
    }

    @PostMapping("/unbindunmappedpoints")
    public ResponseEntity<ResponseResult> unbindUnmappedPoints(@RequestBody StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        standardPointService.unbindUnmappedPoints(standardUnmappedPointsDTO);
        return ResponseHelper.successful();
    }

}
