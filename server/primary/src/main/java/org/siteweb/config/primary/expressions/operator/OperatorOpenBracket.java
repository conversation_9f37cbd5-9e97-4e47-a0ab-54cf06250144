package org.siteweb.config.primary.expressions.operator;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;

/**
 * 表示开括号运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorOpenBracket extends OperatorBase{
    @Override
    public String operatorSymbol() {
        return "(";
    }

    @Override
    public String operatorName() {
        return "左括号";
    }

    @Override
    public int priority() {
        return Integer.MAX_VALUE;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.NONE;
    }

    @Override
    public int operandCount() {
        return 0;
    }

    @Override
    public double onCalculate(double[] operands) {
        throw new RuntimeException("左括号 不允许计算");
    }
}
