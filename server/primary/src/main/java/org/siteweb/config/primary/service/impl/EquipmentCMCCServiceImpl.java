package org.siteweb.config.primary.service.impl;

import org.siteweb.config.common.entity.TblEquipmentCMCC;
import org.siteweb.config.common.mapper.TblEquipmentCMCCMapper;
import org.siteweb.config.common.vo.binterface.EquipmentCMCCVO;
import org.siteweb.config.primary.service.EquipmentCMCCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EquipmentCMCCServiceImpl implements EquipmentCMCCService {
    @Autowired
    TblEquipmentCMCCMapper equipmentCMCCMapper;

    @Override
    public List<EquipmentCMCCVO> findAllVO(){
        return equipmentCMCCMapper.findAllVO();
    }

    @Override
    public int update(TblEquipmentCMCC tblEquipmentCMCC) {
        return equipmentCMCCMapper.updateEntity(tblEquipmentCMCC);
    }
}
