package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.AcrossMonitorUnitSignalDTO;
import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.siteweb.config.common.dto.SignalConfigItem;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.mapper.TslAcrossMonitorUnitSignalMapper;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/11
 */
@Service
public class TslAcrossMonitorUnitSignalServiceImpl implements TslAcrossMonitorUnitSignalService {

    @Autowired
    private TslAcrossMonitorUnitSignalMapper tslAcrossMonitorUnitSignalMapper;

    @Autowired
    private I18n i18n;

    @Autowired
    SignalService signalService;

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    StationService stationService;

    @Autowired
    MonitorUnitService monitorUnitService;

    @Autowired
    ResourceStructureService resourceStructureService;


    @Override
    public void createAcrossMonitorUnitSignal(TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal) {
        TblEquipment equipment = equipmentService.findEquipmentById(tslAcrossMonitorUnitSignal.getEquipmentId());
        if (equipment == null) {
            throw new BusinessException(i18n.T("monitor.equipment.doesNotExist"));
        }
        SignalConfigItem signal = signalService.findByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), tslAcrossMonitorUnitSignal.getSignalId());
        if (signal == null) {
            throw new BusinessException(i18n.T("monitor.signal.not.exist"));
        }
        if (signal.getChannelNo() != -2) {
            throw new BusinessException(i18n.T("monitor.signal.expression.across.site"));
        }
        if (StrUtil.isEmpty(tslAcrossMonitorUnitSignal.getExpression())) {
            throw new BusinessException(i18n.T("monitor.signal.expression.empty"));
        }
        updateOrInsert(tslAcrossMonitorUnitSignal);
    }

    @Override
    public void updateOrInsert(TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal) {
        TslAcrossMonitorUnitSignal acrossMonitorUnitSignal = tslAcrossMonitorUnitSignalMapper.selectOne(new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>()
                .eq(TslAcrossMonitorUnitSignal::getStationId, tslAcrossMonitorUnitSignal.getStationId())
                .eq(TslAcrossMonitorUnitSignal::getEquipmentId, tslAcrossMonitorUnitSignal.getEquipmentId())
                .eq(TslAcrossMonitorUnitSignal::getMonitorUnitId, tslAcrossMonitorUnitSignal.getMonitorUnitId())
                .eq(TslAcrossMonitorUnitSignal::getSignalId, tslAcrossMonitorUnitSignal.getSignalId()));
        if (acrossMonitorUnitSignal != null) {
            tslAcrossMonitorUnitSignalMapper.update(tslAcrossMonitorUnitSignal, new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>()
                    .eq(TslAcrossMonitorUnitSignal::getStationId, tslAcrossMonitorUnitSignal.getStationId())
                    .eq(TslAcrossMonitorUnitSignal::getEquipmentId, tslAcrossMonitorUnitSignal.getEquipmentId())
                    .eq(TslAcrossMonitorUnitSignal::getMonitorUnitId, tslAcrossMonitorUnitSignal.getMonitorUnitId())
                    .eq(TslAcrossMonitorUnitSignal::getSignalId, tslAcrossMonitorUnitSignal.getSignalId()));
            return;
        }
        tslAcrossMonitorUnitSignalMapper.insert(tslAcrossMonitorUnitSignal);
    }

    @Override
    public AcrossMonitorUnitSignalDTO findByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal = tslAcrossMonitorUnitSignalMapper.selectOne(new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>()
                .eq(TslAcrossMonitorUnitSignal::getEquipmentId, equipmentId)
                .eq(TslAcrossMonitorUnitSignal::getSignalId, signalId));
        if (tslAcrossMonitorUnitSignal != null) {
            AcrossMonitorUnitSignalDTO acrossMonitorUnitSignalDTO = new AcrossMonitorUnitSignalDTO();
            TblEquipment equipment = equipmentService.findEquipmentById(tslAcrossMonitorUnitSignal.getEquipmentId());
            acrossMonitorUnitSignalDTO.setEquipmentId(tslAcrossMonitorUnitSignal.getEquipmentId());
            acrossMonitorUnitSignalDTO.setEquipmentName(equipment.getEquipmentName());
            acrossMonitorUnitSignalDTO.setStationId(tslAcrossMonitorUnitSignal.getStationId());
            TblStation station = stationService.findByStationId(tslAcrossMonitorUnitSignal.getStationId());
            acrossMonitorUnitSignalDTO.setStationName(station.getStationName());
            acrossMonitorUnitSignalDTO.setMonitorUnitId(tslAcrossMonitorUnitSignal.getMonitorUnitId());
            MonitorUnitDTO monitorUnitDTO = monitorUnitService.findById(tslAcrossMonitorUnitSignal.getMonitorUnitId());
            acrossMonitorUnitSignalDTO.setMonitorUnitName(monitorUnitDTO.getMonitorUnitName());
            acrossMonitorUnitSignalDTO.setEquipmentId(tslAcrossMonitorUnitSignal.getEquipmentId());
            SignalConfigItem signal = signalService.findByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), tslAcrossMonitorUnitSignal.getSignalId());
            acrossMonitorUnitSignalDTO.setSignalId(tslAcrossMonitorUnitSignal.getSignalId());
            acrossMonitorUnitSignalDTO.setSignalName(signal.getSignalName());
            acrossMonitorUnitSignalDTO.setExpression(tslAcrossMonitorUnitSignal.getExpression());
            List<ResourceStructure> resourceStructures = resourceStructureService.getStructureByStationId(tslAcrossMonitorUnitSignal.getStationId());
            if (resourceStructures != null && !resourceStructures.isEmpty()) {
                acrossMonitorUnitSignalDTO.setResourceStructureId(resourceStructures.get(0).getResourceStructureId());
                acrossMonitorUnitSignalDTO.setResourceStructureName(resourceStructures.get(0).getResourceStructureName());
            }
            return acrossMonitorUnitSignalDTO;
        } else {
            // 查询为空也要返回
            AcrossMonitorUnitSignalDTO acrossMonitorUnitSignalDTO = new AcrossMonitorUnitSignalDTO();
            TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
            acrossMonitorUnitSignalDTO.setEquipmentId(equipmentId);
            acrossMonitorUnitSignalDTO.setEquipmentName(equipment.getEquipmentName());
            SignalConfigItem signal = signalService.findByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), signalId);
            acrossMonitorUnitSignalDTO.setSignalId(signalId);
            acrossMonitorUnitSignalDTO.setSignalName(signal.getSignalName());
            TblStation station = stationService.findByStationId(equipment.getStationId());
            acrossMonitorUnitSignalDTO.setStationId(station.getStationId());
            acrossMonitorUnitSignalDTO.setStationName(station.getStationName());
            MonitorUnitDTO monitorUnit = monitorUnitService.findById(equipment.getMonitorUnitId());
            acrossMonitorUnitSignalDTO.setMonitorUnitId(monitorUnit.getMonitorUnitId());
            acrossMonitorUnitSignalDTO.setMonitorUnitName(monitorUnit.getMonitorUnitName());
            List<ResourceStructure> resourceStructures = resourceStructureService.getStructureByStationId(equipment.getStationId());
            if (resourceStructures != null && !resourceStructures.isEmpty()) {
                acrossMonitorUnitSignalDTO.setResourceStructureId(resourceStructures.get(0).getResourceStructureId());
                acrossMonitorUnitSignalDTO.setResourceStructureName(resourceStructures.get(0).getResourceStructureName());
            }
            return acrossMonitorUnitSignalDTO;
        }

    }

    @Override
    public List<TslAcrossMonitorUnitSignal> findByMonitorUnitId(Integer monitorUnitId) {
        return tslAcrossMonitorUnitSignalMapper.selectList(new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>()
                .eq(TslAcrossMonitorUnitSignal::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public List<TslAcrossMonitorUnitSignal> findByEquipmentId(Integer equipmentId) {
        return tslAcrossMonitorUnitSignalMapper.selectList(new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>()
                .eq(TslAcrossMonitorUnitSignal::getEquipmentId, equipmentId));
    }

    @Override
    public void deleteByEquipmentId(Integer equipmentId) {
        tslAcrossMonitorUnitSignalMapper.delete(Wrappers.lambdaQuery(TslAcrossMonitorUnitSignal.class).eq(TslAcrossMonitorUnitSignal::getEquipmentId,equipmentId));
    }

    @Override
    public TslAcrossMonitorUnitSignal findByStationIdAndEquipmentIdAndSignalId(Integer stationId, Integer equipmentId, Integer signalId) {
        return tslAcrossMonitorUnitSignalMapper.selectOne(Wrappers.lambdaQuery(TslAcrossMonitorUnitSignal.class)
                                                                  .eq(TslAcrossMonitorUnitSignal::getStationId, stationId)
                                                                  .eq(TslAcrossMonitorUnitSignal::getEquipmentId, equipmentId)
                                                                  .eq(TslAcrossMonitorUnitSignal::getSignalId, signalId));
    }
    @Override
    public List<TslAcrossMonitorUnitSignal> findByEquipmentIds(List<Integer> equipmentIds){
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return tslAcrossMonitorUnitSignalMapper.selectList(new LambdaQueryWrapper<TslAcrossMonitorUnitSignal>().in(TslAcrossMonitorUnitSignal::getEquipmentId, equipmentIds));
    }
}
