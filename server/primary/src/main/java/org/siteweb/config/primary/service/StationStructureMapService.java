package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblStationStructureMap;

import java.util.List;
import java.util.function.BooleanSupplier;


/**
 * <AUTHOR> (2024-03-11)
 **/
public interface StationStructureMapService {
    boolean create(TblStationStructureMap stationStructureMap);

    void deleteByStationStructureId(Integer stationStructureId);

    void deleteByStationId(Integer stationId);

    List<TblStationStructureMap> findStationStructureMapList();

    TblStationStructureMap findStationStructureMapByStationId(Integer stationId);

    List<TblStationStructureMap> findStationStructureMapByStructureId(Integer structureId);

    void update(TblStationStructureMap stationStructureMap);

    // 给定一个structureId,返回stationId list
    List<Integer> findStationIdsByStructureId(Integer structureId);

    Boolean deleteByStationIds(List<Integer> stationIds);

    boolean batchCreate(List<TblStationStructureMap> stationStructureMapList);
}
