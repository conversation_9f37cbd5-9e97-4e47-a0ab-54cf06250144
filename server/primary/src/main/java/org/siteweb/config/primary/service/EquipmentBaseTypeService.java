package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.TblEquipmentBaseType;

import java.util.List;
import java.util.Map;
import java.util.function.IntFunction;

public interface EquipmentBaseTypeService {
    List<TblEquipmentBaseType> List();

    List<EquipmentSubTypeDTO> findEquipmentSubTypeById(Integer equipmentTypeId);

    List<EquipmentSubTypeTreeDTO> buildEquipmentSubTypeTree();

    /**
     * 获取设备基类以及大类名称
     *
     *
     * <AUTHOR> (2024/4/22)
     */
    List<EquipmentBaseTypeClassDTO> findEquipmentBaseTypeAndClass();


    List<TblEquipmentBaseType> findEquipmentBaseTypeByBaseEquipmentId(Integer baseEquipmentId);

    /**
     * 获取基类设备类型树
     *
     * @param spliceFlag 拼接标志 true:拼接名称和key
     */
    List<EquipmentBaseTypeTreeDTO>  findEquipmentBaseTypeTree(Boolean spliceFlag);

    Map<Integer, String> getBaseEquipmentNameMap();

    /**
     * 构建基类树
     *
     * @param equipmentBaseType 设备类型
     * @param function 获取不同的子节点
     */
    List<BaseTypeTreeDTO> buildBaseDicTree(Integer equipmentBaseType, IntFunction<List<BaseTypeTreeDTO>> function);
}
