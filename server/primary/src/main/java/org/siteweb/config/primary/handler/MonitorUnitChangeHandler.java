package org.siteweb.config.primary.handler;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.change.ChangeRecord;
import org.siteweb.config.common.change.ObjectChangeHandlerAdapter;
import org.siteweb.config.common.entity.TslMonitorUnit;
import org.siteweb.config.primary.manager.SamplerTreeManager;
import org.siteweb.config.primary.service.MonitorUnitStateService;
import org.siteweb.config.primary.service.PortService;
import org.siteweb.config.primary.service.SamplerUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (2024-04-12)
 **/
@Slf4j
@Component
public class MonitorUnitChangeHandler extends ObjectChangeHandlerAdapter {



    @Autowired
    private MonitorUnitStateService monitorUnitStateService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private PortService portService;


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(TslMonitorUnit.class);
    }


    @Override
    public void onCreate(ChangeRecord changeRecord) {
        TslMonitorUnit monitorUnit = changeRecord.readMessageBody(TslMonitorUnit.class);
//        samplerTreeManager.updateMonitorUnit(monitorUnit, ChangeOperatorEnum.CREATE);
        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());
    }


    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        TslMonitorUnit monitorUnit = changeRecord.readMessageBody(TslMonitorUnit.class);
//        samplerTreeManager.updateMonitorUnit(monitorUnit, ChangeOperatorEnum.UPDATE);
        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        TslMonitorUnit monitorUnit = changeRecord.readMessageBody(TslMonitorUnit.class);
//        samplerTreeManager.updateMonitorUnit(monitorUnit, ChangeOperatorEnum.DELETE);
        monitorUnitStateService.updateMonitorUnit(monitorUnit.getMonitorUnitId());
        // 删除端口
        portService.deleteByMonitorUnitId(monitorUnit.getMonitorUnitId());
    }


}
