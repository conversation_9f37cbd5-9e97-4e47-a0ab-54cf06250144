package org.siteweb.config.primary.service.impl;


import org.siteweb.config.common.entity.DbVersionRecord;
import org.siteweb.config.common.mapper.DbVersionRecordMapper;
import org.siteweb.config.primary.service.DbVersionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/29
 */

@Service
public class DbVersionRecordServiceImpl implements DbVersionRecordService {

    @Autowired
    private DbVersionRecordMapper dbVersionRecordMapper;

    @Override
    public void insertDbVersionRecord(DbVersionRecord dbVersionRecord) {
        dbVersionRecordMapper.insert(dbVersionRecord);
    }
}
