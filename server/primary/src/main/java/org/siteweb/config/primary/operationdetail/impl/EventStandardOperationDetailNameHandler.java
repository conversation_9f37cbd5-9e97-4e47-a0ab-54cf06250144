package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblStandardDicEventMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EventStandardOperationDetailNameHandler extends AbstractNameHandler {

    @Autowired
    TblStandardDicEventMapper standardDicEventMapper;

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.EVENT_STANDARD;
    }

    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Integer> standardDicIds = ids.stream().map(Integer::valueOf).toList();
        return standardDicEventMapper.findNamesByIds(standardDicIds);
    }
}
