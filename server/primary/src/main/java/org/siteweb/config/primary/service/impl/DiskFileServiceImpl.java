package org.siteweb.config.primary.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.siteweb.config.common.entity.DiskFile;
import org.siteweb.config.common.mapper.DiskFileMapper;
import org.siteweb.config.primary.service.DiskFileService;
import org.siteweb.config.toolkit.config.FileServerProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Service
public class DiskFileServiceImpl implements DiskFileService {
    @Autowired
    DiskFileMapper diskFileMapper;
    @Autowired
    FileServerProperty fileServerProperty;

    @Override
    public DiskFile findById(Integer id) {
        return diskFileMapper.selectById(id);
    }

    @Override
    public DiskFile upload(String filePath, String fileName, byte[] bytes) {
        //删除库中的数据与磁盘文件
        deleteFilesFromDiskAndDatabase(filePath, List.of(fileName));
        //获取文件路径并覆盖写入
        writeDisk(filePath, fileName, bytes);
        DiskFile diskFile = DiskFile.builder().filePath(filePath).fileName(fileName).status(0).createTime(LocalDateTime.now()).build();
        int insert = diskFileMapper.insert(diskFile);
        return insert > 0 ? diskFile : null;
    }

    @Override
    public List<DiskFile> findByFilePath(String filePath) {
        return diskFileMapper.selectList(Wrappers.lambdaQuery(DiskFile.class)
                                                 .eq(DiskFile::getFilePath, filePath)
                                                 .orderByDesc(DiskFile::getCreateTime));
    }

    @Override
    public DiskFile findByFilePathAndFileName(String filePath, String fileName) {
        return diskFileMapper.selectOne(Wrappers.lambdaQuery(DiskFile.class)
                                                .eq(DiskFile::getFilePath, filePath)
                                                .eq(DiskFile::getFileName, fileName));
    }

    @Override
    public boolean diskExist(String filePath, String fileName) {
        File file = getFile(filePath, fileName);
        return FileUtil.exist(file);
    }

    @Override
    public void writeDisk(String filePath, String fileName, byte[] bytes){
        File file = getFile(filePath, fileName);
        FileUtil.writeBytes(bytes, file);
    }

    private void deleteDatabaseFileRecordsByPathAndNames(String filePath, List<String> fileNames) {
        if (CollUtil.isEmpty(fileNames)) {
            return;
        }
        diskFileMapper.delete(Wrappers.lambdaQuery(DiskFile.class)
                                      .eq(DiskFile::getFilePath, filePath)
                                      .in(DiskFile::getFileName, fileNames));
    }

    @Override
    public boolean deleteFilesFromDiskAndDatabase(String filePath, List<String> fileNameList) {
        if (CollUtil.isEmpty(fileNameList)) {
            return false;
        }
        for (String fileName : fileNameList) {
            File file = getFile(filePath, fileName);
            FileUtil.del(file);
        }
        deleteDatabaseFileRecordsByPathAndNames(filePath, fileNameList);
        return true;
    }

    @Override
    public Page<DiskFile> findPageByFilePath(Page<DiskFile> page, String filePath) {
        return diskFileMapper.selectPage(page, Wrappers.lambdaQuery(DiskFile.class)
                                                       .eq(DiskFile::getFilePath, filePath)
                                                       .orderByDesc(DiskFile::getCreateTime));
    }

    /**
     * 获取磁盘上的文件路径
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return {@link File }
     */
    @Override
    public File getFile(String filePath, String fileName) {
        Path path = Paths.get(fileServerProperty.getRootPath(), filePath, fileName.trim());
        return path.toFile();
    }

    @Override
    public void rename(String filePath, String oldName, String newName) {
        File file = getFile(filePath, oldName);
        file.renameTo(getFile(filePath, newName));
        diskFileMapper.update(Wrappers.lambdaUpdate(DiskFile.class)
                                      .set(DiskFile::getFileName, newName)
                                      .eq(DiskFile::getFilePath, filePath)
                                      .eq(DiskFile::getFileName, oldName));
    }

    /**
     * 获取磁盘上的目录路径
     *
     * @param filePath 文件路径
     * @return {@link File }
     */
    @Override
    public File getPath(String filePath) {
        Path path = Paths.get(fileServerProperty.getRootPath(), filePath);
        return path.toFile();
    }

    @Override
    public List<DiskFile> findByFileIds(Collection<Integer> ids) {
        return diskFileMapper.selectList(Wrappers.lambdaQuery(DiskFile.class).in(DiskFile::getFileId, ids));
    }
}
