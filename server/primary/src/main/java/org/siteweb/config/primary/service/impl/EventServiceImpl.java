package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.constants.EquipmentTemplateConstant;
import org.siteweb.config.common.constants.EventConstant;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EquipmentEventDTO;
import org.siteweb.config.common.dto.batchtool.EventRequestBySignalId;
import org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO;
import org.siteweb.config.common.dto.excel.EventExcel;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.*;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.enums.*;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.utils.MybatisBatchUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 事件服务实现
 *
 * <AUTHOR>
 * @date 2024/04/02
 */
@Service
public class EventServiceImpl implements EventService {
    @Autowired
    TblEventMapper eventMapper;
    @Autowired
    EventConditionService eventConditionService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    TblEquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    I18n i18n;

    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    TblEventexService eventexService;
    @Autowired
    MybatisBatchUtils mybatisBatchUtils;
    @Autowired
    EventBaseDicService eventBaseDicService;
    @Autowired
    TblStandardBackMapper standardBackMapper;
    @Autowired
    SignalService signalService;
    @Autowired
    @Lazy
    StandardPointService standardPointService;
    @Autowired
    TblEventBaseConfirmMapper eventBaseConfirmMapper;
    @Autowired
    DataItemService dataItemService;

    @Override
    public void createEvent(TblEvent event) {
        if (Objects.equals(event.getSignalId(), EventConstant.GENERATE_EVENT_ID_FLAG) || Objects.isNull(event.getEventId())) {
            Integer eventId = findMaxEventIdByEquipmentTemplateId(event.getEquipmentTemplateId());
            event.setEventId(eventId);
        }
        eventMapper.insert(event);
    }

    @Override
    public int updateEvent(TblEvent event) {
//        String oldEventName = eventMapper.selectById(event.getId()).getEventName();
//        operationDetailService.recordOperationLog(-1, String.valueOf(event.getEquipmentTemplateId()), OperationObjectTypeEnum.EVENT, i18n.T("event.name"), i18n.T("update"), oldEventName, event.getEventName());
// TODO tbl_eventex是否要更新
//        sql = string.Format(@"DELETE FROM tbl_eventex WHERE (EquipmentTemplateId = {0}) AND (EventId = {1});
//        INSERT INTO tbl_eventex (EquipmentTemplateId,EventId,turnover) VALUES ({0},{1},{2});",
//        EquipmentTemplateId, EventId, TurnOver==""?"0": TurnOver);
        return eventMapper.updateById(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEvent(Integer equipmentTemplateId, Integer eventId) {
        // 设备通讯状态事件(ID为-3)不允许删除
        if (Objects.equals(eventId, EventConstant.COMMUNICATION_STATE_EVENT)) {
            return 0;
        }
        EventConfigItem eventConfigItem = eventMapper.findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
        if (Objects.isNull(eventConfigItem)) {
            return 0;
        }
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), equipmentTemplateId + "." + eventId, OperationObjectTypeEnum.EVENT, i18n.T("event.eventName"), i18n.T("delete"), eventConfigItem.getEventName(), "");
        int result = eventMapper.deleteEvent(equipmentTemplateId, eventId);
        // 删除关联的条件表和翻转时间
        eventConditionService.deleteByEvent(equipmentTemplateId, eventId);
        eventexService.deleteByEvent(equipmentTemplateId, eventId);
        sendUpdateMsgByEquipmentTemplateId(equipmentTemplateId);
        return result;
    }

    @Override
    public void updateWorkStationEventName(String prefixName, int equipmentTemplateId) {
        eventMapper.updateWorkStationEventName(prefixName, equipmentTemplateId);
    }

    @Override
    public void updateDBWorkStationEventName(String eventName, Integer equipmentTemplateId, int EventId) {
        eventMapper.update(null, new UpdateWrapper<TblEvent>()
                .set("EventName", eventName)
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("EventId", EventId));
    }

    @Override
    public void copyEvent(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<EventConfigItem> eventConfigItemList = findEventItemByEquipmentTemplateId(originEquipmentTemplateId);
        List<TblEvent> eventList = BeanUtil.copyToList(eventConfigItemList, TblEvent.class);
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        eventList.forEach(event -> event.setEquipmentTemplateId(destEquipmentTemplateId));
        batchEvent(eventList);
        eventConditionService.copyEventCondition(originEquipmentTemplateId, destEquipmentTemplateId);
    }

    @Override
    public void updateSelfDiagnosisEvent(int equipmentTemplateId, int centerId) {
        eventMapper.updateEventIdAndStartExpressionAndSignalId(centerId, equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEventByEventItem(EventConfigItem eventConfigItem) {
        TblEvent event = BeanUtil.copyProperties(eventConfigItem, TblEvent.class, "id");
        createEvent(event);
        List<TblEventCondition> tblEventCondition = eventConfigItem.getTblEventCondition();
        tblEventCondition.forEach(e -> {
            e.setEventId(event.getEventId());
            e.setEquipmentTemplateId(event.getEquipmentTemplateId());
        });
        eventConditionService.batchCreate(tblEventCondition);
        String objectId = event.getEquipmentTemplateId() + "." + event.getEventId();
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), objectId, OperationObjectTypeEnum.EVENT, i18n.T("event.eventName"), i18n.T("add"), "", eventConfigItem.getEventName());
        sendUpdateMsgByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
        eventConfigItem.setId(event.getId());
        eventConfigItem.setEventId(event.getEventId());
        eventConfigItem.setEventConditionList(eventConfigItem.getEventConditionList(tblEventCondition));
    }

    @Override
    public void batchInsertLianTongEvents() {
        eventMapper.batchInsertLianTongEvents();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByEventItem(EventConfigItem eventConfigItem) {
        // 事件名称不能重复
        List<EventConfigItem> eventConfigItems = eventMapper.findEventItemByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
        for (EventConfigItem configItem : eventConfigItems) {
            if (Objects.equals(configItem.getEventId(), eventConfigItem.getEventId())) {
                continue;
            }
            if (Objects.equals(configItem.getEventName(), eventConfigItem.getEventName())) {
                throw new BusinessException(i18n.T("error.duplicate.name", i18n.T("monitor.event"), eventConfigItem.getEventName()));
            }
        }
        TblEvent event = BeanUtil.copyProperties(eventConfigItem, TblEvent.class);
        EventConfigItem oldEventConfig = eventMapper.findByEquipmentTemplateIdAndEventId(eventConfigItem.getEquipmentTemplateId(), eventConfigItem.getEventId());
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldEventConfig, eventConfigItem);
        eventMapper.update(event, Wrappers.lambdaUpdate(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, event.getEquipmentTemplateId())
                .eq(TblEvent::getEventId, event.getEventId()));
        eventConditionService.updateEventCondition(eventConfigItem.getEquipmentTemplateId(), eventConfigItem.getEventId(), eventConfigItem.getTblEventCondition());
        eventexService.updateEventx(eventConfigItem.getEquipmentTemplateId(), eventConfigItem.getEventId(), eventConfigItem.getTurnover());
        sendUpdateMsgByEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateByEventItem(BatchEventConfigItem batchEventConfigItem) {
        List<EventConfigItem> eventConfigItemList = batchEventConfigItem.getEventConfigItemList();
        if (CollUtil.isEmpty(eventConfigItemList)) {
            return;
        }
        List<TblEvent> eventList = new ArrayList<>(batchEventConfigItem.getEventConfigItemList().size());
        List<TblEventex> eventexList = new ArrayList<>(batchEventConfigItem.getEventConfigItemList().size());
        List<TblEventCondition> eventConditionList = new ArrayList<>(batchEventConfigItem.getEventConfigItemList().size());
        List<EventConfigItem> oldEventConfigItemList = eventMapper.findEventItemByEquipmentTemplateId(batchEventConfigItem.getEquipmentTemplateId());
        for (EventConfigItem eventConfigItem : eventConfigItemList) {
            EventConfigItem oldEventConfig = oldEventConfigItemList.stream().filter(f -> Objects.equals(f.getEquipmentTemplateId(), eventConfigItem.getEquipmentTemplateId())
                    && Objects.equals(f.getEventId(), eventConfigItem.getEventId())).findFirst().orElse(null);
            operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldEventConfig, eventConfigItem);
            TblEvent event = BeanUtil.copyProperties(eventConfigItem, TblEvent.class);
            eventList.add(event);
            TblEventex eventEx = TblEventex.builder()
                    .equipmentTemplateId(eventConfigItem.getEquipmentTemplateId())
                    .eventId(eventConfigItem.getEventId())
                    .turnover(eventConfigItem.getTurnover())
                    .build();
            eventexList.add(eventEx);
            eventConditionList.addAll(eventConfigItem.getTblEventCondition());
        }
        //批量更新告警
        eventMapper.batchUpdate(eventList);
        //批量更新告警条件
        eventConditionService.batchUpdate(eventConditionList);
        //批量更新事件拓展表
        eventexService.batchUpdate(eventexList);
        //模板变更通知
        sendUpdateMsgByEquipmentTemplateId(batchEventConfigItem.getEquipmentTemplateId());
    }

    @Override
    public List<TblEvent> diffEvent(Integer oldEquipmentTemplateId, Integer newEquipmentTemplateId) {
        return eventMapper.diffEvent(oldEquipmentTemplateId, newEquipmentTemplateId);
    }

    @Override
    public List<SimplifyEventAndConditionDTO> findSimplifyEventAndCondition(Integer equipmentTemplateId) {
        List<EventConfigItem> eventConfigItems = eventMapper.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        if (CollUtil.isEmpty(eventConfigItems)) {
            return ListUtil.empty();
        }
        return eventConfigItems.stream().flatMap(event -> event.getEventConditionList().stream()
                        .map(condition -> SimplifyEventAndConditionDTO.builder()
                                .equipmentTemplateId(event.getEquipmentTemplateId())
                                .eventId(event.getEventId())
                                .eventName(event.getEventName())
                                .eventConditionId(condition.getEventConditionId())
                                .meanings(condition.getMeanings())
                                .build()))
                .sorted(CompareUtil.comparingPinyin(SimplifyEventAndConditionDTO::getEventName))
                .toList();
    }

    @Override
    public Double getEventProgress(Integer equipmentTemplateId, Map<Integer, List<EventProgressDTO>> eventProgressMap) {
        List<EventProgressDTO> eventProgressList = eventProgressMap.get(equipmentTemplateId);
        if (CollUtil.isEmpty(eventProgressList)) {
            return 100.0;
        }
        int count = 0;
        for (EventProgressDTO eventProgressDTO : eventProgressList) {
            if (Objects.nonNull(eventProgressDTO.getBaseTypeId()) || EquipmentTemplateConstant.CONFIRM_FLAG.equals(eventProgressDTO.getSubState())) {
                count++;
            }
        }
        return NumberUtil.div(Math.multiplyExact(count, 100), eventProgressList.size(), 2);
    }

    @Override
    public Map<Integer, List<EventProgressDTO>> findEventProgressMap() {
        List<EventProgressDTO> eventProgressList = eventMapper.findEventProgressList();
        List<TblEventBaseConfirm> tblEventBaseConfirms = eventBaseConfirmMapper.selectList(Wrappers.emptyWrapper());
        Map<String, String> confirmMap = tblEventBaseConfirms.stream()
                .collect(Collectors.toMap(
                        confirm -> buildConfirmKey(confirm.getEquipmentTemplateId(), confirm.getEventId(), confirm.getEventConditionId()),
                        TblEventBaseConfirm::getSubState,
                        (existing, replacement) -> existing
                ));
        return eventProgressList.parallelStream().peek(m ->
                        m.setSubState(confirmMap.getOrDefault(buildConfirmKey(m.getEquipmentTemplateId(), m.getEventId(), m.getEventConditionId()), null)))
                .collect(Collectors.groupingBy(EventProgressDTO::getEquipmentTemplateId));
    }

    @Override
    public BaseClassStatisticsDTO<EventBaseClassDTO> findEventBaseClassList(Integer equipmentBaseType) {
        List<EventBaseClassDetailDTO> eventBaseClassDetails = eventMapper.findEventBaseClassList(equipmentBaseType);
        buildEventBaseClassDetail(eventBaseClassDetails);
        Map<String, List<EventBaseClassDetailDTO>> collectMap = eventBaseClassDetails.stream()
                .collect(Collectors.groupingBy(g -> String.format("%s%s%s", g.getEquipmentBaseType(), g.getEventName(), g.getMeanings())));
        AtomicInteger confirmCount = new AtomicInteger(0);
        AtomicInteger sumCount = new AtomicInteger(0);
        List<EventBaseClassDTO> dataList = collectMap.values().parallelStream().map(eventBaseClassList -> {
                    // 基于模板名称排序，取第一条数据拼接
                    eventBaseClassList.sort(Comparator.comparing(EventBaseClassDetailDTO::getEquipmentTemplateName, Comparator.nullsLast(Comparator.naturalOrder())));
                    Optional<EventBaseClassDetailDTO> baseClassOptional = eventBaseClassList.stream()
                            .filter(item -> Objects.nonNull(item.getBaseTypeId())).findFirst();
                    EventBaseClassDetailDTO eventBaseClassDetailDTO = baseClassOptional.orElseGet(() -> eventBaseClassList.stream().findFirst().orElse(new EventBaseClassDetailDTO()));
                    EventBaseClassDTO eventBaseClassDTO = BeanUtil.copyProperties(eventBaseClassDetailDTO, EventBaseClassDTO.class);
                    sumCount.getAndAdd(eventBaseClassList.size());
                    eventBaseClassDTO.setEventNumber(eventBaseClassList.size());
                    eventBaseClassDTO.setChildState(getChildState(eventBaseClassList, confirmCount));
                    return eventBaseClassDTO;
                })
                .sorted(Comparator.comparing(EventBaseClassDTO::getEquipmentBaseType).thenComparing(EventBaseClassDTO::getEventName, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(EventBaseClassDTO::getMeanings, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
        BaseClassStatisticsDTO<EventBaseClassDTO> result = new BaseClassStatisticsDTO<>();
        result.setConfirmCount(confirmCount.get());
        result.setSumCount(sumCount.get());
        result.setDataList(dataList);
        return result;
    }

    private void buildEventBaseClassDetail(List<EventBaseClassDetailDTO> eventBaseClassList) {
        Map<Integer, String> eventCategoryMap = dataItemService.findMapByEntryId(DataEntryEnum.EVENT_CATEGORY);
        Map<Integer, String> eventLevelMap = dataItemService.findMapByEntryId(DataEntryEnum.EVENT_LEVEL);
        List<TblEventBaseConfirm> tblEventBaseConfirms = eventBaseConfirmMapper.selectList(Wrappers.emptyWrapper());
        Map<String, String> confirmMap = tblEventBaseConfirms.stream()
                .collect(Collectors.toMap(
                        confirm -> buildConfirmKey(confirm.getEquipmentTemplateId(), confirm.getEventId(), confirm.getEventConditionId()),
                        TblEventBaseConfirm::getSubState,
                        (existing, replacement) -> existing // 处理重复 key，保留第一个
                ));
        eventBaseClassList.forEach(bs -> {
            bs.setEventCategoryName(eventCategoryMap.get(bs.getEventCategory()));
            bs.setEventSeverityName(eventLevelMap.get(bs.getEventSeverity()));
            bs.setSubState(confirmMap.getOrDefault(buildConfirmKey(bs.getEquipmentTemplateId(), bs.getEventId(), bs.getEventConditionId()), null));
        });
        eventBaseClassList.sort(Comparator.comparing(EventBaseClassDetailDTO::getEquipmentBaseType, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(EventBaseClassDetailDTO::getEventName, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(EventBaseClassDetailDTO::getMeanings, Comparator.nullsLast(Comparator.naturalOrder())));
    }

    private String buildConfirmKey(Integer equipmentTemplateId, Integer eventId, Integer eventConditionId) {
        return equipmentTemplateId + "_" + eventId + "_" + Objects.toString(eventConditionId, "");
    }

    /**
     * 获取子项情况
     * 子类下面一个没填就是‘全部空’，如果有部分检查的数据就是‘已检查Part’，全是检查的数据就是‘已检查ALL’。
     * 子类下面有填的数据，如果全部都填了且数据一样就是‘全相同’，有不一样就是‘有不同’，如果有没填的就是‘有遗漏’，如果没填的都检查了则是‘有遗漏OK’
     *
     * @param confirmCount 确认数量 已检查和已填基类的数据
     */
    private String getChildState(List<EventBaseClassDetailDTO> eventBaseClassDTOList, AtomicInteger confirmCount) {
        if (eventBaseClassDTOList.stream().anyMatch(eventBaseClassDTO -> Objects.nonNull(eventBaseClassDTO.getBaseTypeId()))) {
            boolean omitJudge = false;
            boolean omitOkJudge = true;
            boolean sameJudge = true;
            for (EventBaseClassDetailDTO baseClassDTO : eventBaseClassDTOList) {
                if (Objects.isNull(baseClassDTO.getBaseTypeId())) {
                    if (CharSequenceUtil.isBlank(baseClassDTO.getSubState())) {
                        omitOkJudge = false;
                    }
                    omitJudge = true;
                }
                if (Objects.nonNull(baseClassDTO.getBaseTypeId()) || CharSequenceUtil.isNotBlank(baseClassDTO.getSubState())) {
                    confirmCount.getAndIncrement();
                }
                EventBaseClassDetailDTO eventBaseClass1 = eventBaseClassDTOList.get(0);
                sameJudge = sameJudge && (Objects.equals(baseClassDTO.getBaseTypeId(), eventBaseClass1.getBaseTypeId()));
            }
            if (omitOkJudge && omitJudge) {
                return "有遗漏OK";
            }
            if (omitJudge) {
                return "有遗漏";
            }
            if (sameJudge) {
                return "全相同";
            } else {
                return "有不同";
            }
        } else {
            boolean emptyJudge = true;
            boolean confirmJudge = true;
            for (EventBaseClassDetailDTO baseClassDTO : eventBaseClassDTOList) {
                emptyJudge = emptyJudge && CharSequenceUtil.isBlank(baseClassDTO.getSubState());
                confirmJudge = confirmJudge && CharSequenceUtil.isNotBlank(baseClassDTO.getSubState());
                if (CharSequenceUtil.isNotBlank(baseClassDTO.getSubState())) {
                    // 存在检查
                    confirmCount.getAndIncrement();
                }
            }
            if (emptyJudge) {
                return "全部空";
            }
            if (confirmJudge) {
                return "已检查ALL";
            }
            return "已检查Part";

        }
    }

    @Override
    public List<EventBaseClassDetailDTO> findEventBaseClassDetails(Integer equipmentBaseType, String eventName, String meanings) {
        List<EventBaseClassDetailDTO> eventBaseClassDetails = findEventBaseClassDetailList(equipmentBaseType, eventName, meanings);
        if (CollUtil.isEmpty(eventBaseClassDetails)) {
            return Collections.emptyList();
        }
        eventBaseClassDetails.sort(Comparator.comparing(EventBaseClassDetailDTO::getEquipmentTemplateName, Comparator.nullsLast(Comparator.naturalOrder())));
        return eventBaseClassDetails;
    }

    private List<EventBaseClassDetailDTO> findEventBaseClassDetailList(Integer equipmentBaseType, String eventName, String meanings) {
        List<EventBaseClassDetailDTO> eventBaseClassDetails = eventMapper.findEventBaseClassDetails(equipmentBaseType, eventName, meanings);
        buildEventBaseClassDetail(eventBaseClassDetails);
        return eventBaseClassDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearEventBaseType(List<EventBaseTypeConditionDTO> eventBaseTypeConditionDTOS) {
        if (CollUtil.isEmpty(eventBaseTypeConditionDTOS)) {
            return;
        }
        List<EventBaseClassDetailDTO> eventBaseClassDetails = eventMapper.findEventBaseClassList(eventBaseTypeConditionDTOS.get(0).getEquipmentBaseType());
        List<EventBaseCondDTO> eventBaseCondDTOS = eventBaseClassDetails.stream()
                .filter(detail -> eventBaseTypeConditionDTOS.stream().anyMatch(condition ->
                        (Objects.equals(condition.getEquipmentBaseType(), detail.getEquipmentBaseType())) &&
                                (Objects.equals(condition.getEventName(), detail.getEventName())) &&
                                (Objects.equals(condition.getMeanings(), detail.getMeanings()))
                ))
                .map(m -> EventBaseCondDTO.builder().equipmentTemplateId(m.getEquipmentTemplateId())
                        .eventId(m.getEventId())
                        .eventConditionId(m.getEventConditionId())
                        .eventName(m.getEventName())
                        .baseTypeId(null)
                        .build())
                .toList();
        if (CollUtil.isEmpty(eventBaseCondDTOS)) {
            return;
        }
        // 清除基类id以及含义
        updateBaseType(eventBaseCondDTOS, Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEventBaseTypeAndConfirm(BaseTypeCondIdDTO<EventBaseCondDTO> eventBaseTypeCondDTO) {
        List<EventBaseCondDTO> eventConditions = eventBaseTypeCondDTO.getConditionIds();
        if (CollUtil.isEmpty(eventConditions)) {
            return;
        }
        if (Boolean.TRUE.equals(eventBaseTypeCondDTO.getStateFlag())) {
            // 修改检查确认情况
            updateConfirm(eventBaseTypeCondDTO);
        } else {
            eventConditions.forEach(eventCondition -> eventCondition.setBaseTypeId(eventBaseTypeCondDTO.getBaseTypeId()));
            // 修改基类id
            updateBaseType(eventConditions, Boolean.TRUE.equals(eventBaseTypeCondDTO.getCoverFlag()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disposeSimilarEvent(SimilarDataDTO similarEventDTO) {
        Integer startNumber = similarEventDTO.getStartNumber();
        Integer abortNumber = similarEventDTO.getAbortNumber();
        if (Objects.isNull(startNumber) || startNumber < 0 || startNumber > abortNumber) {
            throw new InvalidParameterException("startNumber Exception");
        }
        if (abortNumber > 999) {
            throw new InvalidParameterException(i18n.T("abortNumber.format"));
        }
        String wildcard = similarEventDTO.getWildcard();
        if (StringUtils.isBlank(wildcard) || !wildcard.contains("{0}")) {
            throw new InvalidParameterException(i18n.T("wildcard.format"));
        }
        startNumber = Math.max(startNumber, 1);
        abortNumber = Math.max(abortNumber, 1);
        List<EventBaseCondDTO> eventBaseCondList;
        if (Boolean.TRUE.equals(similarEventDTO.getChildFlag())) {
            // 子类相似事件条件设置 找同模板同事件条件的数据
            eventBaseCondList = Optional.ofNullable(eventMapper.findEventItemByEquipmentTemplateId(similarEventDTO.getEquipmentTemplateId())).orElseGet(ArrayList::new)
                    .stream()
                    .flatMap(eventConfigItem -> eventConfigItem.getEventConditionList().stream()
                            .filter(eventCond -> Objects.equals(eventCond.getMeanings(), similarEventDTO.getMeanings()))
                            .map(eventCond -> {
                                EventBaseCondDTO eventConditionDTO = new EventBaseCondDTO();
                                eventConditionDTO.setEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
                                eventConditionDTO.setEventId(eventConfigItem.getEventId());
                                eventConditionDTO.setEventName(eventConfigItem.getEventName());
                                eventConditionDTO.setEventConditionId(eventCond.getEventConditionId());
                                return eventConditionDTO;
                            })).toList();
        } else {
            // 父类相似事件条件设置 通过基类设备类型和事件条件从缓存拿到对应数据
            eventBaseCondList = findEventBaseClassDetailList(similarEventDTO.getEquipmentBaseType(), null, similarEventDTO.getMeanings())
                    .stream()
                    .map(tblEvent -> {
                                EventBaseCondDTO eventBaseCondDTO = new EventBaseCondDTO();
                                eventBaseCondDTO.setEquipmentTemplateId(tblEvent.getEquipmentTemplateId());
                                eventBaseCondDTO.setEventId(tblEvent.getEventId());
                                eventBaseCondDTO.setEventName(tblEvent.getEventName());
                                eventBaseCondDTO.setEventConditionId(tblEvent.getEventConditionId());
                                return eventBaseCondDTO;
                            }
                    )
                    .toList();
        }
        if (CollUtil.isEmpty(eventBaseCondList)) {
            return;
        }
        // 获取事件名字递增的数据
        List<EventBaseCondDTO> eventBaseCondDTOList = new ArrayList<>();
        long sourceBaseTypeId = (similarEventDTO.getBaseTypeId() / 1000) * 1000;
        int abortNumberMax = 0;
        for (; startNumber <= abortNumber; startNumber++) {
            String eventName = CharSequenceUtil.indexedFormat(similarEventDTO.getWildcard(), startNumber);
            String eventName2 = CharSequenceUtil.indexedFormat(similarEventDTO.getWildcard(), String.format("%02d", startNumber));
            String eventName3 = CharSequenceUtil.indexedFormat(similarEventDTO.getWildcard(), String.format("%03d", startNumber));
            List<EventBaseCondDTO> eventConditions = eventBaseCondList.stream().filter(f ->
                    CharSequenceUtil.equalsAny(f.getEventName(), eventName, eventName2, eventName3)
            ).toList();
            if (CollUtil.isEmpty(eventConditions)) {
                continue;
            }
            abortNumberMax = Math.max(abortNumberMax, startNumber);
            // BaseTypeId递增
            long handleBaseTypeId = sourceBaseTypeId + startNumber;
            for (EventBaseCondDTO eventCondition : eventConditions) {
                eventCondition.setBaseTypeId(handleBaseTypeId);
            }
            eventBaseCondDTOList.addAll(eventConditions);
        }
        if (CollUtil.isEmpty(eventBaseCondDTOList)) {
            return;
        }
        // BaseTypeId会递增，不存在则会新增，只根据匹配到的最大名称数字来新增
        eventBaseDicService.batchAddEventBaseDic(similarEventDTO.getBaseTypeId(), similarEventDTO.getStartNumber(), abortNumberMax);
        // 修改基类id
        updateBaseType(eventBaseCondDTOList, Boolean.TRUE.equals(similarEventDTO.getCoverFlag()));
    }

    @Override
    public void clearBaseTypeByEquipmentTemplate(Integer equipmentTemplateId) {
        List<EventBaseCondDTO> eventBaseCondDTOS = Optional.ofNullable(eventMapper.findEventItemByEquipmentTemplateId(equipmentTemplateId))
                .orElseGet(ArrayList::new)
                .stream()
                .flatMap(eventConfigItem -> eventConfigItem.getEventConditionList().stream()
                        .map(eventCond -> {
                            EventBaseCondDTO eventConditionDTO = new EventBaseCondDTO();
                            eventConditionDTO.setEquipmentTemplateId(eventConfigItem.getEquipmentTemplateId());
                            eventConditionDTO.setEventId(eventConfigItem.getEventId());
                            eventConditionDTO.setEventConditionId(eventCond.getEventConditionId());
                            return eventConditionDTO;
                        })).toList();
        if (CollUtil.isEmpty(eventBaseCondDTOS)) {
            return;
        }
        mybatisBatchUtils.batchUpdateOrInsert(eventBaseCondDTOS, TblEventConditionMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEventCondition.class)
                        .eq(TblEventCondition::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEventCondition::getEventId, item.getEventId())
                        .eq(TblEventCondition::getEventConditionId, item.getEventConditionId())
                        .set(TblEventCondition::getBaseTypeId, null)
                ));
    }

    @Override
    public EventConfigItem findMaxEventByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventMapper.findMaxEventByEquipmentTemplateId(equipmentTemplateId);
    }

    /**
     * 修改基类id
     *
     * @param coverFlag 覆盖标志 true则覆盖已有值的  false不覆盖
     */
    private void updateBaseType(List<EventBaseCondDTO> eventConditions, boolean coverFlag) {
        List<EventBaseCondDTO> conditions;
        List<EventConfigItem> eventItems = eventMapper.findEventItemByEquipmentTemplateIds(eventConditions.stream().map(EventBaseCondDTO::getEquipmentTemplateId).collect(Collectors.toSet()));
        if (Boolean.FALSE.equals(coverFlag)) {
            // 不覆盖，仅赋值未设置的基类ID的数据
            conditions = eventConditions.stream().flatMap(eventBaseCond ->
                    eventItems.stream().filter(f -> Objects.equals(f.getEquipmentTemplateId(), eventBaseCond.getEquipmentTemplateId())
                                    && Objects.equals(f.getEventId(), eventBaseCond.getEventId()))
                            .findFirst()
                            .map(EventConfigItem::getEventConditionList).orElseGet(ArrayList::new)
                            .stream()
                            .filter(eventConditionDTO -> Objects.equals(eventConditionDTO.getEventConditionId(), eventBaseCond.getEventConditionId())
                                    && Objects.isNull(eventConditionDTO.getBaseTypeId()))
                            .map(eventConditionDTO -> EventBaseCondDTO.builder()
                                    .equipmentTemplateId(eventBaseCond.getEquipmentTemplateId())
                                    .eventId(eventBaseCond.getEventId())
                                    .eventConditionId(eventBaseCond.getEventConditionId())
                                    .baseTypeId(eventBaseCond.getBaseTypeId())
                                    .build())
            ).toList();
        } else {
            conditions = eventConditions;
        }
        if (CollUtil.isEmpty(conditions)) {
            return;
        }
        // 设置基类
        mybatisBatchUtils.batchUpdateOrInsert(conditions, TblEventConditionMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEventCondition.class)
                        .eq(TblEventCondition::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEventCondition::getEventId, item.getEventId())
                        .eq(TblEventCondition::getEventConditionId, item.getEventConditionId())
                        .set(TblEventCondition::getBaseTypeId, item.getBaseTypeId())
                ));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 通知缓存
                List<TblEquipmentTemplate> equipmentTemplateList = equipmentTemplateMapper.selectBatchIds(conditions
                        .stream().map(EventBaseCondDTO::getEquipmentTemplateId).collect(Collectors.toSet()));
                changeEventService.sendBatchUpdate(equipmentTemplateList);
            }
        });
    }

    /**
     * 修改检查确认情况
     */
    private void updateConfirm(BaseTypeCondIdDTO<EventBaseCondDTO> eventBaseTypeCondDTO) {
        List<EventBaseCondDTO> eventConditions = eventBaseTypeCondDTO.getConditionIds();
        mybatisBatchUtils.batchUpdateOrInsert(eventConditions, TblEventBaseConfirmMapper.class, (item, mapper) ->
                mapper.delete(Wrappers.lambdaUpdate(TblEventBaseConfirm.class)
                        .eq(TblEventBaseConfirm::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEventBaseConfirm::getEventId, item.getEventId())
                        .eq(TblEventBaseConfirm::getEventConditionId, item.getEventConditionId())
                ));
        // 设置基类情况为已检查
        if (CharSequenceUtil.isNotBlank(eventBaseTypeCondDTO.getSubState())) {
            mybatisBatchUtils.batchUpdateOrInsert(eventConditions, TblEventBaseConfirmMapper.class, (item, mapper) ->
                    mapper.insert(TblEventBaseConfirm.builder()
                            .equipmentTemplateId(item.getEquipmentTemplateId())
                            .eventId(item.getEventId())
                            .eventConditionId(item.getEventConditionId())
                            .subState(eventBaseTypeCondDTO.getSubState())
                            .build())
            );
        }
    }

    @Override
    public List<EquipmentEventDTO> findEventsByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds) {
        if (CollUtil.isEmpty(eventIds)) {
            return new ArrayList<>();
        }
        return eventMapper.findEventsByEquipmentIdAndEventIds(equipmentId, eventIds);
    }

    @Override
    public List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(EventRequestBySignalId eventRequestBySignalId) {
        return eventMapper.findEventsByEquipmentIdAndSignalIds(eventRequestBySignalId);
    }

    /**
     * xsx
     *
     * @param equipmentTemplateId
     * @param eventIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteEvent(int equipmentTemplateId, List<Integer> eventIds) {
        if (ObjectUtil.isEmpty(equipmentTemplateId) || CollectionUtil.isEmpty(eventIds)) return false;
        /**
         * 告警条件
         */
        eventConditionService.batchDelete(equipmentTemplateId, eventIds);
        /**
         * 告警
         */
        QueryWrapper<TblEvent> queryWrapper = new QueryWrapper<>();
        StringBuilder sql = new StringBuilder("(EquipmentTemplateId,EventId) in (");
        eventIds.forEach(id -> {
            sql.append("(").append(equipmentTemplateId).append(",").append(id).append("),");
        });
        sql.deleteCharAt(sql.length() - 1).append(")");
        ;
        queryWrapper.apply(sql.toString());
        eventMapper.delete(queryWrapper);
        // 记录操作日志
        eventIds.forEach(eventId -> operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), equipmentTemplateId + "." + eventId, OperationObjectTypeEnum.EVENT, i18n.T("monitor.event"), i18n.T("delete"), String.valueOf(eventId), ""));
        sendUpdateMsgByEquipmentTemplateId(equipmentTemplateId);
        return true;
    }

    @Override
    public EventConfigItem getEventInfo(Integer equipmentTemplateId, Integer eventId) {
        if (ObjectUtil.isEmpty(equipmentTemplateId) || ObjectUtil.isEmpty(eventId)) {
            return null;
        }
        QueryWrapper<TblEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("EventId", eventId);
        List<TblEvent> tblEvents = eventMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(tblEvents)) {
            return null;
        }
        EventConfigItem eventConfigItem = new EventConfigItem();
        BeanUtil.copyProperties(tblEvents.get(0), eventConfigItem);
        List<TblEventCondition> tblEventConditionList = eventConditionService.findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
        if (CollectionUtil.isNotEmpty(tblEventConditionList)) {
            List<EventConditionDTO> eventConditionDTOList = new ArrayList<>();
            tblEventConditionList.forEach(e -> {
                EventConditionDTO eventConditionDTO = new EventConditionDTO();
                BeanUtil.copyProperties(e, eventConditionDTO);
                eventConditionDTOList.add(eventConditionDTO);
            });
            eventConfigItem.setEventConditionList(eventConditionDTOList);
        }
        return eventConfigItem;
    }

    @Override
    public EventConfigItem findByEquipmentTemplateIdAndEventId(Integer equipmentTemplateId, Integer eventId) {
        return eventMapper.findByEquipmentTemplateIdAndEventId(equipmentTemplateId, eventId);
    }

    @Override
    public List<TblEvent> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventMapper.selectList(new QueryWrapper<TblEvent>().eq("EquipmentTemplateId", equipmentTemplateId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long applyStandard(Integer standardId, List<Integer> equipmentTemplateIds) {
        // 标准化操作前备份
        standardBackMapper.delete(Wrappers.lambdaUpdate(TblStandardBack.class).eq(TblStandardBack::getEntryCategory, 2));
        standardBackMapper.backupEventStandard(standardId, equipmentTemplateIds);
        // 查询要标准化的数据
        List<EventApplyStandardDTO> applyStandards = eventMapper.getApplyStandards(standardId, equipmentTemplateIds);
        if (CollUtil.isEmpty(applyStandards)) {
            return 0L;
        }
        mybatisBatchUtils.batchUpdateOrInsert(applyStandards, TblEventConditionMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEventCondition.class)
                        .eq(TblEventCondition::getId, item.getId())
                        .set(TblEventCondition::getStandardName, item.getStandardName())
                        .set(TblEventCondition::getEventSeverity, item.getEventSeverity())
                        .set(TblEventCondition::getStartDelay, item.getStartDelay())
                        .set(TblEventCondition::getStartCompareValue, item.getStartCompareValue())
                        .set(TblEventCondition::getMeanings, item.getMeanings())
                ));
        return (long) applyStandards.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long restoreStandard() {
        long count = eventMapper.restoreStandard();
        standardBackMapper.delete(Wrappers.lambdaUpdate(TblStandardBack.class).eq(TblStandardBack::getEntryCategory, 2));
        return count;
    }

    @Override
    public List<StandardEventCompareDTO> getStandardCompareData() {
        return eventMapper.getStandardCompareData();
    }

    @Override
    public List<StandardApplyEventCheckDTO> getEventStandardApplyCheckData(Integer standardId) {
        return eventMapper.getEventStandardApplyCheckData(standardId);
    }

    @Override
    public List<StandardMappingEventCheckDTO> getEventStandardMappingCheck(Integer standardId, Integer equipmentCategory) {
        return eventMapper.getEventStandardMappingCheck(standardId, equipmentCategory);
    }

    @Override
    public void applyEventPointMapping(StandardPointMappingDTO mappingDTO) {
        String desLike = String.format("%d-%d", mappingDTO.getEquipmentCategoryId(), mappingDTO.getPointId());
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(mappingDTO.getEquipmentTemplateId());
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("设备模板不存在");
        }
        if (!Objects.equals(equipmentTemplate.getEquipmentCategory(), mappingDTO.getEquipmentCategoryId())) {
            throw new BusinessException("不同设备类型的数据不能绑定");
        }
        if (Objects.isNull(mappingDTO.getExtendStartNum())) {
            TblEvent event = eventMapper.selectOne(Wrappers.lambdaQuery(TblEvent.class).eq(TblEvent::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .eq(TblEvent::getEventId, mappingDTO.getMappingId()));
            if (Objects.isNull(event)) {
                throw new BusinessException("绑定事件不存在");
            }
            Long count = eventMapper.selectCount(Wrappers.lambdaQuery(TblEvent.class)
                    .likeRight(TblEvent::getDescription, desLike)
                    .eq(TblEvent::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .ne(TblEvent::getId, event.getId()));
            if (count > 0) {
                throw new BusinessException("同一标准测点不能绑定多个事件");
            }
            String description = String.format("%s-%03d", desLike, 0);
            eventMapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                    .set(TblEvent::getDescription, description)
                    .eq(TblEvent::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .eq(TblEvent::getEventId, mappingDTO.getMappingId()));
        } else {
            // 有扩展数字，可以绑定多个字段
            List<Integer> mappingIds = mappingDTO.getMappingIds();
            List<TblEvent> eventList = eventMapper.selectList(Wrappers.lambdaQuery(TblEvent.class).eq(TblEvent::getEquipmentTemplateId, mappingDTO.getEquipmentTemplateId())
                    .in(TblEvent::getEventId, mappingIds).orderByAsc(TblEvent::getDisplayIndex));
            if (CollUtil.isEmpty(eventList)) {
                throw new BusinessException("绑定事件不存在");
            }
            int extendStartNum = Math.max(mappingDTO.getExtendStartNum(), 1);
            if (extendStartNum > 999) {
                throw new BusinessException("起始序号必须是1到999之间的整数");
            }
            int endNum = extendStartNum + eventList.size();
            if (endNum > 999) {
                throw new BusinessException("绑定的事件数量会导致id超过999，请重新检查绑定");
            }
            List<String> dataDesList = eventMapper.selectList(Wrappers.lambdaQuery(TblEvent.class)
                            .select(TblEvent::getDescription)
                            .likeRight(TblEvent::getDescription, desLike))
                    .stream().map(TblEvent::getDescription).toList();
            int startNumber = extendStartNum;
            List<TblEvent> updateEvents = new ArrayList<>();
            List<String> desList = new ArrayList<>();
            for (TblEvent event : eventList) {
                String description = String.format("%s-%03d", desLike, startNumber++);
                event.setDescription(description);
                desList.add(description);
                updateEvents.add(event);
            }
            Set<String> containDes = CollUtil.intersectionDistinct(dataDesList, desList);
            if (CollUtil.isNotEmpty(containDes)) {
                throw new BusinessException("请重新检查绑定，增加的序号已被绑定：" + String.join(",", containDes));
            }
            mybatisBatchUtils.batchUpdateOrInsert(updateEvents, TblEventMapper.class, (item, mapper) ->
                    mapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                            .set(TblEvent::getDescription, item.getDescription())
                            .eq(TblEvent::getEquipmentTemplateId, item.getEquipmentTemplateId())
                            .eq(TblEvent::getEventId, item.getEventId()))
            );
        }
    }

    @Override
    public void unbindEventPointMapping(StandardPointUnbindDTO standardPointUnbindDTO) {
        String mappingIds = standardPointUnbindDTO.getMappingIds();
        List<String> mappingIdList = CharSequenceUtil.split(mappingIds, ",");
        eventMapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                .set(TblEvent::getDescription, "")
                .eq(TblEvent::getEquipmentTemplateId, standardPointUnbindDTO.getEquipmentTemplateId())
                .in(TblEvent::getEventId, mappingIdList));
    }

    @Override
    public void updateStandardPointCategory(StandardPointCategoryDTO standardPointCategoryDTO) {
        List<TblEvent> eventList = eventMapper.selectList(Wrappers.lambdaQuery(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, standardPointCategoryDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        List<TblEvent> updateEvents = new ArrayList<>();
        for (TblEvent event : eventList) {
            String description = event.getDescription();
            if (CharSequenceUtil.isNotBlank(description) && description.length() >= 12) {
                String desSub = description.substring(description.indexOf('-') + 1);
                event.setDescription(String.format("%s-%s", standardPointCategoryDTO.getEquipmentCategoryId(), desSub));
                updateEvents.add(event);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateEvents, TblEventMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                        .set(TblEvent::getDescription, item.getDescription())
                        .eq(TblEvent::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEvent::getEventId, item.getEventId()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        List<TblEvent> eventList = eventMapper.selectList(Wrappers.lambdaQuery(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, standardUnmappedPointsDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        List<TblEvent> updateEvents = new ArrayList<>();
        for (TblEvent event : eventList) {
            String description = event.getDescription();
            if (CharSequenceUtil.isBlank(description)) {
                String lastSix = event.getEventId() <= 0 ? String.format("%06d", 999999) : CharSequenceUtil.subSufByLength(String.valueOf(event.getEventId()), 6);
                event.setDescription(String.format("%s-%d-%s", standardUnmappedPointsDTO.getEquipmentCategoryId(), 995, lastSix));
                updateEvents.add(event);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateEvents, TblEventMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                        .set(TblEvent::getDescription, item.getDescription())
                        .eq(TblEvent::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEvent::getEventId, item.getEventId()))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindUnmappedPoints(StandardUnmappedPointsDTO standardUnmappedPointsDTO) {
        List<TblEvent> eventList = eventMapper.selectList(Wrappers.lambdaQuery(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, standardUnmappedPointsDTO.getEquipmentTemplateId()));
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        List<TblEvent> updateEvents = new ArrayList<>();
        for (TblEvent event : eventList) {
            String description = event.getDescription();
            if (CharSequenceUtil.isNotBlank(description) && description.contains("-995-")) {
                event.setDescription("");
                updateEvents.add(event);
            }
        }
        mybatisBatchUtils.batchUpdateOrInsert(updateEvents, TblEventMapper.class, (item, mapper) ->
                mapper.update(Wrappers.lambdaUpdate(TblEvent.class)
                        .set(TblEvent::getDescription, item.getDescription())
                        .eq(TblEvent::getEquipmentTemplateId, item.getEquipmentTemplateId())
                        .eq(TblEvent::getEventId, item.getEventId()))
        );
    }

    @Override
    @Transactional
    public boolean fieldCopy(List<EventFieldCopyDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) return true;
        List<TblEvent> eventList = new ArrayList<>();
        for (EventFieldCopyDTO dto : dtoList) {
            Integer equipmentTemplateId = dto.getEquipmentTemplateId();
            Integer eventId = dto.getEventId();
            String fieldName = dto.getFieldName();
            String fieldValue = dto.getFieldValue();
            if ("eventConditionList".equals(fieldName)) {
                handleEventCondition(equipmentTemplateId, eventId, fieldValue);
                continue;
            }
            TblEvent event = ReflectUtil.newInstance(TblEvent.class);
            event.setEquipmentTemplateId(equipmentTemplateId);
            event.setEventId(eventId);
            ReflectUtil.setFieldValue(event, fieldName, fieldValue);
            eventList.add(event);
        }
        if (!eventList.isEmpty()) {
            eventMapper.batchUpdateField(eventList);
        }
        return true;
    }

    @Override
    public List<EventExcel> findExcelDtoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventMapper.findExcelDtoByEquipmentTemplateId(equipmentTemplateId);
    }

    private void handleEventCondition(Integer srcTemplateId, Integer srcEventId, String fieldValue) {
        String[] dest = fieldValue.split("\\.");
        Integer destTemplateId = Integer.parseInt(dest[0]);
        Integer destEventId = Integer.parseInt(dest[1]);
        List<TblEventCondition> conditions = eventConditionService.findByEquipmentTemplateIdAndEventId(srcTemplateId, srcEventId);
        List<TblEventCondition> copied = conditions.stream()
                                                   .map(c -> {
                                                       c.setId(null);
                                                       c.setEquipmentTemplateId(destTemplateId);
                                                       c.setEventId(destEventId);
                                                       return c;
                                                   }).toList();
        eventConditionService.updateEventCondition(destTemplateId, destEventId, copied);
    }

    @Override
    public List<TblEvent> findByEquipmentCategory(Integer equipmentCategory) {
        return eventMapper.findByEquipmentCategory(equipmentCategory);
    }

    @Override
    public List<EventConfigPointDTO> findEventPoints(Integer equipmentTemplateId) {
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
        if (Objects.isNull(equipmentTemplate)) {
            throw new BusinessException("设备模板不存在");
        }
        List<EventConfigItem> eventConfigItemList = eventMapper.findEventItemByEquipmentTemplateId(equipmentTemplateId);
        if (CollUtil.isEmpty(eventConfigItemList)) {
            return List.of();
        }
        List<StandardPoint> standardPoints = standardPointService.getEventStandardPoints(equipmentTemplate.getEquipmentCategory());
        Map<String, String> pointMap = standardPoints.stream()
                .collect(Collectors.toMap(
                        s -> String.format("%d-%d", s.getEquipmentCategoryId(), s.getPointId()),
                        StandardPoint::getPointName
                ));
        return eventConfigItemList.stream().map(s -> {
            EventConfigPointDTO eventConfigPointDTO = BeanUtil.copyProperties(s, EventConfigPointDTO.class);
            eventConfigPointDTO.setMappingPointName(CharSequenceUtil.isBlank(s.getDescription()) ? null : pointMap.get(CharSequenceUtil.sub(s.getDescription(), 0, s.getDescription().lastIndexOf('-'))));
            return eventConfigPointDTO;
        }).sorted(Comparator.comparing(EventConfigPointDTO::getDisplayIndex, Comparator.nullsLast(Comparator.naturalOrder()))).toList();
    }

    private Integer findMaxEventIdByEquipmentTemplateId(Integer equipmentTemplateId) {
        Integer maxEventId = eventMapper.findMaxEventIdByEquipmentTemplateId(equipmentTemplateId);
        if (Objects.nonNull(maxEventId)) {
            return ++maxEventId;
        }
        return primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EVENT, 0);
    }

    @Override
    public void batchEvent(List<TblEvent> eventList) {
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        eventMapper.insertBatchSomeColumn(eventList);
    }

    @Override
    public List<EventConfigItem> findEventItemByEquipmentTemplateId(Integer equipmentTemplateId) {
        return Optional.ofNullable(eventMapper.findEventItemByEquipmentTemplateId(equipmentTemplateId)).orElseGet(List::of)
                .stream()
                .sorted(Comparator.comparing(EventConfigItem::getDisplayIndex, Comparator.nullsLast(Comparator.naturalOrder())))
                .toList();
    }

    @Override
    public TblEvent findByEventId(Integer eventId) {
        return eventMapper.selectOne(new QueryWrapper<TblEvent>().eq("EventId", eventId));
    }

    @Override
    public void create(TblEvent event) {
        eventMapper.insert(event);
    }

    @Override
    public void createCommunicationStateEvent(Integer equipmentTemplateId) {
        if (communicationStateEventExists(equipmentTemplateId)) {
            return;
        }
        TblEvent event = TblEvent.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .eventId(EventConstant.COMMUNICATION_STATE_EVENT)
                .eventName(i18n.T("monitor.equipment.communicationState"))
                .signalId(EventConstant.COMMUNICATION_STATE_EVENT)
                .startExpression("[-1,-3]")
                .eventCategory(EventCategoryEnum.EQUIPMENT_COMMUNICATION_STATE_EVENT.getValue())
                .startType(StartTypeEnum.CONDITIONAL_EVENT.getValue())
                .endType(EndTypeEnum.CONTINUAL_EVENT.getValue())
                .enable(true)
                .visible(true)
                .build();
        create(event);
        TblEventCondition eventCondition = TblEventCondition.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .eventId(event.getEventId())
                .eventConditionId(0)
                .eventSeverity(EventSeverityEnum.LEVEL_1.getValue())
                .startOperation("=")
                .startCompareValue((double) 0)
                .startDelay(0)
                .meanings(i18n.T("monitor.equipment.communicationFail"))
                .build();
        eventConditionService.createEventCondition(eventCondition);
    }

    @Override
    public List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(Integer equipmentTemplateId) {
        return eventMapper.findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(equipmentTemplateId);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventMapper.delete(Wrappers.lambdaQuery(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, equipmentTemplateId));
        eventConditionService.deleteByEquipmentTemplateId(equipmentTemplateId);
        eventexService.deleteByEquipmentTemplateId(equipmentTemplateId);
    }

    /**
     * 是否存在设备通信状态事件
     *
     * @param equipmentTemplateId 设备模板
     * @return true存在  false不存在
     */
    private boolean communicationStateEventExists(Integer equipmentTemplateId) {
        return eventMapper.exists(Wrappers.lambdaQuery(TblEvent.class)
                .eq(TblEvent::getEquipmentTemplateId, equipmentTemplateId)
                .eq(TblEvent::getEventId, EventConstant.COMMUNICATION_STATE_EVENT));
    }

    @Override
    public void updateEventName(String eventName, Integer eventId) {
        eventMapper.update(null, new UpdateWrapper<TblEvent>()
                .set("EventName", eventName)
                .eq("EventId", eventId));
    }

    /**
     * 将信号关联到事件。
     * 创建一个新的事件记录，并将指定的信号与之关联。
     *
     * @param equipmentTemplateId 设备模板ID
     * @param signalId            信号ID
     * @return 操作是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean linkEvent(Integer equipmentTemplateId, Integer signalId) {
        // 1. 获取信号配置项信息
        SignalConfigItem signalConfigItem = signalService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);

        // 2. 构建事件对象
        TblEvent event = TblEvent.builder()
                .equipmentTemplateId(equipmentTemplateId)
                .eventId(findMaxEventIdByEquipmentTemplateId(equipmentTemplateId))
                // 事件名称 = 信号名称 + "事件"
                .eventName(signalConfigItem.getSignalName() + i18n.T("monitor.event"))
                .enable(true)      // 默认启用
                .visible(true)     // 默认可见
                .startType(StartTypeEnum.CONDITIONAL_EVENT.getValue())      // 1 = 条件事件
                .endType(EndTypeEnum.CONTINUAL_EVENT.getValue())        // 3 = 持续事件
                .eventCategory(EventCategoryEnum.EQUIPMENT_EVENT.getValue())  // 2 = 设备事件
                .signalId(signalId)
                // 开始表达式格式为 "[-1,信号ID]"
                .startExpression(String.format("[-1,%s]", signalId))
                .moduleNo(signalConfigItem.getModuleNo())
                // 获取下一个可用的显示索引
                .displayIndex(getNextDisplayIndex(equipmentTemplateId))
                .build();

        // 3. 保存事件
        create(event);
        // 4. 记录操作日志
        String objectId = String.format("%s.%s", equipmentTemplateId, event.getEventId());
        operationDetailService.recordOperationLog(
                TokenUserUtil.getLoginUserId(),  // 获取当前登录用户ID
                objectId,                        // 操作对象ID，格式：设备模板ID.事件ID
                OperationObjectTypeEnum.EVENT,   // 操作对象类型：事件
                i18n.T("event.eventName"),       // 字段名：事件名称
                i18n.T("add"),                   // 操作：添加
                "",                              // 原值：空
                event.getEventName()             // 新值：事件名称
        );

        // 5. 发送设备模板更新消息
        sendUpdateMsgByEquipmentTemplateId(equipmentTemplateId);

        return true;
    }

    /**
     * 获取下一个可用的显示索引。
     * 如果没有现有索引，则返回1，否则返回最大索引+1。
     *
     * @param equipmentTemplateId 设备模板ID
     * @return 下一个显示索引
     */
    private Integer getNextDisplayIndex(Integer equipmentTemplateId) {
        // 查询当前最大的显示索引
        Integer maxIndex = eventMapper.findMaxDisplayIndexByEquipmentTemplateId(equipmentTemplateId);

        // 如果没有现有索引，返回1作为初始值
        if (Objects.isNull(maxIndex)) {
            return 1;
        }

        // 返回最大索引+1
        return ++maxIndex;
    }

    /**
     * 发送设备模板变更记录
     *
     * @param equipmentTemplateId 设备模板id
     */
    public void sendUpdateMsgByEquipmentTemplateId(Integer equipmentTemplateId) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(equipmentTemplateId);
                if (Objects.isNull(equipmentTemplate)) {
                    return;
                }
                //告警的所有变更属于模板的更新
                changeEventService.sendUpdate(equipmentTemplate);
            }
        });
    }
}
