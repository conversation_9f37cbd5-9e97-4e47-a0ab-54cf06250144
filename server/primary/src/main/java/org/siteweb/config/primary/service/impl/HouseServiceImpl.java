package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblHouse;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblHouseMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.HouseService;
import org.siteweb.config.primary.service.OperationDetailService;
import org.siteweb.config.primary.service.PrimaryKeyValueService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> (2024-03-11)
 **/
@Slf4j
@Service
public class HouseServiceImpl implements HouseService {


    @Autowired
    private TblHouseMapper houseMapper;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private ChangeEventService changeEventService;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;


    @Override
    public TblHouse findStationDefaultHouse(Integer stationId) {
        return houseMapper.selectList(new LambdaQueryWrapper<>(TblHouse.class)
                        .eq(TblHouse::getStationId, stationId)).stream()
                .findFirst().orElse(null);
    }

    @Override
    public boolean createHouse(TblHouse house) {
        if (house.getHouseId() == null || house.getHouseId().equals(0)) {
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setHouseId(houseId);
        }
        house.setLastUpdateDate(LocalDateTime.now());
        if (houseMapper.insert(house) > 0) {
            changeEventService.sendCreate(house);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), house.getHouseId().toString(), OperationObjectTypeEnum.HOUSE, i18n.T("house.houseName"), i18n.T("add"), "", house.getHouseName());
            return true;
        }
        return false;

    }

    @Override
    public TblHouse findHouseByHostName(Integer stationId, String houseName) {
        return houseMapper.selectList(new LambdaQueryWrapper<>(TblHouse.class)
                        .eq(TblHouse::getStationId, stationId)
                        .eq(TblHouse::getHouseName, houseName)).stream()
                .findFirst().orElse(null);
    }

    @Override
    public boolean updateHouse(TblHouse house) {
        TblHouse oldHouse = houseMapper.selectById(house.getHouseId());
        if (houseMapper.updateById(house) > 0) {
            changeEventService.sendUpdate(house);
            operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldHouse, house);
            return true;
        }
        return false;
    }

    @Override
    public boolean deleteHouse(Integer houseId, Integer stationId) {
        TblHouse tblHouse = houseMapper.selectOne(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getHouseId, houseId)
                .eq(TblHouse::getStationId, stationId));
        if (houseMapper.deleteById(houseId) > 0) {
            changeEventService.sendDelete(tblHouse);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), houseId.toString(), OperationObjectTypeEnum.HOUSE, i18n.T("house.houseName"), i18n.T("delete"), tblHouse.getHouseName(), "");
            return true;
        }
        return false;
    }

    @Override
    public List<TblHouse> findHouseByStationId(Integer stationId) {
        return houseMapper.selectList(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getStationId, stationId));
    }

    @Override
    public TblHouse findByHouseId(Integer houseId) {
        return houseMapper.selectById(houseId);
    }

    @Override
    public void deleteHouseByStationId(Integer stationId) {
        List<TblHouse> houses = houseMapper.selectList(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getStationId, stationId));
        houseMapper.delete(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getStationId, stationId));
        houses.forEach(house -> {
            changeEventService.sendDelete(house);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), house.getHouseId().toString(), OperationObjectTypeEnum.HOUSE, i18n.T("house.houseName"), i18n.T("delete"), house.getHouseName(),"");
        });
    }

    @Override
    public List<TblHouse> findHouseList() {
        return houseMapper.selectList(null);
    }

    @Override
    public TblHouse findStationIdAndHouseId(Integer stationId, Integer houseId) {
        return houseMapper.selectOne(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getHouseId, houseId)
                .eq(TblHouse::getStationId, stationId));
    }

    @Override
    public Integer findHouseIdByStationId(Integer stationId) {
        TblHouse tblHouse = houseMapper.selectOne(new LambdaQueryWrapper<>(TblHouse.class)
                .eq(TblHouse::getStationId, stationId).last("limit 1"));
        return Optional.ofNullable(tblHouse).map(TblHouse::getHouseId).orElse(null);
    }
}
