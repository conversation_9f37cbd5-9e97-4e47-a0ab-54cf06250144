package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.Employee;
import org.siteweb.config.common.mapper.EmployeeMapper;
import org.siteweb.config.primary.service.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Override
    public List<Employee> findAll() {
        List<Employee> employees = employeeMapper.selectList(null);
        if (CollUtil.isEmpty(employees)) {
            return new ArrayList<>();
        }
        return employees;
    }

    @Override
    public List<Employee> findByEmployeeIdAndDepartmentId() {
        List<Employee> employees = employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                .gt(Employee::getEmployeeId, 0)
                .eq(Employee::getDepartmentId, 0));
        if (CollUtil.isEmpty(employees)) {
            return new ArrayList<>();
        }
        return employees;
    }

}