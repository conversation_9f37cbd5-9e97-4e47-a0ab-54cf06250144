package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.BaseTypeTreeDTO;
import org.siteweb.config.common.dto.EventBaseDicDTO;
import org.siteweb.config.common.dto.EventBaseDictionaryDTO;
import org.siteweb.config.common.dto.EventBaseDetailDTO;
import org.siteweb.config.common.entity.TblEventBaseDic;
import org.siteweb.config.common.vo.BaseDicFilterVO;

import java.util.List;

public interface EventBaseDicService extends BaseDicService {

    List<TblEventBaseDic> list(EventBaseDicDTO eventBaseDicDTO);

    TblEventBaseDic findByBaseTypeId(Long baseTypeId);

    boolean existsByBaseTypeId(Long baseTypeId);

    void generateBaseDic(Long baseTypeId, Long sourceId);

    List<TblEventBaseDic> findEventBaseDic(BaseDicFilterVO baseDicFilterVO);

    void deleteLianTongByBaseTypeId(int baseTypeId);

    void createEventBaseDic(TblEventBaseDic eventBaseDic);

    void createLianTongEventBaseDic();

    /**
     *  获取告警(事件)基类列表(字典获取)
     */
    List<EventBaseDictionaryDTO> findEventBaseDicList();

    /**
     * 新增告警(事件)基类
     */
    void addEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO);

    /**
     * 修改告警(事件)基类
     */
    void updateEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO);
    /**
     * 删除告警(事件)基类
     */
    void deleteEventBaseDic(Long baseTypeId);

    /**
     * 批量新增告警(事件)基类
     *
     * @param baseTypeId  事件基础类别id
     * @param abortNumber 截止序号
     */
    void batchAddEventBaseDic(Long baseTypeId, Integer startNumber, Integer abortNumber);

    /**
     * 批量修改告警(事件)基类
     */
    int batchUpdateEventBaseDic(EventBaseDictionaryDTO eventBaseDictionaryDTO);

    /**
     * 获取基类事件树
     */
    List<BaseTypeTreeDTO> findEventBaseDicTree(Integer equipmentBaseType, Boolean isSystem);

    /**
     * 获取基类事件详细数据
     */
    EventBaseDetailDTO findEventBaseDicDetail(Long baseTypeId);

    /**
     * 获取事件条件类别
     */
    String getEventCodeCategory(int code);
}
