package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StructureTypeEnum {

    ECC(1, "ECC"),
    PARK(2, "园区"),
    BUILDING(3, "大楼"),
    FLOOR(4, "楼层"),
    ROOM(5, "房间"),
    MDC(6, "MDC"),
    EQUIPMENT(7, "设备"),
    COMMON_OBJECT(8, "通用对象"),
    RACK(9, "机架"),
    IT_EQUIPMENT(10, "IT设备"),
    PROVINCIAL_CENTER(101, "省中心"),
    DOWNTOWN(102, "地市中心"),
    AREA(103, "片区"),
    STATION(104, "基站"),
    STATION_HOUSE(105, "局房"),

    DIAN_XIN(2, "网点");
    private final int value;
    private final String describe;


}
