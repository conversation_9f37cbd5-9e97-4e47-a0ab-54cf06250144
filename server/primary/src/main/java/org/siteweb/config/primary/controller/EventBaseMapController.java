package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.BatchBaseMapDTO;
import org.siteweb.config.common.entity.TblEventBaseMap;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EventBaseMapService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/eventbasemap")
public class EventBaseMapController {
    @Autowired
    private EventBaseMapService eventBaseMapService;

    @PostMapping("/batch")
    public ResponseEntity<ResponseResult> batchCreate(@RequestBody BatchBaseMapDTO batchBaseMapDTO){
        List<TblEventBaseMap> list = Arrays.stream(batchBaseMapDTO.getBaseTypeIds().split(","))
                                           .map(Long::valueOf)
                                           .map(baseTypeId -> {
                                               TblEventBaseMap tblEventBaseMap = new TblEventBaseMap();
                                               tblEventBaseMap.setBaseTypeId(baseTypeId);
                                               tblEventBaseMap.setStandardDicId(batchBaseMapDTO.getStandardDicId());
                                               tblEventBaseMap.setStationBaseType(batchBaseMapDTO.getStationBaseType());
                                               return tblEventBaseMap;
                                           }).toList();
        return ResponseHelper.successful(eventBaseMapService.batchCreate(list));
    }
}
