package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblEquipmentMapper;
import org.siteweb.config.common.mapper.TblEquipmentProjectInfoMapper;
import org.siteweb.config.common.mapper.TblEquipmentTemplateMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.vo.EquipmentReferenceVO;
import org.siteweb.config.common.vo.EquipmentVO;
import org.siteweb.config.common.vo.StationEquipment;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;


@Service()
@Configuration
@Slf4j
public class EquipmentServiceImpl implements EquipmentService {

    @Autowired
    private TblEquipmentMapper equipmentMapper;
    @Autowired
    private ChangeEventService changeEventService;
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private I18n i18n;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    TblEquipmentProjectInfoMapper equipmentProjectInfoMapper;
    @Autowired
    private SamplerService samplerService;
    @Autowired
    private SamplerUnitService samplerUnitService;
    @Autowired
    CopyEquipmentTemplateService copyEquipmentTemplateService;
    @Autowired
    StationService stationService;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    ResourceStructureService resourceStructureService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${bytedance.generate-signal-standard:false}")
    private Boolean generateUUID;

    @Autowired
    private TblEquipmentTemplateMapper equipmentTemplateMapper;

    @Override
    public Collection<TblEquipment> allEquipment() {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .orderByAsc(TblEquipment::getDisplayIndex, TblEquipment::getEquipmentName));
    }

    @Override
    public TblEquipment findEquipmentById(Integer equipmentId) {
        return equipmentMapper.selectByEquipmentId(equipmentId);
    }


    @Override
    @Transactional
    public TblEquipment createEquipment(TblEquipment equipment) {
        // 生成设备ID
        if (equipment.getEquipmentId() == null || equipment.getEquipmentId().equals(0)) {
            Integer eqId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT, 0);
            equipment.setEquipmentId(eqId);
        }
        if (generateUUID) {
            JsonNode extValue = equipment.getExtValue();
            ObjectMapper mapper = new ObjectMapper();

            ObjectNode newValue = mapper.createObjectNode();
            newValue.put("guid", UUID.randomUUID().toString());

            ArrayNode arrayNode;
            if (extValue != null && extValue.isArray()) {
                arrayNode = (ArrayNode) extValue;
            } else {
                arrayNode = mapper.createArrayNode();
            }

            arrayNode.add(newValue);

            equipment.setExtValue(arrayNode);
        }
        int count = equipmentMapper.insert(equipment);
        if (count > 0) {
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), equipment.getEquipmentId().toString(), OperationObjectTypeEnum.EQUIPMENT, i18n.T("equipment.equipmentName"), i18n.T("add"), "", equipment.getEquipmentName());
            TblEquipmentProjectInfo info = equipment.toProjectInfo();
            equipmentProjectInfoMapper.insert(info);
            changeEventService.sendCreate(equipment);
            log.error("开始异步刷设备模板库");
//            asyncWriterService.CloneEquipmentTemplateAsync(baseEquipmentTemplateId, equipmentTemplateId);
            log.error("返回设备数据");
        }
        return equipment;
    }


    @Override
    public boolean existsByEquipmentTemplateId(Integer equipmentTemplateId) {
        return !equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getEquipmentTemplateId, equipmentTemplateId)).isEmpty();
    }


    @Override
    public void updateEquipmentCategoryByCategoryIdMap(int businessId, int categoryTypeId) {
        equipmentMapper.updateEquipmentCategoryByCategoryIdMap(businessId, categoryTypeId);
    }

    @Override
    public EquipmentDetailDTO findEquipmentDetail(Integer equipmentId) {
        return equipmentMapper.findEquipmentDetail(equipmentId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEquipment(EquipmentDetailDTO equipmentDetailDTO) {
        if (ObjectUtil.hasNull(equipmentDetailDTO.getStationId(), equipmentDetailDTO.getMonitorUnitId(), equipmentDetailDTO.getEquipmentId())) {
            throw new InvalidParameterException("stationId, monitorUnitId, equipmentId cannot be empty");
        }
        TblEquipment oldEquipment = equipmentMapper.selectById(equipmentDetailDTO.getEquipmentId());
        if (Objects.isNull(oldEquipment)) {
            throw new BusinessException("equipment info does not exist");
        }
        TblEquipment tblEquipment = BeanUtil.copyProperties(equipmentDetailDTO, TblEquipment.class);
        operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), oldEquipment, tblEquipment);
        //2025-2-8 xsx 更新houseID
        ResourceStructure structure = resourceStructureService.getStructureByID(tblEquipment.getResourceStructureId());
        if (structure != null && structure.getStructureTypeId().equals(StructureTypeEnum.STATION_HOUSE.getValue())) {
            tblEquipment.setHouseId(structure.getOriginId());
        }
        int result = equipmentMapper.updateById(tblEquipment);
        // 修改工程合同信息
        EquipmentDetailDTO.EquipmentProjectInfo equipmentProjectInfo = equipmentDetailDTO.getEquipmentProjectInfo();
        if (Objects.nonNull(equipmentProjectInfo)) {
            equipmentProjectInfoMapper.update(Wrappers.lambdaUpdate(TblEquipmentProjectInfo.class)
                    .set(TblEquipmentProjectInfo::getProjectName, equipmentProjectInfo.getProjectName())
                    .set(TblEquipmentProjectInfo::getContractNo, equipmentProjectInfo.getContractNo())
                    .eq(TblEquipmentProjectInfo::getStationId, equipmentDetailDTO.getStationId())
                    .eq(TblEquipmentProjectInfo::getMonitorUnitId, equipmentDetailDTO.getMonitorUnitId())
                    .eq(TblEquipmentProjectInfo::getEquipmentId, equipmentDetailDTO.getEquipmentId())
            );
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //设备变更通知
                changeEventService.sendUpdate(tblEquipment);
            }
        });
        return result;
    }

    @Override
    public boolean deleteEquipment(Integer equipmentId) {
        TblEquipment eq = findEquipmentById(equipmentId);
        if (eq != null && equipmentMapper.deleteById(equipmentId) > 0) {
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), eq.getEquipmentId().toString(), OperationObjectTypeEnum.EQUIPMENT, i18n.T("equipment.equipmentName"), i18n.T("delete"), eq.getEquipmentName(), "");
            changeEventService.sendDelete(eq);
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean switchEquipmentTemplate(SwitchTemplateDTO switchTemplateDTO) {
        Integer equipmentTemplateId = switchTemplateDTO.getDestTemplateId();
        for (Integer equipmentId : switchTemplateDTO.getEquipmentIds()) {
            switchEquipmentTemplate(equipmentId, equipmentTemplateId);
        }
        return true;
    }

    @Override
    public List<SimplifyEquipmentDTO> findSimplifyEquipmentsByStationId(Integer equipmentId) {
        if (Objects.isNull(equipmentId)) {
            throw new InvalidParameterException("equipmentId can't be empty");
        }
        TblEquipment tblEquipment = findEquipmentById(equipmentId);
        if (Objects.isNull(tblEquipment)) {
            return List.of();
        }
        Predicate<TblEquipment> predicate = e -> Objects.equals(e.getStationId(), tblEquipment.getStationId());
        return getSimplifyEquipmentList(predicate, false);
    }

    @Override
    public List<SimplifyEquipmentDTO> findSimplifyEquipmentsByStationIdAndMonitorUnitId(Integer stationId, Integer monitorUnitId) {
        return equipmentMapper.findByStationIdAndMonitorUnitId(stationId, monitorUnitId);
    }

    @Override
    public List<SimplifyEquipmentDTO> findSimplifyEquipmentsByMonitorUnitId(Integer monitorUnitId, Boolean spliceFlag) {
        Predicate<TblEquipment> predicate = e -> Objects.equals(e.getMonitorUnitId(), monitorUnitId);
        return getSimplifyEquipmentList(predicate, spliceFlag);
    }

    @Override
    public void deleteBySamplerUnitId(Integer samplerUnitId) {
        List<TblEquipment> eqs = findBySamplerUnitId(samplerUnitId);
        if (CollUtil.isNotEmpty(eqs)) {
            int delete = equipmentMapper.delete(Wrappers.lambdaQuery(TblEquipment.class).eq(TblEquipment::getSamplerUnitId, samplerUnitId));
            if (delete > 0) {
                for (TblEquipment eq : eqs) {
                    changeEventService.sendDelete(eq);
                    operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), eq.getEquipmentId().toString(), OperationObjectTypeEnum.EQUIPMENT, i18n.T("equipment.equipmentName"), i18n.T("delete"), eq.getEquipmentName(), "");
                }
            }
        }
    }

    @Override
    public List<IdValueDTO<String, String>> findNamesByIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return equipmentMapper.findNameByIds(equipmentIds);
    }

    @Override
    public List<TblEquipment> findBySamplerUnitId(Integer samplerUnitId) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getSamplerUnitId, samplerUnitId));
    }

    @Override
    public List<TblEquipment> findEquipments() {
        return equipmentMapper.selectAll();
    }

    @Override
    public List<TblEquipment> findEqsByMonitorUnitId(Integer monitorUnitId) {
        return equipmentMapper.selectList(new LambdaQueryWrapper<>(TblEquipment.class)
                .eq(TblEquipment::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public boolean existsByMonitorUnitIdAndEquipmentName(Integer equipmentId, Integer monitorUnitId, String equipmentName) {
        return !equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                        .eq(TblEquipment::getMonitorUnitId, monitorUnitId)
                        .eq(TblEquipment::getEquipmentName, equipmentName)
                        .ne(ObjectUtil.isNotNull(equipmentId), TblEquipment::getEquipmentId, equipmentId))
                .isEmpty();
    }

    @Override
    public List<TblEquipment> findByHouseId(Integer houseId) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getHouseId, houseId));
    }

    @Override
    public List<TblEquipment> findByHouseIdAndStationId(Integer stationId, Integer houseId) {
        // stationId 不能为空
        if (ObjectUtil.isNull(stationId)) {
            throw new InvalidParameterException("stationId can't be empty");
        }
        // 使用新的XML查询方法，关联采集单元和端口信息
        return equipmentMapper.findByHouseIdAndStationIdWithPortInfo(stationId, houseId);
    }

    @Override
    public void deleteByStationId(Integer stationId) {
        // 查询站点下的设备
        List<TblEquipment> eqs = findByStationId(stationId);
        // 删除设备
        equipmentMapper.delete(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getStationId, stationId));
        // 删除设备发送变更通知
        for (TblEquipment eq : eqs) {
            changeEventService.sendDelete(eq);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), eq.getEquipmentId().toString(), OperationObjectTypeEnum.EQUIPMENT, i18n.T("equipment.equipmentName"), i18n.T("delete"), eq.getEquipmentName(), "");
        }
    }

    @Override
    public void syncEquipmentStructure(TblEquipment equipment) {
        if (equipment.getResourceStructureId() != null && equipment.getResourceStructureId() > 0) {
            return;
        }
        ResourceStructure ResourceStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(equipment.getHouseId(), equipment.getStationId(), StructureTypeEnum.STATION_HOUSE.getValue());
        if (ObjectUtil.isNull(ResourceStructure)) {
            return;
        }
        Integer result = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, ResourceStructure.getResourceStructureId())
                .eq(TblEquipment::getEquipmentId, equipment.getEquipmentId()));
        if (result <= 0) {
            return;
        }
        equipment.setResourceStructureId(ResourceStructure.getResourceStructureId());
        changeEventService.sendUpdate(equipment);

    }

    @Override
    public void syncSwatchEquipment(TblEquipment equipment) {
        if (equipment.getResourceStructureId() != null && equipment.getResourceStructureId() > 0) {
            return;
        }
        ResourceStructure ResourceStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(equipment.getHouseId(), equipment.getStationId(), StructureTypeEnum.STATION_HOUSE.getValue());
        if (ObjectUtil.isNull(ResourceStructure)) {
            return;
        }
        int result = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, ResourceStructure.getResourceStructureId())
                .eq(TblEquipment::getEquipmentId, equipment.getEquipmentId()));
        if (result <= 0) {
            return;
        }
        equipment.setResourceStructureId(ResourceStructure.getResourceStructureId());
        changeEventService.sendUpdate(equipment);
    }

    @Override
    public boolean mappingEquipment(MappingEquipmentDTO mappingEquipmentDTO) {
        if (mappingEquipmentDTO.getEquipmentIds().isEmpty() || mappingEquipmentDTO.getResourceStructureId() == null) {
            throw new BusinessException("The number of equipmentIds and monitorUnitIds must be equal");
        }

        // 修改设备的resourceStructureId
        int update = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, mappingEquipmentDTO.getResourceStructureId())
                .in(TblEquipment::getEquipmentId, mappingEquipmentDTO.getEquipmentIds()));
        if (update > 0) {
            List<TblEquipment> eqs = equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                    .in(TblEquipment::getEquipmentId, mappingEquipmentDTO.getEquipmentIds()));
            for (TblEquipment eq : eqs) {
                changeEventService.sendUpdate(eq);
            }
        }
        return true;
    }

    @Override
    public boolean unMappingEquipment(MappingEquipmentDTO mappingEquipmentDTO) {
        if (mappingEquipmentDTO.getEquipmentIds().isEmpty()) {
            throw new BusinessException("The number of equipmentIds and monitorUnitIds must be equal");
        }
        // 修改设备的resourceStructureId
        int update = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, 0)
                .in(TblEquipment::getEquipmentId, mappingEquipmentDTO.getEquipmentIds()));
        if (update > 0) {
            List<TblEquipment> eqs = equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                    .in(TblEquipment::getEquipmentId, mappingEquipmentDTO.getEquipmentIds()));
            for (TblEquipment eq : eqs) {
                changeEventService.sendUpdate(eq);
            }
        }
        return true;
    }

    @Override
    public List<TblEquipment> mappingEquipments(Integer stationId, Integer houseId) {
        return findByHouseIdAndStationId(stationId, houseId);
    }


    @Override
    public boolean updateResourceStructureIdIfNotMapped(Integer stationId, Integer resourceStructureId) {
        return equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, resourceStructureId)
                .eq(TblEquipment::getHouseId, stationId)
                .le(TblEquipment::getResourceStructureId, 0)) > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearEquipmentResourceStructureId(Integer resourceStructureId) {
        List<TblEquipment> equipmentList = equipmentMapper.selectList(
                Wrappers.lambdaQuery(TblEquipment.class)
                        .eq(TblEquipment::getResourceStructureId, resourceStructureId)
        );

        if (CollUtil.isEmpty(equipmentList)) {
            return true;
        }


        int updateCount = equipmentMapper.update(
                Wrappers.lambdaUpdate(TblEquipment.class)
                        .set(TblEquipment::getResourceStructureId, 0)
                        .eq(TblEquipment::getResourceStructureId, resourceStructureId)
        );

        if (updateCount > 0) {
            // Update the resourceStructureId in memory for change notifications
            equipmentList.forEach(eq -> eq.setResourceStructureId(0));

            // Send batch change notifications
            changeEventService.sendBatchUpdate(equipmentList);

            return true;
        }

        return false;
    }

    @Override
    public boolean updateByHouseIdAndResourceStructureId(TblEquipment equipment) {
        if (equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getHouseId, equipment.getHouseId())
                .set(TblEquipment::getResourceStructureId, equipment.getResourceStructureId())
                .eq(TblEquipment::getEquipmentId, equipment.getEquipmentId())) > 0) {
            changeEventService.sendUpdate(equipment);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateHouseId(EquipmentDTO equipmentDTO) {
        TblEquipment equipment = equipmentMapper.selectById(equipmentDTO.getEquipmentId());
        ResourceStructure resourceStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(equipmentDTO.getHouseId(), equipmentDTO.getStationId(), StructureTypeEnum.STATION_HOUSE.getValue());
        if (ObjectUtil.isNotNull(resourceStructure) && equipment.getResourceStructureId() != null && !equipment.getResourceStructureId().equals(resourceStructure.getResourceStructureId())) {
            int update = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                    .set(TblEquipment::getHouseId, equipmentDTO.getHouseId())
                    .set(TblEquipment::getResourceStructureId, resourceStructure.getResourceStructureId())
                    .eq(TblEquipment::getEquipmentId, equipmentDTO.getEquipmentId()));
            if (update > 0) {
                changeEventService.sendUpdate(equipment);
            }
            return update > 0;
        } else {
            int update = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                    .set(TblEquipment::getHouseId, equipmentDTO.getHouseId())
                    .eq(TblEquipment::getEquipmentId, equipmentDTO.getEquipmentId()));
            if (update > 0) {
                changeEventService.sendUpdate(equipment);
            }
            return update > 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateUuid() {
        if (!generateUUID) {
            return false;
        }
        List<TblEquipment> eqs = equipmentMapper.fetchEquipmentWithMissingOrInvalidGuid();
        List<TblEquipment> needUpdateList = new ArrayList<>();
        for (TblEquipment equipment : eqs) {
            // 使用ExtendedField字段，这是一个json数组，遍历数组如果已经存在guid这个key，则跳过，如果没有则生成一个设置进去
            try {
                String jsonString;
                JsonNode exValue = equipment.getExtValue();
                if (exValue == null) {
                    jsonString = "";
                } else {
                    jsonString = objectMapper.writeValueAsString(exValue);
                }

                boolean guidExists = false;
                JSONArray jsonArray = new JSONArray();
                if (StrUtil.isNotEmpty(jsonString)) {
                    jsonArray = JSONUtil.parseArray(jsonString);
                    // 遍历JSON数组中的每个对象
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject item = jsonArray.getJSONObject(i);
                        if (item.containsKey("guid")) {
                            guidExists = true;
                            break;
                        }
                    }
                }

                // 如果不存在guid，则添加一个新的JSON对象到数组
                if (!guidExists) {
                    JSONObject newItem = new JSONObject();
                    newItem.put("guid", UUID.randomUUID().toString());
                    jsonArray.add(newItem);
                    equipment.setExtValue(objectMapper.readTree(jsonArray.toString()));
                    log.info(objectMapper.writeValueAsString((equipment.getExtValue())));
                    needUpdateList.add(equipment);
                }
            } catch (Exception e) {
                // 处理JSON解析异常
                log.error("处理JSON解析异常, equipmentId: {}, err:{}", equipment.getEquipmentId(), e.getMessage());
            }
        }

        // 批量更新处理
        if (!needUpdateList.isEmpty()) {
            batchUpdateExValue(needUpdateList);
        }

        return true;
    }

    @Override
    public void checkEquipmentSyncField(Integer equipmentTemplateId) {
        TblEquipmentTemplate template = equipmentTemplateMapper.selectOne(
                Wrappers.lambdaQuery(TblEquipmentTemplate.class)
                        .eq(TblEquipmentTemplate::getEquipmentTemplateId, equipmentTemplateId));

        if (template == null) {
            return;
        }

        Integer templateCategory = template.getEquipmentCategory();
        String templateVendor = template.getVendor();
        String templateUnit = template.getUnit();
        String templateStyle = template.getEquipmentStyle();

        List<TblEquipment> equipmentList = equipmentMapper.selectList(
                Wrappers.lambdaQuery(TblEquipment.class)
                        .eq(TblEquipment::getEquipmentTemplateId, equipmentTemplateId));

        // 需要更新的设备列表
        List<TblEquipment> equipmentsToUpdate = new ArrayList<>();

        for (TblEquipment equipment : equipmentList) {
            boolean changed = false;

            Integer currentCategory = equipment.getEquipmentCategory();
            String currentVendor = equipment.getVendor();
            String currentUnit = equipment.getUnit();
            String currentStyle = equipment.getEquipmentStyle();

            if (templateCategory != null && !Objects.equals(currentCategory, templateCategory)) {
                equipment.setEquipmentCategory(templateCategory);
                changed = true;
            }
            if (!Objects.equals(currentStyle, templateStyle)) {
                equipment.setEquipmentStyle(templateStyle);
                changed = true;
            }
            if (!Objects.equals(currentVendor, templateVendor)) {
                equipment.setVendor(templateVendor);
                changed = true;
            }
            if (!Objects.equals(currentUnit, templateUnit)) {
                equipment.setUnit(templateUnit);
                changed = true;
            }

            if (changed) {
                equipmentsToUpdate.add(equipment);
            }
        }
        equipmentMapper.updateBatchByIdsSyncTemplate(templateCategory,templateVendor,templateUnit,templateStyle,equipmentTemplateId);
        // 批量更新设备
        if (!equipmentsToUpdate.isEmpty()) {
            // 使用批量更新方法
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 批量发送更新通知
                    changeEventService.sendBatchUpdate(equipmentsToUpdate);
                }
            });
        }
    }


    /**
     * 批量更新ExtendedField字段
     *
     * @param eqs 需要更新的设备列表
     */
    public void batchUpdateExValue(List<TblEquipment> eqs) {
        // 每1000条记录批量更新一次
        int batchSize = 1000;
        List<List<TblEquipment>> batches = new ArrayList<>();

        for (int i = 0; i < eqs.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, eqs.size());
            batches.add(eqs.subList(i, endIndex));
        }

        for (List<TblEquipment> batch : batches) {
            equipmentMapper.updateBatchById(batch);
            changeEventService.sendBatchUpdate(batch);
        }
    }

    // 修改设备的层级ID，参数为equipmentid和resourceStructureId
    @Override
    public boolean updateResourceStructureId(Integer equipmentId, Integer resourceStructureId) {
        boolean updated = equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getResourceStructureId, resourceStructureId)
                .eq(TblEquipment::getEquipmentId, equipmentId)) > 0;
        if (updated) {
            TblEquipment equipment = equipmentMapper.selectById(equipmentId);
            changeEventService.sendUpdate(equipment);
        }
        return updated;
    }

    private List<SimplifyEquipmentDTO> getSimplifyEquipmentList(Predicate<TblEquipment> predicate, Boolean spliceFlag) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)).stream()
                .filter(predicate)
                .map(equipment ->
                        SimplifyEquipmentDTO
                                .builder()
                                .equipmentId(equipment.getEquipmentId())
                                .equipmentName(Boolean.TRUE.equals(spliceFlag) ? CharSequenceUtil.format("{}-{}", equipment.getEquipmentName(), equipment.getEquipmentId())
                                        : equipment.getEquipmentName())
                                .equipmentTemplateId(equipment.getEquipmentTemplateId())
                                .build())
                .sorted(Comparator.comparingInt(SimplifyEquipmentDTO::getEquipmentId))
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int equipmentInstance(Integer equipmentId) {
        TblEquipment equipment = findEquipmentById(equipmentId);
        CopyEquipmentTemplateDTO copyEquipmentTemplateDTO = new CopyEquipmentTemplateDTO();
        copyEquipmentTemplateDTO.setOriginEquipmentTemplateId(equipment.getEquipmentTemplateId());
        copyEquipmentTemplateDTO.setReason(i18n.T("monitor.equipmentInstance"));
        copyEquipmentTemplateDTO.setNewEquipmentTemplateName(equipment.getEquipmentName() + LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.NORM_TIME_PATTERN));
        int newEquipmentTemplateId = copyEquipmentTemplateService.copyTemplate(copyEquipmentTemplateDTO);
        SwitchTemplateDTO switchTemplateDTO = new SwitchTemplateDTO(newEquipmentTemplateId, equipment.getEquipmentTemplateId(), List.of(equipmentId));
        switchEquipmentTemplate(switchTemplateDTO);
        return newEquipmentTemplateId;
    }


    @Override
    public List<TblEquipment> findByStationId(Integer stationId) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getStationId, stationId));
    }

    @Override
    public List<EquipmentVO> findByStructureId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureService.getStructureByID(resourceStructureId);
        if (ObjectUtil.isNull(resourceStructure)) {
            return List.of();
        }
        return equipmentMapper.findVosByStructureId(resourceStructureId);
    }

    @Override
    public List<EquipmentVO> findEquipmentVOsByStructureId(Integer resourceStructureId) {
        return equipmentMapper.findVosByStructureId(resourceStructureId);
    }

    @Override
    public void deleteByMonitorUnitId(Integer monitorUnitId) {
        List<TblEquipment> eqs = findByMonitorUnitId(monitorUnitId);
        int delete = equipmentMapper.delete(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getMonitorUnitId, monitorUnitId));
        if (delete > 0) {
            for (TblEquipment eq : eqs) {
                changeEventService.sendDelete(eq);
            }
        }
    }

    @Override
    public List<StationEquipment> findStationEquipmentList() {
        return equipmentMapper.findStationEquipmentList();
    }

    @Override
    public TblEquipment findEquipmentByStationNameAndEquipmentName(String stationName, String equipmentName) {
        TblStation station = stationService.findStationByName(stationName);
        if (ObjectUtil.isNull(station)) {
            return null;
        }
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                        .eq(TblEquipment::getStationId, station.getStationId())
                        .eq(TblEquipment::getEquipmentName, equipmentName))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public TblEquipment getEquipmentsByEquipmentName(String equipmentName) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                        .eq(TblEquipment::getEquipmentName, equipmentName))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public TblEquipment findByIdFromDB(Integer equipmentId) {
        return equipmentMapper.selectById(equipmentId);
    }

    /**
     * 切换设备模板
     * 对应存储过程 PCT_ReAssignEquipmentTemplate
     *
     * @param equipmentId            设备主键id
     * @param newEquipmentTemplateId 新设备模板id
     */
    private void switchEquipmentTemplate(Integer equipmentId, Integer newEquipmentTemplateId) {
        TblEquipment equipment = findEquipmentById(equipmentId);
        //更新采集单元的动态库
        TslSampler newSampler = samplerService.findByEquipmentTemplateId(newEquipmentTemplateId);
        TslSamplerUnit samplerUnit = samplerUnitService.findBySamplerUnitId(equipment.getSamplerUnitId());
        if (Objects.nonNull(newSampler) && Objects.nonNull(samplerUnit)) {
            samplerUnit.setSamplerId(newSampler.getSamplerId());
            String oldDllPathPrefix = CharSequenceUtil.subBefore(samplerUnit.getDllPath(), ".", true);
            String newDllPathPrefix = CharSequenceUtil.subBefore(newSampler.getDllPath(), ".", true);
            samplerUnit.setDllPath(samplerUnit.getDllPath().replace(oldDllPathPrefix, newDllPathPrefix));
            samplerUnitService.updateSamplerUnit(samplerUnit);
            log.info("采集单元切换成功,equipmentId:{},newEquipmentTemplateId:{}", equipmentId, newEquipmentTemplateId);
        }
        //更新设备模板
        equipmentMapper.update(Wrappers.lambdaUpdate(TblEquipment.class)
                .set(TblEquipment::getEquipmentTemplateId, newEquipmentTemplateId)
                .eq(TblEquipment::getEquipmentId, equipmentId));
        equipment.setEquipmentTemplateId(newEquipmentTemplateId);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                changeEventService.sendUpdate(equipment);
            }
        });
        log.info("设备模板切换成功,equipmentId:{},newEquipmentTemplateId:{}", equipmentId, newEquipmentTemplateId);
    }

    @Override
    public List<TblEquipment> findByMonitorUnitId(Integer monitorUnitId) {
        return equipmentMapper.selectByMonitorUnitId(monitorUnitId);
    }

    @Override
    public List<TblEquipment> findByMonitorUnitIdByCategory(Integer monitorUnitId, List<Integer> EquipmentCategoryIds) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getMonitorUnitId, monitorUnitId).in(TblEquipment::getEquipmentCategory, EquipmentCategoryIds));
    }

    @Override
    public List<TblEquipment> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .eq(TblEquipment::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public List<TblEquipment> findByEquipmentTemplateIds(Set<Integer> equipmentTemplateIds) {
        return equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class)
                .in(TblEquipment::getEquipmentTemplateId, equipmentTemplateIds));
    }

    @Override
    public List<EquipmentReferenceVO> findReferenceVoByEquipmentTemplateId(Integer equipmentTemplateId) {
        return equipmentMapper.findReferenceVoByEquipmentTemplateId(equipmentTemplateId);
    }

    private Integer newEquipmentID() {
        return RandomUtil.randomInt(1, 100000);
    }

    private Integer newEquipmentTemplateID() {
        return RandomUtil.randomInt(1, 100000);
    }


    @Override
    public Map<Integer, Long> countByEquipmentBaseType() {
        List<EquipmentCountDTO> countByEquipmentBaseType = equipmentMapper.findCountByEquipmentBaseType();
        if (Objects.isNull(countByEquipmentBaseType)) {
            return Map.of();
        }
        return countByEquipmentBaseType.stream().collect(Collectors.toMap(EquipmentCountDTO::getEquipmentBaseType, EquipmentCountDTO::getCount, ((v1, v2) -> v1)));
    }


}
