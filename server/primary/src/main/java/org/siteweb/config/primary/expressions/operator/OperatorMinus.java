package org.siteweb.config.primary.expressions.operator;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;

/**
 * 表示减法运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorMinus extends OperatorBase {
    //protected OperatorMinus(Double value) {
    //    super(value);
    //}

    @Override
    public String operatorSymbol() {
        return "-";
    }

    @Override
    public String operatorName() {
        return "减号";
    }

    @Override
    public int priority() {
        return 12;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.LEFT_TO_RIGHT;
    }

    @Override
    public int operandCount() {
        return 2;
    }

    @Override
    public double onCalculate(double[] operands) {
        return operands[0] - operands[1];
    }
}
