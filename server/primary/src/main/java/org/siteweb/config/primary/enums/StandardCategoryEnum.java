package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum StandardCategoryEnum {
    EMR(0, "维谛标准",""),
    MOBILE(1, "移动标准",""),
    TELECOM(2, "电信标准",""),
    UNICOM(3, "联通标准",""),
    BYTEDANTE(4, "字节标准","bd_standardpoint");

    /**
     * 值
     */
    private final int value;
    /**
     * 描述
     */
    private final String describe;
    /**
     * 判断的表
     */
    private final String tableName;

    public static StandardCategoryEnum getByValue(int value) {
        return Arrays.stream(values()).filter(e -> e.value == value).findFirst().orElse(EMR);
    }
}
