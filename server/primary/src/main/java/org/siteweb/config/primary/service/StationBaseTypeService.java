package org.siteweb.config.primary.service;

import jakarta.servlet.http.HttpServletResponse;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.TblStationBaseType;
import org.siteweb.config.common.vo.StandardApplyCheckVO;
import org.siteweb.config.common.vo.StandardCompareVO;
import org.siteweb.config.common.vo.StandardMappingCheckVO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface StationBaseTypeService {

    int deleteByStandardId(Integer standardId);

    int insert(TblStationBaseType tblStationBaseType);

    List<IdValueDTO<Integer, String>> findCurrentStationBaseType();

    /**
     * 获取基类局站类型
     *
     * @param stationBaseTypeId 标准化局站id
     */
    List<BaseStationMapTypeDTO> getBaseStationMapType(Integer stationBaseTypeId);

    /**
     * 保存基类局站类型关系
     */
    void saveBaseStationMapType(BaseStationMapTypeDTO baseStationMapTypeDTO);

    /**
     * 获取模板局站类型映射
     */
    List<EqStationBaseTypeDTO> getEquipmentTemplateStationBaseType();

    /**
     * 获取信号标准化配置
     */
    List<SignalStationBaseTypeDTO> getSignalStationBaseType();

    /**
     * 保存信号标准化配置基类信号
     */
    void saveSignalBaseMap(SignalStationBaseTypeDTO signalStationBaseTypeDTO);

    Map<String, String> getStationBaseTypeName(Object id);

    /**
     * 获取告警标准化配置
     */
    List<EventStationBaseTypeDTO> getEventStationBaseType();

    /**
     * 保存告警标准化配置基类告警
     */
    void saveEventBaseMap(EventStationBaseTypeDTO eventStationBaseTypeDTO);

    /**
     * 获取控制标准化配置
     */
    List<ControlStationBaseTypeDTO> getControlStationBaseType();

    /**
     * 保存控制标准化配置基类告警
     */
    void saveControlBaseMap(ControlStationBaseTypeDTO controlStationBaseTypeDTO);

    /**
     * 获取基类是否映射标准化的警告
     *
     * @param stationBaseType TBL_StationBaseType id
     * @param baseTypeId      baseTypeId
     * @param baseType        1 信号；2 事件；3 控制
     */
    BaseMapCountWarnDTO getWarnCountsInBaseMap(Integer standardDicId, Integer stationBaseType, List<Long> baseTypeId, Integer baseType);

    /**
     * 应用标准化
     */
    Long applyStandard(ApplyStandardDTO applyStandardDTO);

    /**
     * 还原标准化
     *
     * @param type 1 信号；2 事件；3 控制
     */
    Long restoreStandard(Integer type);

    /**
     * 获取标准化比较数据
     *
     * @param type 1 信号；2 事件；3 控制
     */
    StandardCompareVO getStandardCompareData(Integer type);

    /**
     * 获取应用标准化检查
     *
     * @param type 1 信号；2 事件；3 控制
     */
    StandardApplyCheckVO getStandardApplyCheckData(Integer type);

    /**
     * 导出应用标准化检查
     */
    void exportStandardApplyCheck(HttpServletResponse response);

    /**
     * 获取映射标准化检查
     *
     * @param type 1 信号；2 事件；3 控制
     * @param equipmentCategory 设备类型
     */
    StandardMappingCheckVO getStandardMappingCheck(Integer type, Integer equipmentCategory);

    /**
     * 导出客户标准化映射
     */
    void exportCustomStandardMapping(HttpServletResponse response);

    /**
     * 导入客户标准化映射
     */
    int importCustomStandardMapping(MultipartFile file);

    /**
     * 导出ini映射文件
     */
    ResponseEntity<byte[]> exportMappingIniFile();
}
