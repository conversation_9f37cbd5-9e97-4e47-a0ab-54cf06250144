package org.siteweb.config.primary.listener.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.SignalBaseMapExportDTO;
import org.siteweb.config.common.entity.TblSignalBaseMap;
import org.siteweb.config.common.mapper.TBLSignalBaseMapMapper;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.StandardTypeService;

import java.util.List;

@Slf4j
public class SignalBaseMapDataListener implements ReadListener<SignalBaseMapExportDTO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<TblSignalBaseMap> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private final TBLSignalBaseMapMapper signalBaseMapMapper;
    private final StandardTypeService standardTypeService;
    /**
     * 读取的行数
     */
    @Getter
    private int rowCount = 0;

    public SignalBaseMapDataListener(TBLSignalBaseMapMapper signalBaseMapMapper, StandardTypeService standardTypeService) {
        this.signalBaseMapMapper = signalBaseMapMapper;
        this.standardTypeService = standardTypeService;
    }


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     */
    @Override
    public void invoke(SignalBaseMapExportDTO data, AnalysisContext context) {
        if (rowCount == 0) {
            // 有sheet则先删除再保存
            StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
            signalBaseMapMapper.delete(Wrappers.lambdaUpdate(TblSignalBaseMap.class).eq(TblSignalBaseMap::getStandardType, currentStandardType.getValue()));
        }
        rowCount++;
        cachedDataList.add(SignalBaseMapExportDTO.convert(data));
        log.info("<SignalBaseMap>解析到一条数据:{}", SignalBaseMapExportDTO.convert(data));
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("<SignalBaseMap>所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("<SignalBaseMap> {}条数据，开始存储数据库！", cachedDataList.size());
        signalBaseMapMapper.insertBatchSomeColumn(cachedDataList);
        log.info("<SignalBaseMap>存储数据库成功！");
    }
}