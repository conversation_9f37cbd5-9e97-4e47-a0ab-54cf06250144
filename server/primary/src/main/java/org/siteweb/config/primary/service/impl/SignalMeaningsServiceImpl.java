package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.constants.SignalConstant;
import org.siteweb.config.common.dto.batchtool.EquipmentSignalMeaningsDTO;
import org.siteweb.config.common.entity.TblSignalMeanings;
import org.siteweb.config.common.mapper.TblSignalMeaningsMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.service.SignalMeaningsService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SignalMeaningsServiceImpl implements SignalMeaningsService {
    @Autowired
    TblSignalMeaningsMapper signalMeaningsMapper;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    I18n i18n;

    @Override
    public List<TblSignalMeanings> parseSignalMeaningStr(Integer equipmentTemplateId, Integer signalId, String[] meaningsArr) {
        List<TblSignalMeanings> signalMeaningsList = new ArrayList<>(meaningsArr.length);
        for (String meaning : meaningsArr) {
            String[] meaningArr = meaning.split(":");
            if (!NumberUtil.isNumber(meaningArr[0])){
                continue;
            }
            TblSignalMeanings signalMeanings = new TblSignalMeanings();
            signalMeanings.setEquipmentTemplateId(equipmentTemplateId);
            signalMeanings.setSignalId(signalId);
            signalMeanings.setStateValue(Integer.valueOf(meaningArr[0]));
            signalMeanings.setMeanings(meaningArr[1]);
            //注意：预制的十个模板的XML文件中可能没有信号含义的基类信息
            if (meaningArr.length == 3 && CharSequenceUtil.isNotBlank(meaningArr[2])) {
                signalMeanings.setBaseCondId(Integer.valueOf(meaningArr[2]));
            }
            signalMeaningsList.add(signalMeanings);
        }
        return signalMeaningsList;
    }

    @Override
    public void batchCreateSignalMeanings(List<TblSignalMeanings> signalMeaningList){
        if (CollUtil.isEmpty(signalMeaningList)) {
            return;
        }
        signalMeaningsMapper.insertBatchSomeColumn(signalMeaningList);
    }

    @Override
    public void communicationStateSignalMeaning(Integer equipmentTemplateId) {
        //0:通讯异常:0", "1:通讯正常:1
        String[] meaningsArr = {"0:" + i18n.T("monitor.equipment.communicationFail") + ":0", "1:" + i18n.T("monitor.equipment.communicationOk") + ":1"};
        List<TblSignalMeanings> signalMeaningsList = parseSignalMeaningStr(equipmentTemplateId, SignalConstant.COMMUNICATION_STATE_SIGNAL, meaningsArr);
        batchCreateSignalMeanings(signalMeaningsList);
    }

    @Override
    public List<TblSignalMeanings> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        return signalMeaningsMapper.selectList(new QueryWrapper<TblSignalMeanings>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        signalMeaningsMapper.delete(Wrappers.lambdaQuery(TblSignalMeanings.class)
                                            .eq(TblSignalMeanings::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public List<TblSignalMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId){
        return signalMeaningsMapper.selectList(Wrappers.lambdaQuery(TblSignalMeanings.class)
                                                       .eq(TblSignalMeanings::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void copySignalMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<TblSignalMeanings> signalMeaningsList = findByEquipmentTemplateId(originEquipmentTemplateId);
        signalMeaningsList.forEach(meanings -> meanings.setEquipmentTemplateId(destEquipmentTemplateId));
        batchCreateSignalMeanings(signalMeaningsList);
    }

    @Override
    public void updateSignalMeanings(Integer equipmentTemplateId, Integer signalId, List<TblSignalMeanings> signalMeaningsList) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
        if (CollUtil.isEmpty(signalMeaningsList)) {
            return;
        }
        batchCreateSignalMeanings(signalMeaningsList);
    }

    @Override
    public void batchInsertLianTongSignalMeanings() {
        signalMeaningsMapper.batchInsertLianTongSignalMeanings();
    }

    @Override
    public List<EquipmentSignalMeaningsDTO> findSignalsByEquipmentAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return new ArrayList<>();
        }
        return signalMeaningsMapper.findSignalsByEquipmentAndSignalIds(equipmentId, signalIds);
    }

    private void deleteByEquipmentIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        signalMeaningsMapper.delete(Wrappers.lambdaQuery(TblSignalMeanings.class)
                                            .eq(TblSignalMeanings::getEquipmentTemplateId, equipmentTemplateId)
                                            .eq(TblSignalMeanings::getSignalId, signalId));
    }
}
