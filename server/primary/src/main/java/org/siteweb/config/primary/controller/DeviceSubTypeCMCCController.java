package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.DeviceSubTypeCMCCService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/devicesubtypecmcc")
public class DeviceSubTypeCMCCController {
    @Autowired
    DeviceSubTypeCMCCService deviceSubTypeCMCCService;

    @GetMapping(params = "deviceTypeId")
    public ResponseEntity<ResponseResult> getDeviceSubTypeCMCC(Integer deviceTypeId) {
        return ResponseHelper.successful(deviceSubTypeCMCCService.findIdValue(deviceTypeId));
    }
}
