package org.siteweb.config.primary.utils;

import java.nio.file.Paths;

/**
 * 文件后缀处理工具类
 */
public final class FileSuffixUtil {

    private FileSuffixUtil() {
        // 防止实例化
    }

    /**
     * 通用的后缀替换方法，将任何文件路径的现有后缀（最后一个 '.' 之后的全部）替换为 newSuffix。
     *
     * @param fullPath  原始路径，可以是 "foo/bar.txt"、"lib\\abc.DLL"、"noext"、null
     * @param newSuffix 新后缀（可以带点或不带点），如 ".so" 或 "so"
     * @return 替换完毕的新路径；当 fullPath 为 null 时也返回 null
     */
    public static String changeSuffix(String fullPath, String newSuffix) {
        if (fullPath == null) {
            return null;
        }
        if (newSuffix == null || newSuffix.trim().isEmpty()) {
            throw new IllegalArgumentException("newSuffix 不能为空");
        }

        // 规范化 newSuffix：确保以 '.' 开头
        String suffix = newSuffix.startsWith(".") ? newSuffix : "." + newSuffix;

        // 找到最后一个路径分隔符
        int sep = Math.max(fullPath.lastIndexOf('/'), fullPath.lastIndexOf('\\'));
        String dir = sep >= 0 ? fullPath.substring(0, sep + 1) : "";
        String fileName = sep >= 0 ? fullPath.substring(sep + 1) : fullPath;

        // 找到 fileName 中最后一个 '.'
        int dot = fileName.lastIndexOf('.');
        String baseName = (dot > 0) ? fileName.substring(0, dot) : fileName;

        return dir + baseName + suffix;
    }

    /**
     * 专门将后缀替换成 ".so" 的快捷方法。
     *
     * @param fullPath 原始路径
     * @return 把最末的后缀统一换成 ".so"
     */
    public static String changeToSo(String fullPath) {
        return changeSuffix(fullPath, ".so");
    }

    /**
     * 获取路径中的后缀（不包含点），例如 "foo/bar.TXT" 返回 "TXT"；
     * 没有后缀或以点开头的隐藏文件则返回空字符串。
     *
     * @param fullPath 文件路径
     * @return 后缀（不含'.'），如果没有则返回 ""
     */
    public static String getSuffix(String fullPath) {
        if (fullPath == null) {
            return "";
        }
        String fileName = Paths.get(fullPath).getFileName().toString();
        int dot = fileName.lastIndexOf('.');
        if (dot > 0 && dot < fileName.length() - 1) {
            return fileName.substring(dot + 1);
        }
        return "";
    }

    /**
     * 判断路径是否带指定后缀（忽略大小写）。
     *
     * @param fullPath 原始路径
     * @param suffix   后缀，可以带点或不带点
     * @return true if 匹配
     */
    public static boolean hasSuffixIgnoreCase(String fullPath, String suffix) {
        if (fullPath == null || suffix == null) {
            return false;
        }
        String norm = suffix.startsWith(".") ? suffix : "." + suffix;
        return fullPath.toLowerCase().endsWith(norm.toLowerCase());
    }

    /**
     * 移除路径中的后缀（最后一个 '.' 及其后面的内容）。
     *
     * @param fullPath 原始路径
     * @return 移除后缀后的路径；若无后缀则原样返回
     */
    public static String removeSuffix(String fullPath) {
        if (fullPath == null) {
            return null;
        }
        int sep = Math.max(fullPath.lastIndexOf('/'), fullPath.lastIndexOf('\\'));
        String dir = sep >= 0 ? fullPath.substring(0, sep + 1) : "";
        String fileName = sep >= 0 ? fullPath.substring(sep + 1) : fullPath;

        int dot = fileName.lastIndexOf('.');
        String baseName = (dot > 0) ? fileName.substring(0, dot) : fileName;
        return dir + baseName;
    }
}

