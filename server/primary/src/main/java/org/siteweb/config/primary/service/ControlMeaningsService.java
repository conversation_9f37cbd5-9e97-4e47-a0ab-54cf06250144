package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblControlMeanings;

import java.util.List;

public interface ControlMeaningsService {

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    void deleteByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId);

    void deleteControlMeanings(TblControlMeanings controlMeanings);

    /**
     * 复制控制含义
     *
     * @param originEquipmentTemplateId 原始设备模板id
     * @param destEquipmentTemplateId   目标设备模板id
     */
    void copyControlMeanings(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId);

    void batchInsert(List<TblControlMeanings> controlMeaningsList);

    List<TblControlMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsertControlMeanings();

    void batchInsertControlMeanings(Integer equipmentTemplateId, Integer controlId, Integer originEquipmentTemplateId, Integer originControlId);

    void batchUpdateControlMeanings(List<TblControlMeanings> controlMeanings);

    void createControlMeanings(TblControlMeanings controlMeanings);

    List<TblControlMeanings> findByEquipmentTemplateIdAndControlId(Integer equipmentTemplateId, Integer controlId);
}
