package org.siteweb.config.primary.websocket.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WebSocketBusinessTypeEnum {
    CONFIGURATION_GENERATION(1, "配置生成"),
    CONFIGURATION_DISTRIBUTION(2,"配置下发"),
    TELENT_CONNECTION(5,"telnet连接"),
    TELNET_AVAILABLE(6,"telnet连接成功");
    private final Integer type;
    private final String describe;
}
