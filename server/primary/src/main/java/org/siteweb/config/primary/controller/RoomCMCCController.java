package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblRoomCMCC;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.RoomCMCCService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/roomcmcc")
public class RoomCMCCController {
    @Autowired
    private RoomCMCCService roomCMCCService;

    @GetMapping
    public ResponseEntity<ResponseResult> getAll(){
        roomCMCCService.init();
        return ResponseHelper.successful(roomCMCCService.findAll());
    }

    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody TblRoomCMCC tblRoomCMCC){
        return ResponseHelper.successful(roomCMCCService.update(tblRoomCMCC));
    }
}
