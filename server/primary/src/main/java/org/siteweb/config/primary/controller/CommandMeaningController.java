package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblControlMeanings;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.ControlMeaningsService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/10
 */
@Slf4j
@RestController
@RequestMapping("/controlmeanings")
public class CommandMeaningController {

    @Autowired
    ControlMeaningsService controlMeaningsService;


    @PutMapping(value = "/batchupdate", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> batchUpdateControl(@RequestBody List<TblControlMeanings> controlMeanings) {
        controlMeaningsService.batchUpdateControlMeanings(controlMeanings);
        return ResponseHelper.successful();
    }

    @PostMapping(value = "/create", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createControlMeanings(@RequestBody TblControlMeanings controlMeanings) {
        controlMeaningsService.createControlMeanings(controlMeanings);
        return ResponseHelper.successful();
    }

    @DeleteMapping(value = "/delete", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> deleteControlMeanings(@RequestBody TblControlMeanings controlMeanings) {
        controlMeaningsService.deleteControlMeanings(controlMeanings);
        return ResponseHelper.successful();
    }

}
