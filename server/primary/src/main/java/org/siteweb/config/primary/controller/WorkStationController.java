package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.WorkStationDTO;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.vo.ServerSourceVO;
import org.siteweb.config.primary.service.WorkStationService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description: 工作站/服务器接口
 * Author: <EMAIL>
 * Creation Date: 2024/3/12
 */
@Slf4j
@RestController
@RequestMapping("/workstation")
public class WorkStationController {

    @Autowired
    private WorkStationService workStationService;



    @GetMapping(value = "/list")
    public ResponseEntity<ResponseResult> list() {
        return ResponseHelper.successful(workStationService.List());
    }


    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> create(@RequestBody WorkStationDTO workStationDTO) {
        workStationService.create(workStationDTO);
        return ResponseHelper.successful();
    }

    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> update(@RequestBody WorkStationDTO workStationDTO) {
        workStationService.update(workStationDTO);
        return ResponseHelper.successful();
    }


    @DeleteMapping("/delete")
    public ResponseEntity<ResponseResult> delete(@RequestParam List<Integer> ids) {
        return ResponseHelper.successful(workStationService.deleteByWorkStationIds(ids));
    }


    @GetMapping(value = "/server-source-list")
    public ResponseEntity<ResponseResult> getRServerSourceList() {
        List<ServerSourceVO> list = workStationService.findWorkStations().stream()
                .filter(ServerSourceVO::filter)
                .map(ServerSourceVO::from)
                .toList();
        return ResponseHelper.successful(list);
    }


}
