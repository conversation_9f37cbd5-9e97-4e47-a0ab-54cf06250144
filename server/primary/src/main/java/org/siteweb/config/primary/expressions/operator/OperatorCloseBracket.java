package org.siteweb.config.primary.expressions.operator;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;

/**
 * 表示闭括号运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorCloseBracket extends OperatorBase{
    @Override
    public String operatorSymbol() {
        return ")";
    }

    @Override
    public String operatorName() {
        return "右括号";
    }

    @Override
    public int priority() {
        return 0;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.NONE;
    }

    @Override
    public int operandCount() {
        return 2;
    }

    @Override
    public double onCalculate(double[] operands) {
        throw new RuntimeException("右括号 不允许计算");
    }
}
