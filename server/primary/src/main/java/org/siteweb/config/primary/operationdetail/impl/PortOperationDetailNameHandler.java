package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TslPortMapper;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PortOperationDetailNameHandler extends AbstractNameHandler {
    @Autowired
    TslPortMapper tslPortMapper;
    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Integer> idList = ids.stream().map(Integer::valueOf).toList();
        return tslPortMapper.findNamesByIds(idList);
    }

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.PORT;
    }
}
