package org.siteweb.config.primary.service;


import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.vo.EquipmentReferenceVO;
import org.siteweb.config.common.vo.EquipmentVO;
import org.siteweb.config.common.vo.StationEquipment;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;


/***
 * 设备对象管理服务
 * 设备唯一缓存池
 *
 * <AUTHOR> (2024/3/8)
 */
public interface EquipmentService {

    Collection<TblEquipment> allEquipment();


    TblEquipment findEquipmentById(Integer equipmentId);

    /**
     * 创建设备API
     *
     * @param equipment      设备信息
     * <AUTHOR> (2024/3/6)
     */

    TblEquipment createEquipment(TblEquipment equipment);



    boolean existsByEquipmentTemplateId(Integer equipmentTemplateId);

    List<TblEquipment> findByEquipmentTemplateId(Integer equipmentTemplateId);

    List<TblEquipment> findByEquipmentTemplateIds(Set<Integer> equipmentTemplateIds);

    List<EquipmentReferenceVO> findReferenceVoByEquipmentTemplateId(Integer equipmentTemplateId);


    /**
     * 更新设备类别
     * @param businessId
     * @param categoryTypeId
     */
    void updateEquipmentCategoryByCategoryIdMap(int businessId, int categoryTypeId);

    /**
     * 获取设备信息详情
     *
     * @param equipmentId 设备ID
     * @return {@link EquipmentDetailDTO}
     * <AUTHOR> (2024/4/11)
     */
    EquipmentDetailDTO findEquipmentDetail(Integer equipmentId);


    /**
     * 修改设备信息
     *
     * @param equipmentDetailDTO 设备信息详情
     * <AUTHOR> (2024/4/12)
     */
    int updateEquipment(EquipmentDetailDTO equipmentDetailDTO);

    boolean deleteEquipment(Integer equipmentId);


    /**
     * 设备切换模板
     *
     * @param switchTemplateDTO 切换模板dto
     * @return boolean 是否切换成功
     */
    boolean switchEquipmentTemplate(SwitchTemplateDTO switchTemplateDTO);

    List<TblEquipment> findByMonitorUnitId(Integer monitorUnitId);

    List<TblEquipment> findByMonitorUnitIdByCategory(Integer monitorUnitId, List<Integer> EquipmentCategoryIds);

    /**
     * 获取局站下精简的的设备列表
     * 通过设备id拿局站id
     *
     * @param equipmentId 设备id
     * <AUTHOR> (2024/4/17)
     */
    List<SimplifyEquipmentDTO> findSimplifyEquipmentsByStationId(Integer equipmentId);

    /**
     * 获取局站和监控单元下精简的的设备列表（告警过滤表达式用）
     *
     * @param stationId 局站ID
     * @param monitorUnitId 监控单元ID
     * <AUTHOR> (2024/4/17)
     */
    List<SimplifyEquipmentDTO> findSimplifyEquipmentsByStationIdAndMonitorUnitId(Integer stationId, Integer monitorUnitId);

    /**
     * 设备实列化【给设备创建一个新的模板并切换】
     *
     * @param equipmentId 设备主键id
     * @return boolean
     */
    int equipmentInstance(Integer equipmentId);


    List<TblEquipment> findByStationId(Integer stationId);

    List<EquipmentVO> findByStructureId(Integer resourceStructureId);

    List<EquipmentVO> findEquipmentVOsByStructureId(Integer resourceStructureId);

    void deleteByMonitorUnitId(Integer monitorUnitId);

    List<StationEquipment> findStationEquipmentList();

    /**
     * 判断此设备基类下相关设备数量
     *
     * key EquipmentBaseType value 设备数量
     */
    Map<Integer, Long> countByEquipmentBaseType();


    TblEquipment findEquipmentByStationNameAndEquipmentName(String stationName, String equipmentName);

    TblEquipment getEquipmentsByEquipmentName(String equipmentName);

    /**
     * 从数据库获取最新的设备
     */
    TblEquipment findByIdFromDB(Integer equipmentId);
    /**
     * 获取监控单元下的设备列表
     *
     *  @param spliceFlag 拼接标志 true:拼接名称和id
     */
    List<SimplifyEquipmentDTO> findSimplifyEquipmentsByMonitorUnitId(Integer monitorUnitId, Boolean spliceFlag);

    void deleteBySamplerUnitId(Integer samplerUnitId);

    List<IdValueDTO<String, String>> findNamesByIds(List<Integer> equipmentIds);

    List<TblEquipment> findBySamplerUnitId(Integer samplerUnitId);

    List<TblEquipment> findEquipments();

    List<TblEquipment> findEqsByMonitorUnitId(Integer monitorUnitId);

    // 查询监控单元ID和设备名称判断是否相同
    boolean existsByMonitorUnitIdAndEquipmentName(Integer equipmentId, Integer monitorUnitId, String equipmentName);


    List<TblEquipment> findByHouseId(Integer houseId);

    List<TblEquipment> findByHouseIdAndStationId(Integer stationId, Integer houseId);

    void deleteByStationId(Integer stationId);


    void syncEquipmentStructure(TblEquipment equipment);

    void syncSwatchEquipment(TblEquipment equipment);

    boolean mappingEquipment(MappingEquipmentDTO mappingEquipmentDTO);

    boolean unMappingEquipment(MappingEquipmentDTO mappingEquipmentDTO);

    List<TblEquipment> mappingEquipments(Integer stationId, Integer houseId);


    boolean updateResourceStructureId(Integer equipmentId, Integer resourceStructureId);

    boolean updateResourceStructureIdIfNotMapped(Integer stationId, Integer resourceStructureId);

    boolean clearEquipmentResourceStructureId(Integer resourceStructureId);

    boolean updateByHouseIdAndResourceStructureId(TblEquipment equipment);

    boolean updateHouseId(EquipmentDTO equipmentDTO);

    Boolean generateUuid();

    /**
     * 给定一个设备模板ID，获取该设备模板下的所有设备，检查是否设备类型一致，不一致就修改设备类型、单位、厂商、型号
     */
    void checkEquipmentSyncField(Integer equipmentTemplateId);

}
