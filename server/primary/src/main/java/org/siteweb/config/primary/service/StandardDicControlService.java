package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblStandardDicControl;

import java.util.List;

public interface StandardDicControlService {
    List<TblStandardDicControl> findCurrentStandardDicControl();

    TblStandardDicControl create(TblStandardDicControl tblStandardDicControl);

    TblStandardDicControl update(TblStandardDicControl standardDicControl);

    int deleteByStandardDicIdAndStandardType(Integer standardDicId, Integer standardTypeId);

    List<TblStandardDicControl> findByStandardType(Integer standardCategory);
}
