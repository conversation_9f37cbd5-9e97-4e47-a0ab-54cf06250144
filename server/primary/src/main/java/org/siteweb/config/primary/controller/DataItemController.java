package org.siteweb.config.primary.controller;


import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.dataitem.DataItemCreateDTO;
import org.siteweb.config.common.dto.dataitem.DataItemUpdateDTO;
import org.siteweb.config.common.entity.TblDataItem;
import org.siteweb.config.common.entity.TblStandardType;
import org.siteweb.config.common.entity.TblSysConfig;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.utils.StrSplitUtil;
import org.siteweb.config.primary.enums.SysConfigEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;


/**
 * Description: 数据字典值接口
 * Author: <EMAIL>
 * Creation Date: 2024/3/12
 */
@Slf4j
@RestController
@RequestMapping("/dataitems")
public class DataItemController {

    @Autowired
    DataItemService dataItemService;
    @Autowired
    CategoryIdMapService categoryIdMapService;

    @Autowired
    SysConfigService sysConfigService;


    @Autowired
    EquipmentService equipmentService;


    @Autowired
    EquipmentTemplateService equipmentTemplateService;

    @Autowired
    StandardTypeService standardTypeService;


    @GetMapping
    public ResponseEntity<ResponseResult> getDataItems(@RequestParam(value = "entryId", required = false) Integer entryId) {
        return ResponseHelper.successful(dataItemService.findByEntryId(DataEntryEnum.findByValue(entryId)));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateDataItem(@RequestBody DataItemUpdateDTO dataItemUpdateDTO) {
        return ResponseHelper.successful(dataItemService.update(BeanUtil.copyProperties(dataItemUpdateDTO, TblDataItem.class)));
    }

    @PostMapping
    public ResponseEntity<ResponseResult> createDataItem(@RequestBody DataItemCreateDTO dataItemCreateDTO) {
        TblDataItem tblDataItem = BeanUtil.copyProperties(dataItemCreateDTO, TblDataItem.class);
        return ResponseHelper.successful(dataItemService.createDataItem(tblDataItem));
    }

    @DeleteMapping(params = "entryItemIds")
    public ResponseEntity<ResponseResult> deleteDataItem(String entryItemIds) {
        return ResponseHelper.successful(dataItemService.deleteByEntryItemIds(StrSplitUtil.splitToIntList(entryItemIds)));
    }

    /***
     * 获取不同标准下的设备种类下的key，如维谛标准下的电池设备key为24,而联通标准下的电池key有24(蓄电池) 26(低压进线柜) 36
     *
     * @param originCategoryKey 原设备种类下的key，如24
     */
    @GetMapping("/equipmentcategorys")
    public ResponseEntity<ResponseResult> getBusinessCategoryFromOriginCategory(Integer originCategoryKey) {
        return ResponseHelper.successful(categoryIdMapService.findBusinessCategoryFromOriginCategory(originCategoryKey));
    }


    @PostMapping("/bytedance")
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<ResponseResult> refreshByteDanceDataItem() {
        dataItemService.deleteByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY);

        Arrays.asList(
                new TblDataItem(7, 101, 0, 0, true, "电力设备", "电力系统及设备分类", "Power Equipment", "power.png", "1", null),
                new TblDataItem(7, 102, 0, 0, true, "暖通设备", "暖通空调系统及设备分类", "HVAC Equipment", "hvac.png", "2", null),
                new TblDataItem(7, 103, 0, 0, true, "环境设备", "环境监测及控制设备分类", "Environmental Equipment", "environment.png", "3", null),
                new TblDataItem(7, 104, 0, 0, true, "消防设备", "消防系统及设备分类", "Fire Protection Equipment", "fire.png", "4", null),
                new TblDataItem(7, 105, 0, 0, true, "监控设备", "监控系统及设备分类", "Monitoring Equipment", "monitor.png", "5", null),
                new TblDataItem(7, 106, 0, 0, true, "其他设备", "其他系统及设备分类", "Other Equipment", "other.png", "6", null)
        ).forEach(item -> dataItemService.saveDictionaryItemByEntry(item));


        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10101, 0, 101, true, "中压隔离柜", "", "Medium Voltage Isolation Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10102, 0, 101, true, "中压进线柜", "", "Medium Voltage Incoming Power Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10103, 0, 101, true, "中压计量柜", "", "Medium Voltage Measurement Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10104, 0, 101, true, "中压PT柜", "", "Medium Voltage PT cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10105, 0, 101, true, "中压出线柜", "", "Medium Voltage outgoing Power Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10106, 0, 101, true, "中压联络柜", "", "Medium Voltage Bus Coupling Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10107, 0, 101, true, "变压器检修隔离柜", "", "Transformer Maintenance Isolation Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10108, 0, 101, true, "操作电源", "", "Operational Power Supply", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10109, 0, 101, true, "消谐装置", "", "Harmonic Elimination Device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10110, 0, 101, true, "接地装置", "", "Grounding Device", "ground.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10111, 0, 101, true, "中压电容补偿柜", "", "Medium Voltage capacitor compensation cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10112, 0, 101, true, "中压电抗柜", "", "Medium Voltage reactor cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10113, 0, 101, true, "公用测控屏", "", "Public metering and control screen", "screen.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10114, 0, 101, true, "通讯屏", "", "Communication screen", "screen.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10115, 0, 101, true, "中压投切系统", "", "MV Automatic Transfer Switching System", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10116, 0, 101, true, "柴发进线柜", "", "Diesel Generator Incoming Power Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10124, 0, 101, true, "柴发联络柜", "", "Genset Coupling Cabinet", "cabinet.png", "5", null));

        // 变压器系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10201, 0, 101, true, "变压器", "", "Transformer", "transformer.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10202, 0, 101, true, "变压器温控仪", "", "Temperature Controller Of Transformer", "controller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10203, 0, 101, true, "变压器中性点接地电阻柜", "", "Transformer neutral point grounding resistance cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10204, 0, 101, true, "有载调压装置", "", "On-load tap changer (OLTC)", "device.png", "5", null));

        // 低压配电系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10301, 0, 101, true, "低压进线柜", "", "Main LV Switch Board", "switchboard.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10302, 0, 101, true, "低压出线柜", "", "Low Voltage Outgoing Power Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10303, 0, 101, true, "低压联络柜", "", "Low Voltage Bus Coupling Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10304, 0, 101, true, "无功补偿柜", "", "Capacitance Compensation Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10305, 0, 101, true, "有源滤波器", "", "Hamonic Filter", "filter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10306, 0, 101, true, "动力配电柜", "", "AC Power Distribution Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10307, 0, 101, true, "照明配电柜", "", "Lighting Distribution Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10308, 0, 101, true, "消防配电柜", "", "Power Distribution Panel for Fire Protection System", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10309, 0, 101, true, "冷却塔配电柜", "", "Power Distribution Panel for Cooling Tower", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10310, 0, 101, true, "冷机配电柜", "", "Power Distribution Panel for Chiller", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10311, 0, 101, true, "冷冻水泵配电柜", "", "Power Distribution Panel for Chilled Water Pump", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10312, 0, 101, true, "冷冻水二次泵配电柜", "", "Power Distribution Panel for Secondary Chilled Water Pump", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10313, 0, 101, true, "冷却水泵配电柜", "", "Power Distribution Panel for Cooling Water Pump", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10314, 0, 101, true, "放冷泵配电柜", "", "Power Distribution Panel for Heat Dissipation Pump", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10315, 0, 101, true, "空调末端配电柜", "", "Power Distribution Panel for Air conditioning", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10316, 0, 101, true, "动力配电箱", "", "Power Distribution Box", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10317, 0, 101, true, "普通照明配电箱", "", "Power Distribution Box for General Lighting", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10318, 0, 101, true, "应急照明配电箱", "", "Power Distribution Box for Emergency Lighting", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10319, 0, 101, true, "消防配电箱", "", "Power Distribution Box for Fire Protection", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10320, 0, 101, true, "空调末端配电箱", "", "Power Distribution Box for Air conditioning", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10321, 0, 101, true, "低压母线", "", "Low Voltage Switchboard Incoming Cable Terminations", "cable.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10322, 0, 101, true, "ATS", "", "Automatic Transfer Switch Panel", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10323, 0, 101, true, "低压出线柜抽屉式开关", "", "Low-voltage output cabinet drawer-type switch", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10324, 0, 101, true, "三相STS", "", "STS Distribution Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10325, 0, 101, true, "低压备用柜", "", "Low Voltage Standby Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10326, 0, 101, true, "低压出线柜【交流列头柜】", "", "Low Voltage Power Distribution Panel [AC RPP]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10327, 0, 101, true, "低压出线柜【AHU】", "", "Low Voltage Power Distribution Panel [AHU]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10328, 0, 101, true, "低压柴发进线柜", "", "Low-voltage diesel generator set input cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10329, 0, 101, true, "低压出线柜【弱电】", "", "Low Voltage Power Distribution Panel [ELV]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10330, 0, 101, true, "低压出线柜【新风】", "", "Low Voltage Power Distribution Panel [Fresh Air System]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10331, 0, 101, true, "低压出线柜【消防用电】", "", "Low Voltage Power Distribution Panel [Fire Protection]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10332, 0, 101, true, "低压出线柜【操作电源】", "", "Low-voltage output cabinet [operating power supply]", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10333, 0, 101, true, "低压出线柜【电伴热】", "", "Low-voltage output cabinet [electric heating]", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10334, 0, 101, true, "低压出线柜【空调末端】", "", "Low Voltage Power Distribution Panel [ACMV]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10335, 0, 101, true, "低压出线柜【插座照明】", "", "Low Voltage Power Distribution Panel [General Power & Lighting]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10336, 0, 101, true, "低压出线柜【冷源辅助设施】", "", "Low Voltage Power Distribution Panel [cold source auxiliary facilities]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10337, 0, 101, true, "低压出线柜【油机辅助电源】", "", "Low Voltage Power Distribution Panel [Generator auxiliary power supply]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10338, 0, 101, true, "低压出线柜【公共电源】", "", "Low Voltage Power Distribution Panel [public power supply]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10339, 0, 101, true, "低压出线柜【冷机】", "", "Low Voltage Power Distribution Panel [chiller]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10340, 0, 101, true, "低压出线柜【冷冻泵】", "", "Low Voltage Power Distribution Panel [chilled water pump]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10341, 0, 101, true, "低压出线柜【二次泵】", "", "Low Voltage Power Distribution Panel [secondary chilled water pump]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10342, 0, 101, true, "低压出线柜【冷却泵】", "", "Low Voltage Power Distribution Panel [cooling pump]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10343, 0, 101, true, "低压出线柜【冷却塔】", "", "Low Voltage Power Distribution Panel [cooling tower]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10344, 0, 101, true, "低压出线柜【UPS】", "", "Low Voltage Power Distribution Panel [UPS]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10345, 0, 101, true, "低压出线柜【HVDC】", "", "Low Voltage Power Distribution Panel [HVDC]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10346, 0, 101, true, "低压出线柜【热源系统】", "", "Low Voltage Power Distribution Panel [thermal source system]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10347, 0, 101, true, "低压出线柜【动力配电】", "", "Low Voltage Power Distribution Panel [General Power]", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10348, 0, 101, true, "弱电配电箱", "", "Low Voltage Power Distribution Box [ELV]", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10349, 0, 101, true, "AHU配电箱", "", "Low Voltage Power Distribution Box [AHU]", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10350, 0, 101, true, "三相电源分配单元", "", "Three-Phase Power Distribution Unit", "unit.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10351, 0, 101, true, "低压隔离柜", "", "Low voltage isolation cabinet", "cabinet.png", "5", null));
        // 柴油发电机系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10401, 0, 101, true, "低压柴油发电机", "", "Low Voltage Fuel Generator", "generator.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10402, 0, 101, true, "中压柴油发电机", "", "Medium Voltage Fuel Generator", "generator.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10403, 0, 101, true, "日用油箱", "", "Day tank", "tank.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10404, 0, 101, true, "储油罐", "", "Fuel Tank", "tank.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10405, 0, 101, true, "加油装置配电柜【箱】", "", "Fuel Charging Device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10406, 0, 101, true, "分控制屏", "", "Sub Control Panel (Generator)", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10407, 0, 101, true, "柴发并机进线柜", "", "Diesel generator set input cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10408, 0, 101, true, "柴发并机出线柜", "", "Diesel generator set output cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10409, 0, 101, true, "柴发PT柜", "", "Diesel generator set PT cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10410, 0, 101, true, "假负载出线柜", "", "Loadbank output cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10411, 0, 101, true, "接地电阻开关柜", "", "Grounding resistance switchgear cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10412, 0, 101, true, "接地电阻柜", "", "Grounding resistance cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10413, 0, 101, true, "柴发直流操作电源", "", "Diesel generator set DC operating power supply", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10414, 0, 101, true, "柴发供油泵", "", "Diesel generator set fuel supply pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10415, 0, 101, true, "柴发泄油泵", "", "Diesel generator set oil drain pump", "pump.png", "5", null));
        // UPS系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10501, 0, 101, true, "单相UPS", "", "Single Phase UPS", "ups.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10502, 0, 101, true, "三相UPS【IT】", "", "3 Phase UPS [IT]", "ups.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10503, 0, 101, true, "三相模块化UPS【IT】", "", "3 Phase Modular UPS [IT]", "ups.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10504, 0, 101, true, "逆变器", "", "Inverter", "inverter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10505, 0, 101, true, "UPS主路进线柜", "", "UPS Input Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10506, 0, 101, true, "UPS出线柜", "", "UPS Output Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10507, 0, 101, true, "UPS出线柜抽屉式开关", "", "UPS output cabinet drawer-type switch", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10509, 0, 101, true, "三相UPS【DL】", "", "Three-phase UPS [Mech]", "ups.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10510, 0, 101, true, "三相模块化UPS【DL】", "", "Three-phase modular UPS [Mech]", "ups.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10513, 0, 101, true, "UPS旁路进线柜", "", "UPS Bypass Incoming Power Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10514, 0, 101, true, "UPS维修旁路", "", "UPS maintenance bypass", "bypass.png", "5", null));
        // HVDC系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10601, 0, 101, true, "HVDC电源", "", "High Voltage DC Power Supply", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10602, 0, 101, true, "HVDC电源交流柜", "", "HVDC AC Power Distribution", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10603, 0, 101, true, "HVDC电源整流柜", "", "HVDC Rectifier Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10604, 0, 101, true, "HVDC电源直流柜", "", "HVDC DC Power Distribution", "cabinet.png", "5", null));
        // 直流电源系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10701, 0, 101, true, "48V电源", "", "48VDC Switch Power Supply", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10702, 0, 101, true, "48V电源交流柜", "", "AC Power Distribution of 48VDC", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10703, 0, 101, true, "48V电源整流柜", "", "48VDC Rectifier", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10704, 0, 101, true, "48V电源直流柜", "", "DC Power Distribution of 48VDC", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10705, 0, 101, true, "48V嵌入式电源", "", "48V Embedded PS", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10706, 0, 101, true, "直流-直流变换器", "", "DC-DC Converter", "converter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10707, 0, 101, true, "110V直流电源", "", "110V DC power supply", "power.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10708, 0, 101, true, "220V直流电源", "", "220V DC power supply", "power.png", "5", null));
        // 电池系统设备小1
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10801, 0, 101, true, "铅酸阀控蓄电池组【12V】", "", "Lead-acid valve-regulated battery pack [12V]", "battery.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10802, 0, 101, true, "铅酸阀控蓄电池组【2V】", "", "Lead-acid valve-regulated battery pack [2V]", "battery.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10803, 0, 101, true, "燃料电池", "", "Fuel Cell", "cell.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10804, 0, 101, true, "三元锂离子电池", "", "Ternary Lithium-Ion Battery", "battery.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10805, 0, 101, true, "磷酸铁锂电池组", "", "Lithium Iron Phosphate Battery", "battery.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10807, 0, 101, true, "电池组", "", "Battery Group", "battery.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10808, 0, 101, true, "柴油发电机蓄电池组", "", "Diesel generator battery pack", "battery.png", "5", null));
        // 列头柜系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10901, 0, 101, true, "交流精密列头柜", "", "AC Precise Head Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10902, 0, 101, true, "交流非精密列头柜", "", "AC Head Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10903, 0, 101, true, "高压直流列头柜", "", "HVDC Head Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10904, 0, 101, true, "48V列头柜", "", "48V Head Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10905, 0, 101, true, "交直流列头柜", "", "AC&DC Head Cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10906, 0, 101, true, "交流插接箱", "", "AC Tap Off Unit", "unit.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10907, 0, 101, true, "直流插接箱", "", "DC Tap Off Unit", "unit.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 10908, 0, 101, true, "交流始端箱", "", "End Feed", "feed.png", "5", null));

        // 直流配电设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11001, 0, 101, true, "48V配电柜", "", "DC Power Distribution Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11002, 0, 101, true, "高压直流配电柜", "", "HVDC Power Distribution Panel", "panel.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11003, 0, 101, true, "110V直流配电柜", "", "110V DC distribution box", "box.png", "5", null));

        // PDU设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11101, 0, 101, true, "三相交流rPDU", "", "AC rPDU(three-phase)", "pdu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11102, 0, 101, true, "直流rPDU", "", "DC rPDU", "pdu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11103, 0, 101, true, "单相交流rPDU", "", "AC rPDU", "pdu.png", "5", null));

        // 电量计量设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11201, 0, 101, true, "交流电量仪", "", "AC Power Meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11202, 0, 101, true, "基本型电量仪2", "", "High Level AC Power Meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11203, 0, 101, true, "增强型电量仪", "", "Enhanced power meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11204, 0, 101, true, "直流电量仪", "", "DC Power Meter", "meter.png", "5", null));
        // 变频设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11301, 0, 101, true, "变频器", "", "Variable-Frequency Drive", "drive.png", "5", null));

        // 软启动设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 11401, 0, 101, true, "软启动器", "", "Soft Starter", "starter.png", "5", null));

        // 动力设备局房
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 51, 0, 101, true, "动力设备局房", "", "Power Equipment House", "Equipment.png", "5", null));


        // 空调制冷设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20101, 0, 102, true, "冷冻水型精密空调", "", "Water Cooled Air Conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20102, 0, 102, true, "风冷型精密空调", "", "Air Cooled Air Conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20103, 0, 102, true, "双冷源精密空调", "", "Double Cooling Source Air Conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20104, 0, 102, true, "冷冻水型精密列间空调", "", "Chilled water type precision in-row air conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20105, 0, 102, true, "风冷型精密列间空调", "", "Air-cooled precision in-row air conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20106, 0, 102, true, "双冷源精密列间空调", "", "Double cooling source precision in-row air conditioner", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20107, 0, 102, true, "风墙AHU空调", "", "Fanwall Air Handling Unit", "ahu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20108, 0, 102, true, "间接蒸发自然冷却AHU空调", "", "Indirect evaporative cooling Air Handling Unit", "ahu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20109, 0, 102, true, "OCU", "", "Opening Cooling Unit", "ocu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20110, 0, 102, true, "新风机", "", "Fresh Air Unit", "fan.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20111, 0, 102, true, "风机", "", "Exhaust Fan", "fan.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20112, 0, 102, true, "湿膜加湿器", "", "Humidifier", "humidifier.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20113, 0, 102, true, "高压微雾加湿器", "", "High Pressure Micro Fog Humidifier", "humidifier.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20114, 0, 102, true, "除湿机", "", "Dehumidifier", "dehumidifier.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20115, 0, 102, true, "湿膜恒湿机", "", "Humidifying film humidifier", "humidifier.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20116, 0, 102, true, "VRV空调", "", "VRV air conditioning", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20117, 0, 102, true, "全热交换器", "", "Heat recovery ventilator (HRV)", "hrv.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20118, 0, 102, true, "化学过滤器", "", "Chemical filter", "filter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20119, 0, 102, true, "电动调节风阀", "", "Air Value", "valve.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20120, 0, 102, true, "电动开关风阀", "", "48V Head Cabinet Of Each Row", "valve.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20121, 0, 102, true, "冷冻水型风墙空调末端", "", "Chilled water type fan wall", "fanwall.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20122, 0, 102, true, "CDU", "", "CDU", "cdu.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20123, 0, 102, true, "Cubic", "", "Cubic", "cubic.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20125, 0, 102, true, "iRack", "", "IRack", "rack.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20127, 0, 102, true, "整体式氟泵PHU", "", "Integral fluorine pump PHU", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20128, 0, 102, true, "相变冷却内机", "", "Phase-change cooling indoor unit", "cooling.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20129, 0, 102, true, "相变冷却外机", "", "Phase-change cooling outdoor unit", "cooling.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20130, 0, 102, true, "Delta立方体阵列空调", "", "Delta Cube Array", "ac.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20131, 0, 102, true, "氟泵多联", "", "fluorine pump group", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20132, 0, 102, true, "RTU屋顶集成空气处理单元", "", "Rooftop Unit", "rtu.png", "5", null));

        // 制冷系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20201, 0, 102, true, "水冷冷水机组", "", "Water Cooled Chiller", "chiller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20202, 0, 102, true, "风冷冷水机组", "", "Air Cooled Chiller", "chiller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20204, 0, 102, true, "冷却塔", "", "Cooling Tower", "tower.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20205, 0, 102, true, "开式塔", "", "Open cooling tower", "tower.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20206, 0, 102, true, "闭式塔", "", "closed circuit type cooling tower", "tower.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20207, 0, 102, true, "冷冻水泵", "", "Chilled Water Pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20208, 0, 102, true, "冷冻水二次泵", "", "Chilled water secondary pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20209, 0, 102, true, "冷却水泵", "", "condenser water pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20210, 0, 102, true, "加湿水泵", "", "Humidification water pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20211, 0, 102, true, "冷却塔补水泵", "", "Cooling tower makeup water pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20212, 0, 102, true, "放冷泵", "", "Heat dissipation pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20213, 0, 102, true, "补水泵", "", "Make-up water pump", "pump.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20214, 0, 102, true, "定压装置", "", "Pressure Stabilizing Device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20215, 0, 102, true, "蓄冷罐", "", "Thermal Storage Tank", "tank.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20216, 0, 102, true, "加药装置", "", "Dosing device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20217, 0, 102, true, "软化水装置", "", "Demineralized Water Device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20218, 0, 102, true, "水池（箱）", "", "Water Tank", "tank.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20219, 0, 102, true, "板式换热器", "", "Plate Type Heat Exchanger", "exchanger.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20220, 0, 102, true, "冷凝器在线清洗装置", "", "Condenser online cleaning device", "device.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20221, 0, 102, true, "旁滤装置", "", "Side filter device", "filter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20222, 0, 102, true, "间接蒸发冷却冷水机组", "", "Indirect evaporative cooling chiller unit", "chiller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20223, 0, 102, true, "间接蒸发补冷机组", "", "Indirect evaporative cooling supplementary cooling unit", "cooling.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20228, 0, 102, true, "干冷器", "", "Dry cooler", "cooler.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20229, 0, 102, true, "间接冷站-新疆华亦", "", "Indirect cooling station", "station.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20230, 0, 102, true, "间接冷站-新疆绿色使者", "", "Indirect cooling station", "station.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20231, 0, 102, true, "RO水装置", "", "", "device.png", "5", null));

        // 暖通附件设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20301, 0, 102, true, "比例阀门", "", "Motor Proportional Valve", "valve.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20302, 0, 102, true, "开关阀门", "", "Motorised Valve", "valve.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20303, 0, 102, true, "电伴热", "", "Heater", "heater.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20304, 0, 102, true, "安全阀", "", "Safety valve", "valve.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20305, 0, 102, true, "温度计", "", "Thermometer", "thermometer.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20306, 0, 102, true, "压力表", "", "Pressure gauge", "gauge.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20307, 0, 102, true, "远传水表", "", "Water meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20308, 0, 102, true, "保温隔热层", "", "Thermal insulation layer", "insulation.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20309, 0, 102, true, "过滤器", "", "Filter", "filter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20310, 0, 102, true, "冷冻水管", "", "chilled water pipe", "pipe.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20311, 0, 102, true, "冷却水管", "", "cooling water pipe", "pipe.png", "5", null));


        // 新风系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 20401, 0, 102, true, "直接冷却新风", "", "Direct cooling", "cooling.png", "5", null));

        // 传感器设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30101, 0, 103, true, "水温传感器", "", "Temperature Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30102, 0, 103, true, "湿度传感器", "", "Humidity Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30103, 0, 103, true, "设施包间温湿度传感器", "", "Temperature And Humidity Sensor [Facility Room]", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30104, 0, 103, true, "漏水检测器", "", "Water Leakage Detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30105, 0, 103, true, "定位漏水检测器", "", "Water leakage location detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30106, 0, 103, true, "液位传感器", "", "Liquid Level Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30107, 0, 103, true, "水压力传感器", "", "Lqguid Pressure Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30108, 0, 103, true, "单向流量传感器", "", "Single Direction Flow Meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30109, 0, 103, true, "双向流量传感器", "", "2-way Flow Meter", "meter.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30110, 0, 103, true, "干湿球温度传感器", "", "Dry And Wet Ball Temperature Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30111, 0, 103, true, "水压差传感器", "", "Differential Pressure Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30112, 0, 103, true, "冷媒泄露检测器", "", "Refrigerant Leakage Detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30113, 0, 103, true, "二氧化碳传感器", "", "Co2 Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30114, 0, 103, true, "大气压力传感器", "", "Atmospheric pressure Sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30115, 0, 103, true, "二氧化硫传感器", "", "Sulphur dioxide sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30116, 0, 103, true, "PM2.5传感器", "", "pm2.5 sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30117, 0, 103, true, "空气污染物传感器", "", "Air pollutant sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30118, 0, 103, true, "冷通道温湿度传感器", "", "cold aisle T&H sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30119, 0, 103, true, "热通道温湿度传感器", "", "hot aisle T&H sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30120, 0, 103, true, "交直流电压变送器", "", "A/C voltage transfer sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30121, 0, 103, true, "直流电流传感器", "", "Direct current sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30123, 0, 103, true, "单相电量采集器", "", "single phase power sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30124, 0, 103, true, "一体化数据采集器", "", "all in one alarm collector", "collector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30125, 0, 103, true, "双鉴探测器", "", "Dual-sensor detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30126, 0, 103, true, "磁力传感器", "", "Magnetic sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30127, 0, 103, true, "电子脉冲围栏", "", "Electronic pulse fence", "fence.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30128, 0, 103, true, "红外对射探测器", "", "Infrared photoelectric beam detector", "detector.png", "5", null));

        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30129, 0, 104, true, "感温火灾探测器", "", "Heat-sensitive fire detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30130, 0, 104, true, "感烟火灾探测器", "", "Smoke-sensitive fire detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30131, 0, 104, true, "极早期火灾探测器", "", "Aspirating smoke detector", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30132, 0, 104, true, "极早期烟雾报警传感器（消防系统）", "", "Very early smoke detection apparatus (VESDA) for fire alarm systems", "sensor.png", "5", null));

        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30133, 0, 103, true, "氢气浓度传感器", "", "Hydrogen concentration sensor", "sensor.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30135, 0, 103, true, "室外空气探测器", "", "", "detector.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30136, 0, 103, true, "库房温湿度传感器", "", "Warehouse temperature and humidity sensor", "sensor.png", "5", null));

        // 控制器设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30201, 0, 103, true, "智能采集控制器", "", "Intelligent Collector", "controller.png", "5", null));

        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30202, 0, 105, true, "DDC", "", "Direct Digital Control", "controller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30203, 0, 105, true, "采集模块", "", "Collecting Module", "module.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30204, 0, 105, true, "门禁控制器", "", "Access control controller", "controller.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30205, 0, 105, true, "网络摄像机", "", "Network camera", "camera.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30206, 0, 105, true, "门禁读卡器", "", "Access control card reader", "reader.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30207, 0, 105, true, "HDMI数字矩阵", "", "HDMI digital matrix", "matrix.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30208, 0, 105, true, "视频采集服务器", "", "Video capture server", "server.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30209, 0, 105, true, "消防输入输出模块", "", "Fire input/output module", "module.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30210, 0, 105, true, "气体消防控制箱", "", "Gas fire control cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30211, 0, 105, true, "电气火灾控制箱", "", "Electrical fire control cabinet", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30212, 0, 105, true, "火灾报警电源监控箱", "", "Fire alarm power supply monitoring box", "box.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30213, 0, 105, true, "存储服务器", "", "Storage server", "server.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30214, 0, 105, true, "防火门监控箱", "", "Fireproof door monitoring box", "box.png", "5", null));

        // 监控系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30301, 0, 105, true, "动环监控", "", "Power And Environment Monitoring System", "system.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30302, 0, 105, true, "电力监控", "", "Power Monitoring System", "system.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30303, 0, 105, true, "暖通监控", "", "Hvac Monitoring System", "system.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30304, 0, 105, true, "安防监控", "", "Security System", "system.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30305, 0, 105, true, "消防监控", "", "Fire Alarm System", "system.png", "5", null));


        // 网络设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30401, 0, 105, true, "二层交换机", "", "Two Layer Switch", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30402, 0, 105, true, "三层交换机", "", "Three Layer Switch", "switch.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30403, 0, 105, true, "路由器", "", "Router", "router.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30404, 0, 105, true, "防火墙", "", "Firewall", "firewall.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30405, 0, 105, true, "无线AP", "", "Wireless access point (AP)", "ap.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30406, 0, 105, true, "光纤收发器", "", "Fiber optic transceiver", "transceiver.png", "5", null));


        // 能效管理设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30501, 0, 105, true, "机房能效计量", "", "Power Effective Measurement", "measurement.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30502, 0, 105, true, "机房其他设备", "", "Other System", "system.png", "5", null));
        // 门禁
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 82, 0, 105, true, "门禁", "", "Door Access Control", "Door.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 98, 0, 105, true, "指纹录入仪", "", "Finger Reader", "system.png", "5", null));


        // 空间设施设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30601, 0, 106, true, "机柜", "", "", "cabinet.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30602, 0, 106, true, "机列", "", "", "rack.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30603, 0, 106, true, "包间", "", "", "room.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30604, 0, 106, true, "楼宇", "", "", "building.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30605, 0, 106, true, "园区", "", "", "park.png", "5", null));


        // 门禁系统设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30901, 0, 106, true, "普通门禁", "", "", "access.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30902, 0, 106, true, "指纹门禁", "", "", "fingerprint.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30903, 0, 106, true, "视频门禁", "", "", "video.png", "5", null));
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 30904, 0, 106, true, "电梯控制器", "", "", "elevator.png", "5", null));


        // 其他设备小类
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 40101, 0, 106, true, "其他", "", "Other", "other.png", "5", null));
        // 通信设备局房
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 53, 0, 106, true, "通信设备局房", "", "Communication Equipment House", "Equipment.png", "5", null));
        // 自诊断设备
        dataItemService.saveDictionaryItemByEntry(new TblDataItem(7, 99, 0, 106, true, "自诊断设设备", "", "SelfDiagnostics", "Equipment.png", "5", null));
//        INSERT INTO siteweb.tbl_dataitem
//                (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5)
//        VALUES(85, 0, 0, 7, 51, '动力设备局房', 'Power Equipment House', 1, 1, 0, '', 'Equipment.png', NULL, NULL, NULL, NULL);


//        INSERT INTO siteweb.tbl_dataitem
//                (EntryItemId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, Enable, IsSystem, IsDefault, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5)
//        VALUES(125, 0, 0, 7, 99, '自诊断设备', 'SelfDiagnostics', 1, 1, 0, '', NULL, NULL, NULL, NULL, NULL);


        categoryIdMapService.byteDanceBusinessCategoryFromOriginCategory();


        equipmentService.updateEquipmentCategoryByCategoryIdMap(4, 2);
        equipmentTemplateService.updateEquipmentTemplateCategoryByCategoryIdMap(4, 2);

        // 插入当前客户类型选择
        TblSysConfig standardVer = sysConfigService.findByKey(SysConfigEnum.STANDARD_VER);

        standardVer.setConfigValue("BYTEDANCE");
        sysConfigService.updateSysConfig(standardVer);

        // 向sys_config中插入记录
        TblSysConfig standardCategory = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);

        standardCategory.setConfigValue("4");
        sysConfigService.updateSysConfig(standardCategory);

        standardTypeService.deleteStandardType(4);
        TblStandardType tblStandardType = new TblStandardType();
        tblStandardType.setStandardId(4);
        tblStandardType.setStandardName("字节标准");
        tblStandardType.setStandardAlias("BYTEDANCE");
        standardTypeService.create(tblStandardType);

        return ResponseHelper.successful();
    }

}
