package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.entity.TblConfigChangeMacroLog;
import org.siteweb.config.common.mapper.TblConfigChangeMacroLogMapper;
import org.siteweb.config.primary.service.TBL_ConfigChangeMacroLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class TBL_ConfigChangeMacroLogServiceImpl implements TBL_ConfigChangeMacroLogService {
    @Autowired
    TblConfigChangeMacroLogMapper configChangeMacroLogMapper;

    @Override
    public void configChangeLog(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum) {
        //是否已经存在，存在则更新
        if (isExists(objectId, configId, changeOperatorEnum)) {
            updateDateTime(objectId, configId, changeOperatorEnum);
            return;
        }
        //不存在则先删除在插入
        deleteByObjectIdAndConfigId(objectId, configId);
        TblConfigChangeMacroLog tblConfigChangeMicroLog = new TblConfigChangeMacroLog(objectId, configId, changeOperatorEnum.getValue(), LocalDateTime.now());
        configChangeMacroLogMapper.insert(tblConfigChangeMicroLog);
    }

    private void updateDateTime(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum) {
        LambdaUpdateWrapper<TblConfigChangeMacroLog> updateWrapper = Wrappers.lambdaUpdate(TblConfigChangeMacroLog.class)
                                                                             .set(TblConfigChangeMacroLog::getUpdateTime, LocalDateTime.now())
                                                                             .eq(TblConfigChangeMacroLog::getObjectId, objectId)
                                                                             .eq(TblConfigChangeMacroLog::getConfigId, configId)
                                                                             .eq(TblConfigChangeMacroLog::getEditType, changeOperatorEnum.getValue());
        configChangeMacroLogMapper.update(updateWrapper);
    }

    private void deleteByObjectIdAndConfigId(String objectId, Integer configId) {
        configChangeMacroLogMapper.delete(Wrappers.lambdaQuery(TblConfigChangeMacroLog.class)
                .eq(TblConfigChangeMacroLog::getObjectId, objectId)
                .eq(TblConfigChangeMacroLog::getConfigId, configId));
    }

    private boolean isExists(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum) {
        return configChangeMacroLogMapper.exists(Wrappers.lambdaQuery(TblConfigChangeMacroLog.class)
                .eq(TblConfigChangeMacroLog::getObjectId, objectId)
                .eq(TblConfigChangeMacroLog::getConfigId, configId)
                .eq(TblConfigChangeMacroLog::getEditType, changeOperatorEnum.getValue()));
    }
}
