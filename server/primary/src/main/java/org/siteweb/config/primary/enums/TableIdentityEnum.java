package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部分需要通过查表获取主键的枚举
 */
@Getter
@AllArgsConstructor
public enum TableIdentityEnum {
    TBL_EQUIPMENT_TEMPLATE("TBL_EquipmentTemplate", "设备模板表"),
    TBL_DATA_ITEM("TBL_DataItem", "数据项"),
    TSL_SAMPLER("TSL_Sampler", "采集器表"),
    TBL_SIGNAL("TBL_Signal", "信号表"),
    TBL_EVENT("TBL_Event", "告警表"),
    TBL_CONTROL("TBL_Control", "控制表"),
    TBL_EVENT_CONDITION("TBL_EventCondition", "告警条件表"),
    TBL_STATION_STRUCTURE("TBL_StationStructure", "局站结构"),
    TBL_WORKSTATION("TBL_WorkStation", "服务器"),
    TBL_STATION("TBL_Station", "局站"),
    RESOURCE_STRUCTURE("resourcestructure", "管理结构"),
    TBL_HOUSE("TBL_House", "局房"),
    TSL_MONITOR_UNIT("TSL_MonitorUnit", "监控单元"),
    TSL_PORT("TSL_Port", "端口"),
    TBL_EQUIPMENT("TBL_Equipment","设备"),
    TSL_SAMPLER_UNIT("TSL_SamplerUnit", "采集单元"),
    TBL_EVENT_LOG_ACTION("TBL_EventLogAction", "告警联动"),
    TBL_DOOR("TBL_Door", "门设备")
    ;
    private final String tableName;
    private final String description;
}
