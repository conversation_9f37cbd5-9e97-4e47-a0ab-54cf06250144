package org.siteweb.config.primary.batchtool.warning.warningImpl;

import cn.hutool.core.util.ObjectUtil;
import org.siteweb.config.common.vo.batchtool.EquipmentSettingVO;
import org.siteweb.config.primary.batchtool.warning.SettingWarning;
import org.siteweb.config.primary.batchtool.warning.WarningEnum;

import java.util.Objects;

/**
 * 同一RMU服务器下，全局唯一校验，动态库名称唯一
 *
 * <AUTHOR>
 * @date 2022/04/11
 */
public class DllPathWarning implements SettingWarning {
    @Override
    public String warningField() {
        return WarningEnum.DLL_PATH_WARNING.getWarningField();
    }

    @Override
    public void isWarning(EquipmentSettingVO origin, EquipmentSettingVO destination) {
        if (ObjectUtil.isNull(origin.getWorkStationId()) || ObjectUtil.isNull(destination.getWorkStationId())) {
            return;
        }
        // 监控单元相同不警告
        if (ObjectUtil.equals(origin.getSamplerUnitId(), destination.getSamplerUnitId())) {
            return;
        }
        // 同一RMU服务器下，地址全局唯一校验，动态库名称唯一
        if (Objects.equals(origin.getWorkStationId(), destination.getWorkStationId()) && Objects.equals(origin.getDllPath(), destination.getDllPath())) {
            this.addWarningField(origin);
        }
    }
}
