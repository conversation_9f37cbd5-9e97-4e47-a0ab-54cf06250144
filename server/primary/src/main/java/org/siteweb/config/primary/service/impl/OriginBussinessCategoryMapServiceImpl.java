package org.siteweb.config.primary.service.impl;

import org.siteweb.config.common.entity.TblOriginBussinessCategoryMap;
import org.siteweb.config.common.mapper.TblOriginBussinessCategoryMapMapper;
import org.siteweb.config.primary.service.OriginBussinessCategoryMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OriginBussinessCategoryMapServiceImpl implements OriginBussinessCategoryMapService {
    @Autowired
    TblOriginBussinessCategoryMapMapper originBussinessCategoryMapMapper;

    @Override
    public void create(TblOriginBussinessCategoryMap originBussinessCategoryMap) {
        originBussinessCategoryMapMapper.insert(originBussinessCategoryMap);
    }
}
