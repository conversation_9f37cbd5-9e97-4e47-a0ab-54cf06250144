package org.siteweb.config.primary.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.siteweb.config.common.dto.SignalConfigItem;
import org.siteweb.config.common.dto.batchtool.*;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.mapper.TblEquipmentMapper;
import org.siteweb.config.common.mapper.VirtualEquipmentMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.vo.batchtool.*;
import org.siteweb.config.primary.enums.EquipmentPropertyEnum;
import org.siteweb.config.primary.enums.PortTypeEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.expressions.service.ExpressionService;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrossSiteVirtualEquipmentServiceImpl implements CrossSiteVirtualEquipmentService {
    Pattern simpleCrossSiteSignalPattern = Pattern.compile("^\\[[^\\[\\]]*]$");
    @Autowired
    VirtualEquipmentServiceImpl virtualEquipmentService;
    @Autowired
    EquipmentTemplateService equipmentTemplateService;
    @Autowired
    I18n i18n;
    @Autowired
    TBL_ConfigChangeMacroLogService configChangeMacroLogService;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    TslAcrossMonitorUnitSignalService tslAcrossMonitorUnitSignalService;
    @Autowired
    PortService portService;
    @Autowired
    private SamplerService samplerService;
    @Autowired
    private SamplerUnitService samplerUnitService;
    @Autowired
    EquipmentService  equipmentService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    StationService stationService;
    @Autowired
    MonitorUnitService monitorUnitService;
    @Autowired
    HouseService houseService;
    @Autowired
    private TblEquipmentMapper equipmentMapper;
    @Autowired
    private SignalService signalService;
    @Autowired
    private VirtualEquipmentMapper virtualEquipmentMapper;
    @Autowired
    private TblEquipmentMapper tblEquipmentMapper;
    @Autowired
    private TslAcrossMonitorUnitSignalService tlsAcrossMonitorUnitSignalService;
    @Autowired
    ExpressionService expressionService;
    @Autowired
    private EventService eventService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean splitEquipment(CrossSiteSplitEquipmentVO crossSiteSplitEquipmentVO) {
        // 生成跨站虚拟模板
        Integer crossSiteVirtualTemplateId = virtualEquipmentService.buildEquipmentTemplate(crossSiteSplitEquipmentVO.getEquipmentId(), crossSiteSplitEquipmentVO.getVirtualEquipmentName(), i18n.T("virtualEquipment.crossSiteVirtualTemplate"));
        //与非跨站虚拟设备的区别 需要自行创建tsl_port与tsl_samplerUnit
        Integer portId = createPort(crossSiteSplitEquipmentVO.getMonitorUnitId());
        Integer samplerUnitId = createSamplerUnit(crossSiteSplitEquipmentVO.getMonitorUnitId(), crossSiteVirtualTemplateId, portId);
        // 生成虚拟设备
        VirtualEquipmentSaveDTO virtualEquipmentSaveDto = BeanUtil.copyProperties(crossSiteSplitEquipmentVO, VirtualEquipmentSaveDTO.class);
        TblEquipment virtualEquipment = buildCrossSiteEquipment(virtualEquipmentSaveDto, crossSiteVirtualTemplateId,crossSiteSplitEquipmentVO.getMonitorUnitId(),samplerUnitId);
        // 生成虚拟信号 老设备id新信号id获取老设备id老信号id 为了获取跨站表达式
        Map<EquipmentSignalIdVO, EquipmentSignalIdVO> signalOriginEquipmentMap = new HashMap<>();
        // 存储老信号与新信号的映射关系，主要为了生成事件时候的startExpression字段与Signal字段
        Map<EquipmentSignalIdVO, Integer> equipmentNewSignalMap = new HashMap<>();
        List<EquipmentSignalDTO> signalList = virtualEquipmentService.buildSignal(crossSiteSplitEquipmentVO.getEquipmentSplitInfoVos(), crossSiteVirtualTemplateId, signalOriginEquipmentMap, equipmentNewSignalMap);
        // 生成采集单元信号表达式
        this.buildAcrossMonitorUnitSignal(virtualEquipment, signalOriginEquipmentMap, signalList);
        // 生成事件
        virtualEquipmentService.buildEvent(crossSiteSplitEquipmentVO.getEquipmentSplitInfoVos(), crossSiteVirtualTemplateId, equipmentNewSignalMap);
        //生成通讯状态相关信号与事件
        signalService.createCommunicationStateSignal(crossSiteVirtualTemplateId);
        eventService.createCommunicationStateEvent(crossSiteVirtualTemplateId);
        // 生成模板记录日志便于更新诸如设备\信号\事件等缓存
        configChangeMacroLogService.configChangeLog(crossSiteVirtualTemplateId.toString(), 6, ChangeOperatorEnum.UPDATE);
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(crossSiteVirtualTemplateId);
        changeEventService.sendCreate(equipmentTemplate);
        return true;
    }

    @Override
    public List<ImportErrorInfoDTO> importVirtualEquipmentSetting(VirtualEquipmentSettingVO virtualEquipmentSettingVo) {
        Map<String, String> equipmentErrorHashMap = buildEquipment(virtualEquipmentSettingVo);
        return this.buildCrossMonitorUnitSignal(virtualEquipmentSettingVo, equipmentErrorHashMap);
    }

    private List<ImportErrorInfoDTO> buildCrossMonitorUnitSignal(VirtualEquipmentSettingVO virtualEquipmentSettingVo, Map<String, String> equipmentErrorHashMap) {
        List<VirtualSignalExcelVo> virtualSignalExcelList = virtualEquipmentSettingVo.getVirtualSignalExcelList();
        List<ImportErrorInfoDTO> importErrorInfoDTOList = new ArrayList<>();
        Map<String, TblEquipment> equipmentMap = new HashMap<>();
        List<MonitorSignalUnitSaveDTO> monitorSignalUnitSaveDtoList = new ArrayList<>(virtualSignalExcelList.size());
        for (int i = 0; i < virtualSignalExcelList.size(); i++) {
            if (equipmentErrorHashMap.containsKey(virtualSignalExcelList.get(i).getVirtualEquipmentName())) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "", equipmentErrorHashMap.get(virtualSignalExcelList.get(i).getVirtualEquipmentName())));
                continue;
            }
            TblEquipment equipment = this.findEquipmentByName(equipmentMap, virtualSignalExcelList.get(i).getEquipmentName());
            if (ObjectUtil.isNull(equipment)) {
                continue;
            }
            // 判断虚拟设备名是否存在
            TblEquipment virtualEquipment = this.findEquipmentByName(equipmentMap, virtualSignalExcelList.get(i).getVirtualEquipmentName());
            if (ObjectUtil.isNull(virtualEquipment)) {
                continue;
            }
            // 判断虚拟设备信号是否存在
            SignalConfigItem virtualSignal = null;
            if (ObjectUtil.isNotNull(virtualEquipment)) {
                List<SignalConfigItem> signalConfigItemList = signalService.findItemByEquipmentTemplateId(virtualEquipment.getEquipmentTemplateId());
                String virtualSignalName = virtualSignalExcelList.get(i).getVirtualSignalName();
                virtualSignal = signalConfigItemList.stream().filter(e -> CharSequenceUtil.equals(e.getSignalName(), virtualSignalName)).findFirst().orElse(null);
            }
            if (ObjectUtil.isNull(virtualSignal)) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "", i18n.T("virtualEquipment.notExist.virtualSignal")));
                continue;
            }
            // 判断信号通道是否存在
            TblSignal signal = null;
            if (ObjectUtil.isNotNull(equipment)) {
                List<TblSignal> tblSignals = signalService.findByEquipmentTemplateIdAndChannelNo(equipment.getEquipmentTemplateId(), virtualSignalExcelList.get(i).getChannelNo());
                if (CollUtil.isNotEmpty(tblSignals)) {
                    signal = tblSignals.get(0);
                }
            }
            if (ObjectUtil.isNull(signal)) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "", "virtualEquipment.notExist.originChannel"));
                continue;
            }
            MonitorSignalUnitSaveDTO monitorSignalUnitSaveDto = new MonitorSignalUnitSaveDTO(virtualEquipment, signal.getSignalId(), virtualSignal.getSignalId(), equipment.getEquipmentId());
            monitorSignalUnitSaveDtoList.add(monitorSignalUnitSaveDto);
        }
        // 完全没有错误才添加表达式
        if (CollUtil.isEmpty(importErrorInfoDTOList)) {
            for (MonitorSignalUnitSaveDTO monitorSignalUnitSaveDto : monitorSignalUnitSaveDtoList) {
                this.buildCrossMonitorUnitSignal(monitorSignalUnitSaveDto.getVirtualEquipment(), monitorSignalUnitSaveDto.getSignalId(), monitorSignalUnitSaveDto.getVirtualSignalId(), monitorSignalUnitSaveDto.getEquipmentId());
            }
        }
        return importErrorInfoDTOList;
    }

    private void buildCrossMonitorUnitSignal(TblEquipment virtualEquipment, Integer realSignalId, Integer virtualSignalId, Integer equipmentId) {
        TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal = new TslAcrossMonitorUnitSignal();
        tslAcrossMonitorUnitSignal.setStationId(virtualEquipment.getStationId());
        tslAcrossMonitorUnitSignal.setMonitorUnitId(virtualEquipment.getMonitorUnitId());
        tslAcrossMonitorUnitSignal.setEquipmentId(virtualEquipment.getEquipmentId());
        tslAcrossMonitorUnitSignal.setSignalId(virtualSignalId);
        String expression = CharSequenceUtil.concat(true, "[", equipmentId.toString(), ",", realSignalId.toString(), "]");
        tslAcrossMonitorUnitSignal.setExpression(expression);
        tslAcrossMonitorUnitSignalService.updateOrInsert(tslAcrossMonitorUnitSignal);
    }

    /**
     * 设备是否存在
     *
     * @param equipmentMap  设备名与设备的映射map
     * @param equipmentName 设备名
     * @return {@link Boolean}
     */
    private TblEquipment findEquipmentByName(Map<String, TblEquipment> equipmentMap, String equipmentName) {
        if (equipmentMap.containsKey(equipmentName)) {
            return equipmentMap.get(equipmentName);
        }
        TblEquipment equipmentsByEquipmentName = equipmentService.getEquipmentsByEquipmentName(equipmentName);
        if (ObjectUtil.isNotNull(equipmentsByEquipmentName)) {
            equipmentMap.put(equipmentName, equipmentsByEquipmentName);
            return equipmentsByEquipmentName;
        }
        return null;
    }

    @Override
    public void deleteVirtualEquipment(Integer equipmentId) {
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        // 不是跨站虚拟设备 无法删除
        if (!isCrossSiteVirtualEquipment(equipment.getProperty())) {
            return;
        }
        equipmentService.deleteEquipment(equipmentId);
        //virtualEquipmentService.deleteVirtualTemplate(equipment.getEquipmentTemplateId());
        tslAcrossMonitorUnitSignalService.deleteByEquipmentId(equipmentId);
    }

    @Override
    public VirtualEquipmentSettingVO exportVirtualEquipmentSetting(List<Integer> equipmentIds) {
        // 查找虚拟设备信息
        List<VirtualEquipmentExcelVO> virtualEquipmentExcelList = virtualEquipmentService.getVirtualEquipmentInfo(equipmentIds);
        // 查找虚拟信号信息
        List<VirtualSignalExcelVo> virtualSignalExcelList = this.getVirtualSignalInfo(equipmentIds);
        return new VirtualEquipmentSettingVO(virtualEquipmentExcelList, virtualSignalExcelList);
    }

    /**
     * 获取指定属性的虚拟设备列表
     *
     * @return 跨站虚拟设备基本信息列表
     */
    @Override
    public List<CrossVirtualEquipmentBasicInfoVO> virtualEquipmentList() {
        // 查找指定属性的设备ID列表
        List<Integer> equipmentIds = tblEquipmentMapper.findEquipmentIdsByProperty(String.valueOf(EquipmentPropertyEnum.CROSS_VIRTUAL_EQUIPMENT.getId()));

        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }

        // 获取虚拟设备基本信息
        List<CrossVirtualEquipmentBasicInfoVO> virtualEquipments = virtualEquipmentMapper.findCrossVirtualEquipmentBasicInfo(equipmentIds);
        // 设置允许配置项导出的标志位
        setExportPermissions(virtualEquipments, equipmentIds);
        return virtualEquipments;
    }

    @Override
    public List<CrossSiteVirtualEquipmentDetailVO> findVirtualEquipmentDetail(Integer equipmentId) {
        TblEquipment virtualEquipment = equipmentService.findEquipmentById(equipmentId);
        VirtualEquipmentBasicInfoVO virtualEquipmentBasicInfo = virtualEquipmentService.findEquipmentBasicInfo(equipmentId);
        List<TslAcrossMonitorUnitSignal> tslAcrossMonitorUnitSignalList = tlsAcrossMonitorUnitSignalService.findByEquipmentId(equipmentId);
        Map<Integer, TblSignal> signalMap = signalService.findByEquipmentTemplateId(virtualEquipment.getEquipmentTemplateId())
                                                         .stream()
                                                         .collect(Collectors.toMap(TblSignal::getSignalId, Function.identity()));
        //解析表达式
        return tslAcrossMonitorUnitSignalList.parallelStream()
                                             .map(e -> createCrossSiteVirtualEquipmentDetailVO(virtualEquipmentBasicInfo, e, signalMap))
                                             .toList();
    }

    private CrossSiteVirtualEquipmentDetailVO createCrossSiteVirtualEquipmentDetailVO(VirtualEquipmentBasicInfoVO basicInfo, TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal, Map<Integer, TblSignal> signalMap) {
        CrossSiteVirtualEquipmentDetailVO detailVO = BeanUtil.copyProperties(basicInfo, CrossSiteVirtualEquipmentDetailVO.class);
        TblSignal tblSignal = signalMap.get(tslAcrossMonitorUnitSignal.getSignalId());
        detailVO.setVirtualSignalName(tblSignal.getSignalName());
        detailVO.setCrossSiteExpression(expressionService.analysisSignal(tblSignal.getEquipmentTemplateId(),tslAcrossMonitorUnitSignal.getExpression(),Boolean.TRUE));
        return detailVO;
    }

    /**
     * 设置虚拟设备的配置项导出权限
     *
     * @param virtualEquipments 虚拟设备列表
     * @param equipmentIds 设备ID列表
     */
    private void setExportPermissions(List<CrossVirtualEquipmentBasicInfoVO> virtualEquipments, List<Integer> equipmentIds) {
        // 查找具有复杂信号表达式的设备ID
        Set<Integer> equipmentIdsWithComplexSignals = findEquipmentIdsWithComplexSignals(equipmentIds);
        // 设置导出权限（只有简单信号配置的设备允许导出）
        virtualEquipments.forEach(equipment ->
                equipment.setConfigItemsExportAllowed(!equipmentIdsWithComplexSignals.contains(equipment.getVirtualEquipmentId()))
        );
    }

    /**
     * 查找具有复杂信号表达式的设备ID
     *
     * @param equipmentIds 设备ID列表
     * @return 具有复杂信号表达式的设备ID集合
     */
    private Set<Integer> findEquipmentIdsWithComplexSignals(List<Integer> equipmentIds) {
        List<TslAcrossMonitorUnitSignal> signals = tslAcrossMonitorUnitSignalService.findByEquipmentIds(equipmentIds);

        return signals.stream()
                      .filter(signal -> !hasSimpleSignalExpression(signal.getExpression()))
                      .map(TslAcrossMonitorUnitSignal::getEquipmentId)
                      .collect(Collectors.toSet());
    }

    /**
     * 判断信号表达式是否简单（只包含一对中括号）
     * 暂时只能支持配置了一个信号的跨站设备配置进行导出
     *
     * @param expression 信号表达式
     * @return 如果表达式只包含一对中括号则返回true，否则返回false
     */
    private boolean hasSimpleSignalExpression(String expression) {
        return expression != null && simpleCrossSiteSignalPattern.matcher(expression).matches();
    }

    private List<VirtualSignalExcelVo> getVirtualSignalInfo(List<Integer> equipmentIds) {
        List<VirtualSignalExcelVo> virtualSignalExcelList = new ArrayList<>();
        List<TblEquipment> tblEquipments = equipmentMapper.selectList(Wrappers.lambdaQuery(TblEquipment.class).in(TblEquipment::getEquipmentId, equipmentIds));
        Map<Integer, List<TblSignal>> signalMaps = signalService.findSignalMaps(tblEquipments.stream().map(TblEquipment::getEquipmentTemplateId).collect(Collectors.toSet()));
        for (TblEquipment virtualEquipment : tblEquipments) {
            List<TblSignal> signalList = signalMaps.get(virtualEquipment.getEquipmentTemplateId());
            for (TblSignal signal : signalList) {
                VirtualSignalExcelVo virtualSignalExcelVo = new VirtualSignalExcelVo();
                virtualSignalExcelVo.setVirtualEquipmentName(virtualEquipment.getEquipmentName());
                virtualSignalExcelVo.setVirtualSignalName(signal.getSignalName());
                // 获取源设备名源型号源通道号
                OriginSignalInfoDTO originSignalInfoDto = this.findOriginSignalInfo(virtualEquipment.getStationId(), virtualEquipment.getEquipmentId(), signal.getSignalId());
                virtualSignalExcelVo.setEquipmentName(originSignalInfoDto.getEquipmentName());
                virtualSignalExcelVo.setChannelNo(originSignalInfoDto.getChannelNo());
                virtualSignalExcelList.add(virtualSignalExcelVo);
            }
        }
        return virtualSignalExcelList;
    }

    private OriginSignalInfoDTO findOriginSignalInfo(Integer stationId, Integer equipmentId, Integer signalId) {
        TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal = tslAcrossMonitorUnitSignalService.findByStationIdAndEquipmentIdAndSignalId(stationId, equipmentId, signalId);
        if (ObjectUtil.isNull(tslAcrossMonitorUnitSignal)) {
            return new OriginSignalInfoDTO();
        }
        String expression = tslAcrossMonitorUnitSignal.getExpression();
        // 0源设备id 1源信号id
        String[] originString = expression.replace("[", "")
                                          .replace("]", "")
                                          .split(",");
        OriginSignalInfoDTO originSignalInfoDto = new OriginSignalInfoDTO();
        TblEquipment equipment = equipmentService.findByIdFromDB(Integer.parseInt(originString[0]));
        originSignalInfoDto.setEquipmentName(equipment.getEquipmentName());
        SignalConfigItem configSignal = signalService.findByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), Integer.parseInt(originString[1]));
        originSignalInfoDto.setChannelNo(configSignal.getChannelNo());
        originSignalInfoDto.setSignalName(configSignal.getSignalName());
        return originSignalInfoDto;
    }

    private boolean isCrossSiteVirtualEquipment(String property) {
        return CharSequenceUtil.contains(property, String.valueOf(EquipmentPropertyEnum.CROSS_VIRTUAL_EQUIPMENT.getId()));
    }
    private Integer createSamplerUnit(Integer monitorUnitId, Integer equipmentTemplateId, Integer portId) {
       String protocolCode = equipmentTemplateService.findProtocolCodeByEquipmentTemplateId(equipmentTemplateId);
        TslSampler sampler = samplerService.findByProtocolCode(protocolCode);
        Integer samplerUnitId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_SAMPLER_UNIT, 0);
        TslSamplerUnit samplerUnit = new TslSamplerUnit();
        samplerUnit.setSamplerUnitId(samplerUnitId);
        samplerUnit.setPortId(portId);
        samplerUnit.setMonitorUnitId(monitorUnitId);
        samplerUnit.setSamplerId(sampler.getSamplerId());
        samplerUnit.setParentSamplerUnitId(0);
        samplerUnit.setSamplerType((short)0);
        samplerUnit.setSamplerUnitName(sampler.getSamplerName() + System.currentTimeMillis());
        samplerUnit.setAddress(1);
        samplerUnit.setSpUnitInterval(2.0);
        samplerUnit.setDllPath(sampler.getDllPath());
        samplerUnitService.createSamplerUnit(samplerUnit);
        return samplerUnitId;
    }

    /**
     * 创建端口
     * @param monitorUnitId 监控单元id
     */
    private Integer createPort(Integer monitorUnitId) {
        Integer maxPortNo = portService.getMaxPortByMonitorUnitId(monitorUnitId);
        TslPort tslPort = new TslPort();
        tslPort.setMonitorUnitId(monitorUnitId);
        tslPort.setPortType(PortTypeEnum.SIMPLE_LOGIC_CONTROL_PORT.getValue());
        tslPort.setPortNo(++maxPortNo);
        tslPort.setPortName("COM" + maxPortNo);
        tslPort.setSetting(CharSequenceUtil.EMPTY);
        portService.createPort(tslPort);
        return tslPort.getPortId();
    }

    /**
     * 生成跨站虚拟设备
     *
     * @param virtualEquipmentSaveDto 虚拟设备添加dto
     * @param equipmentTemplateId 虚拟设备模板id
     * @param crossSiteMonitorUnitId  跨站监控单元id
     * @param samplerUnitId 采集单元id
     * @return {@link TblEquipment}
     */
    public TblEquipment buildCrossSiteEquipment(VirtualEquipmentSaveDTO virtualEquipmentSaveDto,Integer equipmentTemplateId, Integer crossSiteMonitorUnitId,Integer samplerUnitId) {
        TblEquipment equipment = equipmentService.findEquipmentById(virtualEquipmentSaveDto.getEquipmentId());
        if (ObjectUtil.isNull(equipment)) {
            return null;
        }
        TblEquipment virtualEquipment = BeanUtil.copyProperties(equipment, TblEquipment.class);
        // 设备主键
        int subEquipmentId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_EQUIPMENT, 0);
        virtualEquipment.setEquipmentId(subEquipmentId);
        // 模板id、采集单元id、设备类型、名称、局站id、局房id
        Integer stationId = monitorUnitService.getStationIdByMonitorUnitId(crossSiteMonitorUnitId);
        Integer houseId = houseService.findHouseIdByStationId(stationId);
        virtualEquipment.setEquipmentName(virtualEquipmentSaveDto.getVirtualEquipmentName());
        virtualEquipment.setSamplerUnitId(virtualEquipmentSaveDto.getSamplerUnitId());
        virtualEquipment.setEquipmentTemplateId(equipmentTemplateId);
        virtualEquipment.setEquipmentCategory(virtualEquipmentSaveDto.getEquipmentCategory());
        virtualEquipment.setMonitorUnitId(crossSiteMonitorUnitId);
        virtualEquipment.setSamplerUnitId(samplerUnitId);
        virtualEquipment.setStationId(stationId);
        virtualEquipment.setHouseId(houseId);
        String propertyFlag = String.valueOf(EquipmentPropertyEnum.CROSS_VIRTUAL_EQUIPMENT.getId());
        String property = CharSequenceUtil.isBlank(equipment.getProperty()) ? propertyFlag : equipment.getProperty() + "/" + propertyFlag;
        virtualEquipment.setProperty(property);
        if (ObjectUtil.isNotNull(virtualEquipmentSaveDto.getResourceStructureId())) {
            virtualEquipment.setResourceStructureId(virtualEquipmentSaveDto.getResourceStructureId());
        }
        if (ObjectUtil.isNotNull(virtualEquipmentSaveDto.getHouseId())) {
            virtualEquipment.setHouseId(virtualEquipmentSaveDto.getHouseId());
        }
        virtualEquipment.setUpdateTime(LocalDateTime.now());
        equipmentService.createEquipment(virtualEquipment);
        return virtualEquipment;
    }

    private void buildAcrossMonitorUnitSignal(TblEquipment virtualEquipment, Map<EquipmentSignalIdVO, EquipmentSignalIdVO> signalOriginEquipmentMap, List<EquipmentSignalDTO> signalList) {
        for (EquipmentSignalDTO signal : signalList) {
            EquipmentSignalIdVO equipmentSignalIdVo = signalOriginEquipmentMap.get(new EquipmentSignalIdVO(signal.getEquipmentId(), signal.getSignalId()));
            String expression = CharSequenceUtil.concat(true, "[", equipmentSignalIdVo.getEquipmentId().toString(), ",", equipmentSignalIdVo.getSignalId().toString(), "]");
            TslAcrossMonitorUnitSignal acrossMonitorUnitSignal = TslAcrossMonitorUnitSignal.builder()
                                                                                           .stationId(virtualEquipment.getStationId())
                                                                                           .monitorUnitId(virtualEquipment.getMonitorUnitId())
                                                                                           .equipmentId(virtualEquipment.getEquipmentId())
                                                                                           .signalId(signal.getSignalId())
                                                                                           .expression(expression)
                                                                                           .build();
            acrossMonitorUnitSignal.setExpression(expression);
            tslAcrossMonitorUnitSignalService.updateOrInsert(acrossMonitorUnitSignal);
        }
    }
    /**
     * 导入的虚拟设备校验与添加
     *
     * @param virtualEquipmentSettingVo 导入的虚拟设备excel列表
     */
    private Map<String, String> buildEquipment(VirtualEquipmentSettingVO virtualEquipmentSettingVo) {
        Map<String, String> errorMap = new HashMap<>(virtualEquipmentSettingVo.getVirtualEquipmentExcelList().size());
        Map<String, Integer> categoryMap = dataItemService.getDataItemMap(DataEntryEnum.EQUIPMENT_CATEGORY).entrySet()
                                                          .stream()
                                                          .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (k1, k2) -> k1));
        Map<String, String> virtualRealEquipmentMap = virtualEquipmentSettingVo.getVirtualSignalExcelList()
                                                                               .stream()
                                                                               .filter(e -> ObjectUtil.isNotNull(e.getVirtualEquipmentName()) &&
                                                                                       ObjectUtil.isNotNull(e.getEquipmentName()))
                                                                               .collect(Collectors.toMap(
                                                                                       VirtualSignalExcelVo::getVirtualEquipmentName,
                                                                                       VirtualSignalExcelVo::getEquipmentName,
                                                                                       (k1, k2) -> k1));
        Map<String, TblEquipment> equipmentNameMap = equipmentService.allEquipment().stream().collect(Collectors.toMap(TblEquipment::getEquipmentName, Function.identity(), (e1, e2) -> e1));
        Map<String, TblStation> stationNameMap = stationService.findAllStation().stream().collect(Collectors.toMap(TblStation::getStationName, Function.identity(), (e1, e2) -> e1));
        for (VirtualEquipmentExcelVO virtualEquipmentExcelVo : virtualEquipmentSettingVo.getVirtualEquipmentExcelList()) {
            // 虚拟设备局站不存在
            TblStation station = stationNameMap.get(virtualEquipmentExcelVo.getVirtualStationName());
            if (ObjectUtil.isNull(station)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.virtualEquipmentStation"));
                continue;
            }
            // 虚拟监控单元不存在
            MonitorUnitDTO monitorUnit = monitorUnitService.findMonitorUnit(station.getStationName(), virtualEquipmentExcelVo.getVirtualMonitorUnitName());
            if (ObjectUtil.isNull(monitorUnit)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.virtualMonitUnit"));
                continue;
            }
            // 采集单元信息不存在
            TslSamplerUnit samplerUnit = samplerUnitService.findSamplerUnit(monitorUnit.getMonitorUnitId(), virtualEquipmentExcelVo.getVirtualSamplerUnitName(), virtualEquipmentExcelVo.getVirtualPortName());
            if (ObjectUtil.isNull(samplerUnit)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.sampleUnit"));
                continue;
            }
            // 判断局房是否存在
            TblHouse house = houseService.findHouseByHostName(station.getStationId(), virtualEquipmentExcelVo.getVirtualHouseName());
            if (ObjectUtil.isNull(house)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.virtualEquipmentHouse"));
                continue;
            }
            // 虚拟模板是否正确
            TblEquipmentTemplate virtualEquipmentTemplate = equipmentTemplateService.findByName(virtualEquipmentExcelVo.getVirtualEquipmentTemplateName());
            if (ObjectUtil.isNull(virtualEquipmentTemplate)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.virtualTemplate"));
                continue;
            }
            // 虚拟设备类型不存在
            if (!categoryMap.containsKey(virtualEquipmentExcelVo.getVirtualEquipmentCategoryName())) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.virtualEquipmentCategory"));
                continue;
            }
            // 真实设备不存在
            String realEquipmentName = virtualRealEquipmentMap.get(virtualEquipmentExcelVo.getVirtualEquipmentName());
            TblEquipment realEquipment = equipmentNameMap.get(realEquipmentName);
            if (ObjectUtil.isNull(realEquipment)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.notExist.originEquipment"));
                continue;
            }
            // 虚拟设备已存在
            TblEquipment virtualEquipment = equipmentNameMap.get(virtualEquipmentExcelVo.getVirtualEquipmentName());
            if (ObjectUtil.isNotNull(realEquipment) && ObjectUtil.isNotNull(virtualEquipment) && ObjectUtil.notEqual(realEquipment.getMonitorUnitId(), virtualEquipment.getMonitorUnitId())) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), i18n.T("virtualEquipment.cannotAcross.monitUnit"));
            }
            if (ObjectUtil.isNotNull(virtualEquipment)) {
                continue;
            }
            Integer resourceStructureId = realEquipment.getResourceStructureId();
            Integer portId = createPort(monitorUnit.getMonitorUnitId());
            Integer samplerUnitId = createSamplerUnit(monitorUnit.getMonitorUnitId(), virtualEquipmentTemplate.getEquipmentTemplateId(), portId);
            VirtualEquipmentSaveDTO virtualEquipmentSaveDto = new VirtualEquipmentSaveDTO(
                    realEquipment.getEquipmentId(), virtualEquipmentExcelVo.getVirtualEquipmentName(),
                    virtualEquipmentTemplate.getEquipmentTemplateId(), samplerUnitId,
                    categoryMap.get(virtualEquipmentExcelVo.getVirtualEquipmentCategoryName()),
                    resourceStructureId, house.getHouseId());
            this.buildCrossSiteEquipment(virtualEquipmentSaveDto, virtualEquipmentTemplate.getEquipmentTemplateId(), monitorUnit.getMonitorUnitId(), samplerUnitId);
        }
        return errorMap;
    }
}
