package org.siteweb.config.primary.handler;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.change.ChangeMessage;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.change.ChangeRecord;
import org.siteweb.config.common.change.ObjectChangeHandlerAdapter;
import org.siteweb.config.common.entity.*;

import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * ResourceStructure 变更事件处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class StationChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private StationProjectInfoService stationProjectInfoService;

    @Autowired
    private HouseService houseService;

    @Autowired
    private TslMonitorUnitSignalService tslMonitorUnitSignalService;

    @Autowired
    private TSLMonitorUnitEventService tslMonitorUnitEventService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    MonitorUnitService monitorUnitService;

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    StationStructureMapService stationStructureMapService;

    @Autowired
    TblSwatchStationService tblSwatchStationService;

    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private StationStructureService stationStructureService;

    @Autowired
    private TBL_ConfigChangeMacroLogService tblConfigChangeMacroLogService;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(TblStation.class);
    }


    /**
     * Station的onCreate事件处理器
     * <p>
     * 如果是stationCategory为2或者（105-108）的局站，创建对应resourceStructure
     * 1. 查询station的关联stationStructure,从ResourceStructureService中获取对应的ResourceStructure
     * 2. 如果没有找到对应的ResourceStructure,则创建
     * 3. 如果已经创建了对应的ResourceStructure,则直接return
     * 4. 通知所有人更新层级树
     *
     * @param changeRecord 变更事件记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onCreate(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStation station = msg.readBody(TblStation.class);
        // 记录日志
        log.info("局站创建成功,stationName:{},stationId:{},stationCategory:{}", station.getStationName(), station.getStationId(), station.getStationCategory());
        // bordNumber为0的的局站则需要创建对应的resourceStructure
        if (station.getBordNumber() != null && station.getBordNumber() == 0 && station.getStationId() > 0) {
            // 查询station的关联stationStructure,从ResourceStructureService中获取对应的ResourceStructure
            TblStationStructureMap TblStationStructureMap = stationStructureMapService.findStationStructureMapByStationId(station.getStationId());
            if (TblStationStructureMap == null) {
                log.error("找不到对应的TblStationStructureMap");
                return;
            }

            TblStationStructure stationStructure = stationStructureService.findStructureById(TblStationStructureMap.getStructureId());
            if (stationStructure == null) {
                log.error("找不到对应的stationStructure");
                return;
            }
            ResourceStructure parentResourceStructure = resourceStructureService.findByOriginIdAndStructureType(TblStationStructureMap.getStructureId(), 103);
            if (stationStructure.getParentStructureId() == 0) { // 表示挂载到根节点
                parentResourceStructure = resourceStructureService.getRootStructure();
            }

            // 如果没有找到对应的ResourceStructure,则创建

            if (parentResourceStructure == null) {
                // 同步所有的局站分组
                resourceStructureService.createStationResourceStructure(TblStationStructureMap.getStructureId()).forEach(resourceStructureService::create);
                // 再查询一次
                TblStationStructureMap = stationStructureMapService.findStationStructureMapByStationId(station.getStationId());
                parentResourceStructure = resourceStructureService.findByOriginIdAndStructureType(TblStationStructureMap.getStructureId(), 103);
                ResourceStructure resourceStructure = new ResourceStructure();
//                resourceStructure.setSceneId(2);
                resourceStructure.setStructureTypeId(104);
                resourceStructure.setResourceStructureName(station.getStationName());
                resourceStructure.setParentResourceStructureId(parentResourceStructure.getResourceStructureId());
                resourceStructure.setLevelOfPath(parentResourceStructure.getLevelOfPath());
                resourceStructure.setDisplay(true);
                resourceStructure.setSortValue(1);
                resourceStructure.setOriginId(station.getStationId());
                resourceStructure.setOriginParentId(TblStationStructureMap.getStructureId());
                resourceStructureService.create(resourceStructure);
            } else {
                log.info("station {} has been created", station.getStationName());
                ResourceStructure resourceStructure = new ResourceStructure();
//                resourceStructure.setSceneId(2);
                resourceStructure.setStructureTypeId(104);
                resourceStructure.setResourceStructureName(station.getStationName());
                resourceStructure.setParentResourceStructureId(parentResourceStructure.getResourceStructureId());
                resourceStructure.setLevelOfPath(parentResourceStructure.getLevelOfPath());
                resourceStructure.setDisplay(true);
                resourceStructure.setSortValue(1);
                resourceStructure.setOriginId(station.getStationId());
                resourceStructure.setOriginParentId(TblStationStructureMap.getStructureId());
                resourceStructureService.create(resourceStructure);
            }
            // 通知所有人更新层级树
            tblConfigChangeMacroLogService.configChangeLog("-1", 27, ChangeOperatorEnum.CREATE);

        }
    }

    /**
     * Station的onDelete事件处理器
     * <p>
     * 删除与station相关的所有关联数据，包括：
     * 1. 站点项目信息
     * 2. 房间信息
     * 3. 监控单元信号
     * 4. 监控单元事件
     * 5. 监控单元
     * 6. 设备
     * 7. 局站局站分组映射
     * 8. 局站样板站
     * 9. 层级结构
     *
     * @param changeRecord 变更事件记录
     */
    @Override
    public void onDelete(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStation station = msg.readBody(TblStation.class);
        stationProjectInfoService.deleteStationProjectInfo(station.getStationId());
        // 监控单元项目信息
        // 设备项目信息
        // 删除房间
        houseService.deleteHouseByStationId(station.getStationId());
        // 删除监控单元信号
        tslMonitorUnitSignalService.deleteByStationId(station.getStationId());
        // 删除监控单元事件
        tslMonitorUnitEventService.deleteByStationId(station.getStationId());
        // 删除监控单元
        monitorUnitService.deleteByStationId(station.getStationId());
        // 删除设备
        equipmentService.deleteByStationId(station.getStationId());
        // 删除局站局站分组映射
        stationStructureMapService.deleteByStationId(station.getStationId());
        // 局站样板站
        tblSwatchStationService.deleteByStationId(station.getStationId());
        // 删除层级
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(station.getStationId(), 104);
        if (resourceStructure != null) {
            resourceStructureService.deleteByID(resourceStructure.getResourceStructureId());
        }
    }

    /**
     * Station的onUpdate事件处理器
     * <p>
     * 如果station的bordNumber为0，则检查是否需要更新对应的resourceStructure名称。
     * 如果resourceStructure存在且名称与station名称不一致，则更新resourceStructure的名称。
     *
     * @param changeRecord 变更事件记录
     */
    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStation station = msg.readBody(TblStation.class);
        // bordNumber为0的的局站则需要创建对应的resourceStructure
        if (station.getBordNumber() != null && station.getBordNumber() == 0) {
            // 修改名称则需要对应修改层级的名称
            // 修改名称则需要对应修改层级的名称
            ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(station.getStationId(), 104);
            // 不为空并且stationname和层级名称不相同
            if (resourceStructure != null && !resourceStructure.getResourceStructureName().equals(station.getStationName())) {
                resourceStructure.setResourceStructureName(station.getStationName());
                resourceStructureService.update(resourceStructure);
            }
        }
    }



}
