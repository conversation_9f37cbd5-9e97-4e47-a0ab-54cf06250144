package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.CenterDTO;
import org.siteweb.config.primary.service.CenterService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.siteweb.config.common.utils.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 监控中心接口
 * Author: <EMAIL>
 * Creation Date: 2024/3/12
 */

@Slf4j
@RestController
@RequestMapping("/center")
public class CenterController {

    @Autowired
    CenterService centerService;


    @PostMapping(value = "/create", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createEquipment(@RequestBody CenterDTO centerDTO) {
        centerService.create(centerDTO);
        return ResponseHelper.successful();
    }

}
