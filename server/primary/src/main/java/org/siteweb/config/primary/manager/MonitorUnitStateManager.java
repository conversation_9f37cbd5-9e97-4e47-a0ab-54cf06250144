package org.siteweb.config.primary.manager;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.siteweb.config.common.mapper.TslMonitorUnitMapper;
import org.siteweb.config.primary.cache.CacheComplete;
import org.siteweb.config.primary.enums.MonitorUnitCategoryEnum;
import org.siteweb.config.primary.enums.MonitorUnitStateEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 下发状态描述
 * 待下发情况：
 *
 * 生成XML文件。
 * 修改监控单元、设备/设备模板、采集单元、端口。
 * 如果issync为0，即使之前失败了，重启后仍需要重新执行下发操作。
 * 无需下发：
 *
 * 对于RMU下MU，采用手动下发方式，无需自动下发。
 * 正在下发：
 *
 * 外部发起下发请求，正在执行下发过程。
 * 下发成功：
 *
 * 下发采集器成功并完成采集操作。
 * 如果issync为1，表示下发成功且记录了最近一次的下发时间。
 * 下发失败：
 *
 * 在下发采集器过程中发生异常，可能包括：
 * 密码错误。
 * 连接失败。
 * 代码异常。
 * 删除文件失败。
 * 上传文件失败。
 * 执行重启失败。
 * 重启采集器后的下发状态
 * 待下发：
 * 如果issync为0，表示未同步过，即便之前下发失败，重启后仍需重新下发。
 * 下发成功：
 * 如果issync为1，表示之前下发成功，并且有最近一次的下发时间记录。
 * 无需下发：
 * 对于RMU下MU，保持手动下发方式，无需自动下发。
 **/
@Slf4j
@Component
public class MonitorUnitStateManager implements CacheComplete {
    @Autowired
    private TslMonitorUnitMapper monitorUnitMapper;


    // key：monitorUnitId value:status
    private final ConcurrentHashMap<Integer, Integer> monitorUnitStatusHashmap = new ConcurrentHashMap<>();

    private final ConcurrentHashMap<Integer, MonitorUnitDTO> activeMonitorUnitDTOMap = new ConcurrentHashMap<>();



    @Override
    public void cacheLoadCompleteAfter() {
        // 为所有监控单元初始化一个状态
        monitorUnitMapper.selectAll().forEach(monitorUnit -> {
            if (monitorUnit.getIsSync()) {
                monitorUnit.setState(MonitorUnitStateEnum.SUCCESS.getValue());
                monitorUnitStatusHashmap.put(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.SUCCESS.getValue());
            } else {
                monitorUnit.setState(MonitorUnitStateEnum.PENDING.getValue());
                monitorUnitStatusHashmap.put(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.PENDING.getValue());
            }
            if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue() || monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
                monitorUnit.setState(MonitorUnitStateEnum.NO_NEED_TO_SEND.getValue());
                monitorUnitStatusHashmap.put(monitorUnit.getMonitorUnitId(), MonitorUnitStateEnum.NO_NEED_TO_SEND.getValue());
            }
        });
    }

    // 更新监控单元状态
    public void updateMonitorUnitStatus(Integer monitorUnitId, MonitorUnitStateEnum status) {
        // 如果是下发状态,则monitorUnitDTOMap put一个数据
        if (status == MonitorUnitStateEnum.SENDING) {
            MonitorUnitDTO monitorUnitDTO = monitorUnitMapper.selectByMonitorUnitId(monitorUnitId);
            activeMonitorUnitDTOMap.put(monitorUnitId, monitorUnitDTO);
        } else if (status == MonitorUnitStateEnum.SUCCESS) {
            activeMonitorUnitDTOMap.remove(monitorUnitId);
        }
        monitorUnitStatusHashmap.put(monitorUnitId, status.getValue());
    }

    // 获取所有下发的监控单元
    public List<Integer> getMonitorUnitIdsByState(int state) {
        return monitorUnitStatusHashmap.entrySet().stream()
                .filter(entry -> entry.getValue().equals(state))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    // 获取监控单元状态
    public Integer getMonitorUnitStatus(Integer monitorUnitId) {
        return monitorUnitStatusHashmap.get(monitorUnitId);
    }

    // 获取监控单元DTO
    public MonitorUnitDTO getActiveMonitorUnitDTO(Integer monitorUnitId) {
        return activeMonitorUnitDTOMap.get(monitorUnitId);
    }

    // 根据MonitorUnitIds获取多个
    public List<MonitorUnitDTO> getMonitorUnitDTOs(List<Integer> monitorUnitIds) {
        return monitorUnitIds.stream().map(this::getActiveMonitorUnitDTO).collect(Collectors.toList());
    }


}
