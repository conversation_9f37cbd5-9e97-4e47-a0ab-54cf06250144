package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblStationStructureMap;
import org.siteweb.config.common.mapper.TblStationStructureMapMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.service.StationStructureMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> (2024-03-12)
 **/
@Slf4j
@Service
public class StationStructureMapServiceImpl implements StationStructureMapService {
    @Autowired
    private TblStationStructureMapMapper stationStructureMapMapper;

    @Autowired
    ChangeEventService changeEventService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(TblStationStructureMap stationStructureMap) {
        int result = stationStructureMapMapper.insert(stationStructureMap);
        if (result > 0) {
            changeEventService.sendCreate(stationStructureMap);
        }
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByStationStructureId(Integer stationStructureId) {
        stationStructureMapMapper.delete(Wrappers.lambdaQuery(TblStationStructureMap.class)
                .eq(TblStationStructureMap::getStructureId, stationStructureId));
    }

    @Override
    public void deleteByStationId(Integer stationId) {
        stationStructureMapMapper.delete(Wrappers.lambdaQuery(TblStationStructureMap.class)
                .eq(TblStationStructureMap::getStationId, stationId));
    }

    @Override
    public List<TblStationStructureMap> findStationStructureMapList() {
        return stationStructureMapMapper.selectList(null);
    }

    @Override
    public TblStationStructureMap findStationStructureMapByStationId(Integer stationId) {
        return stationStructureMapMapper.selectOne(Wrappers.lambdaQuery(TblStationStructureMap.class)
                .eq(TblStationStructureMap::getStationId, stationId));
    }

    @Override
    public List<TblStationStructureMap> findStationStructureMapByStructureId(Integer structureId) {
        return stationStructureMapMapper.selectList(Wrappers.lambdaQuery(TblStationStructureMap.class)
                .eq(TblStationStructureMap::getStructureId, structureId));
    }

    @Override
    public void update(TblStationStructureMap stationStructureMap) {
        int result = stationStructureMapMapper.updateById(stationStructureMap);
        if (result > 0) {
            changeEventService.sendUpdate(stationStructureMap);
        }
    }

    @Override
    public List<Integer> findStationIdsByStructureId(Integer structureId) {
        return stationStructureMapMapper.selectList(Wrappers.lambdaQuery(TblStationStructureMap.class)
                .select(TblStationStructureMap::getStationId)
                .eq(TblStationStructureMap::getStructureId, structureId))
                .stream().map(TblStationStructureMap::getStationId).toList();
    }

    @Override
    public Boolean deleteByStationIds(List<Integer> stationIds) {
        return stationStructureMapMapper.deleteBatchIds(stationIds) > 0;

    }

    @Override
    public boolean batchCreate(List<TblStationStructureMap> stationStructureMapList) {
        return stationStructureMapMapper.insertBatchSomeColumn(stationStructureMapList) > 0;
    }
}
