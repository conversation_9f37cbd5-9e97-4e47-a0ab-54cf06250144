package org.siteweb.config.primary.manager;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/6/21
 */
@Component
@Slf4j
public class FtpManager {

    private final Map<String, FTPClient> ftpClientMap = new HashMap<>();


    public void addFtpClient(String key, FTPClient ftpClient) {
        ftpClientMap.put(key, ftpClient);
    }

    public FTPClient getFtpClient(String key) {
        return ftpClientMap.get(key);
    }

    public void removeFtpClient(String key) {
        ftpClientMap.remove(key);
    }

    public void closeFtpClient(String key) {
        FTPClient ftpClient = ftpClientMap.get(key);
        if (ftpClient != null && ftpClient.isConnected()) {
            try {
                ftpClient.logout();
                ftpClient.disconnect();
            } catch (Exception e) {
                log.error("close ftp client error", e);
            }
        }
    }

    @PreDestroy
    public void closeAllFtpClient() {
        ftpClientMap.forEach((key, ftpClient) -> {
            if (ftpClient != null && ftpClient.isConnected()) {
                try {
                    ftpClient.logout();
                    ftpClient.disconnect();
                } catch (Exception e) {
                    log.error("close ftp client error", e);
                }
            }
        });
    }

}
