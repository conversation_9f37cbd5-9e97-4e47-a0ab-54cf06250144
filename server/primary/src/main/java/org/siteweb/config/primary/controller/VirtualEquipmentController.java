package org.siteweb.config.primary.controller;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.common.utils.RestfulCode;
import org.siteweb.config.common.vo.batchtool.SplitEquipmentVo;
import org.siteweb.config.common.vo.batchtool.VirtualEquipmentSettingVO;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.service.StationService;
import org.siteweb.config.primary.service.VirtualEquipmentService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BA设备拆分控制类
 */
@RestController
@RequestMapping
@Slf4j
public class VirtualEquipmentController {
    /**
     * 生成的最大虚拟设备的数量
     */
    private static final int MAX_VIRTUAL_EQUIPMENT_COUNT = 200;
    @Autowired
    private VirtualEquipmentService virtualEquipmentService;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private StationService stationService;
    @Autowired
    private I18n i18n;

    /**
     * 获取层级资源(监控单元与设备)
     * @return
     */
    @GetMapping("/resourcestructuremonitorunit")
    public ResponseEntity<ResponseResult> getResourceStructureEquipmentMonitorUnit(){
         return ResponseHelper.successful(virtualEquipmentService.getResourceStructureEquipmentMonitorUnit());
    }

    /**
     * 获取拆分设备信息
     * @param equipmentId
     * @return
     */
    @GetMapping("/splitequipment/{equipmentId}")
    public ResponseEntity<ResponseResult> getSplitInfo(@PathVariable("equipmentId") Integer equipmentId){
           return ResponseHelper.successful(virtualEquipmentService.findSplitInfo(equipmentId));
    }

    /**
     * 拆分设备
     * @param splitEquipmentVo
     * @return
     */
    @PostMapping("/splitequipment")
    public ResponseEntity<ResponseResult> buildSplitEquipment(@RequestBody SplitEquipmentVo splitEquipmentVo) {
        TblEquipment equipment = equipmentService.findEquipmentById(splitEquipmentVo.getEquipmentId());
        TblStation station = stationService.findByStationId(equipment.getStationId());
        if (equipmentService.findEquipmentByStationNameAndEquipmentName(station.getStationName(), splitEquipmentVo.getVirtualEquipmentName()) != null) {
            return ResponseHelper.failed(RestfulCode.MonitorUnitHasEquipment.IntValue(), i18n.T("common.msg.equipmentExists"));
        }
        return ResponseHelper.successful(virtualEquipmentService.splitEquipment(splitEquipmentVo));
    }

    /**
     * 虚拟设备列表
     * @return
     */
    @GetMapping("/virtualequipment")
    public ResponseEntity<ResponseResult> virtualEquipment(){
        return ResponseHelper.successful(virtualEquipmentService.virtualEquipmentList());
    }

    /**
     * 导出虚拟设备配置
     * @param equipmentIds
     * @return
     */
    @PostMapping("/export/equipmentsetting")
    public ResponseEntity<ResponseResult> exportVirtualEquipmentSetting(@RequestBody List<Integer> equipmentIds){
        return ResponseHelper.successful(virtualEquipmentService.exportVirtualEquipmentSetting(equipmentIds));
    }

    /**
     * 导入虚拟设备配置
     * @param virtualEquipmentSettingVo
     * @return
     */
    @PostMapping("/import/equipmentsetting")
    public ResponseEntity<ResponseResult> importVirtualEquipmentSetting(@RequestBody VirtualEquipmentSettingVO virtualEquipmentSettingVo){
        if (CollUtil.size(virtualEquipmentSettingVo.getVirtualEquipmentExcelList()) > MAX_VIRTUAL_EQUIPMENT_COUNT) {
            return ResponseHelper.failed(i18n.T("virtualEquipment.quantityTooLarge", MAX_VIRTUAL_EQUIPMENT_COUNT));
        }
        return ResponseHelper.successful(virtualEquipmentService.importVirtualEquipmentSetting(virtualEquipmentSettingVo));
    }

    /**
     * 删除虚拟设备
     * @param id
     * @return
     */
    @DeleteMapping("/virtualequipment/{id}")
    public ResponseEntity<ResponseResult> deleteVirtualEquipment(@PathVariable Integer id){
        virtualEquipmentService.deleteVirtualEquipment(id);
        return ResponseHelper.successful();
    }

    /**
     * 获取虚拟设备详情
     * @param id
     * @return
     */
    @GetMapping("/virtualequipment/{id}")
    public ResponseEntity<ResponseResult> getVirtualEquipmentDetail(@PathVariable Integer id){
        return ResponseHelper.successful(virtualEquipmentService.findVirtualEquipmentDetail(id));
    }
}
