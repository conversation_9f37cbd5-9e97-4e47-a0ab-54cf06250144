package org.siteweb.config.primary.service;

import org.dom4j.Element;
import org.siteweb.config.common.entity.TblEquipmentTemplate;
import org.siteweb.config.common.entity.TslSampler;
import org.siteweb.config.common.vo.SamplerVO;
import org.siteweb.config.primary.enums.ProtocolTypeEnum;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> (2024-03-11)
 **/
public interface SamplerService {
    Collection<TslSampler> findAll();

    TslSampler findById(Integer samplerId);

    List<TslSampler> findByIds(List<Integer> samplerIdList);

    void createSamplersFromXml(TblEquipmentTemplate tblEquipmentTemplate, Element samplersElementList);

    void create(TslSampler sampler);

    void deleteByProtocolCode(String protocolCode);

    Set<String> findReferenceSamplerNameBySamplerIdList(List<Integer> samplerIdList);



    TslSampler findByNameAndDllPath(String samplerName,String dllPath);


    /**
     * 通过协议代码查找引用采样器名称
     *
     * @param protocolCode 协议代码
     * @return {@link Set}<{@link String}>
     */
    Set<String> findReferenceSamplerNameByProtocolCodes(List<String> protocolCode);

    Set<String> findReferenceEquipmentNameByProtocolCodes(List<String> protocolCodes);

    List<SamplerVO> findAllVo();

    boolean deleteByIds(List<Integer> samplerIdList);

    boolean deleteById(Integer samplerId);

    boolean update(TslSampler sampler);

    TslSampler findBySamplerType(int samplerType);

    TslSampler findByProtocolCode(String protocolCode);

    void batchInsertLianTongSampler();

    /**
     * 取得模板的采集器信息
     *
     * @param equipmentTemplateId 模板id
     * @return {@link TslSampler}
     */
    TslSampler findByEquipmentTemplateId(Integer equipmentTemplateId);

    void uploadProtocolFile(TslSampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName);

    void deleteProtocolFile(String protocolCode, ProtocolTypeEnum protocolTypeEnum);
}
