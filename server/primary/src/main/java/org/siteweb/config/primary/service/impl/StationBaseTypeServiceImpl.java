package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletResponse;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.excel.SheetDataWrapper;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.*;
import org.siteweb.config.common.vo.StandardApplyCheckVO;
import org.siteweb.config.common.vo.StandardCompareVO;
import org.siteweb.config.common.vo.StandardMappingCheckVO;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.listener.excel.*;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.primary.utils.ExcelExportUtil;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/28
 */

@Service
public class StationBaseTypeServiceImpl implements StationBaseTypeService {

    private static final String LIMIT_1 = "limit 1";
    public static final String TBL_STATION_BASE_MAP = "TBL_StationBaseMap";
    public static final String TBL_SIGNAL_BASE_MAP = "TBL_SignalBaseMap";
    public static final String TBL_EVENT_BASE_MAP = "TBL_EventBaseMap";
    public static final String TBL_COMMAND_BASE_MAP = "TBL_CommandBaseMap";
    public static final String LOGIC_CLASS_ENTRY = "TBL_LogicClassEntry";
    @Autowired
    TblStationBaseTypeMapper tblStationBaseTypeMapper;
    @Autowired
    StandardTypeService standardTypeService;
    @Autowired
    TblStationBaseMapMapper tblStationBaseMapMapper;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    I18n i18n;
    @Autowired
    TBLSignalBaseMapMapper signalBaseMapMapper;
    @Autowired
    TblEventBaseMapMapper eventBaseMapMapper;
    @Autowired
    TblCommandBaseMapMapper commandBaseMapMapper;
    @Autowired
    TblStandardDicSigMapper standardDicSigMapper;
    @Autowired
    TblStandardDicEventMapper standardDicEventMapper;
    @Autowired
    TblStandardDicControlMapper standardDicControlMapper;
    @Autowired
    TblSignalBaseDicMapper signalBaseDicMapper;
    @Autowired
    TblEventBaseDicMapper eventBaseDicMapper;
    @Autowired
    TblCommandBaseDicMapper commandBaseDicMapper;
    @Autowired
    SignalService signalService;
    @Autowired
    EventService eventService;
    @Autowired
    ControlService controlService;
    @Autowired
    StationService stationService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    TblStationBaseMapMapper stationBaseMapMapper;
    @Autowired
    TblLogicClassEntryService logicClassEntryService;

    @Override
    public int deleteByStandardId(Integer standardId) {
        return tblStationBaseTypeMapper.delete(new QueryWrapper<TblStationBaseType>().eq("StandardId", standardId));
    }

    @Override
    public int insert(TblStationBaseType tblStationBaseType) {
        return tblStationBaseTypeMapper.insert(tblStationBaseType);
    }

    @Override
    public List<IdValueDTO<Integer, String>> findCurrentStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<TblStationBaseType> tblStationBaseTypes = tblStationBaseTypeMapper.selectList(Wrappers.lambdaQuery(TblStationBaseType.class)
                .eq(TblStationBaseType::getStandardId, currentStandardType.getValue()));
        return tblStationBaseTypes.stream().map(e -> new IdValueDTO<>(e.getId(), e.getType())).toList();
    }

    @Override
    public List<BaseStationMapTypeDTO> getBaseStationMapType(Integer stationBaseTypeId) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return tblStationBaseTypeMapper.getBaseStationMapType(stationBaseTypeId, currentStandardType.getValue());
    }

    @Override
    public void saveBaseStationMapType(BaseStationMapTypeDTO baseStationMapTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        if (Boolean.TRUE.equals(baseStationMapTypeDTO.getChecked())) {
            // 先删除之前的关联
            tblStationBaseMapMapper.delete(Wrappers.lambdaQuery(TblStationBaseMap.class)
                    .eq(TblStationBaseMap::getStationCategory, baseStationMapTypeDTO.getItemId())
                    .eq(TblStationBaseMap::getStandardType, currentStandardType.getValue()));
            // 新增关联
            TblStationBaseMap stationBaseMap = new TblStationBaseMap();
            stationBaseMap.setStationBaseType(baseStationMapTypeDTO.getId());
            stationBaseMap.setStationCategory(baseStationMapTypeDTO.getItemId());
            stationBaseMap.setStandardType(currentStandardType.getValue());
            tblStationBaseMapMapper.insert(stationBaseMap);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(stationBaseMap.getStationBaseType()),
                    OperationObjectTypeEnum.STANDARD, "局站类型映射", i18n.T("add"), "",
                    String.format("%s-%s", baseStationMapTypeDTO.getType(), baseStationMapTypeDTO.getItemValue()));
        } else {
            // 取消关联
            tblStationBaseMapMapper.delete(Wrappers.lambdaQuery(TblStationBaseMap.class).eq(TblStationBaseMap::getStationBaseType, baseStationMapTypeDTO.getId())
                    .eq(TblStationBaseMap::getStationCategory, baseStationMapTypeDTO.getItemId())
                    .eq(TblStationBaseMap::getStandardType, currentStandardType.getValue())
            );
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(baseStationMapTypeDTO.getId()),
                    OperationObjectTypeEnum.STANDARD, "局站类型映射", i18n.T("delete"),
                    String.format("%s-%s", baseStationMapTypeDTO.getType(), baseStationMapTypeDTO.getItemValue()), "");
        }
    }

    @Override
    public List<EqStationBaseTypeDTO> getEquipmentTemplateStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return tblStationBaseTypeMapper.getEquipmentTemplateStationBaseType(currentStandardType.getValue());
    }

    @Override
    public List<SignalStationBaseTypeDTO> getSignalStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<SignalStationBaseTypeDTO> signalStationBaseTypes = tblStationBaseTypeMapper.getSignalStationBaseType(currentStandardType.getValue());
        List<SignalBaseMapDTO> signalBaseMaps = signalBaseMapMapper.getSignalBaseMap(currentStandardType.getValue());
        signalStationBaseTypes.forEach(signalStationBaseTypeDTO -> {
            List<SignalBaseMapDTO> signalBaseMapDTOS = signalBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), signalStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), signalStationBaseTypeDTO.getId())).toList();
            if (CollUtil.isNotEmpty(signalBaseMapDTOS)) {
                signalStationBaseTypeDTO.setBaseTypeId(signalBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                signalStationBaseTypeDTO.setBaseTypeName(signalBaseMapDTOS.stream().map(SignalBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return signalStationBaseTypes;
    }

    @Override
    public void saveSignalBaseMap(SignalStationBaseTypeDTO signalStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        signalBaseMapMapper.delete(Wrappers.lambdaQuery(TblSignalBaseMap.class).eq(TblSignalBaseMap::getStandardDicId, signalStationBaseTypeDTO.getStandardDicId())
                .eq(TblSignalBaseMap::getStandardType, currentStandardType.getValue())
                .eq(TblSignalBaseMap::getStationBaseType, signalStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(signalStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(signalStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .toList();
        List<TblSignalBaseMap> signalBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            TblSignalBaseMap signalBaseMap = new TblSignalBaseMap();
            signalBaseMap.setStandardDicId(signalStationBaseTypeDTO.getStandardDicId());
            signalBaseMap.setStandardType(currentStandardType.getValue());
            signalBaseMap.setStationBaseType(signalStationBaseTypeDTO.getId());
            signalBaseMap.setBaseTypeId(baseTypeId);
            return signalBaseMap;
        }).toList();
        signalBaseMapMapper.insertBatchSomeColumn(signalBaseMaps);
    }

    @Override
    public Map<String, String> getStationBaseTypeName(Object id) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        TblStationBaseType tblStationBaseTypes = tblStationBaseTypeMapper.selectOne(Wrappers.lambdaQuery(TblStationBaseType.class)
                .eq(TblStationBaseType::getStandardId, currentStandardType.getValue())
                .eq(TblStationBaseType::getId, id));
        return Map.of("type", tblStationBaseTypes.getType());
    }

    @Override
    public List<EventStationBaseTypeDTO> getEventStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<EventStationBaseTypeDTO> eventStationBaseTypeList = tblStationBaseTypeMapper.getEventStationBaseType(currentStandardType.getValue());
        List<EventBaseMapDTO> eventBaseMaps = eventBaseMapMapper.getEventBaseMap(currentStandardType.getValue());
        eventStationBaseTypeList.forEach(eventStationBaseTypeDTO -> {
            List<EventBaseMapDTO> eventBaseMapDTOS = eventBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), eventStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), eventStationBaseTypeDTO.getId())).toList();
            if (CollUtil.isNotEmpty(eventBaseMapDTOS)) {
                eventStationBaseTypeDTO.setBaseTypeId(eventBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                eventStationBaseTypeDTO.setBaseTypeName(eventBaseMapDTOS.stream().map(EventBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return eventStationBaseTypeList;
    }

    @Override
    public void saveEventBaseMap(EventStationBaseTypeDTO eventStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        eventBaseMapMapper.delete(Wrappers.lambdaQuery(TblEventBaseMap.class).eq(TblEventBaseMap::getStandardDicId, eventStationBaseTypeDTO.getStandardDicId())
                .eq(TblEventBaseMap::getStandardType, currentStandardType.getValue())
                .eq(TblEventBaseMap::getStationBaseType, eventStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(eventStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(eventStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .toList();
        List<TblEventBaseMap> eventBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            TblEventBaseMap eventBaseMap = new TblEventBaseMap();
            eventBaseMap.setStandardDicId(eventStationBaseTypeDTO.getStandardDicId());
            eventBaseMap.setStandardType(currentStandardType.getValue());
            eventBaseMap.setStationBaseType(eventStationBaseTypeDTO.getId());
            eventBaseMap.setBaseTypeId(baseTypeId);
            return eventBaseMap;
        }).toList();
        eventBaseMapMapper.insertBatchSomeColumn(eventBaseMaps);
    }

    @Override
    public List<ControlStationBaseTypeDTO> getControlStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<ControlStationBaseTypeDTO> controlStationBaseTypeList = tblStationBaseTypeMapper.getControlStationBaseType(currentStandardType.getValue());
        List<CommandBaseMapDTO> controlBaseMaps = commandBaseMapMapper.getControlBaseMap(currentStandardType.getValue());
        controlStationBaseTypeList.forEach(controlStationBaseTypeDTO -> {
            List<CommandBaseMapDTO> commandBaseMapDTOS = controlBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), controlStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), controlStationBaseTypeDTO.getId())).toList();
            if (CollUtil.isNotEmpty(commandBaseMapDTOS)) {
                controlStationBaseTypeDTO.setBaseTypeId(commandBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                controlStationBaseTypeDTO.setBaseTypeName(commandBaseMapDTOS.stream().map(CommandBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return controlStationBaseTypeList;
    }

    @Override
    public void saveControlBaseMap(ControlStationBaseTypeDTO controlStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        commandBaseMapMapper.delete(Wrappers.lambdaQuery(TblCommandBaseMap.class).eq(TblCommandBaseMap::getStandardDicId, controlStationBaseTypeDTO.getStandardDicId())
                .eq(TblCommandBaseMap::getStandardType, currentStandardType.getValue())
                .eq(TblCommandBaseMap::getStationBaseType, controlStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(controlStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(controlStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .toList();
        List<TblCommandBaseMap> commandBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            TblCommandBaseMap commandBaseMap = new TblCommandBaseMap();
            commandBaseMap.setStandardDicId(controlStationBaseTypeDTO.getStandardDicId());
            commandBaseMap.setStandardType(currentStandardType.getValue());
            commandBaseMap.setStationBaseType(controlStationBaseTypeDTO.getId());
            commandBaseMap.setBaseTypeId(baseTypeId);
            return commandBaseMap;
        }).toList();
        commandBaseMapMapper.insertBatchSomeColumn(commandBaseMaps);
    }

    @Override
    public BaseMapCountWarnDTO getWarnCountsInBaseMap(Integer standardDicId, Integer stationBaseType, List<Long> baseTypeId, Integer baseType) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Long count = switch (baseType) {
            case 1 ->
                    signalBaseMapMapper.selectCount(Wrappers.lambdaQuery(TblSignalBaseMap.class).eq(TblSignalBaseMap::getStandardType, currentStandardType.getValue())
                            .eq(TblSignalBaseMap::getStationBaseType, stationBaseType).in(TblSignalBaseMap::getBaseTypeId, baseTypeId)
                            .ne(TblSignalBaseMap::getStandardDicId, standardDicId));
            case 2 ->
                    eventBaseMapMapper.selectCount(Wrappers.lambdaQuery(TblEventBaseMap.class).eq(TblEventBaseMap::getStandardType, currentStandardType.getValue())
                            .eq(TblEventBaseMap::getStationBaseType, stationBaseType).in(TblEventBaseMap::getBaseTypeId, baseTypeId)
                            .ne(TblEventBaseMap::getStandardDicId, standardDicId));
            case 3 ->
                    commandBaseMapMapper.selectCount(Wrappers.lambdaQuery(TblCommandBaseMap.class).eq(TblCommandBaseMap::getStandardType, currentStandardType.getValue())
                            .eq(TblCommandBaseMap::getStationBaseType, stationBaseType).in(TblCommandBaseMap::getBaseTypeId, baseTypeId)
                            .ne(TblCommandBaseMap::getStandardDicId, standardDicId));
            default -> throw new IllegalArgumentException("Unexpected baseType: " + baseType);
        };
        if (count <= 0) {
            return BaseMapCountWarnDTO.builder().status(false).build();
        }
        return switch (baseType) {
            case 1 -> {
                List<TblSignalBaseMap> signalBaseMaps = signalBaseMapMapper.selectList(Wrappers.lambdaQuery(TblSignalBaseMap.class).eq(TblSignalBaseMap::getStandardType, currentStandardType.getValue())
                        .eq(TblSignalBaseMap::getStationBaseType, stationBaseType).in(TblSignalBaseMap::getBaseTypeId, baseTypeId)
                        .ne(TblSignalBaseMap::getStandardDicId, standardDicId));
                TblSignalBaseMap signalBaseMap = signalBaseMaps.stream().findFirst().orElseThrow();
                TblStandardDicSig standardDicSig = standardDicSigMapper.selectOne(Wrappers.lambdaQuery(TblStandardDicSig.class)
                        .eq(TblStandardDicSig::getStandardDicId, signalBaseMap.getStandardDicId())
                        .eq(TblStandardDicSig::getStandardType, signalBaseMap.getStandardType()).last(LIMIT_1)
                );
                TblSignalBaseDic signalBaseDic = signalBaseDicMapper.selectById(signalBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(signalBaseDic.getBaseTypeName())
                        .standardName(standardDicSig.getSignalStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类信号[{0}]已映射标准化信号[{1}]，请重新选择基类信号或取消原来选择信号",
                                signalBaseDic.getBaseTypeName(), standardDicSig.getSignalStandardName()))
                        .build();
            }
            case 2 -> {
                List<TblEventBaseMap> eventBaseMaps = eventBaseMapMapper.selectList(Wrappers.lambdaQuery(TblEventBaseMap.class).eq(TblEventBaseMap::getStandardType, currentStandardType.getValue())
                        .eq(TblEventBaseMap::getStationBaseType, stationBaseType).in(TblEventBaseMap::getBaseTypeId, baseTypeId)
                        .ne(TblEventBaseMap::getStandardDicId, standardDicId));
                TblEventBaseMap eventBaseMap = eventBaseMaps.stream().findFirst().orElseThrow();
                TblStandardDicEvent standardDicEvent = standardDicEventMapper.selectOne(Wrappers.lambdaQuery(TblStandardDicEvent.class)
                        .eq(TblStandardDicEvent::getStandardDicId, eventBaseMap.getStandardDicId())
                        .eq(TblStandardDicEvent::getStandardType, eventBaseMap.getStandardType()).last(LIMIT_1)
                );
                TblEventBaseDic eventBaseDic = eventBaseDicMapper.selectById(eventBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(eventBaseDic.getBaseTypeName())
                        .standardName(standardDicEvent.getEventStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类告警[{0}]已映射标准化告警[{1}]，请重新选择基类告警或取消原来选择告警",
                                eventBaseDic.getBaseTypeName(), standardDicEvent.getEventStandardName()))
                        .build();
            }
            case 3 -> {
                List<TblCommandBaseMap> commandBaseMaps = commandBaseMapMapper.selectList(Wrappers.lambdaQuery(TblCommandBaseMap.class).eq(TblCommandBaseMap::getStandardType, currentStandardType.getValue())
                        .eq(TblCommandBaseMap::getStationBaseType, stationBaseType).in(TblCommandBaseMap::getBaseTypeId, baseTypeId)
                        .ne(TblCommandBaseMap::getStandardDicId, standardDicId));
                TblCommandBaseMap commandBaseMap = commandBaseMaps.stream().findFirst().orElseThrow();
                TblStandardDicControl standardDicControl = standardDicControlMapper.selectOne(Wrappers.lambdaQuery(TblStandardDicControl.class)
                        .eq(TblStandardDicControl::getStandardDicId, commandBaseMap.getStandardDicId())
                        .eq(TblStandardDicControl::getStandardType, commandBaseMap.getStandardType()).last(LIMIT_1)
                );
                TblCommandBaseDic commandBaseDic = commandBaseDicMapper.selectById(commandBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(commandBaseDic.getBaseTypeName())
                        .standardName(standardDicControl.getControlStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类控制[{0}]已映射标准化控制[{1}]，请重新选择基类控制或取消原来选择控制",
                                commandBaseDic.getBaseTypeName(), standardDicControl.getControlStandardName()))
                        .build();
            }
            default -> throw new IllegalArgumentException("Unexpected baseType: " + baseType);
        };
    }

    @Override
    public Long applyStandard(ApplyStandardDTO applyStandardDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Integer type = applyStandardDTO.getType();
        return switch (type) {
            case 1 -> // 信号标准化
                    signalService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            case 2 -> // 告警标准化
                    eventService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            case 3 -> // 控制标准化
                    controlService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            default -> throw new IllegalArgumentException("Unexpected baseType: " + type);
        };
    }

    @Override
    public Long restoreStandard(Integer type) {
        return switch (type) {
            case 1 -> signalService.restoreStandard();
            case 2 -> eventService.restoreStandard();
            case 3 -> controlService.restoreStandard();
            default -> throw new IllegalArgumentException("Unexpected baseType: " + type);
        };
    }

    @Override
    public StandardCompareVO getStandardCompareData(Integer type) {
        StandardCompareVO standardCompareVO = new StandardCompareVO();
        List<StandardSignalCompareDTO> signalCompares = null;
        List<StandardEventCompareDTO> eventCompares = null;
        List<StandardControlCompareDTO> controlCompares = null;
        if (type == null) {
            type = 0;
        }
        switch (type) {
            case 1 -> signalCompares = signalService.getStandardCompareData();
            case 2 -> eventCompares = eventService.getStandardCompareData();
            case 3 -> controlCompares = controlService.getStandardCompareData();
            default -> {
                // 查全部
                signalCompares = signalService.getStandardCompareData();
                eventCompares = eventService.getStandardCompareData();
                controlCompares = controlService.getStandardCompareData();
            }
        }
        standardCompareVO.setSignalCompares(signalCompares);
        standardCompareVO.setEventCompares(eventCompares);
        standardCompareVO.setControlCompares(controlCompares);
        return standardCompareVO;
    }

    @Override
    public StandardApplyCheckVO getStandardApplyCheckData(Integer type) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        StandardApplyCheckVO standardApplyCheckVO = new StandardApplyCheckVO();
        List<StandardApplySignalCheckDTO> signalChecks = null;
        List<StandardApplyEventCheckDTO> eventChecks = null;
        List<StandardApplyControlCheckDTO> controlChecks = null;
        if (type == null) {
            type = 0;
        }
        switch (type) {
            case 1 -> signalChecks = signalService.getSignalStandardApplyCheckData(currentStandardType.getValue());
            case 2 -> eventChecks = eventService.getEventStandardApplyCheckData(currentStandardType.getValue());
            case 3 -> controlChecks = controlService.getControlStandardApplyCheckData(currentStandardType.getValue());
            default -> {
                // 查全部
                signalChecks = signalService.getSignalStandardApplyCheckData(currentStandardType.getValue());
                eventChecks = eventService.getEventStandardApplyCheckData(currentStandardType.getValue());
                controlChecks = controlService.getControlStandardApplyCheckData(currentStandardType.getValue());
            }
        }
        standardApplyCheckVO.setSignalChecks(signalChecks);
        standardApplyCheckVO.setEventChecks(eventChecks);
        standardApplyCheckVO.setControlChecks(controlChecks);
        return standardApplyCheckVO;
    }

    @Override
    public void exportStandardApplyCheck(HttpServletResponse response) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<StandardApplySignalCheckDTO> signalChecks = signalService.getSignalStandardApplyCheckData(currentStandardType.getValue());
        List<StandardApplyEventCheckDTO> eventChecks = eventService.getEventStandardApplyCheckData(currentStandardType.getValue());
        List<StandardApplyControlCheckDTO> controlChecks = controlService.getControlStandardApplyCheckData(currentStandardType.getValue());
        Map<String, SheetDataWrapper> sheetDataMap = new LinkedHashMap<>();
        sheetDataMap.put("SignalStandardedCheck", SheetDataWrapper.of(StandardApplySignalCheckDTO.class, signalChecks));
        sheetDataMap.put("EventStandardedCheck", SheetDataWrapper.of(StandardApplyEventCheckDTO.class, eventChecks));
        sheetDataMap.put("ControlStandardedCheck", SheetDataWrapper.of(StandardApplyControlCheckDTO.class, controlChecks));
        try {
            ExcelExportUtil.exportExcelMultiSheet(response, sheetDataMap, "标准化检查文件");
        } catch (IOException e) {
            throw new BusinessException("export StandardApplyCheck Excel error:", e);
        }
    }

    @Override
    public StandardMappingCheckVO getStandardMappingCheck(Integer type, Integer equipmentCategory) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Map<Integer, String> stationCategoryMap = dataItemService.getDataItemMap(DataEntryEnum.STATION_CATEGORY);
        Map<Integer, String> eqTypes = dataItemService.getDataItemMap(DataEntryEnum.EQUIPMENT_CATEGORY);
        StandardMappingCheckVO standardMappingCheckVO = new StandardMappingCheckVO();
        List<StandardMappingSignalCheckDTO> signalChecks = null;
        List<StandardMappingEventCheckDTO> eventChecks = null;
        List<StandardMappingControlCheckDTO> controlChecks = null;
        if (type == null) {
            type = 0;
        }
        switch (type) {
            case 1 ->
                    signalChecks = signalService.getSignalStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
            case 2 ->
                    eventChecks = eventService.getEventStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
            case 3 ->
                    controlChecks = controlService.getControStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
            default -> {
                // 查全部
                signalChecks = signalService.getSignalStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
                eventChecks = eventService.getEventStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
                controlChecks = controlService.getControStandardMappingCheck(currentStandardType.getValue(), equipmentCategory);
            }
        }
        if (CollUtil.isNotEmpty(signalChecks)) {
            // 字典
            for (StandardMappingSignalCheckDTO signalCheck : signalChecks) {
                signalCheck.setStationCategoryName(stationCategoryMap.get(signalCheck.getStationCategory()));
                signalCheck.setEquipmentCategoryName(eqTypes.get(signalCheck.getEquipmentCategory()));
            }
        }
        if (CollUtil.isNotEmpty(eventChecks)) {
            for (StandardMappingEventCheckDTO eventCheck : eventChecks) {
                eventCheck.setStationCategoryName(stationCategoryMap.get(eventCheck.getStationCategory()));
                eventCheck.setEquipmentCategoryName(eqTypes.get(eventCheck.getEquipmentCategory()));
            }
        }
        if (CollUtil.isNotEmpty(controlChecks)) {
            for (StandardMappingControlCheckDTO controlCheck : controlChecks) {
                controlCheck.setStationCategoryName(stationCategoryMap.get(controlCheck.getStationCategory()));
                controlCheck.setEquipmentCategoryName(eqTypes.get(controlCheck.getEquipmentCategory()));
            }
        }
        standardMappingCheckVO.setSignalChecks(signalChecks);
        standardMappingCheckVO.setEventChecks(eventChecks);
        standardMappingCheckVO.setControlChecks(controlChecks);
        return standardMappingCheckVO;
    }

    @Override
    public void exportCustomStandardMapping(HttpServletResponse response) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<StationBaseMapExportDTO> stationBaseMapExports = stationBaseMapMapper.getStationBaseMapExport(currentStandardType.getValue());
        List<SignalBaseMapExportDTO> signalBaseMapExports = signalBaseMapMapper.getSignalBaseMapExport(currentStandardType.getValue());
        List<EventBaseMapExportDTO> eventBaseMapExports = eventBaseMapMapper.getEventBaseMapExport(currentStandardType.getValue());
        List<CommandBaseMapExportDTO> commandBaseMapExports = commandBaseMapMapper.getCommandBaseMapExport(currentStandardType.getValue());
        List<LogicClassEntryExportDTO> logicClassEntryExports = logicClassEntryService.getLogicClassEntryExport(currentStandardType.getValue());
        Map<String, Collection<?>> sheetDataMap = new LinkedHashMap<>();
        sheetDataMap.put(TBL_STATION_BASE_MAP, stationBaseMapExports);
        sheetDataMap.put(TBL_SIGNAL_BASE_MAP, signalBaseMapExports);
        sheetDataMap.put(TBL_EVENT_BASE_MAP, eventBaseMapExports);
        sheetDataMap.put(TBL_COMMAND_BASE_MAP, commandBaseMapExports);
        sheetDataMap.put(LOGIC_CLASS_ENTRY, logicClassEntryExports);
        try {
            ExcelExportUtil.exportProtectionExcelMultiSheet(response, sheetDataMap, "客户标准化映射文件", "admin");
        } catch (IOException e) {
            throw new BusinessException("export CustomStandardMapping Excel error:", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importCustomStandardMapping(MultipartFile file) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        try (ExcelReader excelReader = EasyExcelFactory.read(file.getInputStream()).build()) {
            // 获取所有的 Sheet
            List<ReadSheet> sheetList = excelReader.excelExecutor().sheetList();
            checkImportExcel(sheetList, currentStandardType.getValue());

            StationBaseMapDataListener stationBaseMapDataListener = new StationBaseMapDataListener(stationBaseMapMapper, standardTypeService);
            ReadSheet readSheet1 = EasyExcelFactory.readSheet(TBL_STATION_BASE_MAP)
                    .head(StationBaseMapExportDTO.class).registerReadListener(stationBaseMapDataListener).build();

            SignalBaseMapDataListener signalBaseMapDataListener = new SignalBaseMapDataListener(signalBaseMapMapper, standardTypeService);
            ReadSheet readSheet2 = EasyExcelFactory.readSheet(TBL_SIGNAL_BASE_MAP)
                    .head(SignalBaseMapExportDTO.class).registerReadListener(signalBaseMapDataListener).build();

            EventBaseMapDataListener eventBaseMapDataListener = new EventBaseMapDataListener(eventBaseMapMapper, standardTypeService);
            ReadSheet readSheet3 = EasyExcelFactory.readSheet(TBL_EVENT_BASE_MAP)
                    .head(EventBaseMapExportDTO.class).registerReadListener(eventBaseMapDataListener).build();

            CommandBaseMapDataListener commandBaseMapDataListener = new CommandBaseMapDataListener(commandBaseMapMapper, standardTypeService);
            ReadSheet readSheet4 = EasyExcelFactory.readSheet(TBL_COMMAND_BASE_MAP)
                    .head(CommandBaseMapExportDTO.class).registerReadListener(commandBaseMapDataListener).build();

            LogicClassEntryDataListener logicClassEntryDataListener = new LogicClassEntryDataListener(logicClassEntryService, standardTypeService);
            ReadSheet readSheet5 = EasyExcelFactory.readSheet(LOGIC_CLASS_ENTRY)
                    .head(LogicClassEntryExportDTO.class).registerReadListener(logicClassEntryDataListener).build();
            // 这里注意 一定要把sheet1 sheet2 一起传进去，不然有个问题就是03版的excel 会读取多次，浪费性能
            excelReader.read(readSheet1, readSheet2, readSheet3, readSheet4, readSheet5);
            // 获取读取的行数
            BigDecimal count = NumberUtil.add(stationBaseMapDataListener.getRowCount(), signalBaseMapDataListener.getRowCount(),
                    eventBaseMapDataListener.getRowCount(), commandBaseMapDataListener.getRowCount(), logicClassEntryDataListener.getRowCount());
            return count.intValue();
        } catch (IOException e) {
            throw new BusinessException("import CustomStandardMapping Excel error:", e);
        }
    }

    private void checkImportExcel(List<ReadSheet> sheetList, Integer standardType) {
        // 检查 sheet 是否存在
        StringBuilder strOutMsg = new StringBuilder();
        boolean stationExists = false;
        boolean signalExists = false;
        boolean eventExists = false;
        boolean commandExists = false;
        boolean logicExists = false;
        for (ReadSheet readSheet : sheetList) {
            if (Objects.equals(readSheet.getSheetName(), TBL_STATION_BASE_MAP)) {
                stationExists = true;
            }
            if (Objects.equals(readSheet.getSheetName(), TBL_SIGNAL_BASE_MAP)) {
                signalExists = true;
            }
            if (Objects.equals(readSheet.getSheetName(), TBL_EVENT_BASE_MAP)) {
                eventExists = true;
            }
            if (Objects.equals(readSheet.getSheetName(), TBL_COMMAND_BASE_MAP)) {
                commandExists = true;
            }
            if (Objects.equals(readSheet.getSheetName(), LOGIC_CLASS_ENTRY)) {
                logicExists = true;
            }
        }
        boolean sheetExists = true;
        if (!stationExists) {
            sheetExists = false;
            strOutMsg.append("TBL_StationBaseMap ");
        }
        if (standardType == 2 && !signalExists) {
            sheetExists = false;
            strOutMsg.append("TBL_SignalBaseMap ");
        }
        if ((standardType == 1 || standardType == 2) && !eventExists) {
            sheetExists = false;
            strOutMsg.append("TBL_EventBaseMap ");
        }
        if (standardType == 2 && !commandExists) {
            sheetExists = false;
            strOutMsg.append("TBL_CommandBaseMap ");
        }
        if (!logicExists) {
            sheetExists = false;
            strOutMsg.append("TBL_LogicClassEntry ");
        }
        if (!sheetExists) {
            throw new BusinessException(String.format("Sheet %s 不存在！", strOutMsg));
        }
    }

    @Override
    public ResponseEntity<byte[]> exportMappingIniFile() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        // 使用 ByteArrayOutputStream 来存储 INI 文件内容
        byte[] iniFileBytes;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
             BufferedWriter writer = new BufferedWriter(outputStreamWriter)) {
            List<StandardDicSigIniDTO> sigInis = standardDicSigMapper.getStandardDicSigInis(currentStandardType.getValue());
            // 写入数据
            writer.write("#-------------------标准化信号映射关系--------------------\n\n");
            for (StandardDicSigIniDTO sigIni : sigInis) {
                String baseNames = sigIni.getBaseTypeList()
                        .stream().map(BaseTypeInnerDTO::getBaseTypeName).collect(Collectors.joining(","));
                String baseIds = sigIni.getBaseTypeList()
                        .stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(","));
                writer.write(String.format("#%s\t%s\t%s%n",
                        Objects.toString(sigIni.getSignalLogicClass(), ""),
                        Objects.toString(sigIni.getSignalStandardName(), ""),
                        baseNames));
                writer.write(sigIni.getNetManageId() + "=" + baseIds + "\n\n");
            }
            List<StandardDicEventIniDTO> eventInis = standardDicEventMapper.getStandardDicEventInis(currentStandardType.getValue());
            writer.write("#--------------------标准化事件映射关系--------------------\n\n");
            for (StandardDicEventIniDTO eventIni : eventInis) {
                String baseNames = eventIni.getBaseTypeList()
                        .stream().map(BaseTypeInnerDTO::getBaseTypeName).collect(Collectors.joining(","));
                String baseIds = eventIni.getBaseTypeList()
                        .stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(","));
                writer.write(String.format("#%s\t%s\t%s%n",
                        Objects.toString(eventIni.getEventLogicClass(), ""),
                        Objects.toString(eventIni.getEventStandardName(), ""),
                        baseNames));
                writer.write(eventIni.getNetManageId() + "=" + baseIds + "\n\n");
            }
            writer.write("#--------------------标准化控制映射关系--------------------\n\n");
            List<StandardDicControlIniDTO> controlInis = standardDicControlMapper.getStandardDicControlInis(currentStandardType.getValue());
            for (StandardDicControlIniDTO controlIni : controlInis) {
                String baseNames = controlIni.getBaseTypeList()
                        .stream().map(BaseTypeInnerDTO::getBaseTypeName).collect(Collectors.joining(","));
                String baseIds = controlIni.getBaseTypeList()
                        .stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(","));
                writer.write(String.format("#%s\t%s\t%s%n",
                        Objects.toString(controlIni.getControlLogicClass(), ""),
                        Objects.toString(controlIni.getControlStandardName(), ""),
                        baseNames));
                writer.write(controlIni.getNetManageId() + "=" + baseIds + "\n\n");
            }
            // 确保数据写入到 ByteArrayOutputStream
            writer.flush();
            // 获取生成的字节数组
            iniFileBytes = outputStream.toByteArray();
        } catch (IOException e) {
            throw new BusinessException("export MappingIni file error:", e);
        }
        // 设置响应头并返回文件
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "SignalIdMap.ini");
        return ResponseEntity.ok()
                .headers(headers)
                .body(iniFileBytes);
    }
}
