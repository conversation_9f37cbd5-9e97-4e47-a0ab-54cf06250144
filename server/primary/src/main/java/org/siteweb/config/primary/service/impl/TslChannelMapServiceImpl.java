package org.siteweb.config.primary.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.siteweb.config.common.entity.TslChannelMap;
import org.siteweb.config.common.mapper.TslChannelMapMapper;
import org.siteweb.config.primary.service.TslChannelMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/17
 */
@Service
public class TslChannelMapServiceImpl implements TslChannelMapService {


    @Autowired
    private TslChannelMapMapper tslChannelMapMapper;

    @Override
    public List<TslChannelMap> findByMonitorUnitId(int monitorUnitId) {
        return tslChannelMapMapper.selectList(new LambdaQueryWrapper<>(TslChannelMap.class)
                .eq(TslChannelMap::getMonitorUnitId, monitorUnitId));
    }
}
