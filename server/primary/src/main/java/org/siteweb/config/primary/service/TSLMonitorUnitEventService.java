package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.MonitorUnitEventDTO;
import org.siteweb.config.common.entity.TslMonitorUnitEvent;

import java.util.List;

public interface TSLMonitorUnitEventService {
    List<TslMonitorUnitEvent> findByMonitorUnitIdExcludingEquipmentId(Integer monitorUnitId, Integer equipmentId);

    List<TslMonitorUnitEvent> findByMonitorUnitId(Integer monitorUnitId);

    /**
     * 通过设备id与事件id查找事件实例
     *
     * @param equipmentId 设备主键id
     * @param eventId     事件主键id
     * @return {@link TslMonitorUnitEvent}
     */
    MonitorUnitEventDTO findByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    /**
     * 通过设备id与事件id判断事件实例是否存在
     *
     * @param equipmentId 设备主键id
     * @param eventId     事件主键id
     * @return boolean
     */
    boolean existsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    TslMonitorUnitEvent createOrUpdate(TslMonitorUnitEvent monitorUnitEvent);

    List<MonitorUnitEventDTO> findByEquipmentId(Integer equipmentId);

    int deleteByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    int deleteByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIdList);

    void deleteByStationId(Integer stationId);
}
