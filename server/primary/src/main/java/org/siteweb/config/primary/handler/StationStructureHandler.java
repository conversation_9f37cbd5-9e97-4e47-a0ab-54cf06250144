package org.siteweb.config.primary.handler;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.change.ChangeMessage;
import org.siteweb.config.common.change.ChangeRecord;
import org.siteweb.config.common.change.ObjectChangeHandlerAdapter;
import org.siteweb.config.common.entity.ResourceStructure;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.entity.TblStationStructure;
import org.siteweb.config.common.entity.TblStationStructureMap;
import org.siteweb.config.primary.service.ResourceStructureService;
import org.siteweb.config.primary.service.StationService;
import org.siteweb.config.primary.service.StationStructureMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/17
 */
@Slf4j
@RestController
@RequestMapping("/StationStructureHandler")
public class StationStructureHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private StationStructureMapService stationStructureMapService;

    @Autowired
    private StationService stationService;

    @Autowired
    private ResourceStructureService resourceStructureService;


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(TblStationStructure.class);
    }


    @Override
    public void onCreate(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStationStructure stationStructure = msg.readBody(TblStationStructure.class);
        log.info("局站分组创建成功 stationStructureName:{}", stationStructure.getStructureName());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStationStructure stationStructure = msg.readBody(TblStationStructure.class);
        log.info("局站分组 删除成功 stationStructureName:{}", stationStructure.getStructureName());
        // 删除对应的resourceStructure
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(stationStructure.getStructureId(), 103);
        if (resourceStructure != null) {
            resourceStructureService.deleteByID(resourceStructure.getResourceStructureId());
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblStationStructure stationStructure = msg.readBody(TblStationStructure.class);
        log.info("局站分组 修改成功 stationStructureName:{}", stationStructure.getStructureName());
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(stationStructure.getStructureId(), 103);
        if (resourceStructure != null && !resourceStructure.getResourceStructureName().equals(stationStructure.getStructureName())) {
            resourceStructure.setResourceStructureName(stationStructure.getStructureName());
            resourceStructureService.update(resourceStructure);
        }

    }
}