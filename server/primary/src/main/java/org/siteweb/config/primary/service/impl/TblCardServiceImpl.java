package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblCard;
import org.siteweb.config.common.mapper.TblCardMapper;
import org.siteweb.config.primary.service.TblCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (2024-09-26)
 **/

@Service
public class TblCardServiceImpl implements TblCardService {

    @Autowired
    private TblCardMapper tblCardMapper;


    @Override
    public TblCard findCardById(Integer cardId) {
        return tblCardMapper.selectById(cardId);
    }

    @Override
    public TblCard findCardByCode(String cardCode) {
        return tblCardMapper.selectOne(Wrappers.lambdaQuery(TblCard.class).eq(TblCard::getCardCode, cardCode));
    }
}
