package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblStandardDicSig;

import java.util.List;

public interface StandardDicSigService {
    /**
     * 获取当前的信号标准化字典
     * @return {@link List }<{@link TblStandardDicSig }>
     */
    List<TblStandardDicSig> findCurrentStandardDicSig();

    TblStandardDicSig update(TblStandardDicSig standardDicSig);

    int deleteByStandardDicIdAndStandardType(Integer standardDicId, Integer standardTypeId);

    TblStandardDicSig create(TblStandardDicSig tblStandardDicSig);

    List<TblStandardDicSig> findByStandardType(Integer standardType);

    Integer getMaxStandardDicId();
}
