package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.*;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.enums.SysConfigEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/8/21
 */
@Slf4j
@Service
public class TblSwatchStationServiceImpl implements TblSwatchStationService {

    @Autowired
    private TblSwatchStationMapper tblSwatchStationMapper;

    @Autowired
    private TblStationSwatchMapMapper tblStationSwatchMapMapper;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    StationService stationService;

    @Autowired
    MonitorUnitService monitorUnitService;

    @Autowired
    private ResourceStructureService resourceStructureService;


    @Autowired
    private I18n i18n;

    @Autowired
    private PortService portService;

    @Autowired
    SamplerUnitService samplerUnitService;

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    TslMonitorUnitSignalService tslMonitorUnitSignalService;

    @Autowired
    TSLMonitorUnitEventService tslMonitorUnitEventService;

    @Autowired
    HouseService houseService;

    @Autowired
    SysConfigService sysConfigService;

    @Autowired
    StationStructureMapService stationStructureMapService;

    @Autowired
    TblEventLogActionService tblEventLogActionService;

    @Autowired
    TblControlLogActionService tblControlLogActionService;

    @Autowired
    TblBusinessTypeMapper tblBusinessTypeMapper;


    @Autowired
    BizExpStationsMapMapper bizExpStationsMapMapper;

    @Autowired
    BizExpEquSignalsMapMapper bizExpEquSignalsMapMapper;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    StationStructureService stationStructureService;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    DataItemService dataItemService;


    @Override
    public List<TblSwatchStation> selectAll() {
        return tblSwatchStationMapper.selectAll();
    }

    @Override
    public boolean update(TblSwatchStation tblSwatchStation) {
        return tblSwatchStationMapper.updateById(tblSwatchStation) > 0;
    }

    @Override
    public boolean delete(Integer swatchStationId) {
        tblStationSwatchMapMapper.delete(new LambdaQueryWrapper<TblStationSwatchMap>().eq(TblStationSwatchMap::getSwatchStationId, swatchStationId));
        return tblSwatchStationMapper.delete(new LambdaQueryWrapper<TblSwatchStation>().eq(TblSwatchStation::getSwatchStationId, swatchStationId)) > 0;
    }

    @Override
    public boolean add(TblSwatchStation tblSwatchStation) {
        // 根据传入的stationid判断是否表中已存在
        QueryWrapper<TblSwatchStation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("StationId", tblSwatchStation.getStationId());
        if (tblSwatchStationMapper.selectOne(queryWrapper) != null) {
            throw new BusinessException("不允许重复设置样板站");
        }
        // 不允许重名
        queryWrapper.clear();
        queryWrapper.eq("SwatchStationName", tblSwatchStation.getSwatchStationName());
        if (tblSwatchStationMapper.selectOne(queryWrapper) != null) {
            throw new BusinessException("样板站名称重复");
        }
        tblSwatchStation.setCreateTime(LocalDateTime.now());
        if (tblSwatchStationMapper.insert(tblSwatchStation) > 0) {
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), tblSwatchStation.getSwatchStationId().toString(), OperationObjectTypeEnum.SWATCH_STATION, i18n.T("port.name"), i18n.T("delete"), tblSwatchStation.getSwatchStationName(),"");
            return true;
        }
        return false;

    }

    @Override
    public TblSwatchStation selectByStationId(Integer stationId) {
        return tblSwatchStationMapper.selectOne(new LambdaQueryWrapper<TblSwatchStation>().eq(TblSwatchStation::getStationId, stationId));
    }

    @Override
    public List<TblStation> selectStationBySwatchStationId(Integer swatchStationId) {
        List<TblStationSwatchMap> tblStationSwatchMaps = tblStationSwatchMapMapper.selectList(new LambdaQueryWrapper<TblStationSwatchMap>().eq(TblStationSwatchMap::getSwatchStationId, swatchStationId));
        List<Integer> stationIds = new ArrayList<>();
        for (TblStationSwatchMap tblStationSwatchMap : tblStationSwatchMaps) {
            stationIds.add(tblStationSwatchMap.getStationId());
        }
        if (CollUtil.isEmpty(stationIds)) {
            return new ArrayList<>();
        }
        return stationService.findStationList(stationIds);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addStation(SwatchStationDTO swatchStationDTO) {
        // Validate swatchStationDTO items for duplicates
        if (!CollUtil.isEmpty(swatchStationDTO.getSwatchStationItems())) {
            Set<String> stationSet = new HashSet<>();
            Set<String> ipSet = new HashSet<>();
            Set<String> combinedSet = new HashSet<>();

            for (SwatchStationItem item : swatchStationDTO.getSwatchStationItems()) {
                // Normalize the input: trim and convert to lowercase
                String stationName = item.getStationName() != null ?
                        item.getStationName().trim().toLowerCase() : "";
                String ipAddress = item.getIpAddress() != null ?
                        item.getIpAddress().trim() : "";

                // Check combined uniqueness (station + IP)
                String uniqueKey = stationName + "|" + ipAddress;
                if (!combinedSet.add(uniqueKey)) {
                    throw new BusinessException(String.format(
                            "存在完全重复的记录：站点名称[%s]和IP地址[%s]",
                            item.getStationName(),
                            item.getIpAddress()
                    ));
                }

                // Check station name uniqueness
                if (!stationSet.add(stationName)) {
                    throw new BusinessException(String.format(
                            "站点名称重复：[%s]",
                            item.getStationName()
                    ));
                }

                // Check IP address uniqueness
                if (!ipSet.add(ipAddress)) {
                    throw new BusinessException(String.format(
                            "IP地址重复：[%s]",
                            item.getIpAddress()
                    ));
                }
            }
        }
        TblSwatchStation tblSwatchStation = tblSwatchStationMapper.selectById(swatchStationDTO.getSwatchStationId());
        if (tblSwatchStation == null) {
            throw new BusinessException("样板站不存在");
        }
        TblStation oldStation = stationService.findByStationId(tblSwatchStation.getStationId());
        if (oldStation == null) {
            throw new BusinessException("局站不存在");
        }
        SystemConfig sceneMode = systemConfigService.findBySystemConfigKey("scenemode");
        if (!sceneMode.getSystemConfigValue().equals("2")) {
            throw new BusinessException("不是电信场景");
        }
        ResourceStructure parentResourceStructure = resourceStructureService.getStructureByID(swatchStationDTO.getResourceStructureId());
        if (parentResourceStructure == null) {
            throw new BusinessException("层级不存在");
        }
        if (parentResourceStructure.getStructureTypeId() != 103) {
            throw new BusinessException("不是局站分组");
        }

        if (CollUtil.isEmpty(swatchStationDTO.getSwatchStationItems())) {
            throw new BusinessException("局站名称不能为空");
        }
        ArrayList<EventLogActionDTO<LogActionEquipment>> eventLogActionDTOS = new ArrayList<>();
        ArrayList<ExpressionSwatch<LogActionEquipment>> expressionSwatches = new ArrayList<>();
        for (SwatchStationItem swatchStationItemDTO : swatchStationDTO.getSwatchStationItems()) {
            TblStation exitStation = stationService.findStationByName(swatchStationItemDTO.getStationName());
            if (exitStation != null) {
                throw new BusinessException("局站名称重复");
            }
            if (StrUtil.isEmpty(swatchStationItemDTO.getStationName())) {
                throw new BusinessException("局站名称不能为空");
            }
            if (StrUtil.isEmpty(swatchStationItemDTO.getIpAddress())) {
                throw new BusinessException("IP地址不能为空");
            }
            String newStationName = swatchStationItemDTO.getStationName().trim();
            String ipAddress = swatchStationItemDTO.getIpAddress().trim();
            TblStation tblStation = new TblStation();
            BeanUtil.copyProperties(oldStation, tblStation);
            tblStation.setStationId(null);
            tblStation.setStationName(newStationName);
            tblStation.setConnectState(2);
            stationService.insert(tblStation);

            // 增加对应的层级
            ResourceStructure resourceStructure = new ResourceStructure();
            resourceStructure.setSceneId(2);
            resourceStructure.setStructureTypeId(104);
            resourceStructure.setResourceStructureName(newStationName);
            resourceStructure.setParentResourceStructureId(swatchStationDTO.getResourceStructureId());
            resourceStructure.setLevelOfPath(parentResourceStructure.getLevelOfPath());
            resourceStructure.setDisplay(true);
            resourceStructure.setSortValue(1);
            resourceStructure.setOriginId(tblStation.getStationId());
            resourceStructureService.create(resourceStructure);


            List<MonitorUnitDTO> MonitorUnitDTO = monitorUnitService.findByStationId(oldStation.getStationId());
            int i = 0;
            for (MonitorUnitDTO monitorUnitDTO : MonitorUnitDTO) {
                EventLogActionDTO<LogActionEquipment> newEventLogActionSwatch = new EventLogActionDTO<>();
                ExpressionSwatch<LogActionEquipment> newExpressionSwatch = new ExpressionSwatch<LogActionEquipment>();
                newEventLogActionSwatch.setStationId(tblStation.getStationId());
                newEventLogActionSwatch.setOldStationId(oldStation.getStationId());
                newExpressionSwatch.setStationId(tblStation.getStationId());
                newExpressionSwatch.setOldStationId(oldStation.getStationId());
                HashMap<Integer, Integer> dicPortId = new HashMap<>();
                HashMap<Integer, Integer> dicLinkSamplerUnitIds = new HashMap<>();
                HashMap<Integer, Integer> dicSamplerUnitId = new HashMap<>();
                HashMap<Integer, Integer> dicParentSamplerUnitId = new HashMap<>();
                HashMap<Integer, Integer> dicEquipmentId = new HashMap<>();
                MonitorUnitDTO mu = new MonitorUnitDTO();
                BeanUtil.copyProperties(monitorUnitDTO, mu);
                mu.setMonitorUnitName(String.format("%s %d#", tblStation.getStationName(), i + 1));
                mu.setStationId(tblStation.getStationId());
                mu.setIpAddress(ipAddress);
                mu.setUpdateTime(LocalDateTime.now());
                mu.setAppConfigId(1);
                mu.setConnectState(2);
                mu.setMonitorUnitId(null);
                monitorUnitService.createSwatchMonitorUnit(mu);
                newEventLogActionSwatch.setMonitorUnitId(mu.getMonitorUnitId());
                newExpressionSwatch.setMonitorUnitId(mu.getMonitorUnitId());

                // 端口
                List<TslPort> oldPorts = portService.findByMonitorUnit(monitorUnitDTO.getMonitorUnitId());
                for (TslPort oldPort : oldPorts) {
                    TslPort port = new TslPort();
                    BeanUtil.copyProperties(oldPort, port);
                    port.setPortId(null);
                    port.setMonitorUnitId(mu.getMonitorUnitId());
                    port.setId(null);
                    portService.createPort(port);
                    dicPortId.put(oldPort.getPortId(), port.getPortId());
                    if (oldPort.getLinkSamplerUnitId() != null) {
                        dicLinkSamplerUnitIds.put(oldPort.getPortId(), oldPort.getLinkSamplerUnitId());
                    }
                }

                // 采集单元
                List<TslSamplerUnit> oldSamplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                for (TslSamplerUnit samplerUnit : oldSamplerUnits) {
                    TslSamplerUnit unit = new TslSamplerUnit();
                    BeanUtil.copyProperties(samplerUnit, unit);
                    unit.setSamplerUnitId(null);
                    unit.setId(null);
                    unit.setMonitorUnitId(mu.getMonitorUnitId());
                    unit.setPortId(dicPortId.get(samplerUnit.getPortId()));
                    samplerUnitService.createSamplerUnit(unit);
                    dicSamplerUnitId.put(samplerUnit.getSamplerUnitId(), unit.getSamplerUnitId());
                    if (samplerUnit.getParentSamplerUnitId() != null) {
                        dicParentSamplerUnitId.put(samplerUnit.getSamplerUnitId(), samplerUnit.getParentSamplerUnitId());
                    }
                }


                for (Integer oldSamplerUnitId : dicSamplerUnitId.keySet()) {
                    List<Integer> lst_Pid = getKeysFromMap(dicLinkSamplerUnitIds, oldSamplerUnitId);
                    List<Integer> lst_newSpuId = getKeysFromMap(dicParentSamplerUnitId, oldSamplerUnitId);
                    for (Integer _pid : lst_Pid) {
                        dicLinkSamplerUnitIds.put(_pid, dicSamplerUnitId.get(oldSamplerUnitId));
                    }
                    for (Integer newSpuId : lst_newSpuId) {
                        dicParentSamplerUnitId.put(newSpuId, dicSamplerUnitId.get(oldSamplerUnitId));
                    }
                }

                for (Integer pid : dicLinkSamplerUnitIds.keySet()) {
                    TslPort port = portService.findByPortId(pid);
                    port.setLinkSamplerUnitId(dicLinkSamplerUnitIds.get(pid));
                    portService.updatePort(port);
                }

                for (Integer spuId : dicParentSamplerUnitId.keySet()) {
                    TslSamplerUnit su = samplerUnitService.findBySamplerUnitId(spuId);
                    su.setParentSamplerUnitId(dicParentSamplerUnitId.get(spuId));
                    samplerUnitService.updateSamplerUnit(su);
                }

                List<TblEquipment> oldEquipments = equipmentService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                ArrayList<TblEquipment> tblEquipments = new ArrayList<>();
                for (TblEquipment equipment : oldEquipments) {
                    TblEquipment tblEquipment = new TblEquipment();
                    BeanUtil.copyProperties(equipment, tblEquipment);
                    tblEquipment.setEquipmentId(null);
                    tblEquipment.setMonitorUnitId(mu.getMonitorUnitId());
//                if (equipment.getEquipmentCategory() == 99) {
//                    // 对自诊断设备改名的逻辑
//                }
                    tblEquipment.setStationId(tblStation.getStationId());
                    if (equipment.getSamplerUnitId() != 50000) {
                        tblEquipment.setSamplerUnitId(dicSamplerUnitId.get(equipment.getSamplerUnitId()));
                    }

                    equipmentService.createEquipment(tblEquipment);
                    dicEquipmentId.put(tblEquipment.getEquipmentId(), equipment.getEquipmentId());

                    if (newEventLogActionSwatch.getControlLogActions() == null) {
                        ArrayList<LogActionEquipment> logActionEquipments = new ArrayList<>();
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        logActionEquipments.add(logActionEquipment);
                        newEventLogActionSwatch.setControlLogActions(logActionEquipments);
                    } else {
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        newEventLogActionSwatch.getControlLogActions().add(logActionEquipment);
                    }

                    if (newExpressionSwatch.getSwatchList() == null) {
                        ArrayList<LogActionEquipment> logActionEquipments = new ArrayList<>();
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        logActionEquipments.add(logActionEquipment);
                        newExpressionSwatch.setSwatchList(logActionEquipments);
                    } else {
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        newExpressionSwatch.getSwatchList().add(logActionEquipment);
                    }


                    tblEquipments.add(tblEquipment);

                }
                //转换设备告警过滤表达式和上一级设备
                for (TblEquipment equipment : tblEquipments) {
                    if (StrUtil.isNotEmpty(equipment.getEventExpression())) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            equipment.setEventExpression(equipment.getEventExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    if (equipment.getParentEquipmentId() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            equipment.setParentEquipmentId(equipment.getParentEquipmentId().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    EquipmentDetailDTO equipmentDetailDTO = new EquipmentDetailDTO();
                    BeanUtil.copyProperties(equipment, equipmentDetailDTO);
                    equipmentService.updateEquipment(equipmentDetailDTO);
                }
                //增加实例信号
                List<TslMonitorUnitSignal> tslMonitorUnitSignals = tslMonitorUnitSignalService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());

                for (TslMonitorUnitSignal tslMonitorUnitSignal : tslMonitorUnitSignals) {
                    TslMonitorUnitSignal signal = new TslMonitorUnitSignal();
                    BeanUtil.copyProperties(tslMonitorUnitSignal, signal);
                    signal.setMonitorUnitId(mu.getMonitorUnitId());
                    signal.setStationId(tblStation.getStationId());
                    // 遍历dicEquipmentId，key是新的设备id，value是旧的设备id
                    for (Integer newId : dicEquipmentId.keySet()) {
                        if (Objects.equals(signal.getEquipmentId(), dicEquipmentId.get(newId))) {
                            signal.setEquipmentId(newId);
                        }
                    }
                    if (signal.getReferenceSamplerUnitId() != null) {
                        signal.setReferenceSamplerUnitId(dicSamplerUnitId.get(signal.getReferenceSamplerUnitId()));
                    }
                    if (signal.getExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            signal.setExpression(signal.getExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    tslMonitorUnitSignalService.create(signal);
                }
                //增加实例事件
                List<TslMonitorUnitEvent> tslMonitorUnitEvents = tslMonitorUnitEventService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                for (TslMonitorUnitEvent tslMonitorUnitEvent : tslMonitorUnitEvents) {
                    TslMonitorUnitEvent event = new TslMonitorUnitEvent();
                    BeanUtil.copyProperties(tslMonitorUnitEvent, event);
                    event.setMonitorUnitId(mu.getMonitorUnitId());
                    event.setStationId(tblStation.getStationId());
                    // 遍历dicEquipmentId，key是新的设备id，value是旧的设备id
                    for (Integer newId : dicEquipmentId.keySet()) {
                        if (Objects.equals(event.getEquipmentId(), dicEquipmentId.get(newId))) {
                            event.setEquipmentId(newId);
                        }
                    }
                    if (event.getStartExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            event.setStartExpression(event.getStartExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    if (event.getSuppressExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            event.setSuppressExpression(event.getSuppressExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    tslMonitorUnitEventService.createOrUpdate(event);
                }
                eventLogActionDTOS.add(newEventLogActionSwatch);
                expressionSwatches.add(newExpressionSwatch);
                i++;

            }
            // 增加局房间和层级
            List<TblHouse> oldHouses = houseService.findHouseByStationId(oldStation.getStationId());
            for (TblHouse oldHouse : oldHouses) {
                TblHouse house = new TblHouse();
                BeanUtil.copyProperties(oldHouse, house);
                house.setHouseId(null);
                house.setStationId(tblStation.getStationId());
                houseService.createHouse(house);

                ResourceStructure houseResourceStructure = new ResourceStructure();
//                houseResourceStructure.setSceneId(2);
                houseResourceStructure.setStructureTypeId(StructureTypeEnum.STATION_HOUSE.getValue());
                houseResourceStructure.setResourceStructureName(house.getHouseName());
                houseResourceStructure.setParentResourceStructureId(resourceStructure.getResourceStructureId());
                houseResourceStructure.setLevelOfPath(resourceStructure.getLevelOfPath());
                houseResourceStructure.setDisplay(true);
                houseResourceStructure.setSortValue(1);
                houseResourceStructure.setOriginId(house.getHouseId());
                houseResourceStructure.setOriginParentId(tblStation.getStationId());
                resourceStructureService.create(houseResourceStructure);
            }
            // 增加移动B接口配置
            TblSysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
            if (sysConfig.getConfigValue().equals("1")) {
                tblSwatchStationMapper.insertEquipmentCMCC(tblStation.getStationId(), oldStation.getStationId());
                tblSwatchStationMapper.updateEquipmentCMCCEquipmentid(tblStation.getStationId());
                tblSwatchStationMapper.updateEquipmentCMCCMonitorUnitId(tblStation.getStationId());
            }

            // 将局站添加进分组
            TblStationStructureMap tblStationStructureMap = new TblStationStructureMap();
            tblStationStructureMap.setStationId(tblStation.getStationId());
            tblStationStructureMap.setStructureId(swatchStationDTO.getStationStructureId());
            stationStructureMapService.create(tblStationStructureMap);


            TblStationSwatchMap tblStationSwatchMap = new TblStationSwatchMap();
            tblStationSwatchMap.setStationId(tblStation.getStationId());
            tblStationSwatchMap.setSwatchStationId(tblSwatchStation.getSwatchStationId());
            tblStationSwatchMapMapper.insert(tblStationSwatchMap);
        }

        for (EventLogActionDTO<LogActionEquipment> eventLogActionDTO : eventLogActionDTOS) {
            List<TblEventLogAction> oldTblEventLogActions = tblEventLogActionService.findByStationId(eventLogActionDTO.getOldStationId());
            for (TblEventLogAction oldTblEventLogAction : oldTblEventLogActions) {
                EventLogActionDTO<TblControlLogAction> newEventLogActionDTO = new EventLogActionDTO<>();
                newEventLogActionDTO.setStationId(eventLogActionDTO.getStationId());
                newEventLogActionDTO.setMonitorUnitId(eventLogActionDTO.getMonitorUnitId());
                newEventLogActionDTO.setActionName(oldTblEventLogAction.getActionName());
                newEventLogActionDTO.setInformMsg(oldTblEventLogAction.getInformMsg());
                newEventLogActionDTO.setDescription(oldTblEventLogAction.getDescription());
                newEventLogActionDTO.setTriggerType(oldTblEventLogAction.getTriggerType());
                newEventLogActionDTO.setStartExpression(oldTblEventLogAction.getStartExpression());
                for (LogActionEquipment logActionEquipment : eventLogActionDTO.getControlLogActions()) {
                    newEventLogActionDTO.setStartExpression(newEventLogActionDTO.getStartExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                newEventLogActionDTO.setSuppressExpression(oldTblEventLogAction.getSuppressExpression());
                for (LogActionEquipment logActionEquipment : eventLogActionDTO.getControlLogActions()) {
                    newEventLogActionDTO.setSuppressExpression(newEventLogActionDTO.getSuppressExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                List<TblControlLogAction> oldTblControlLogActions = tblControlLogActionService.findByLogActionIdAndActionName(oldTblEventLogAction.getLogActionId(), oldTblEventLogAction.getActionName());
                ArrayList<TblControlLogAction> tblControlLogActions = new ArrayList<>();
                for (TblControlLogAction oldTblControlLogAction : oldTblControlLogActions) {
                    TblControlLogAction newTblControlLogAction = new TblControlLogAction();
                    BeanUtil.copyProperties(oldTblControlLogAction, newTblControlLogAction);
                    newTblControlLogAction.setLogActionId(newEventLogActionDTO.getLogActionId());
                }
                newEventLogActionDTO.setControlLogActions(tblControlLogActions);
                tblEventLogActionService.createEventLogAction(newEventLogActionDTO);
            }
        }

        for (ExpressionSwatch<LogActionEquipment> expressionSwatch : expressionSwatches) {
            List<BizExpStationsMap> bizExpStationsMaps = bizExpStationsMapMapper.selectList(Wrappers.lambdaQuery(BizExpStationsMap.class).eq(BizExpStationsMap::getStationId, expressionSwatch.getOldStationId()));
            for (BizExpStationsMap oldBusinessExpressionVO : bizExpStationsMaps) {
                BizExpStationsMap newBusinessExpressionVO = new BizExpStationsMap();
                BeanUtil.copyProperties(oldBusinessExpressionVO, newBusinessExpressionVO);
                newBusinessExpressionVO.setStationId(expressionSwatch.getStationId());
                newBusinessExpressionVO.setMonitorUnitId(expressionSwatch.getMonitorUnitId());
                newBusinessExpressionVO.setExpression(oldBusinessExpressionVO.getExpression());
                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    newBusinessExpressionVO.setExpression(newBusinessExpressionVO.getExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                newBusinessExpressionVO.setSuppressExpression(oldBusinessExpressionVO.getSuppressExpression());
                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    newBusinessExpressionVO.setSuppressExpression(newBusinessExpressionVO.getSuppressExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                bizExpStationsMapMapper.insert(newBusinessExpressionVO);
            }
            List<BizExpEquSignalsMap> oldBizExpEquSignalsMaps = bizExpEquSignalsMapMapper.selectList(Wrappers.lambdaQuery(BizExpEquSignalsMap.class).eq(BizExpEquSignalsMap::getStationId, expressionSwatch.getOldStationId()));
            for (BizExpEquSignalsMap oldBizExpEquSignalsMap : oldBizExpEquSignalsMaps) {
                BizExpEquSignalsMap newBizExpEquSignalsMap = new BizExpEquSignalsMap();
                BeanUtil.copyProperties(oldBizExpEquSignalsMap, newBizExpEquSignalsMap);
                newBizExpEquSignalsMap.setStationId(expressionSwatch.getStationId());
                newBizExpEquSignalsMap.setMonitorUnitId(expressionSwatch.getMonitorUnitId());

                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    if (Objects.equals(logActionEquipment.getOldEquipmentId(), oldBizExpEquSignalsMap.getEquipmentId())) {
                        newBizExpEquSignalsMap.setEquipmentId(logActionEquipment.getNewEquipmentId());
                    }
                }
                bizExpEquSignalsMapMapper.insert(newBizExpEquSignalsMap);
            }
        }
        return true;
    }

    @Override
    public boolean addStationV3(SwatchStationDTO swatchStationDTO) {
        if (!CollUtil.isEmpty(swatchStationDTO.getSwatchStationItems())) {
            Set<String> stationSet = new HashSet<>();
            Set<String> ipSet = new HashSet<>();
            Set<String> combinedSet = new HashSet<>();

            for (SwatchStationItem item : swatchStationDTO.getSwatchStationItems()) {
                // Normalize the input: trim and convert to lowercase
                String stationName = item.getStationName() != null ?
                        item.getStationName().trim().toLowerCase() : "";
                String ipAddress = item.getIpAddress() != null ?
                        item.getIpAddress().trim() : "";

                // Check combined uniqueness (station + IP)
                String uniqueKey = stationName + "|" + ipAddress;
                if (!combinedSet.add(uniqueKey)) {
                    throw new BusinessException(String.format(
                            "存在完全重复的记录：站点名称[%s]和IP地址[%s]",
                            item.getStationName(),
                            item.getIpAddress()
                    ));
                }

                // Check station name uniqueness
                if (!stationSet.add(stationName)) {
                    throw new BusinessException(String.format(
                            "站点名称重复：[%s]",
                            item.getStationName()
                    ));
                }

                // Check IP address uniqueness
                if (!ipSet.add(ipAddress)) {
                    throw new BusinessException(String.format(
                            "IP地址重复：[%s]",
                            item.getIpAddress()
                    ));
                }
                // 校验IP地址是否已在数据库中存在
                if (StrUtil.isNotEmpty(ipAddress)) { // 只有当IP地址非空时才校验
                    List<TslMonitorUnit> existingUnits = monitorUnitService.findByIpAddress(ipAddress);
                    if (!CollUtil.isEmpty(existingUnits)) {
                        throw new BusinessException(String.format(
                                "IP地址[%s]已在系统中存在，请使用其他IP地址",
                                item.getIpAddress()
                        ));
                    }
                }
            }
        }

        TblSwatchStation tblSwatchStation = tblSwatchStationMapper.selectById(swatchStationDTO.getSwatchStationId());
        if (tblSwatchStation == null) {
            throw new BusinessException("样板站不存在");
        }
        TblStation oldStation = stationService.findByStationId(tblSwatchStation.getStationId());
        if (oldStation == null) {
            throw new BusinessException("局站不存在");
        }
        TblStationStructure stationStructure = stationStructureService.findStructureById(swatchStationDTO.getStationStructureId());
        if (stationStructure == null) {
            throw new BusinessException("分组不存在不存在");
        }

        if (CollUtil.isEmpty(swatchStationDTO.getSwatchStationItems())) {
            throw new BusinessException("局站名称不能为空");
        }
        ArrayList<EventLogActionDTO<LogActionEquipment>> eventLogActionDTOS = new ArrayList<>();
        ArrayList<ExpressionSwatch<LogActionEquipment>> expressionSwatches = new ArrayList<>();
        for (SwatchStationItem swatchStationItemDTO : swatchStationDTO.getSwatchStationItems()) {
            TblStation exitStation = stationService.findStationByName(swatchStationItemDTO.getStationName());
            if (exitStation != null) {
                throw new BusinessException("局站名称重复");
            }
            if (StrUtil.isEmpty(swatchStationItemDTO.getStationName())) {
                throw new BusinessException("局站名称不能为空");
            }
            if (StrUtil.isEmpty(swatchStationItemDTO.getIpAddress())) {
                throw new BusinessException("IP地址不能为空");
            }
            // 将局站添加进分组
            Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);

            TblStationStructureMap tblStationStructureMap = new TblStationStructureMap();
            tblStationStructureMap.setStationId(stationId);
            tblStationStructureMap.setStructureId(swatchStationDTO.getStationStructureId());
            stationStructureMapService.create(tblStationStructureMap);
            String newStationName = swatchStationItemDTO.getStationName().trim();
            String ipAddress = swatchStationItemDTO.getIpAddress().trim();
            TblStation tblStation = new TblStation();
            BeanUtil.copyProperties(oldStation, tblStation);
            tblStation.setStationId(stationId);
            tblStation.setStationName(newStationName);
            tblStation.setConnectState(2);
            tblStation.setProjectName(swatchStationDTO.getProjectName());
            tblStation.setContractNo(swatchStationDTO.getContactNo());
            stationService.insert(tblStation);


            List<MonitorUnitDTO> MonitorUnitDTO = monitorUnitService.findByStationId(oldStation.getStationId());
            int i = 0;
            for (MonitorUnitDTO monitorUnitDTO : MonitorUnitDTO) {
                EventLogActionDTO<LogActionEquipment> newEventLogActionSwatch = new EventLogActionDTO<>();
                ExpressionSwatch<LogActionEquipment> newExpressionSwatch = new ExpressionSwatch<LogActionEquipment>();
                newEventLogActionSwatch.setStationId(tblStation.getStationId());
                newEventLogActionSwatch.setOldStationId(oldStation.getStationId());
                newExpressionSwatch.setStationId(tblStation.getStationId());
                newExpressionSwatch.setOldStationId(oldStation.getStationId());
                HashMap<Integer, Integer> dicPortId = new HashMap<>();
                HashMap<Integer, Integer> dicLinkSamplerUnitIds = new HashMap<>();
                HashMap<Integer, Integer> dicSamplerUnitId = new HashMap<>();
                HashMap<Integer, Integer> dicParentSamplerUnitId = new HashMap<>();
                HashMap<Integer, Integer> dicEquipmentId = new HashMap<>();
                MonitorUnitDTO mu = new MonitorUnitDTO();
                BeanUtil.copyProperties(monitorUnitDTO, mu);
                mu.setMonitorUnitName(String.format("%s%d#", tblStation.getStationName(), i + 1));
                mu.setStationId(tblStation.getStationId());
                mu.setIpAddress(ipAddress);
                mu.setUpdateTime(LocalDateTime.now());
                mu.setAppConfigId(1);
                mu.setConnectState(2);
                mu.setMonitorUnitId(null);
                mu.setProjectName(swatchStationDTO.getProjectName());
                mu.setContractNo(swatchStationDTO.getContactNo());
                monitorUnitService.createSwatchMonitorUnit(mu);
                newEventLogActionSwatch.setMonitorUnitId(mu.getMonitorUnitId());
                newExpressionSwatch.setMonitorUnitId(mu.getMonitorUnitId());

                // 端口
                List<TslPort> oldPorts = portService.findByMonitorUnit(monitorUnitDTO.getMonitorUnitId());
                for (TslPort oldPort : oldPorts) {
                    TslPort port = new TslPort();
                    BeanUtil.copyProperties(oldPort, port);
                    port.setPortId(null);
                    port.setMonitorUnitId(mu.getMonitorUnitId());
                    port.setId(null);
                    portService.createPort(port);
                    dicPortId.put(oldPort.getPortId(), port.getPortId());
                    if (oldPort.getLinkSamplerUnitId() != null) {
                        dicLinkSamplerUnitIds.put(oldPort.getPortId(), oldPort.getLinkSamplerUnitId());
                    }
                }

                // 采集单元
                List<TslSamplerUnit> oldSamplerUnits = samplerUnitService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                for (TslSamplerUnit samplerUnit : oldSamplerUnits) {
                    TslSamplerUnit unit = new TslSamplerUnit();
                    BeanUtil.copyProperties(samplerUnit, unit);
                    unit.setSamplerUnitId(null);
                    unit.setId(null);
                    unit.setMonitorUnitId(mu.getMonitorUnitId());
                    unit.setPortId(dicPortId.get(samplerUnit.getPortId()));
                    samplerUnitService.createSamplerUnit(unit);
                    dicSamplerUnitId.put(samplerUnit.getSamplerUnitId(), unit.getSamplerUnitId());
                    if (samplerUnit.getParentSamplerUnitId() != null) {
                        dicParentSamplerUnitId.put(samplerUnit.getSamplerUnitId(), samplerUnit.getParentSamplerUnitId());
                    }
                }


                for (Integer oldSamplerUnitId : dicSamplerUnitId.keySet()) {
                    List<Integer> lst_Pid = getKeysFromMap(dicLinkSamplerUnitIds, oldSamplerUnitId);
                    List<Integer> lst_newSpuId = getKeysFromMap(dicParentSamplerUnitId, oldSamplerUnitId);
                    for (Integer _pid : lst_Pid) {
                        dicLinkSamplerUnitIds.put(_pid, dicSamplerUnitId.get(oldSamplerUnitId));
                    }
                    for (Integer newSpuId : lst_newSpuId) {
                        dicParentSamplerUnitId.put(newSpuId, dicSamplerUnitId.get(oldSamplerUnitId));
                    }
                }

                for (Integer pid : dicLinkSamplerUnitIds.keySet()) {
                    TslPort port = portService.findByPortId(pid);
                    port.setLinkSamplerUnitId(dicLinkSamplerUnitIds.get(pid));
                    portService.updatePort(port);
                }

                for (Integer spuId : dicParentSamplerUnitId.keySet()) {
                    TslSamplerUnit su = samplerUnitService.findBySamplerUnitId(spuId);
                    su.setParentSamplerUnitId(dicParentSamplerUnitId.get(spuId));
                    samplerUnitService.updateSamplerUnit(su);
                }

                List<TblEquipment> oldEquipments = equipmentService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                ArrayList<TblEquipment> tblEquipments = new ArrayList<>();
                for (TblEquipment equipment : oldEquipments) {
                    TblEquipment tblEquipment = new TblEquipment();
                    BeanUtil.copyProperties(equipment, tblEquipment);
                    tblEquipment.setEquipmentId(null);
                    tblEquipment.setResourceStructureId(0);
                    tblEquipment.setMonitorUnitId(mu.getMonitorUnitId());
                    tblEquipment.setProjectName(swatchStationDTO.getProjectName());
                    tblEquipment.setContractNo(swatchStationDTO.getContactNo());
                    if (equipment.getEquipmentCategory() == 99) {
                        TblDataItem dataItem = dataItemService.findByEntryIdAndItemId(DataEntryEnum.MONITOR_UNIT_TYPE, monitorUnitDTO.getMonitorUnitCategory());
                        if (dataItem != null) {
                            tblEquipment.setEquipmentName(dataItem.getItemValue() + i18n.T("site.self.diagnosis.equipment") + "-" + mu.getMonitorUnitName());
                        } else {
                            tblEquipment.setEquipmentName(i18n.T("site.self.diagnosis.equipment") + "-" + mu.getMonitorUnitName());
                        }
                    }
                    if (equipment.getEquipmentCategory() == 51) {
                        TblDataItem dataItem = dataItemService.findByEntryIdAndItemId(DataEntryEnum.MONITOR_UNIT_TYPE, monitorUnitDTO.getMonitorUnitCategory());
                        if (dataItem != null ) {
                            tblEquipment.setEquipmentName(dataItem.getItemValue() + i18n.T("io.equipment") + "-" + mu.getMonitorUnitName());

                        } else {
                            tblEquipment.setEquipmentName(i18n.T("io.equipment") + "-" + mu.getMonitorUnitName());
                        }

                    }
                    tblEquipment.setStationId(tblStation.getStationId());
                    if (equipment.getSamplerUnitId() != 50000) {
                        tblEquipment.setSamplerUnitId(dicSamplerUnitId.get(equipment.getSamplerUnitId()));
                    }

                    equipmentService.createEquipment(tblEquipment);
                    dicEquipmentId.put(tblEquipment.getEquipmentId(), equipment.getEquipmentId());

                    if (newEventLogActionSwatch.getControlLogActions() == null) {
                        ArrayList<LogActionEquipment> logActionEquipments = new ArrayList<>();
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        logActionEquipments.add(logActionEquipment);
                        newEventLogActionSwatch.setControlLogActions(logActionEquipments);
                    } else {
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        newEventLogActionSwatch.getControlLogActions().add(logActionEquipment);
                    }

                    if (newExpressionSwatch.getSwatchList() == null) {
                        ArrayList<LogActionEquipment> logActionEquipments = new ArrayList<>();
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        logActionEquipments.add(logActionEquipment);
                        newExpressionSwatch.setSwatchList(logActionEquipments);
                    } else {
                        LogActionEquipment logActionEquipment = new LogActionEquipment();
                        logActionEquipment.setOldEquipmentId(equipment.getEquipmentId());
                        logActionEquipment.setNewEquipmentId(tblEquipment.getEquipmentId());
                        newExpressionSwatch.getSwatchList().add(logActionEquipment);
                    }


                    tblEquipments.add(tblEquipment);

                }
                //转换设备告警过滤表达式和上一级设备
                for (TblEquipment equipment : tblEquipments) {
                    if (StrUtil.isNotEmpty(equipment.getEventExpression())) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            equipment.setEventExpression(equipment.getEventExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    if (equipment.getParentEquipmentId() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            equipment.setParentEquipmentId(equipment.getParentEquipmentId().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    EquipmentDetailDTO equipmentDetailDTO = new EquipmentDetailDTO();
                    BeanUtil.copyProperties(equipment, equipmentDetailDTO);
                    equipmentService.updateEquipment(equipmentDetailDTO);
                }
                //增加实例信号
                List<TslMonitorUnitSignal> tslMonitorUnitSignals = tslMonitorUnitSignalService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());

                for (TslMonitorUnitSignal tslMonitorUnitSignal : tslMonitorUnitSignals) {
                    TslMonitorUnitSignal signal = new TslMonitorUnitSignal();
                    BeanUtil.copyProperties(tslMonitorUnitSignal, signal);
                    signal.setMonitorUnitId(mu.getMonitorUnitId());
                    signal.setStationId(tblStation.getStationId());
                    // 遍历dicEquipmentId，key是新的设备id，value是旧的设备id
                    for (Integer newId : dicEquipmentId.keySet()) {
                        if (Objects.equals(signal.getEquipmentId(), dicEquipmentId.get(newId))) {
                            signal.setEquipmentId(newId);
                        }
                    }
                    if (signal.getReferenceSamplerUnitId() != null) {
                        signal.setReferenceSamplerUnitId(dicSamplerUnitId.get(signal.getReferenceSamplerUnitId()));
                    }
                    if (signal.getExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            signal.setExpression(signal.getExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    tslMonitorUnitSignalService.create(signal);
                }
                //增加实例事件
                List<TslMonitorUnitEvent> tslMonitorUnitEvents = tslMonitorUnitEventService.findByMonitorUnitId(monitorUnitDTO.getMonitorUnitId());
                for (TslMonitorUnitEvent tslMonitorUnitEvent : tslMonitorUnitEvents) {
                    TslMonitorUnitEvent event = new TslMonitorUnitEvent();
                    BeanUtil.copyProperties(tslMonitorUnitEvent, event);
                    event.setMonitorUnitId(mu.getMonitorUnitId());
                    event.setStationId(tblStation.getStationId());
                    // 遍历dicEquipmentId，key是新的设备id，value是旧的设备id
                    for (Integer newId : dicEquipmentId.keySet()) {
                        if (Objects.equals(event.getEquipmentId(), dicEquipmentId.get(newId))) {
                            event.setEquipmentId(newId);
                        }
                    }
                    if (event.getStartExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            event.setStartExpression(event.getStartExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    if (event.getSuppressExpression() != null) {
                        for (Integer newId : dicEquipmentId.keySet()) {
                            event.setSuppressExpression(event.getSuppressExpression().replace(dicEquipmentId.get(newId).toString(), newId.toString()));
                        }
                    }
                    tslMonitorUnitEventService.createOrUpdate(event);
                }
                eventLogActionDTOS.add(newEventLogActionSwatch);
                expressionSwatches.add(newExpressionSwatch);
                i++;

            }
            // 增加局房间和层级
            List<TblHouse> oldHouses = houseService.findHouseByStationId(oldStation.getStationId());
            // 增加老房间id和新房间id的map
            Map<Integer, Integer> oldHouseIdMap = new HashMap<>();
            for (TblHouse oldHouse : oldHouses) {
                TblHouse house = new TblHouse();
                BeanUtil.copyProperties(oldHouse, house);
                house.setHouseId(null);
                house.setStationId(tblStation.getStationId());
                houseService.createHouse(house);
                oldHouseIdMap.put(oldHouse.getHouseId(), house.getHouseId());
            }
            // 遍历equipment列表，将新房间id和旧房间id的map中的数据同步到新的房间中
            List<TblEquipment> equipments = equipmentService.findByStationId(tblStation.getStationId());
            for (TblEquipment equipment : equipments) {
                equipment.setHouseId(oldHouseIdMap.get(equipment.getHouseId()));
                ResourceStructure resourceStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(equipment.getHouseId(), equipment.getStationId(), StructureTypeEnum.STATION_HOUSE.getValue());
                if (resourceStructure != null) {
                    equipment.setResourceStructureId(resourceStructure.getResourceStructureId());
                }
                // 标识是样板站层级映射变更
                equipment.setChangeType(0);
                EquipmentDetailDTO equipmentDetailDTO = new EquipmentDetailDTO();
                BeanUtil.copyProperties(equipment, equipmentDetailDTO);
                equipmentService.updateEquipment(equipmentDetailDTO);
            }
            // 增加移动B接口配置
            TblSysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
            if (sysConfig.getConfigValue().equals("1")) {
                tblSwatchStationMapper.insertEquipmentCMCC(tblStation.getStationId(), oldStation.getStationId());
                tblSwatchStationMapper.updateEquipmentCMCCEquipmentid(tblStation.getStationId());
                tblSwatchStationMapper.updateEquipmentCMCCMonitorUnitId(tblStation.getStationId());
            }


            TblStationSwatchMap tblStationSwatchMap = new TblStationSwatchMap();
            tblStationSwatchMap.setStationId(tblStation.getStationId());
            tblStationSwatchMap.setSwatchStationId(tblSwatchStation.getSwatchStationId());
            tblStationSwatchMapMapper.insert(tblStationSwatchMap);
        }

        for (EventLogActionDTO<LogActionEquipment> eventLogActionDTO : eventLogActionDTOS) {
            List<TblEventLogAction> oldTblEventLogActions = tblEventLogActionService.findByStationId(eventLogActionDTO.getOldStationId());
            for (TblEventLogAction oldTblEventLogAction : oldTblEventLogActions) {
                EventLogActionDTO<TblControlLogAction> newEventLogActionDTO = new EventLogActionDTO<>();
                newEventLogActionDTO.setStationId(eventLogActionDTO.getStationId());
                newEventLogActionDTO.setMonitorUnitId(eventLogActionDTO.getMonitorUnitId());
                newEventLogActionDTO.setActionName(oldTblEventLogAction.getActionName());
                newEventLogActionDTO.setInformMsg(oldTblEventLogAction.getInformMsg());
                newEventLogActionDTO.setDescription(oldTblEventLogAction.getDescription());
                newEventLogActionDTO.setTriggerType(oldTblEventLogAction.getTriggerType());
                newEventLogActionDTO.setStartExpression(oldTblEventLogAction.getStartExpression());
                for (LogActionEquipment logActionEquipment : eventLogActionDTO.getControlLogActions()) {
                    newEventLogActionDTO.setStartExpression(newEventLogActionDTO.getStartExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                newEventLogActionDTO.setSuppressExpression(oldTblEventLogAction.getSuppressExpression());
                for (LogActionEquipment logActionEquipment : eventLogActionDTO.getControlLogActions()) {
                    newEventLogActionDTO.setSuppressExpression(newEventLogActionDTO.getSuppressExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                List<TblControlLogAction> oldTblControlLogActions = tblControlLogActionService.findByLogActionIdAndActionName(oldTblEventLogAction.getLogActionId(), oldTblEventLogAction.getActionName());
                ArrayList<TblControlLogAction> tblControlLogActions = new ArrayList<>();
                for (TblControlLogAction oldTblControlLogAction : oldTblControlLogActions) {
                    TblControlLogAction newTblControlLogAction = new TblControlLogAction();
                    BeanUtil.copyProperties(oldTblControlLogAction, newTblControlLogAction);
                    newTblControlLogAction.setLogActionId(newEventLogActionDTO.getLogActionId());
                }
                newEventLogActionDTO.setControlLogActions(tblControlLogActions);
                tblEventLogActionService.createEventLogAction(newEventLogActionDTO);
            }
        }

        for (ExpressionSwatch<LogActionEquipment> expressionSwatch : expressionSwatches) {
            List<BizExpStationsMap> bizExpStationsMaps = bizExpStationsMapMapper.selectList(Wrappers.lambdaQuery(BizExpStationsMap.class).eq(BizExpStationsMap::getStationId, expressionSwatch.getOldStationId()));
            for (BizExpStationsMap oldBusinessExpressionVO : bizExpStationsMaps) {
                BizExpStationsMap newBusinessExpressionVO = new BizExpStationsMap();
                BeanUtil.copyProperties(oldBusinessExpressionVO, newBusinessExpressionVO);
                newBusinessExpressionVO.setStationId(expressionSwatch.getStationId());
                newBusinessExpressionVO.setMonitorUnitId(expressionSwatch.getMonitorUnitId());
                newBusinessExpressionVO.setExpression(oldBusinessExpressionVO.getExpression());
                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    newBusinessExpressionVO.setExpression(newBusinessExpressionVO.getExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                newBusinessExpressionVO.setSuppressExpression(oldBusinessExpressionVO.getSuppressExpression());
                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    newBusinessExpressionVO.setSuppressExpression(newBusinessExpressionVO.getSuppressExpression().replace(logActionEquipment.getOldEquipmentId().toString(), logActionEquipment.getNewEquipmentId().toString()));
                }
                bizExpStationsMapMapper.insert(newBusinessExpressionVO);
            }
            List<BizExpEquSignalsMap> oldBizExpEquSignalsMaps = bizExpEquSignalsMapMapper.selectList(Wrappers.lambdaQuery(BizExpEquSignalsMap.class).eq(BizExpEquSignalsMap::getStationId, expressionSwatch.getOldStationId()));
            for (BizExpEquSignalsMap oldBizExpEquSignalsMap : oldBizExpEquSignalsMaps) {
                BizExpEquSignalsMap newBizExpEquSignalsMap = new BizExpEquSignalsMap();
                BeanUtil.copyProperties(oldBizExpEquSignalsMap, newBizExpEquSignalsMap);
                newBizExpEquSignalsMap.setStationId(expressionSwatch.getStationId());
                newBizExpEquSignalsMap.setMonitorUnitId(expressionSwatch.getMonitorUnitId());

                for (LogActionEquipment logActionEquipment : expressionSwatch.getSwatchList()) {
                    if (Objects.equals(logActionEquipment.getOldEquipmentId(), oldBizExpEquSignalsMap.getEquipmentId())) {
                        newBizExpEquSignalsMap.setEquipmentId(logActionEquipment.getNewEquipmentId());
                    }
                }
                bizExpEquSignalsMapMapper.insert(newBizExpEquSignalsMap);
            }
        }
        return true;
    }

    @Override
    public void deleteByStationId(Integer stationId) {
        tblStationSwatchMapMapper.delete(new LambdaQueryWrapper<TblStationSwatchMap>().eq(TblStationSwatchMap::getStationId, stationId));
    }

    public static List<Integer> getKeysFromMap(Map<Integer, Integer> map, int value) {
        List<Integer> keys = new ArrayList<>();

        for (Integer key : map.keySet()) {
            if (map.get(key) == value) {
                keys.add(key);
            }
        }

        return keys;
    }
}
