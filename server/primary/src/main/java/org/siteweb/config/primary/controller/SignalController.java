package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.constants.SignalConstant;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.entity.TslAcrossMonitorUnitSignal;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.service.OperationDetailService;
import org.siteweb.config.primary.service.SignalService;
import org.siteweb.config.primary.service.TslAcrossMonitorUnitSignalService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/signal")
public class SignalController {

    @Autowired
    SignalService signalService;
    @Autowired
    I18n i18n;

    @Autowired
    TslAcrossMonitorUnitSignalService tslAcrossMonitorUnitSignalService;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    EquipmentService equipmentService;

    @PostMapping(value = "/create", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createSignal(@RequestBody SignalConfigItem signalConfigItem) {
        signalService.createSignal(signalConfigItem);
        operationDetailService.recordOperationLog(
                TokenUserUtil.getLoginUserId(), signalConfigItem.getEquipmentTemplateId() + "." + signalConfigItem.getSignalId(), OperationObjectTypeEnum.SIGNAL, i18n.T("monitor.signal.name"), i18n.T("add"), "", signalConfigItem.getSignalName());
        return ResponseHelper.successful(signalConfigItem);
    }

    @GetMapping(params = "equipmentTemplateId")
    public ResponseEntity<ResponseResult> findSignalByEquipmentTemplateId(Integer equipmentTemplateId){
        return ResponseHelper.successful(signalService.findItemByEquipmentTemplateId(equipmentTemplateId));
    }

    @GetMapping("/point")
    public ResponseEntity<ResponseResult> findSignalPoints(Integer equipmentTemplateId){
        return ResponseHelper.successful(signalService.findSignalPoints(equipmentTemplateId));
    }

    @GetMapping(params = {"equipmentTemplateId", "equipmentId"})
    public ResponseEntity<ResponseResult> findSignalByEquipmentTemplateIdAndEquipmentId(Integer equipmentTemplateId, Integer equipmentId){
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.doesNotExist"));
        }
        return ResponseHelper.successful(signalService.findItemByEquipmentTemplateIdAndEquipmentId(equipmentTemplateId, equipmentId));
    }

    @GetMapping(params = "equipmentId")
    public ResponseEntity<ResponseResult> findSignalByEquipmentId(Integer equipmentId){
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.doesNotExist"));
        }
        return ResponseHelper.successful(signalService.findItemByEquipmentTemplateId(equipment.getEquipmentTemplateId()));
    }

    @GetMapping(value = "/simplifysignals")
    public ResponseEntity<ResponseResult> findSimplifySignal(Integer equipmentTemplateId){
        return ResponseHelper.successful(signalService.findSimplifySignal(equipmentTemplateId));
    }

    @PutMapping(value = "/update", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> updateSignal(@RequestBody @Validated SignalConfigItem signalConfigItem) {
        return ResponseHelper.successful(signalService.updateSignal(signalConfigItem));
    }

    @DeleteMapping(value = "/delete", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> deleteSignal(@RequestParam(name = "eqTemplateId") int equipmentTemplateId, @RequestParam(name = "signalId") int signalId) {
        //设备通讯状态信号(ID为-3)不允许删除
        if (Objects.equals(signalId, SignalConstant.COMMUNICATION_STATE_SIGNAL)) {
            return ResponseHelper.failed(i18n.T("monitor.communicationStateSignal.doNotAllowDelete"));
        }
        return ResponseHelper.successful(signalService.deleteSignal(equipmentTemplateId, signalId));
    }

    @DeleteMapping(value = "/batchdelete")
    public ResponseEntity<ResponseResult> batchDeleteSignal(@RequestParam(name = "eqTemplateId") int equipmentTemplateId, @RequestParam(name = "signalIds") List<Integer> signalIds) {
        signalService.batchDeleteSingal(equipmentTemplateId, signalIds);
        return ResponseHelper.successful(true);
    }

    /**
     * 获取信号下的基类状态含义
     */
    @GetMapping(value = "/basetypemeanings")
    public ResponseEntity<ResponseResult> findEquipmentBaseTypeMeaningsBySignal(Integer equipmentTemplateId, Integer signalId) {
        if (Objects.isNull(equipmentTemplateId) || Objects.isNull(signalId)) {
            return ResponseHelper.failed("equipmentTemplateId or signalId is not null");
        }
        return ResponseHelper.successful(signalService.findEquipmentBaseTypeMeaningsBySignal(equipmentTemplateId, signalId));
    }

    @GetMapping(value = "/baseclass")
    public ResponseEntity<ResponseResult> findSignalBaseClassList(Integer equipmentBaseType){
        return ResponseHelper.successful(signalService.findSignalBaseClassList(equipmentBaseType));
    }

    @GetMapping(value = "/baseclass/details")
    public ResponseEntity<ResponseResult> findSignalBaseClassDetails(Integer equipmentBaseType, String signalName, String meanings) {
        return ResponseHelper.successful(signalService.findSignalBaseClassDetails(equipmentBaseType, signalName, meanings));
    }

    @GetMapping(value = "/signalinfo/{equipmentTemplateId}/{signalId}")
    public ResponseEntity<ResponseResult> getSignalInfo(@PathVariable(value = "equipmentTemplateId",required = true)Integer equipmentTemplateId,@PathVariable(value = "signalId",required = true)Integer signalId){
        return ResponseHelper.successful(signalService.getSignalInfo(equipmentTemplateId,signalId));
    }

    @PutMapping(value = "/basetype")
    public ResponseEntity<ResponseResult> updateSignalBaseTypeAndCondId(@RequestBody BaseTypeCondIdDTO<SignalConditionDTO> baseTypeCondIdDTO) {
        signalService.updateSignalBaseTypeAndCondId(baseTypeCondIdDTO);
        return ResponseHelper.successful();
    }

    @PostMapping(value = "/basetype/clear")
    public ResponseEntity<ResponseResult> clearSignalBaseTypeAndCondId(@RequestBody List<SignalBaseTypeConditionDTO> clearSignalBaseTypeDTOList) {
        signalService.clearSignalBaseTypeAndCondId(clearSignalBaseTypeDTOList);
        return ResponseHelper.successful();
    }

    @PutMapping(value = "/similarsignal")
    public ResponseEntity<ResponseResult> disposeSimilarSignal(@RequestBody SimilarDataDTO similarSignalDTO) {
        signalService.disposeSimilarSignal(similarSignalDTO);
        return ResponseHelper.successful();
    }
    @PostMapping(value = "/createacrossmonitorunitsignal", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createAcrossMonitorUnitSignal(@RequestBody TslAcrossMonitorUnitSignal tslAcrossMonitorUnitSignal) {
        tslAcrossMonitorUnitSignalService.createAcrossMonitorUnitSignal(tslAcrossMonitorUnitSignal);
        return ResponseHelper.successful();
    }

    @GetMapping(value = "/crossmonitorunitsignal/condition")
    public ResponseEntity<ResponseResult> findCrossMonitorUnitSignal(Integer equipmentId, Integer signalId) {
        if (Objects.isNull(equipmentId) || Objects.isNull(signalId)) {
            return ResponseHelper.failed("equipmentId or signalId is not null");
        }
        return ResponseHelper.successful(tslAcrossMonitorUnitSignalService.findByEquipmentIdAndSignalId(equipmentId, signalId));
    }

    @GetMapping(value = "/cfgsignalevent", produces = MediaType.APPLICATION_JSON_VALUE, params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getSignalEvent(@RequestParam("equipmentId") Integer equipmentId){
        return ResponseHelper.successful(signalService.findSignalEvent(equipmentId));
    }

    @PutMapping(value = "/field/copy")
    public ResponseEntity<ResponseResult> fieldCopy(@RequestBody List<SignalFieldCopyDTO> signalFieldCopyDTOList){
        return ResponseHelper.successful(signalService.fieldCopy(signalFieldCopyDTOList));
    }
}
