package org.siteweb.config.primary.service;

import org.siteweb.config.primary.enums.TableIdentityEnum;

public interface PrimaryKeyValueService {

    /**
     * 对应存储过程：PBL_GenerateId
     */
    int getGlobalIdentity(TableIdentityEnum tableIdentityEnum, int postalCode);

    /**
     * 初始化TBL_PrimaryKeyValue，生成TBL_Siganl和TBL_Event的ID初始值
     * 对应存储过程：PCT_InitPrimaryKeyValue
     * @param centerId
     * @param defaultValue
     */
    void initPrimaryIdentityValue(int centerId, int defaultValue);

}
