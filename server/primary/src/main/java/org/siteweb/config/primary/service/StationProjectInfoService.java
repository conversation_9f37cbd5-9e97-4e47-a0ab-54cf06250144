package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblStationProjectInfo;

public interface StationProjectInfoService {

    /**
     * 查询或创建局站工程信息
     * @param stationId
     */
    void findOrCreateStationProjectInfo(int stationId, String projectName, String ContractNo);

    void updateProjectNameAndContractNo(int stationId, String projectName, String ContractNo);

    void deleteStationProjectInfo(int stationId);

    TblStationProjectInfo findByStationId(int stationId);

}
