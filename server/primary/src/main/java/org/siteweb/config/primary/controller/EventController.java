package org.siteweb.config.primary.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EventRequestBySignalId;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.primary.service.EventService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/event")
public class EventController {
    @Autowired
    EventService eventService;

    @Autowired
    private I18n i18n;

    @Autowired
    private EquipmentService equipmentService;

    @PostMapping(value = "/create", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> createEvent(@RequestBody EventConfigItem eventConfigItem) {
        eventService.createEventByEventItem(eventConfigItem);
        return ResponseHelper.successful(eventConfigItem);
    }

    @PutMapping(value = "/update", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> updateEvent(@RequestBody @Validated EventConfigItem eventConfigItem) {
        eventService.updateByEventItem(eventConfigItem);
        return ResponseHelper.successful(eventConfigItem);
    }

    @PutMapping(value = "/update/batch", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> batchUpdateEvent(@RequestBody @Validated BatchEventConfigItem batchEventConfigItem) {
        eventService.batchUpdateByEventItem(batchEventConfigItem);
        return ResponseHelper.successful(batchEventConfigItem);
    }

    @DeleteMapping(value = "/delete", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> deleteEvent(@RequestParam(name = "eqTemplateId") int equipmentTemplateId, @RequestParam(name = "eventId") int eventId) {
        return ResponseHelper.successful(eventService.deleteEvent(equipmentTemplateId, eventId));
    }

    @DeleteMapping(value = "/batchdelete", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<ResponseResult> batchDeleteEvent(@RequestParam(name = "eqTemplateId") int equipmentTemplateId, @RequestParam(name = "eventIds") List<Integer> eventIds){
        if(ObjectUtil.isEmpty(equipmentTemplateId) || CollectionUtil.isEmpty(eventIds)){
            return ResponseHelper.failed("input parameter error, please check parameter");
        }
        Boolean result = eventService.batchDeleteEvent(equipmentTemplateId,eventIds);
        ResponseEntity<ResponseResult> response = result?ResponseHelper.successful(true):ResponseHelper.failed("batch delete event error");
        return response;
    }
    
    @GetMapping(params = "equipmentTemplateId")
    public ResponseEntity<ResponseResult> findByEquipmentTemplateId(Integer equipmentTemplateId){
        return ResponseHelper.successful(eventService.findEventItemByEquipmentTemplateId(equipmentTemplateId));
    }

    @GetMapping("/point")
    public ResponseEntity<ResponseResult> findEventPoints(Integer equipmentTemplateId){
        return ResponseHelper.successful(eventService.findEventPoints(equipmentTemplateId));
    }

    @GetMapping(params = "equipmentId")
    public ResponseEntity<ResponseResult> findByEquipmentId(Integer equipmentId){
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.doesNotExist"));
        }
        return ResponseHelper.successful(eventService.findEventItemByEquipmentTemplateId(equipment.getEquipmentTemplateId()));
    }

    @GetMapping("/simplifyeventcondition")
    public ResponseEntity<ResponseResult> findSimplifyEventAndCondition(Integer equipmentTemplateId){
        return ResponseHelper.successful(eventService.findSimplifyEventAndCondition(equipmentTemplateId));
    }

    @GetMapping(value = "/baseclass")
    public ResponseEntity<ResponseResult> findEventBaseClassList(Integer equipmentBaseType){
        return ResponseHelper.successful(eventService.findEventBaseClassList(equipmentBaseType));
    }

    @GetMapping(value = "/baseclass/details")
    public ResponseEntity<ResponseResult> findEventBaseClassDetails(Integer equipmentBaseType, String eventName, String meanings) {
        return ResponseHelper.successful(eventService.findEventBaseClassDetails(equipmentBaseType, eventName, meanings));
    }

    @GetMapping(value = "/eventinfo/{equipmentTemplateId}/{eventId}")
    public ResponseEntity<ResponseResult> getEventInfo(@PathVariable(value = "equipmentTemplateId",required = true)Integer equipmentTemplateId,@PathVariable("eventId")Integer eventId){
        return ResponseHelper.successful(eventService.getEventInfo(equipmentTemplateId,eventId));
    }

    @PutMapping(value = "/basetype")
    public ResponseEntity<ResponseResult> updateEventBaseTypeAndConfirm(@RequestBody BaseTypeCondIdDTO<EventBaseCondDTO> eventBaseTypeCondDTO) {
        eventService.updateEventBaseTypeAndConfirm(eventBaseTypeCondDTO);
        return ResponseHelper.successful();
    }

    @PostMapping(value = "/basetype/clear")
    public ResponseEntity<ResponseResult> clearSignalBaseTypeAndCondId(@RequestBody List<EventBaseTypeConditionDTO> eventBaseTypeConditionDTOS) {
        eventService.clearEventBaseType(eventBaseTypeConditionDTOS);
        return ResponseHelper.successful();
    }

    @PutMapping(value = "/similarevent")
    public ResponseEntity<ResponseResult> disposeSimilarEvent(@RequestBody SimilarDataDTO similarEventDTO) {
        eventService.disposeSimilarEvent(similarEventDTO);
        return ResponseHelper.successful();
    }

    @PostMapping("/cfgeventbysignalids")
    public ResponseEntity<ResponseResult> getEvent(@RequestBody EventRequestBySignalId eventRequestBySignalId){
        return ResponseHelper.successful(eventService.findEventsByEquipmentIdAndSignalIds(eventRequestBySignalId));
    }

    @PostMapping(value = "/linkevent")
    public ResponseEntity<ResponseResult> linkEvent(@RequestBody EquipmentTemplateSignalIdDTO equipmentTemplateSignalIDDTO) {
        return ResponseHelper.successful(eventService.linkEvent(equipmentTemplateSignalIDDTO.getEquipmentTemplateId(), equipmentTemplateSignalIDDTO.getSignalId()));
    }

    @PutMapping(value = "/field/copy")
    public ResponseEntity<ResponseResult> fieldCopy(@RequestBody List<EventFieldCopyDTO> eventFieldCopyDTOList){
        return ResponseHelper.successful(eventService.fieldCopy(eventFieldCopyDTOList));
    }
}
