package org.siteweb.config.primary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblStationProjectInfo;
import org.siteweb.config.common.mapper.TblStationProjectInfoMapper;
import org.siteweb.config.primary.service.StationProjectInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/5/11
 */
@Service
public class StationProjectInfoServiceImpl implements StationProjectInfoService {

    @Autowired
    private TblStationProjectInfoMapper stationProjectInfoMapper;

    @Override
    public void findOrCreateStationProjectInfo(int stationId, String projectName, String ContractNo) {
        TblStationProjectInfo stationProjectInfo = stationProjectInfoMapper.selectById(stationId);
        if (stationProjectInfo == null) {
            stationProjectInfo = new TblStationProjectInfo();
            stationProjectInfo.setProjectName(projectName);
            stationProjectInfo.setContractNo(ContractNo);
            stationProjectInfo.setInstallTime(DateUtil.date());
            stationProjectInfo.setStationId(stationId);
            stationProjectInfoMapper.insert(stationProjectInfo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectNameAndContractNo(int stationId, String projectName, String ContractNo) {
        stationProjectInfoMapper.update(Wrappers.lambdaUpdate(TblStationProjectInfo.class)
                .eq(TblStationProjectInfo::getStationId, stationId)
                .set(TblStationProjectInfo::getProjectName, projectName)
                .set(TblStationProjectInfo::getContractNo, ContractNo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStationProjectInfo(int stationId) {
        stationProjectInfoMapper.delete(Wrappers.lambdaQuery(TblStationProjectInfo.class)
                .eq(TblStationProjectInfo::getStationId, stationId));
    }

    @Override
    public TblStationProjectInfo findByStationId(int stationId) {
        return stationProjectInfoMapper.selectById(stationId);
    }
}
