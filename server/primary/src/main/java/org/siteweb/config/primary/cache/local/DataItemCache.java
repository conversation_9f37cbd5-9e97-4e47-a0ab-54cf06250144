

package org.siteweb.config.primary.cache.local;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblDataItem;
import org.siteweb.config.common.mapper.TblDataItemMapper;
import org.siteweb.config.primary.cache.CacheComponent;
import org.siteweb.config.primary.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR> (2024-04-17)
 **/
@CacheComponent
public class DataItemCache implements CacheManager<Integer, List<TblDataItem>> {

    private final Map<Integer,List<TblDataItem>> dataEntryCache = new ConcurrentHashMap<>();

    @Autowired
    private TblDataItemMapper dataItemMapper;


    @Override
    public void reload() {
        dataEntryCache.clear();
        List<TblDataItem> list = dataItemMapper.selectList(Wrappers.emptyWrapper());
        for (TblDataItem item : list) {
            dataEntryCache.computeIfAbsent(item.getEntryId(), k -> new ArrayList<>())
                          .add(item);
        }
    }

    @Override
    public List<TblDataItem> get(Integer key) {
        return dataEntryCache.get(key);
    }

    @Override
    public void put(Integer key, List<TblDataItem> value) {
        dataEntryCache.put(key, value);
    }

    @Override
    public void remove(Integer key) {
        dataEntryCache.remove(key);
    }

    @Override
    public Collection<List<TblDataItem>> values() {
        return dataEntryCache.values();
    }

    public void removeItem(Integer entryId, Integer itemId) {
        List<TblDataItem> tblDataItemList = dataEntryCache.get(entryId);
        if (CollUtil.isEmpty(tblDataItemList)) {
            return;
        }
        tblDataItemList.removeIf(e -> Objects.equals(e.getItemId(), itemId));
        //集合中没有元素了，直接remove该key
        if (CollUtil.isEmpty(tblDataItemList)) {
            remove(entryId);
        }
    }

    public void putItem(TblDataItem dataItem) {
        dataEntryCache.computeIfAbsent(dataItem.getEntryId(), k -> new ArrayList<>())
                      .add(dataItem);
    }
    public void updateItem(TblDataItem dataItem) {
        remove(dataItem.getEntryId());
        List<TblDataItem> tblDataItems = dataItemMapper.selectList(Wrappers.lambdaQuery(TblDataItem.class)
                                                                           .eq(TblDataItem::getEntryId, dataItem.getEntryId()));
        put(dataItem.getEntryId(), tblDataItems);
    }
}
