package org.siteweb.config.primary.expressions.calculate;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;
import org.siteweb.config.primary.expressions.operator.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

/**
 * 计算类 负责计算表达式中的值
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class ExpressionCalculate {

    public static double calculate(String expression) {
        // 预处理后缀表达式
        List<IOperatorOrOperand> postfix = convertInfixToPostfix(expression);
        // 操作数栈
        Stack<Double> data = new Stack<>();

        // 遍历
        for (IOperatorOrOperand item : postfix) {
            if (item.isOperator()) {
                // 运算符
                OperatorBase opr = (OperatorBase) item;

                // 从操作数栈中取出操作数
                if (data.size() < opr.operandCount()) {
                    throw new RuntimeException("操作数不足的错误信息"); // LocalizationService.GetString("CarrierKey0081")的替代
                }
                double[] operands = new double[opr.operandCount()];
                for (int i = opr.operandCount() - 1; i >= 0; i--) {
                    operands[i] = data.pop();
                }

                // 计算并将结果压回栈中
                data.push(opr.onCalculate(operands));
            } else {
                // 操作数
                // 压入操作数栈
                data.push(((OperandInfo)item).getValue());
            }
        }

        // 取最后结果
        if (data.size() != 1) {
            throw new RuntimeException("栈中结果数量不为1的错误信息");
        }
        return data.pop();
    }

    /**
     * 将表达式转换为后缀表达式
     *
     * @param expression 表达式
     * @return {@link List}<{@link IOperatorOrOperand}>
     */
    private static List<IOperatorOrOperand> convertInfixToPostfix(String expression) {
        // 预处理中缀表达式
        List<IOperatorOrOperand> infix = splitExpression(expression);
        // 运算符栈
        Stack<OperatorBase> opr = new Stack<>();
        // 后缀表达式输出
        List<IOperatorOrOperand> output = new ArrayList<>();

        // 遍历
        for (IOperatorOrOperand item : infix) {
            if (item.isOperator()) {
                // 是运算符
                if (item instanceof OperatorCloseBracket) {
                    // 闭括号

                    // 弹出运算符，直至遇到左括号为止
                    while (!(opr.peek() instanceof OperatorOpenBracket)) {
                        output.add(opr.pop());
                        if (opr.isEmpty()) {
                            // 括号不配对
                            throw new RuntimeException("括号不配对的错误信息"); // LocalizationService.GetString("CarrierKey0080")的替代

                        }
                    }

                    // 弹出左括号
                    opr.pop();
                } else {
                    // 其它运算符
                    OperatorBase thisopr = (OperatorBase) item;

                    // 弹出优先级高或相等的运算符
                    int thisPriority = thisopr.priority();
                    while (!opr.isEmpty()) {
                        OperatorBase topopr = opr.peek();
                        if (!(topopr instanceof OperatorOpenBracket)) {
                            // 如果栈顶运算符不为左括号
                            if (topopr.priority() > thisopr.priority()) {
                                // 如果栈顶中的运算符优先级高于当前运算符，则输出并弹出栈
                                output.add(opr.pop());
                            } else if (topopr.priority() == thisopr.priority()) {
                                // 如果栈顶中的运算符优先级与当前运算符相等
                                if (topopr.direction() == OperatingDirectionEnum.LEFT_TO_RIGHT) {
                                    // 如果栈顶运算符结合性方向为从左至右，则输出并弹出栈
                                    output.add(opr.pop());
                                } else {
                                    // 如果是从右至左，终止弹栈
                                    break;
                                }
                            } else {
                                // 终止弹栈
                                break;
                            }
                        } else {
                            // 终止弹栈
                            break;
                        }
                    }

                    // 将当前运算符压入栈中
                    opr.push(thisopr);
                }
            } else {
                // 是操作数
                // 直接输出
                output.add(item);
            }
        }

        // 遍历结束，输出栈中全部剩余
        while (!opr.isEmpty()) {
            output.add(opr.pop());
        }

        return output;
    }

    /**
     * 将表达式中的操作数和运算符分割出来
     *
     * @param expression 操作数与运算符表
     * @return {@link List}<{@link IOperatorOrOperand}>
     */
    public static List<IOperatorOrOperand> splitExpression(String expression) {
        List<IOperatorOrOperand> output = new ArrayList<>();
        StringBuilder operandBuf = new StringBuilder();
        StringBuilder operatorBuf = new StringBuilder();

        // 记录刚才最后输出的表达式项
        IOperatorOrOperand lastItem = null;

        // 在尾部添加一个空格，帮助分离最后一个操作数或运算符
        expression += " ";

        Double result;
        for (int i = 0; i < expression.length(); i++) {
            if (Character.isDigit(expression.charAt(i)) || expression.charAt(i) == '.') {
                // 如果是数字或小数点（操作数成份）

                // 结束前一个运算符
                if (!operatorBuf.isEmpty()) {
                    // 尝试获取运算符
                    OperatorBase opr = tryGetOperator(operatorBuf.toString(), lastItem);
                    if (opr != null) {
                        output.add(opr);
                        lastItem = opr;
                        operatorBuf.setLength(0);
                    } else {
                        //throw new InvalidCastException(operatorBuf.toString() + "错误信息"); // LocalizationService.GetString("CarrierKey0078")的替代
                        throw new RuntimeException("操作异常");
                    }
                }

                // 合并入当前操作数项
                operandBuf.append(expression.charAt(i));
            } else {
                // 不是数字或小数点（运算符成份）

                // 结束前一个操作数
                if (!operandBuf.isEmpty()) {
                    result = Double.valueOf(operandBuf.toString());
                    //if (!tryParseDouble(, result)) {
                    //    throw new RuntimeException(operandBuf.toString() + "错误信息"); // LocalizationService.GetString("CarrierKey0079")的替代
                    //}

                    // 输出操作数
                    OperandInfo operand = new OperandInfo(Double.parseDouble(operandBuf.toString()));
                    output.add(operand);
                    lastItem = operand;
                    operandBuf.setLength(0);
                }

                // 合并非空白字符到当前运算符项
                if (!Character.isWhitespace(expression.charAt(i))) {
                    operatorBuf.append(expression.charAt(i));
                }

                // 分析并输出运算符
                if (!operatorBuf.isEmpty()) {
                    // 尝试获取运算符
                    OperatorBase opr = tryGetOperator(operatorBuf.toString(), lastItem);
                    if (opr != null) {
                        output.add(opr);
                        lastItem = opr;
                        operatorBuf.setLength(0);
                    }
                }
            }
        }

        return output;
    }

    private static OperatorBase tryGetOperator(String expression, IOperatorOrOperand leftItem) {
        // 判断左侧是否是操作数
        boolean hasLeftOperand = false;
        if (leftItem == null) {
            // 没有左项
            hasLeftOperand = false;
        } else if (leftItem.isOperand()) {
            // 左项是操作数
            hasLeftOperand = true;
        } else if (leftItem instanceof OperatorCloseBracket) {
            // 左项是闭括号
            hasLeftOperand = true;
        } else {
            // 其它情况
            hasLeftOperand = false;
        }

        // 根据符号文本判断
        String symbol = expression.toUpperCase();
        switch (symbol) {
            case "(":
                return new OperatorOpenBracket();
            case ")":
                return new OperatorCloseBracket();
        }

        // 根据左操作数情况判断
        if (hasLeftOperand) {
            // 有左操作数者
            switch (expression.toUpperCase()) {
                case "+":
                    return new OperatorPlus();
                case "-":
                    return new OperatorMinus();
                case "*":
                    return new OperatorMultiply();
                case "/":
                    return new OperatorDivide();
                case "%":
                    return new OperatorMod();
                case "^":
                    return new OperatorPower();
                case "AND":
                    return new OperatorBitAnd();
                case "OR":
                    return new OperatorBitOr();
                case "XOR":
                    return new OperatorBitXor();
                case "<<":
                    return new OperatorBitShiftLeft();
                case ">>":
                    return new OperatorBitShiftRight();
            }
        } else {
            // 没有左操作数
            switch (expression.toUpperCase()) {
                case "+":
                    return new OperatorPositive();
                case "-":
                    return new OperatorNegative();
                case "~":
                    return new OperatorBitReverse();
            }
        }
        // 不可判断者，返回空
        return null;
    }
}
