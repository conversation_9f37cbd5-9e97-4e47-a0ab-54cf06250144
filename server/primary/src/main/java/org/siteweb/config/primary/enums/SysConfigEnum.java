package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SysConfigEnum {
    STANDAR_CATEGORY("StandardCategory", "标准类型"),
    STANDARD_VER("StandardVer", "标准化类型"),
    DATA_SERVICE_CENTER_PORT("DSCPort", "DS中心端口"),
    REAL_DATA_SERVICE_CENTER_PORT("RDSPort", "RDS中心端口"),
    MU_MIN_PORT("MinPortNO", "mu最小网络端口"),
    MU_MAX_PORT("MaxPortNO", "mu最大网络端口"),
    B_VERSION("BDicVersion","标准化字典版本信息");
    private final String configKey;
    private final String describe;
}
