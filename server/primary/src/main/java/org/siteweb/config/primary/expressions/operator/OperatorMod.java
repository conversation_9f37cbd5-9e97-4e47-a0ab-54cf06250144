package org.siteweb.config.primary.expressions.operator;

import org.siteweb.config.primary.expressions.enums.OperatingDirectionEnum;

/**
 * 表示取余运算符
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public class OperatorMod extends OperatorBase {
    @Override
    public String operatorSymbol() {
        return "%";
    }

    @Override
    public String operatorName() {
        return "取余";
    }

    @Override
    public int priority() {
        return 13;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.LEFT_TO_RIGHT;
    }

    @Override
    public int operandCount() {
        return 2;
    }

    @Override
    public double onCalculate(double[] operands) {
        return operands[0] % operands[1];
    }
}
