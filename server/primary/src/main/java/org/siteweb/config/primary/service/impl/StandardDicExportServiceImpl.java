package org.siteweb.config.primary.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.XmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblLogicClassEntry;
import org.siteweb.config.common.entity.TblStandardDicControl;
import org.siteweb.config.common.entity.TblStandardDicEvent;
import org.siteweb.config.common.entity.TblStandardDicSig;
import org.siteweb.config.primary.enums.CmccCustomerTypeEnum;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class StandardDicExportServiceImpl implements StandardDicExportService {
    @Autowired
    private StandardTypeService standardTypeService;
    @Autowired
    TblLogicClassEntryService logicClassEntryService;
    @Autowired
    StandardDicSigService standardDicSigService;
    @Autowired
    StandardDicEventService standardDicEventService;
    @Autowired
    StandardDicControlService standardDicControlService;
    @Autowired
    I18n i18n;


    @Override
    public List<String> exportScript() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<String> logicClassEntrySql = getLogicClassEntrySql(currentStandardType);
        List<String> exportSqlList = new ArrayList<>(logicClassEntrySql);
        List<String> standardDicSigSql = getStandardDicSigSql(currentStandardType);
        exportSqlList.addAll(standardDicSigSql);
        List<String> standardDicEventSql = getStandardDicEventSql(currentStandardType);
        exportSqlList.addAll(standardDicEventSql);
        List<String> standardDicControlSql = getStandardDicControlSql(currentStandardType);
        exportSqlList.addAll(standardDicControlSql);
        return exportSqlList;
    }

    @Override
    public String exportXmlStr(Integer customerType) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Document document = XmlUtil.createXml();
        document.setXmlStandalone(false);
        Element sdandardDicElement = document.createElement("StandardDic");
        sdandardDicElement.setAttribute("StandardName",i18n.T("standardized.dictionary.table"));
        //获取信号节点
        Element signalsElement = getSignalsElement(document, currentStandardType.getValue());
        sdandardDicElement.appendChild(signalsElement);
        //获取告警节点
        Element eventsElement = getEventsElement(document, currentStandardType.getValue(),customerType);
        sdandardDicElement.appendChild(eventsElement);
        //获取控制节点
        Element controlsElement = getControlsElement(document, currentStandardType.getValue());
        sdandardDicElement.appendChild(controlsElement);
        return XmlUtil.toStr(sdandardDicElement,true);
    }

    private Element getControlsElement(Document document, Integer standardType) {
        Element events = document.createElement("Controls");
        events.setAttribute("Name", i18n.T("standardized.control"));
        List<TblStandardDicControl> standardDicControlList = standardDicControlService.findByStandardType(standardType);
        for (TblStandardDicControl standardDicControl : standardDicControlList) {
            Element controlElement = document.createElement("Control");
            controlElement.setAttribute("Name",standardDicControl.getControlStandardName());
            //中国联通的单位是空 其他是使用扩展字段1
            if (Objects.equals(standardType, StandardCategoryEnum.UNICOM.getValue())) {
                controlElement.setAttribute("Unit", "");
            } else {
                controlElement.setAttribute("Unit", standardDicControl.getExtendFiled1());
            }
            controlElement.setAttribute("Code",standardDicControl.getNetManageId());
            events.appendChild(controlElement);
        }
        return events;
    }

    private Element getEventsElement(Document document, Integer standardType, Integer customerType) {
        Element events = document.createElement("Events");
        events.setAttribute("Name",i18n.T("standardized.event"));
        List<TblStandardDicEvent> standardDicEventList = standardDicEventService.findByStandardType(standardType);
        for (TblStandardDicEvent standardDicEvent : standardDicEventList) {
            Element eventElement = document.createElement("Event");
            eventElement.setAttribute("Name",standardDicEvent.getEventStandardName());
            eventElement.setAttribute("Meaning",standardDicEvent.getMeanings());
            eventElement.setAttribute("Code",standardDicEvent.getNetManageId());
            eventElement.setAttribute("AlarmLevelOfTXJL", getUserGrade(standardDicEvent.getEventSeverity()));
            if (Objects.equals(customerType, CmccCustomerTypeEnum.CHINA_MOBILE.getId())) {
                //中国移动
                eventElement.setAttribute("AlarmLevelOfCSJD", getUserGrade(standardDicEvent.getExtendFiled1()));
                eventElement.setAttribute("AlarmLevelOfTXJZ", getUserGrade(standardDicEvent.getExtendFiled2()));
                eventElement.setAttribute("AlarmLevelOfIDC", getUserGrade(standardDicEvent.getExtendFiled3()));
            }else{
                //其他
                eventElement.setAttribute("AlarmLevelOfCSJD", "");
                eventElement.setAttribute("AlarmLevelOfTXJZ", "");
            }
            eventElement.setAttribute("Threshold", standardDicEvent.getCompareValue());
            eventElement.setAttribute("StartOper", "");
            eventElement.setAttribute("StartDelay", standardDicEvent.getStartDelay());
            eventElement.setAttribute("EndDelay", "");
            eventElement.setAttribute("Hyteresis", "");
            events.appendChild(eventElement);
        }
        return events;
    }

    /**
     * 获取告警等级
     * 这个告警等级是与4互补的
     * @param eventSeverity 告警等级
     * @return {@link String }
     */
    public String getUserGrade(Object eventSeverity){
        if (Objects.isNull(eventSeverity) || CharSequenceUtil.isBlank(eventSeverity.toString())) {
            return "";
        }
        Integer severity = Convert.convert(Integer.class, eventSeverity);
        return String.valueOf((4 - severity));
    }

    private Element getSignalsElement(Document document, Integer standardType) {
        Element signals = document.createElement("Signals");
        signals.setAttribute("Name",i18n.T("standardized.signal"));
        List<TblStandardDicSig> standarddicSigList = standardDicSigService.findByStandardType(standardType);
        for (TblStandardDicSig standardDicSig : standarddicSigList) {
            Element signalElement = document.createElement("Signal");
            signalElement.setAttribute("Name",standardDicSig.getSignalStandardName());
            signalElement.setAttribute("Unit","");
            signalElement.setAttribute("Code",standardDicSig.getNetManageId());
            signalElement.setAttribute("StoreInterval", Convert.convert(String.class, standardDicSig.getStoreInterval(),""));
            signalElement.setAttribute("Relativeval", Convert.convert(String.class, standardDicSig.getPercentThreshold(),""));
            signalElement.setAttribute("Absoluteval", Convert.convert(String.class, standardDicSig.getAbsValueThreshold(),""));
            signals.appendChild(signalElement);
        }
        return signals;
    }

    private List<String> getStandardDicControlSql(StandardCategoryEnum standardCategoryEnum) {
        List<String> standardDicControlSql = new ArrayList<>();
        standardDicControlSql.add(CharSequenceUtil.format("DELETE FROM TBL_StandardDicControl WHERE StandardType = {};", standardCategoryEnum.getValue()));
        List<TblStandardDicControl> standardDicControlList = standardDicControlService.findByStandardType(standardCategoryEnum.getValue());
        for (TblStandardDicControl tblStandardDicControl : standardDicControlList) {
            String sql = "INSERT INTO TBL_StandardDicControl (StandardDicId, StandardType, EquipmentLogicClassId, EquipmentLogicClass, ControlLogicClassId, ControlLogicClass, ControlStandardName, NetManageId, StationCategory, ModifyType, Description, ExtendFiled1, ExtendFiled2) VALUES (%d, %d, %d, '%s', %s, '%s', '%s', '%s', %s, %s, '%s', '%s', '%s');";
            sql = String.format(sql,
                    tblStandardDicControl.getStandardDicId(),
                    tblStandardDicControl.getStandardType(),
                    tblStandardDicControl.getEquipmentLogicClassId(),
                    tblStandardDicControl.getEquipmentLogicClass(),
                    tblStandardDicControl.getControlLogicClassId() != null ? tblStandardDicControl.getControlLogicClassId().toString() : "NULL",
                    tblStandardDicControl.getControlLogicClass() != null ? tblStandardDicControl.getControlLogicClass().trim() : "",
                    tblStandardDicControl.getControlStandardName() != null ? tblStandardDicControl.getControlStandardName().trim() : "",
                    tblStandardDicControl.getNetManageId() != null ? tblStandardDicControl.getNetManageId().trim() : "",
                    tblStandardDicControl.getStationCategory() != null ? tblStandardDicControl.getStationCategory().toString() : "NULL",
                    tblStandardDicControl.getModifyType() != null ? tblStandardDicControl.getModifyType().toString() : "NULL",
                    tblStandardDicControl.getDescription() != null ? tblStandardDicControl.getDescription().trim() : "",
                    tblStandardDicControl.getExtendFiled1() != null ? tblStandardDicControl.getExtendFiled1().trim() : "",
                    tblStandardDicControl.getExtendFiled2() != null ? tblStandardDicControl.getExtendFiled2().trim() : ""
            );
            standardDicControlSql.add(sql);
        }
        return standardDicControlSql;
    }

    private List<String> getStandardDicEventSql(StandardCategoryEnum standardCategoryEnum) {
        List<String> standardDicEventSql = new ArrayList<>();
        standardDicEventSql.add(CharSequenceUtil.format("DELETE FROM TBL_StandardDicEvent WHERE StandardType = {};", standardCategoryEnum.getValue()));
        List<TblStandardDicEvent> standardDicEventList =  standardDicEventService.findByStandardType(standardCategoryEnum.getValue());
        for (TblStandardDicEvent tblStandardDicEvent : standardDicEventList) {
            String sql = "INSERT INTO TBL_StandardDicEvent (StandardDicId, StandardType, EquipmentLogicClassId, EquipmentLogicClass, EventLogicClassId, EventLogicClass, EventClass, EventStandardName, NetManageId, EventSeverity, CompareValue, StartDelay, Meanings, EquipmentAffect, BusinessAffect, StationCategory, ModifyType, Description, ExtendFiled1, ExtendFiled2, ExtendFiled3) VALUES " +
                    "(%d, %d, %d, '%s', %s, '%s', '%s', '%s', '%s', %s, %s, %s, '%s', '%s', '%s', %s, %s, '%s', '%s', '%s', '%s');";
            sql = String.format(sql,
                    tblStandardDicEvent.getStandardDicId(),
                    tblStandardDicEvent.getStandardType(),
                    tblStandardDicEvent.getEquipmentLogicClassId(),
                    tblStandardDicEvent.getEquipmentLogicClass(),
                    tblStandardDicEvent.getEventLogicClassId() != null ? tblStandardDicEvent.getEventLogicClassId().toString() : "NULL",
                    tblStandardDicEvent.getEventLogicClass() != null ? tblStandardDicEvent.getEventLogicClass().trim() : "",
                    tblStandardDicEvent.getEventClass() != null ? tblStandardDicEvent.getEventClass().trim() : "",
                    tblStandardDicEvent.getEventStandardName() != null ? tblStandardDicEvent.getEventStandardName().trim() : "",
                    tblStandardDicEvent.getNetManageId() != null ? tblStandardDicEvent.getNetManageId().trim() : "",
                    tblStandardDicEvent.getEventSeverity() != null ? tblStandardDicEvent.getEventSeverity().toString() : "NULL",
                    tblStandardDicEvent.getCompareValue() != null ? "'" + tblStandardDicEvent.getCompareValue().trim() + "'" : "NULL",
                    tblStandardDicEvent.getStartDelay() != null ? "'" + tblStandardDicEvent.getStartDelay().trim() + "'" : "NULL",
                    tblStandardDicEvent.getMeanings() != null ? tblStandardDicEvent.getMeanings().trim() : "",
                    tblStandardDicEvent.getEquipmentAffect() != null ? tblStandardDicEvent.getEquipmentAffect().trim() : "",
                    tblStandardDicEvent.getBusinessAffect() != null ? tblStandardDicEvent.getBusinessAffect().trim() : "",
                    tblStandardDicEvent.getStationCategory() != null ? tblStandardDicEvent.getStationCategory().toString() : "NULL",
                    tblStandardDicEvent.getModifyType() != null ? tblStandardDicEvent.getModifyType().toString() : "NULL",
                    tblStandardDicEvent.getDescription() != null ? tblStandardDicEvent.getDescription().trim() : "",
                    tblStandardDicEvent.getExtendFiled1() != null ? tblStandardDicEvent.getExtendFiled1().trim() : "",
                    tblStandardDicEvent.getExtendFiled2() != null ? tblStandardDicEvent.getExtendFiled2().trim() : "",
                    tblStandardDicEvent.getExtendFiled3() != null ? tblStandardDicEvent.getExtendFiled3().trim() : ""
            );
            standardDicEventSql.add(sql);
        }
        return standardDicEventSql;
    }

    private List<String> getStandardDicSigSql(StandardCategoryEnum standardCategoryEnum) {
        List<String> standardDicSigSql = new ArrayList<>();
        standardDicSigSql.add(CharSequenceUtil.format("DELETE FROM TBL_StandardDicSig WHERE StandardType = {};", standardCategoryEnum.getValue()));
        List<TblStandardDicSig> standardDicSigList = standardDicSigService.findByStandardType(standardCategoryEnum.getValue());
        for (TblStandardDicSig tblStandardDicSig : standardDicSigList) {
            String sql = "INSERT INTO TBL_StandardDicSig (StandardDicId, StandardType, EquipmentLogicClassId, EquipmentLogicClass, SignalLogicClassId, SignalLogicClass, SignalStandardName, NetManageId, StoreInterval, AbsValueThreshold, StatisticsPeriod, PercentThreshold, StationCategory, ModifyType, Description, ExtendFiled1, ExtendFiled2) VALUES (%d, %d, %d, '%s', %s, '%s', '%s', '%s', %s, %s, %s, %s, %s, %s, '%s', '%s', '%s');";

            sql = String.format(sql,
                    tblStandardDicSig.getStandardDicId(),
                    tblStandardDicSig.getStandardType(),
                    tblStandardDicSig.getEquipmentLogicClassId(),
                    tblStandardDicSig.getEquipmentLogicClass(),
                    tblStandardDicSig.getSignalLogicClassId() != null ? tblStandardDicSig.getSignalLogicClassId().toString() : "NULL",
                    tblStandardDicSig.getSignalLogicClass() != null ? tblStandardDicSig.getSignalLogicClass().trim() : "",
                    tblStandardDicSig.getSignalStandardName(),
                    tblStandardDicSig.getNetManageId() != null ? tblStandardDicSig.getNetManageId().trim() : "",
                    tblStandardDicSig.getStoreInterval() != null ? tblStandardDicSig.getStoreInterval().toString() : "NULL",
                    tblStandardDicSig.getAbsValueThreshold() != null ? tblStandardDicSig.getAbsValueThreshold().toString() : "NULL",
                    tblStandardDicSig.getStatisticsPeriod() != null ? tblStandardDicSig.getStatisticsPeriod().toString() : "NULL",
                    tblStandardDicSig.getPercentThreshold() != null ? tblStandardDicSig.getPercentThreshold().toString() : "NULL",
                    tblStandardDicSig.getStationCategory() != null ? tblStandardDicSig.getStationCategory().toString() : "NULL",
                    tblStandardDicSig.getModifyType() != null ? tblStandardDicSig.getModifyType().toString() : "NULL",
                    tblStandardDicSig.getDescription() != null ? tblStandardDicSig.getDescription().trim() : "",
                    tblStandardDicSig.getExtendFiled1() != null ? tblStandardDicSig.getExtendFiled1().trim() : "",
                    tblStandardDicSig.getExtendFiled2() != null ? tblStandardDicSig.getExtendFiled2().trim() : ""
            );
            standardDicSigSql.add(sql);
        }
        return standardDicSigSql;
    }

    private List<String> getLogicClassEntrySql(StandardCategoryEnum currentStandardType) {
        List<String> result = new ArrayList<>();
        List<TblLogicClassEntry> logicClassEntryList = logicClassEntryService.findByStandardType(currentStandardType.getValue());
        String deleteLogicClassEntrySql = CharSequenceUtil.format("DELETE FROM TBL_LogicClassEntry WHERE StandardType = {};", currentStandardType.getValue());
        result.add(deleteLogicClassEntrySql);
        for (TblLogicClassEntry tblLogicClassEntry : logicClassEntryList) {
            String insertLogicClassEntrySql = CharSequenceUtil.format(
                    "INSERT INTO TBL_LogicClassEntry (EntryId, EntryCategory, LogicClassId, LogicClass, StandardType, Description) VALUES ({}, {}, {}, '{}', {}, {});",
                    tblLogicClassEntry.getEntryId(), tblLogicClassEntry.getEntryCategory(),
                    tblLogicClassEntry.getLogicClassId(), tblLogicClassEntry.getLogicClass(), tblLogicClassEntry.getStandardType(),
                    tblLogicClassEntry.getDescription() == null ? "NULL" : "'" + tblLogicClassEntry.getDescription().trim() + "'");
            result.add(insertLogicClassEntrySql);
        }
        return result;
    }
}
