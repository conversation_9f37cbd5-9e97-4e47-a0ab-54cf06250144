package org.siteweb.config.primary.expressions.service;

import org.siteweb.config.common.dto.ComplexIndexDTO;
import org.siteweb.config.common.dto.EventExpressionDTO;
import org.siteweb.config.common.dto.ExpressionDTO;
import org.siteweb.config.common.dto.SignalExpressionDTO;
import org.siteweb.config.primary.expressions.validate.ExpressionValidationResult;

import java.util.List;

/**
 * 表达式解析服务
 *
 * <AUTHOR>
 * @date 2024/03/22
 */
public interface ExpressionService {
    boolean validateExpression(String expression);

    List<SignalExpressionDTO> findSignalExpression(Integer equipmentTemplateId);

    List<EventExpressionDTO> findEventExpression(Integer equipmentTemplateId);
    /**
     * 解析表达式
     * @param expressionDTO 表达式
     * @return {@link String}
     */
    String expressionAnalysis(ExpressionDTO expressionDTO);
    ExpressionValidationResult validateComplexIndexExpression(String expression);

    String analysisSignal(Integer parsedEquipmentTemplateId, String expression, Boolean isCrossStationMonitorUnit);
}
