package org.siteweb.config.primary.service;

import java.util.List;
import java.util.function.IntFunction;

public interface BaseDicService {
    /**
     * 更新模板中的基类信息
     * PCT_GenerateBaseDic存储过程实现
     * @param equipmentTemplateId 设备模板
     */
    default void updateBaseClassStandardDictionary(Integer equipmentTemplateId){}

    default boolean existsByBaseTypeId(Long baseTypeId){
        return false;
    }

    default void generateBaseDic(Long baseTypeId, Long sourceId){
    }

    default void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, BaseDicService baseDicService, Integer equipmentTemplateId) {

    }
}
