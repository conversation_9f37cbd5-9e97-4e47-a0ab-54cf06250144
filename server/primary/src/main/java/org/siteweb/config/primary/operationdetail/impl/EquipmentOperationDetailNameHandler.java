package org.siteweb.config.primary.operationdetail.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.primary.operationdetail.AbstractNameHandler;
import org.siteweb.config.primary.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EquipmentOperationDetailNameHandler extends AbstractNameHandler {

    @Autowired
    EquipmentService equipmentService;

    @Override
    public OperationObjectTypeEnum getOperationObjectType() {
        return OperationObjectTypeEnum.EQUIPMENT;
    }

    @Override
    protected List<IdValueDTO<String, String>> findNamesByIds(List<String> ids) {
        List<Integer> equipmentIds = ids.stream().map(Integer::valueOf).toList();
        return equipmentService.findNamesByIds(equipmentIds);
    }
}
