package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.entity.TblDeviceSubTypeCMCC;
import org.siteweb.config.common.mapper.TblDeviceSubTypeCMCCMapper;
import org.siteweb.config.primary.service.DeviceSubTypeCMCCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceSubTypeCMCCServiceImpl implements DeviceSubTypeCMCCService {
    @Autowired
    TblDeviceSubTypeCMCCMapper tblDeviceSubTypeCMCCMapper;

    @Override
    public List<IdValueDTO<Integer,String>> findIdValue(Integer deviceTypeId){
        List<TblDeviceSubTypeCMCC> deviceSubTypeCMCCList = tblDeviceSubTypeCMCCMapper.selectList(Wrappers.lambdaQuery(TblDeviceSubTypeCMCC.class)
                                                                                                         .eq(TblDeviceSubTypeCMCC::getDeviceTypeId, deviceTypeId));
        return deviceSubTypeCMCCList.stream()
                                    .map(subType-> new IdValueDTO<>(subType.getDeviceSubTypeId(), subType.getDeviceSubTypeName()))
                                    .toList();
    }
}
