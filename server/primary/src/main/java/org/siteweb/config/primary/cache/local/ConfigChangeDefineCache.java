
package org.siteweb.config.primary.cache.local;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblConfigChangeDefine;
import org.siteweb.config.common.mapper.TblConfigChangeDefineMapper;
import org.siteweb.config.primary.cache.CacheComponent;
import org.siteweb.config.primary.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;



/**
 * <AUTHOR> (2024-04-18)
 **/

@CacheComponent
public class ConfigChangeDefineCache implements CacheManager<Integer, TblConfigChangeDefine> {

    private final Map<Integer, TblConfigChangeDefine> cache = new ConcurrentHashMap<>();


    @Autowired
    private TblConfigChangeDefineMapper configChangeDefineMapper;


    @Override
    public void reload() {
        cache.clear();
        List<TblConfigChangeDefine> list = configChangeDefineMapper.selectList(Wrappers.emptyWrapper());
        for (TblConfigChangeDefine item : list) {
            cache.put(item.getConfigId(), item);
        }
    }

    @Override
    public TblConfigChangeDefine get(Integer key) {
        return cache.get(key);
    }

    @Override
    public void put(Integer key, TblConfigChangeDefine value) {
        cache.put(key, value);
    }

    @Override
    public void remove(Integer key) {
        cache.remove(key);
    }

    @Override
    public Collection<TblConfigChangeDefine> values() {
        return cache.values();
    }

}
