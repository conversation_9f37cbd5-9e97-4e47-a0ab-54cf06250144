package org.siteweb.config.primary.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblStation;
import org.siteweb.config.common.entity.TslMonitorUnit;
import org.siteweb.config.common.entity.TslPort;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TslMonitorUnitMapper;
import org.siteweb.config.common.mapper.TslPortMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.vo.MonitorUnitStationVO;
import org.siteweb.config.primary.enums.MonitorUnitCategoryEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.OperationDetailService;
import org.siteweb.config.primary.service.PortService;
import org.siteweb.config.primary.service.PrimaryKeyValueService;
import org.siteweb.config.primary.service.StationService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.exception.InvalidParameterException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2024-03-11)
 **/
@Slf4j
@Service
public class PortServiceImpl implements PortService {

    @Autowired
    private I18n i18n;


    @Autowired
    TslMonitorUnitMapper monitorUnitMapper;

    @Autowired
    private TslPortMapper portMapper;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private ChangeEventService changeEventService;

    @Autowired
    OperationDetailService operationDetailService;


    @Autowired
    private StationService stationService;

    @Override
    public List<TslPort> findByMonitorUnit(Integer monitorUnitId) {
        return portMapper.selectList(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public TslPort findByMonitorUnitAndPortId(Integer monitorUnitId, Integer portId) {
        return portMapper.selectOne(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getMonitorUnitId, monitorUnitId).eq(TslPort::getPortId, portId));
    }

    @Override
    public TslPort findByPortId(Integer portId) {
        return portMapper.selectOne(new LambdaQueryWrapper<>(TslPort.class).eq(TslPort::getPortId, portId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPort(TslPort port) {
        // 端口类型不能为空
        if (ObjectUtil.isNull(port.getPortType())) {
            throw new BusinessException("端口类型不能为空");
        }
        // 端口名称不能为空
        if (ObjectUtil.isNull(port.getPortName())) {
            throw new BusinessException("端口名称不能为空");
        }
        // 端口号不能为空
        if (ObjectUtil.isNull(port.getPortNo())) {
            throw new BusinessException("端口号不能为空");
        }
        if (ObjectUtil.isNull(port.getSetting())) {
            port.setSetting("");
        }
        if (port.getPortId() == null || port.getPortId().equals(0)) {
            Integer portId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_PORT, 0);
            port.setPortId(portId);
        }
        if (portMapper.insert(port) > 0) {
            changeEventService.sendCreate(port);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(port.getId()), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("add"), "", port.getPortName());
        }
    }

    @Override
    public boolean updatePort(TslPort port) {
        TslPort originPort = findByPortId(port.getPortId());
        int rows = portMapper.updateById(port);
        if (rows > 0) {
            operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), originPort, port);
            changeEventService.sendUpdate(port);
        }
        return rows > 0;
    }

    @Override
    public boolean deletePort(Integer portId) {
        TslPort originPort = findByPortId(portId);
        if (originPort != null) {
            portMapper.delete(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getPortId, portId));
            changeEventService.sendDelete(originPort);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), originPort.getId().toString(), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("delete"), originPort.getPortName(), "");
        }
        return originPort != null;
    }

    @Override
    public boolean deleteByMonitorUnitId(Integer monitorUnitId) {
        List<TslPort> ports = portMapper.selectList(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getMonitorUnitId, monitorUnitId));
        int deletedCount = portMapper.delete(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getMonitorUnitId, monitorUnitId));
        if (deletedCount > 0) {
            for (TslPort port : ports) {
                changeEventService.sendDelete(port);
                operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), port.getId().toString(), OperationObjectTypeEnum.PORT, i18n.T("port.name"), i18n.T("delete"), port.getPortName(), "");
            }
        }
        return deletedCount > 0;
    }

    @Override
    public void verification(TslPort port) {
        // 判断同一个监控单元下端口号是否重复
        List<TslPort> ports = portMapper.selectList(Wrappers.lambdaQuery(TslPort.class)
                .eq(TslPort::getMonitorUnitId, port.getMonitorUnitId())
                .ne(port.getPortId() != null, TslPort::getPortId, port.getPortId())); // 排除当前正在修改的记录

        if (ports.stream().anyMatch(e -> Objects.equals(e.getPortNo(), port.getPortNo()))) {
            throw new InvalidParameterException(i18n.T("error.duplicate.portNo", i18n.T("monitor.unit"), port.getPortNo()));
        }

        // 判断监控单元是RMU下MU、跨站RMU
        TslMonitorUnit monitorUnit = monitorUnitMapper.selectById(port.getMonitorUnitId());
        if (monitorUnit == null) {
            throw new InvalidParameterException(i18n.T("monitor.unit.not.exist"));
        }

        if (monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.MU_OF_RMU.getValue()
                || monitorUnit.getMonitorUnitCategory() == MonitorUnitCategoryEnum.ACROSS_MU_OF_RMU.getValue()) {
            // 通过workstationId找到所有监控单元，判断端口是否重复
            List<TslMonitorUnit> monitorUnits = monitorUnitMapper.selectList(
                    Wrappers.lambdaQuery(TslMonitorUnit.class)
                            .eq(TslMonitorUnit::getWorkStationId, monitorUnit.getWorkStationId()));

            List<Integer> monitorUnitIds = monitorUnits.stream()
                    .map(TslMonitorUnit::getMonitorUnitId)
                    .toList();

            // 根据监控单元IDs，查询所有端口，排除当前正在修改的端口
            List<TslPort> portsByMonitorUnitIds = portMapper.selectList(
                    Wrappers.lambdaQuery(TslPort.class)
                            .in(TslPort::getMonitorUnitId, monitorUnitIds)
                            .ne(port.getPortId() != null, TslPort::getPortId, port.getPortId())); // 排除当前正在修改的记录

            if (portsByMonitorUnitIds.stream().anyMatch(e -> Objects.equals(e.getPortNo(), port.getPortNo()))) {
                throw new InvalidParameterException(i18n.T("error.duplicate.portNo", i18n.T("monitor.unit"), port.getPortNo()));
            }
        }
    }

    @Override
    public String checkRmuPortConflict(Integer workStationId) {
        List<TslPort> ports = portMapper.findPortsByWorkStationIdAndLinkSamplerUnitId(workStationId);
        List<MonitorUnitStationVO> monitorUnitStationVOs = monitorUnitMapper.findMonitorUnitStationVOs(workStationId);
        if (ports.isEmpty()) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        Map<Integer, Long> queryPortNo = ports.stream()
                .collect(Collectors.groupingBy(TslPort::getPortNo, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!queryPortNo.isEmpty()) {
            // 遍历queryPortNo，批量ports中的端口号
            queryPortNo.forEach((portNo, count) -> {
                List<TslPort> portList = ports.stream()
                        .filter(port -> port.getPortNo().equals(portNo))
                        .toList();
                sb.append(i18n.T("error.conflic.portno", portNo));
                sb.append("\n");
                portList.forEach(port -> {
                    // 通过端口号查询采集单元
                    MonitorUnitStationVO monitorUnitStationVO = monitorUnitStationVOs.stream()
                            .filter(monitorUnit -> monitorUnit.getMonitorUnitId().equals(port.getMonitorUnitId()))
                            .findFirst().orElse(null);
                    if (monitorUnitStationVO != null) {
                        TblStation station = stationService.findByStationId(monitorUnitStationVO.getStationId());
                        sb.append("\t");
                        sb.append(i18n.T("error.conflic.port", station.getStationName(), monitorUnitStationVO.getMonitorUnitName(), port.getPortName()));
                        sb.append("\n");
                    }
                });
            });
        }
        Map<String, Long> querySetting = ports.stream()
                .collect(Collectors.groupingBy(TslPort::getSetting, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!querySetting.isEmpty()) {
            querySetting.forEach((setting, count) -> {
                List<TslPort> portList = ports.stream()
                        .filter(port -> port.getSetting().equals(setting))
                        .toList();
                sb.append(i18n.T("error.conflic.portsetting", setting));
                sb.append("\n");
                portList.forEach(port -> {
                    // 通过端口号查询采集单元
                    MonitorUnitStationVO monitorUnitStationVO = monitorUnitStationVOs.stream()
                            .filter(monitorUnit -> monitorUnit.getMonitorUnitId().equals(port.getMonitorUnitId()))
                            .findFirst().orElse(null);
                    if (monitorUnitStationVO != null) {
                        TblStation station = stationService.findByStationId(monitorUnitStationVO.getStationId());
                        sb.append("\t");
                        sb.append(i18n.T("error.conflic.port.setting", station.getStationName(), monitorUnitStationVO.getMonitorUnitName(), port.getPortName()));
                        sb.append("\n");
                    }
                });
            });
        }
        if (sb.isEmpty()) {
            return i18n.T("error.conflic.port.setting.notfound");
        }
        return sb.toString();
    }

    @Override
    public List<TslPort> findPortByMonitUnitId(Integer monitorUnitId) {
        return portMapper.selectList(Wrappers.lambdaQuery(TslPort.class).eq(TslPort::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public List<TslPort> findPorts() {
        return portMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<TslPort> findPortByMonitorUnitIdAndLinkSamplerUnitId(Integer monitorUnitId, Integer linkSamplerUnitId) {
        return portMapper.selectList(Wrappers.lambdaQuery(TslPort.class)
                .eq(TslPort::getMonitorUnitId, monitorUnitId)
                .eq(TslPort::getLinkSamplerUnitId, linkSamplerUnitId));
    }

    @Override
    public Integer getMaxPortByMonitorUnitId(Integer monitorUnitId) {
        if (ObjectUtil.isEmpty(monitorUnitId)) return null;
        QueryWrapper<TslPort> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MonitorUnitId", monitorUnitId);
        queryWrapper.select("max(PortNo) as PortNo");
        TslPort tslPort = portMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(tslPort)) return 1;
        return tslPort.getPortNo();
    }
}
