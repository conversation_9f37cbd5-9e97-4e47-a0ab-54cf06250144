package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.dto.StationDTO;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.mapper.TblStationBaseTypeMapper;
import org.siteweb.config.common.mapper.TblStationMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.enums.SysConfigEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2024-03-11)
 **/
@Slf4j
@Service
public class StationServiceImpl implements StationService {

    @Autowired
    private I18n i18n;


    @Autowired
    private TblStationMapper stationMapper;


    @Autowired
    private HouseService houseService;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    StandardTypeService standardTypeService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    TblStationBaseTypeMapper stationBaseTypeMapper;

    @Autowired
    StationProjectInfoService stationProjectInfoService;

    @Autowired
    ChangeEventService changeEventService;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    StationStructureMapService stationStructureMapService;

    private List<StationSwatch> stationSwatches = new ArrayList<>();

    @Autowired
    SysConfigService sysConfigService;

    @Autowired
    private ObjectMapper objectMapper;


    @Override
    public TblStation findByStationId(int stationId) {
        return stationMapper.findStationById(stationId);
    }

    @Override
    public TblStation findResourceStructure(Integer structureId) {
        return stationMapper.findByResourceStructure(structureId);
    }

    @Override
    public List<IdValueDTO<Integer, String>> findCategoryList() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        //使用正常的维谛标准
        if (currentStandardType.getValue() <= 0) {
            List<TblDataItem> tblDataItemList = dataItemService.findByEntryId(DataEntryEnum.STATION_CATEGORY);
            return tblDataItemList.stream()
                    .map(item -> new IdValueDTO<>(item.getItemId(), item.getItemValue()))
                    .toList();
        }
        //使用特定的标准
        List<TblStationBaseType> stationBaseTypeList = stationBaseTypeMapper.selectList(Wrappers.lambdaQuery(TblStationBaseType.class)
                .eq(TblStationBaseType::getStandardId, currentStandardType.getValue()));
        return stationBaseTypeList.stream()
                .map(type -> new IdValueDTO<>(type.getId(), type.getType()))
                .toList();
    }

    @Override
    public Map<String, Object> findCategoryMap(Object key) {
        List<IdValueDTO<Integer, String>> categoryList = findCategoryList();
        IdValueDTO<Integer, String> idValueDTO = categoryList.stream().filter(f -> Objects.equals(f.getId(), Convert.toInt(key))).findFirst().orElse(null);
        return BeanUtil.beanToMap(idValueDTO);
    }

    @Override
    public Map<String, List<TblDataItem>> categorizeDataItemsByType() {
        Map<String, List<TblDataItem>> categorizedDataItems = new HashMap<>();
        List<TblDataItem> dataItems = dataItemService.findByEntryId(DataEntryEnum.STATION_CATEGORY);
        categorizedDataItems.put(i18n.T("base.station"), dataItems);
        categorizedDataItems.put(i18n.T("equipment.room"), dataItems);
        return categorizedDataItems;
    }


    @Override
    public TblStation findStationByName(String stationName) {
        return stationMapper.selectOne(Wrappers.lambdaQuery(TblStation.class).eq(TblStation::getStationName, stationName));
    }

    @Override
    public List<StationSwatch> findStationTemplateList() {
        if (stationSwatches.isEmpty()) {
            ClassPathResource resource = new ClassPathResource("templates/station/template.json");
            try {
                stationSwatches = objectMapper.readValue(resource.getInputStream(), new TypeReference<List<StationSwatch>>() {
                });
            } catch (IOException e) {
                log.error("Failed to read station template", e);
            }
        }
        return stationSwatches;
    }

    @Override
    public List<TblStation> findStationList(List<Integer> stationIds) {
        return stationMapper.selectBatchIds(stationIds);
    }

    @Override
    public List<TblStation> findAllStation() {
        return stationMapper.selectList(Wrappers.lambdaQuery(TblStation.class));
    }

    @Override
    public List<TblStation> findUnboundStation() {
        return stationMapper.selectList(Wrappers.lambdaQuery(TblStation.class).eq(TblStation::getStationId, 0));
    }

    @Override
    public TblStation findById(Integer stationId) {
        return stationMapper.selectById(stationId);
    }

    @Override
    public void updateStationNameInSiteWeb(String stationName, String description, Integer stationId) {
        TblStation station = findById(stationId);
        if (Objects.isNull(station)) {
            return;
        }
        station.setStationName(stationName);
        station.setDescription(description);
        update(station);
    }

    @Override
    public TblStation findLatestStation() {
        TblStation station = stationMapper.selectOne(Wrappers.lambdaQuery(TblStation.class).orderByDesc(TblStation::getUpdateTime).last("limit 1"));
        if (station == null) {
            return null;
        }
        var projectionInfo = stationProjectInfoService.findByStationId(station.getStationId());
        if (projectionInfo != null) {
            station.setProjectName(projectionInfo.getProjectName());
            station.setContractNo(projectionInfo.getContractNo());
        }
        return station;


    }

    @Override
    public Boolean updateBordNumber(Integer stationId, Integer bordNumber) {
        TblStation station = stationMapper.selectById(stationId);
        station.setBordNumber(bordNumber);
        return update(station);
    }

    @Override
    public Boolean updateBordNumberIfNull(Integer bordNumber) {
        int updatedCount = stationMapper.update(Wrappers.lambdaUpdate(TblStation.class)
                .set(TblStation::getBordNumber, bordNumber)
                .isNull(TblStation::getBordNumber));
        return updatedCount > 0;
    }


    @Override
    public Boolean create(TblStation station) {
        if (station.getStationId() == null || station.getStationId().equals(0)) {
            Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
            station.setStationId(stationId);
        }
        boolean status = stationMapper.insert(station) > 0;
        if (status) {
            // 创建局站项目信息
            stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
            createDefaultHouse(station);
            changeEventService.sendCreate(station);
        }
        return status;
    }

    @Override
    public Boolean insert(TblStation station) {
        if (station.getStationId() == null || station.getStationId().equals(0)) {
            Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
            station.setStationId(stationId);
        }
        boolean status = stationMapper.insert(station) > 0;
        if (status) {
            // 创建局站项目信息
            stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
            changeEventService.sendCreate(station);
        }
        return status;
    }

    /**
     * 创建默认局房
     *
     * @param station 局站对象
     * <AUTHOR> (2024/3/27)
     */
    private void createDefaultHouse(TblStation station) {
        if (station.getStationTemplateId() == null || station.getStationTemplateId() == 0) { // 空局站
            //  创建默认局房
            TblHouse house = new TblHouse();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName(i18n.T("house.default.name"));
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(house);
        } else if (station.getStationTemplateId() == 1) { // 空局站
            TblHouse house = new TblHouse();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName(i18n.T("house.default.name"));
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(house);
            TblHouse baseStationHouse = new TblHouse();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName(i18n.T("baseStation.house.name"));
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(baseStationHouse);
        } else if (station.getStationTemplateId() == 2) { // 江苏基站
            //  默认具有“基站”局房
            TblHouse house = new TblHouse();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName(i18n.T("house.default.name"));
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(house);
            TblHouse baseStationHouse = new TblHouse();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName(i18n.T("baseStation.house.name"));
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(baseStationHouse);

            TblHouse importantStationHouse = new TblHouse();
            Integer importantStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            importantStationHouse.setStationId(station.getStationId());
            importantStationHouse.setHouseId(importantStationHouseId);
            importantStationHouse.setHouseName(i18n.T("important.signal.house.name"));
            importantStationHouse.setDescription(house.getHouseName());
            importantStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.createHouse(importantStationHouse);
        }


    }


    @Override
    public Boolean update(TblStation station) {
        if (stationMapper.updateById(station) > 0) {
            changeEventService.sendUpdate(station);
            return true;
        }
        return false;
    }

    @Override
    public Boolean updateDTO(StationDTO station) {
        TblStation cacheStation = stationMapper.findStationById(station.getStationId());
        if (cacheStation != null) {
            // 检查局站名称是否发生变更
            if (!cacheStation.getStationName().equals(station.getStationName())) {
                // 如果名称变更，检查新名称是否重复
                TblStation existingStation = findStationByName(station.getStationName());
                if (existingStation != null && !existingStation.getStationId().equals(station.getStationId())) {
                    throw new BusinessException("局站名称已存在，请使用其他名称");
                }
            }
            TblStation updateStation = new TblStation();
            BeanUtils.copyProperties(cacheStation, updateStation);
            station.copyTo(updateStation);
            updateStation.setUpdateTime(LocalDateTime.now());
            boolean result = update(updateStation);
            if (result) {
                // 更新tbl_stationprojectinfo表
                stationProjectInfoService.updateProjectNameAndContractNo(updateStation.getStationId(), station.getProjectName(), station.getContractNo());
                operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), cacheStation, updateStation);
            }
            return result;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createDTO(StationDTO station) {
        // 局站名称要唯一
        TblStation stationExit = findStationByName(station.getStationName());
        if (stationExit != null) {
            throw new BusinessException("局站名称已存在");
        }


        TblStation insertStation = new TblStation();
        if (station.getStationId() == null || station.getStationId().equals(0)) {
            Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
            station.setStationId(stationId);
        }
        List<TblDataItem> centerDataItem = dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY);
        insertStation.setStationName(station.getStationName());
        insertStation.setConnectState(2);
        insertStation.setUpdateTime(LocalDateTime.now());
        if (station.getStationCategory() == null) {
            insertStation.setStationCategory(1);
        } else {
            insertStation.setStationCategory(station.getStationCategory());
        }
        insertStation.setStationGrade(1);
        insertStation.setStationState(1);
        insertStation.setCenterId(centerDataItem.get(0).getItemId());
        insertStation.setEnable(true);
        insertStation.setProjectName(station.getProjectName());
        insertStation.setContractNo(station.getContractNo());
        insertStation.setStationTemplateId(station.getStationTemplateId());
        insertStation.setStationId(station.getStationId());
        if (station.getBordNumber() == null) {
            insertStation.setBordNumber(0);
        } else {
            insertStation.setBordNumber(station.getBordNumber());
        }
        TblStationStructureMap tblStationStructureMap = new TblStationStructureMap();
        tblStationStructureMap.setStationId(insertStation.getStationId());
        tblStationStructureMap.setStructureId(station.getStationStructureId());
        int insert = stationMapper.insert(insertStation);
        if (insert > 0) {
            stationStructureMapService.create(tblStationStructureMap);
            changeEventService.sendCreate(insertStation);
            insertStation.setStationTemplateId(null);
            createDefaultHouse(insertStation);
            // 创建局站项目信息
            stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
        }
        return insert > 0;
    }

    @Override
    public Boolean deleteByID(Integer stationId) {
        TblStation station = stationMapper.selectById(stationId);
        if (station == null) {
            return false;
        }
        if (stationMapper.deleteById(stationId) > 0) {
            changeEventService.sendDelete(station);
            return true;
        }
        return false;
    }


}
