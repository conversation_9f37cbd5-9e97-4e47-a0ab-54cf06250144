package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.ResourceStructureDTO;
import org.siteweb.config.common.dto.StationDTO;
import org.siteweb.config.common.entity.*;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.ResourceStructureMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.primary.enums.StructureTypeEnum;
import org.siteweb.config.primary.enums.SysConfigEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> (2024-03-11)
 **/
@Slf4j
@Service
public class ResourceStructureServiceImpl implements ResourceStructureService {

    @Autowired
    private ChangeEventService changeEventService;

    @Autowired
    private ResourceStructureMapper resourceStructureMapper;

    @Autowired
    private DataItemService dataItemService;

    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private StationService stationService;

    @Autowired
    private StationStructureMapService stationStructureMapService;

    @Autowired
    private StationStructureService stationStructureService;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;

    @Autowired
    SysConfigService sysConfigService;

    @Autowired
    HouseService houseService;


    @Autowired
    private ObjectMapper objectMapper;

    @Value("${bytedance.generate-signal-standard:false}")
    private Boolean generateUUID;



    @PostConstruct
    @Transactional(rollbackFor = Exception.class)
    public void init() {
        // 查询所有restructuretype等于104的
        List<ResourceStructure> resourceStructures = resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getStructureTypeId, StructureTypeEnum.STATION.getValue()));
        if (resourceStructures.isEmpty()) {
            return;
        }
        // 遍历层级，获取OriginId
        for (ResourceStructure resourceStructure : resourceStructures) {
            if (resourceStructure.getOriginId() == null) {
                continue;
            }
            TblStation station = stationService.findByStationId(resourceStructure.getOriginId());
            if (station == null) {
                continue;
            }
            if (station.getBordNumber() != null && station.getBordNumber() == 0) {
                continue;
            }
            // 修改station的bordNumber为0
            stationService.updateBordNumber(station.getStationId(), 0);
        }

        // 再统一将没有bordNumber为null的设置为1
        stationService.updateBordNumberIfNull(1);
    }


    @Override
    public ResourceStructure getStructureByID(Integer resourceStructureID) {
        return resourceStructureMapper.selectById(resourceStructureID);
    }

    @Override
    public ResourceStructure getRootStructure() {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getParentResourceStructureId, 0));
    }

    @Override
    public List<ResourceStructure> getChildrenByParentId(Integer parentId) {
        return resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getParentResourceStructureId, parentId));
    }

    @Override
    public List<ResourceStructure> getStructureByStationId(Integer stationId) {

        return resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, stationId));
//        return resourceStructureCache.values().stream()
//                .filter(e -> stationId.equals(e.getOriginId()))
//                .toList();
    }

    @Override
    public Boolean create(ResourceStructure resourceStructure) {
        if (resourceStructure.getResourceStructureId() == null || resourceStructure.getResourceStructureId().equals(0)) {
            Integer structureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
            resourceStructure.setResourceStructureId(structureId);
        }
        // 主动清空sceneId
        resourceStructure.setSceneId(null);
        // 设置层级路径
        if (resourceStructure.getLevelOfPath() == null) {
            resourceStructure.setLevelOfPath(resourceStructure.getResourceStructureId().toString());
        } else {
            // 在原有层级路径上追加当前层级ID
            resourceStructure.setLevelOfPath(resourceStructure.getLevelOfPath() + "." + resourceStructure.getResourceStructureId());
        }

        // 设置guid
        if (generateUUID) {
            JsonNode extValue = resourceStructure.getExtendedField();
            ObjectMapper mapper = new ObjectMapper();

            ObjectNode newValue = mapper.createObjectNode();
            newValue.put("guid", UUID.randomUUID().toString());

            ArrayNode arrayNode;
            if (extValue != null && extValue.isArray()) {
                arrayNode = (ArrayNode) extValue;
            } else {
                arrayNode = mapper.createArrayNode();
            }

            arrayNode.add(newValue);

            resourceStructure.setExtendedField(arrayNode);
        }

        boolean result = resourceStructureMapper.insert(resourceStructure) > 0;
        if (result) {
            changeEventService.sendCreate(resourceStructure);
            operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), resourceStructure.getResourceStructureId().toString(), OperationObjectTypeEnum.RESOURCE_STRUCTURE, i18n.T("resource.structure.name"), i18n.T("add"), "", resourceStructure.getResourceStructureName());
        }
        return result;
    }

    public Boolean batchInsert(List<ResourceStructure> resourceStructures) {
        if (resourceStructures == null || resourceStructures.isEmpty()) {
            return false;
        }

        // 设置 ResourceStructureId 和 LevelOfPath
        for (ResourceStructure resourceStructure : resourceStructures) {
            resourceStructure.setSceneId(null);
            if (resourceStructure.getResourceStructureId() == null || resourceStructure.getResourceStructureId().equals(0)) {
                Integer structureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
                resourceStructure.setResourceStructureId(structureId);
            }

            // 设置层级路径
//            if (resourceStructure.getLevelOfPath() == null) {
//                resourceStructure.setLevelOfPath(resourceStructure.getResourceStructureId().toString());
//            } else {
//                resourceStructure.setLevelOfPath(resourceStructure.getLevelOfPath() + "." + resourceStructure.getResourceStructureId());
//            }
        }

        // 执行批量插入
        boolean result = resourceStructureMapper.insertBatchSomeColumn(resourceStructures) > 0;

        // 插入成功后，更新缓存并发送创建事件
        if (result) {
            for (ResourceStructure resourceStructure : resourceStructures) {
//                changeEventService.sendCreate(resourceStructure);
                operationDetailService.recordOperationLog(
                        TokenUserUtil.getLoginUserId(),
                        resourceStructure.getResourceStructureId().toString(),
                        OperationObjectTypeEnum.RESOURCE_STRUCTURE,
                        i18n.T("resource.structure.name"),
                        i18n.T("add"),
                        "",
                        resourceStructure.getResourceStructureName()
                );
            }
        }


        return result;
    }


    @Override
    public Boolean update(ResourceStructure resourceStructure) {
        ResourceStructure originResourceStructure = getStructureByID(resourceStructure.getResourceStructureId());
        if (originResourceStructure == null) {
            return false;
        }
        if (originResourceStructure.getOriginId() != null && !Objects.equals(resourceStructure.getStructureTypeId(), originResourceStructure.getStructureTypeId())) {
            throw new BusinessException("映射层级不可以修改层级类型!");
        }
        // 主动将sceneId清空
        resourceStructure.setSceneId(null);
        boolean result = resourceStructureMapper.updateById(resourceStructure) > 0;
        if (result) {
            changeEventService.sendUpdate(resourceStructure);
            operationDetailService.compareEntitiesRecordLog(TokenUserUtil.getLoginUserId(), originResourceStructure, resourceStructure);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByID(Integer resourceStructureID) {
        boolean result = false;
        ResourceStructure resourceStructure = resourceStructureMapper.selectById(resourceStructureID);
        if (resourceStructure != null) {
            // 如果是删除parentresourcestructure为0的则抛出异常不允许删除
            if (resourceStructure.getParentResourceStructureId() == 0) {
                throw new BusinessException(i18n.T("resource.structure.root.delete.error"));
            }
            result = resourceStructureMapper.deleteById(resourceStructureID) > 0;
            if (result) {
                changeEventService.sendDelete(resourceStructure);
                operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), resourceStructure.getResourceStructureId().toString(), OperationObjectTypeEnum.RESOURCE_STRUCTURE, i18n.T("resource.structure.name"), i18n.T("delete"),resourceStructure.getResourceStructureName(),"");
            }
        }
        return result;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TblStation createDefaultStation(ResourceStructureDTO resourceStructureDTO) {
        List<TblDataItem> centerDataItem = dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY);
        if (centerDataItem.isEmpty()) {
            throw new BusinessException("中心数据项不存在");
        }
        if (resourceStructureDTO.getStation() == null) {
            throw new BusinessException("局站信息不存在");
        }
        StationDTO stationDTO = resourceStructureDTO.getStation();
        TblStation station = new TblStation();
        BeanUtil.copyProperties(stationDTO, station);
        station.setCenterId(centerDataItem.get(0).getItemId());
        station.setUpdateTime(LocalDateTime.now());
        TblSysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
        if (sysConfig != null && sysConfig.getConfigValue().equals("2")) {
            station.setStationCategory(101);
        } else if (station.getStationCategory() == null) {
            station.setStationCategory(1);
        }

        if (station.getStationGrade() == null) {
            station.setStationGrade(1);
        }
        if (station.getStationName() == null) {
            station.setStationName(resourceStructureDTO.getResourceStructureName());
        }
        // 默认值
        station.setStationState(1);
        station.setEnable(true);
        station.setConnectState(2);
        station.setContainNode(false);
        stationService.create(station);
        // 增加tbl_stationstructuremap记录
        TblStationStructureMap tblStationStructureMap = new TblStationStructureMap();
        tblStationStructureMap.setStationId(station.getStationId());
        // StructureId为StructureGroupId = 1 的tbl_stationstructure的记录
        if (stationDTO.getStationStructureId() == null && resourceStructureDTO.getStationStructureId() == null) {
            List<TblStationStructure> tblStationStructures = stationStructureService.getStructureByStructureGroupId(1);
            tblStationStructureMap.setStructureId(tblStationStructures.get(0).getStructureId());
        } else if (resourceStructureDTO.getStationStructureId() != null) {
            tblStationStructureMap.setStructureId(resourceStructureDTO.getStationStructureId());
        } else {
            tblStationStructureMap.setStructureId(stationDTO.getStationStructureId());
        }

        stationStructureMapService.create(tblStationStructureMap);
        // 记录日志
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), station.getStationId().toString(), OperationObjectTypeEnum.STATION, i18n.T("station.name"), i18n.T("add"), "", station.getStationName());
        return station;
    }

    @Override
    public List<ResourceStructure> findResourceStructures() {
//        return resourceStructureCache.values()
//                .stream()
//                .sorted(Comparator.comparing(ResourceStructure::getSortValue).thenComparing(ResourceStructure::getResourceStructureName))
//                .toList();
        return resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .orderByAsc(ResourceStructure::getSortValue)
                .orderByAsc(ResourceStructure::getResourceStructureName));
    }


    @Override
    public Integer getTreeRootId() {
        QueryWrapper<ResourceStructure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ParentResourceStructureId", 0);
        queryWrapper.select("ResourceStructureId");
        try {
            ResourceStructure resourceStructure = resourceStructureMapper.selectOne(queryWrapper);
            return resourceStructure.getResourceStructureId();
        } catch (Exception e) {
            log.error("query resource structure tree root error, the reason is {0}", e.getCause());
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createStationTemplate(ResourceStructure resourceStructure) {
        if (resourceStructure.getSceneId() == 1) {
            return false;
        }
        if (resourceStructure.getOriginId() == null) {
            return false;
        }
        TblStation station = stationService.findByStationId(resourceStructure.getOriginId());
        if (station == null) {
            return false;
        }
        List<TblHouse> house = houseService.findHouseByStationId(station.getStationId());
        if (house.isEmpty()) {
            return false;
        }
        for (TblHouse tblHouse : house) {
            ResourceStructure houseStructure = getResourceStructure(resourceStructure, tblHouse, station);
            this.create(houseStructure);
        }
        return true;
    }

    @Override
    public ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureTypeId) {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getStructureTypeId, structureTypeId));
    }

    @Override
    public ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer originId, Integer parentId, Integer structureTypeId) {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getOriginParentId, parentId)
                .eq(ResourceStructure::getStructureTypeId, structureTypeId));
    }

    @Override
    public List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId) {
        return resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getParentResourceStructureId, parentResourceStructureId));
    }

    private static ResourceStructure getResourceStructure(ResourceStructure resourceStructure, TblHouse tblHouse, TblStation station) {
        ResourceStructure houseStructure = new ResourceStructure();
        houseStructure.setResourceStructureName(tblHouse.getHouseName());
        houseStructure.setParentResourceStructureId(resourceStructure.getResourceStructureId());
        houseStructure.setSortValue(1);
        houseStructure.setOriginId(tblHouse.getHouseId());
        houseStructure.setOriginParentId(station.getStationId());
        houseStructure.setSceneId(2);
        houseStructure.setDisplay(true);
        houseStructure.setLevelOfPath(resourceStructure.getLevelOfPath());
        houseStructure.setStructureTypeId(StructureTypeEnum.STATION_HOUSE.getValue());
        return houseStructure;
    }


    /**
     * 创建局站的ResourceStructure。
     * 该函数通过查询现有的ResourceStructure和局站结构，找到缺失的ResourceStructure并创建它们。
     * 创建过程中会确保父子关系的正确性，并生成相应的路径层级信息。
     *
     * @param stationStructureId 局站结构的ID，用于查找相关的局站及其父节点
     * @return 返回创建的ResourceStructure列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ResourceStructure> createStationResourceStructure(Integer stationStructureId) {
        // 查询所有的ResourceStructure
        List<ResourceStructure> resourceStructures = findResourceStructures();

        // 查找根节点，即parentResourceStructureId为0的ResourceStructure
        ResourceStructure rootResourceStructure = resourceStructures.stream()
                .filter(resource -> resource.getParentResourceStructureId() != null
                        && resource.getParentResourceStructureId() == 0)
                .findFirst()
                .orElse(null);

        // 查询所有的局站结构
        List<TblStationStructure> stationStructureMaps = stationStructureService.findALL();

        // 查找指定局站及其所有父节点
        List<TblStationStructure> stationAndParents = findStationAndParents(stationStructureId, stationStructureMaps);

        // 找出所有缺失的ResourceStructure，即局站结构中未在ResourceStructure中存在的节点
        List<TblStationStructure> missingStructures = new ArrayList<>(stationAndParents.stream()
                .filter(stationStructure -> resourceStructures.stream()
                        .noneMatch(resource -> Objects.equals(resource.getOriginId(), stationStructure.getStructureId()) && resource.getStructureTypeId() == 103))
                .filter(stationStructure -> !Objects.equals(stationStructure.getParentStructureId(), 0))
                .toList());

        // 按照父子关系对缺失的局站结构进行排序，确保父节点在前
        missingStructures.sort((a, b) -> {
            if (Objects.equals(a.getParentStructureId(), b.getStructureId())) {
                return 1;
            }
            if (Objects.equals(b.getParentStructureId(), a.getStructureId())) {
                return -1;
            }
            return 0;
        });

        // 用于存储新创建的ResourceStructure
        List<ResourceStructure> createdResources = new ArrayList<>();

        // 遍历缺失的局站结构，创建对应的ResourceStructure
        return missingStructures.stream().map(stationStructure -> {
            ResourceStructure newResource = new ResourceStructure();
            Integer resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
            newResource.setResourceStructureId(resourceStructureId);
            newResource.setStructureTypeId(103);
            newResource.setResourceStructureName(stationStructure.getStructureName());

            // 查找父级ResourceStructure的ID，如果找不到则使用根节点作为父级
            Integer parentId = findParentResourceStructureId(stationStructure.getParentStructureId(),
                    Stream.concat(resourceStructures.stream(), createdResources.stream())
                            .collect(Collectors.toList()));
            if (parentId == null) {
                newResource.setParentResourceStructureId(rootResourceStructure.getResourceStructureId());
            } else {
                newResource.setParentResourceStructureId(parentId);
            }

            newResource.setPhoto(null);
            newResource.setPosition(null);

            // 构建路径层级信息
            String levelOfPath = buildLevelOfPath(stationStructure.getParentStructureId(), rootResourceStructure.getResourceStructureId(),
                    Stream.concat(resourceStructures.stream(), createdResources.stream())
                            .collect(Collectors.toList()));
            newResource.setLevelOfPath(levelOfPath);

            newResource.setDisplay(true);
            newResource.setSortValue(1);
            newResource.setOriginId(stationStructure.getStructureId());
            newResource.setOriginParentId(stationStructure.getParentStructureId());

            // 将新创建的ResourceStructure添加到临时列表中
            createdResources.add(newResource);
            return newResource;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ResourceStructure> getAllStructures() {
        return resourceStructureMapper.selectList(null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateUuid() {
        if (!generateUUID) {
            return false;
        }
        List<ResourceStructure> resourceStructures = resourceStructureMapper.fetchResourcesWithMissingOrInvalidGuid();
        List<ResourceStructure> needUpdateList = new ArrayList<>();

        for (ResourceStructure resourceStructure : resourceStructures) {
            try {
                String jsonString;
                JsonNode extendedField = resourceStructure.getExtendedField();

                if (extendedField == null) {
                    jsonString = "";
                } else {
                    jsonString = objectMapper.writeValueAsString(extendedField);
                }

                boolean guidExists = false;
                JSONArray jsonArray = new JSONArray();
                if (StrUtil.isNotEmpty(jsonString)) {
                    jsonArray = JSONUtil.parseArray(jsonString);
                    // 遍历JSON数组中的每个对象
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject item = jsonArray.getJSONObject(i);
                        if (item.containsKey("guid")) {
                            guidExists = true;
                            break;
                        }
                    }
                }

                // 如果不存在guid，则添加一个新的JSON对象到数组并加入待更新列表
                if (!guidExists) {
                    JSONObject newItem = new JSONObject();
                    newItem.put("guid", UUID.randomUUID().toString());
                    jsonArray.add(newItem);
                    resourceStructure.setExtendedField(objectMapper.readTree(jsonArray.toString()));
                    log.info(objectMapper.writeValueAsString((resourceStructure.getExtendedField())));
                    needUpdateList.add(resourceStructure);
                }
            } catch (Exception e) {
                // 处理JSON解析异常
                log.error("处理JSON解析异常, resourceStructureId: {}, err:{}", resourceStructure.getResourceStructureId(), e.getMessage());
            }
        }


        // 批量更新处理
        if (!needUpdateList.isEmpty()) {
            batchUpdateExtendedField(needUpdateList);
        }

        return true;
    }

    /**
     * 批量更新ExtendedField字段
     * @param resourceStructures 需要更新的资源结构列表
     */
    /**
     * 批量更新ExtendedField字段
     * @param resourceStructures 需要更新的资源结构列表
     */
    public void batchUpdateExtendedField(List<ResourceStructure> resourceStructures) {
        // 每1000条记录批量更新一次
        int batchSize = 1000;
        List<List<ResourceStructure>> batches = new ArrayList<>();

        for (int i = 0; i < resourceStructures.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, resourceStructures.size());
            batches.add(resourceStructures.subList(i, endIndex));
        }

        for (List<ResourceStructure> batch : batches) {
            resourceStructureMapper.updateBatchById(batch);
            changeEventService.sendBatchUpdate(batch);
        }
    }



    /**
     * 递归查找父节点的 LevelOfPath，并拼接当前节点。
     *
     * 该函数通过递归查找给定站点的父节点，并构建其 LevelOfPath 路径。路径由根节点和当前节点的 ID 组成，使用点号（.）进行拼接。
     *
     * @param stationStructureId 当前站点的结构 ID，用于查找对应的资源结构。
     * @param rootResourceStructureId 根资源结构的 ID，作为路径的起始点。
     * @param resourceStructures 资源结构列表，包含所有可能的资源结构信息。
     * @return 返回拼接后的 LevelOfPath 路径，格式为 "rootResourceStructureId.currentResourceStructureId"。
     */
    private String buildLevelOfPath(Integer stationStructureId, Integer rootResourceStructureId, List<ResourceStructure> resourceStructures) {
        // 初始化路径段列表，并添加根节点 ID 作为路径的起始点
        List<String> pathSegments = new ArrayList<>();
        pathSegments.add(rootResourceStructureId.toString());

        // 在资源结构列表中查找与当前站点 ID 和结构类型匹配的资源结构
        ResourceStructure current = resourceStructures.stream()
                .filter(resource -> Objects.equals(resource.getOriginId(), stationStructureId) && resource.getStructureTypeId() == 103)
                .findFirst()
                .orElse(null);

        // 如果找到匹配的资源结构，将其 ID 添加到路径段列表中
        if (current != null) {
            pathSegments.add(current.getResourceStructureId().toString());
        }

        // 将路径段列表中的元素用点号拼接成完整的 LevelOfPath 路径
        return String.join(".", pathSegments);
    }

    /**
     * 递归查找指定站点结构及其所有父级站点结构，包括当前节点。
     *
     * @param stationStructureId 要查找的站点结构的ID
     * @param allStructures 所有站点结构的列表，用于查找父级节点
     * @return 包含指定站点结构及其所有父级站点结构的列表，按从子节点到父节点的顺序排列
     */
    private List<TblStationStructure> findStationAndParents(Integer stationStructureId, List<TblStationStructure> allStructures) {
        List<TblStationStructure> result = new ArrayList<>();

        // 查找当前节点
        TblStationStructure current = allStructures.stream()
                .filter(station -> station.getStructureId().equals(stationStructureId))
                .findFirst()
                .orElse(null);

        // 从当前节点开始，逐级向上查找父节点，直到没有父节点为止
        while (current != null) {
            result.add(current);

            // 查找当前节点的父级节点
            TblStationStructure finalCurrent = current;
            current = allStructures.stream()
                    .filter(station -> station.getStructureId().equals(finalCurrent.getParentStructureId()))
                    .findFirst()
                    .orElse(null);
        }

        return result;
    }

    // 根据父级，查找父级在 resourceStructures 中的位置
    private Integer findParentResourceStructureId(Integer parentStructureId, List<ResourceStructure> resourceStructures) {
        return resourceStructures.stream()
                .filter(resource -> Objects.equals(resource.getOriginId(), parentStructureId) && resource.getStructureTypeId() == 103)
                .map(ResourceStructure::getResourceStructureId)
                .findFirst()
                .orElse(null); // 如果父级不存在于 resourceStructures 中，则返回 null
    }

}
