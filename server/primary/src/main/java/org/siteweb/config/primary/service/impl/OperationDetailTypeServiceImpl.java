package org.siteweb.config.primary.service.impl;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.primary.service.OperationDetailTypeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class OperationDetailTypeServiceImpl implements OperationDetailTypeService {
    @Override
    public List<IdValueDTO<Integer, String>> findAll() {
        List<IdValueDTO<Integer, String>> result = new ArrayList<>();
        Arrays.stream(OperationObjectTypeEnum.values()).forEach(type -> result.add(new IdValueDTO<>(type.getValue(), type.getDescribe())));
        return result;
    }
}
