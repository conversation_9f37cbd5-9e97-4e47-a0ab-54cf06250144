package org.siteweb.config.primary.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.siteweb.config.common.entity.TslMonitorUnitConfig;
import org.siteweb.config.common.mapper.TslMonitorUnitConfigMapper;
import org.siteweb.config.primary.service.MonitorUnitConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/20
 */

@Service
public class MonitorUnitConfigServiceImpl implements MonitorUnitConfigService {

    @Autowired
    private TslMonitorUnitConfigMapper monitorUnitConfigMapper;

    @Override
    public void updateAllIpAddressDS(String ipAddressDS) {
        monitorUnitConfigMapper.update(null ,new UpdateWrapper<TslMonitorUnitConfig>().set("IpAddressDS", String.format("udp://%s:9000", ipAddressDS)));

    }

    @Override
    public List<TslMonitorUnitConfig> findByMonitorUnitId(Integer monitorUnitId) {
        return monitorUnitConfigMapper.findByMonitorUnitId(monitorUnitId);
    }
}
