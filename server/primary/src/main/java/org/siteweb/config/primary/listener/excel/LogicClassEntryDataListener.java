package org.siteweb.config.primary.listener.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.LogicClassEntryExportDTO;
import org.siteweb.config.common.entity.TblLogicClassEntry;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.service.StandardTypeService;
import org.siteweb.config.primary.service.TblLogicClassEntryService;

import java.util.List;

@Slf4j
public class LogicClassEntryDataListener implements ReadListener<LogicClassEntryExportDTO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<TblLogicClassEntry> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    /**
     * 读取的行数
     */
    @Getter
    private int rowCount = 0;
    private final TblLogicClassEntryService logicClassEntryService;
    private final StandardTypeService standardTypeService;


    public LogicClassEntryDataListener(TblLogicClassEntryService logicClassEntryService,StandardTypeService standardTypeService) {
        this.logicClassEntryService = logicClassEntryService;
        this.standardTypeService = standardTypeService;
    }


    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(LogicClassEntryExportDTO data, AnalysisContext context) {
        if (rowCount == 0) {
            // 有sheet则先删除再保存
            StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
            logicClassEntryService.deleteByStandardType(currentStandardType.getValue());
        }
        rowCount++;
        cachedDataList.add(LogicClassEntryExportDTO.convert(data));
        log.info("<LogicClassEntryExportDTO>解析到一条数据:{}", LogicClassEntryExportDTO.convert(data));
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("<LogicClassEntryExportDTO>所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("<LogicClassEntryExportDTO> {}条数据，开始存储数据库！", cachedDataList.size());
        logicClassEntryService.saveBatch(cachedDataList);
        log.info("<LogicClassEntryExportDTO> 存储数据库成功！");
    }
}