package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.ExpressionDTO;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.expressions.service.ExpressionService;
import org.siteweb.config.primary.service.EquipmentService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/expression")
public class ExpressionController {
    @Autowired
    ExpressionService expressionService;

    @Autowired
    I18n i18n;

    @Autowired
    private EquipmentService equipmentService;

    @GetMapping("/signal/{equipmentTemplateId}")
    public ResponseEntity<ResponseResult> findSignalExpression(@PathVariable("equipmentTemplateId") Integer equipmentTemplateId){
        return ResponseHelper.successful(expressionService.findSignalExpression(equipmentTemplateId));
    }

    @GetMapping(value = "/signal",params = "equipmentId")
    public ResponseEntity<ResponseResult> findSignalExpressionByEquipmentId(Integer equipmentId){
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.doesNotExist"));
        }
        return ResponseHelper.successful(expressionService.findSignalExpression(equipment.getEquipmentTemplateId()));
    }


    @GetMapping("/event/{equipmentTemplateId}")
    public ResponseEntity<ResponseResult> findEventExpression(@PathVariable("equipmentTemplateId") Integer equipmentTemplateId) {
        return ResponseHelper.successful(expressionService.findEventExpression(equipmentTemplateId));
    }

    @GetMapping(value = "/event",params = "equipmentId")
    public ResponseEntity<ResponseResult> findEventExpressionByEquipmentId(Integer equipmentId) {
        TblEquipment equipment = equipmentService.findEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return ResponseHelper.failed(i18n.T("monitor.equipment.doesNotExist"));
        }
        return ResponseHelper.successful(expressionService.findEventExpression(equipment.getEquipmentTemplateId()));
    }

    @PostMapping(value = "/validate")
    public ResponseEntity<ResponseResult> validateExpression(@RequestBody ExpressionDTO expressionDTO){
        return ResponseHelper.successful(expressionService.validateExpression(expressionDTO.getExpression()));
    }

    @PostMapping(value = "/analysis")
    public ResponseEntity<ResponseResult> expressionAnalysis(@RequestBody ExpressionDTO expressionDTO){
        return ResponseHelper.successful(expressionService.expressionAnalysis(expressionDTO));
    }
    @PostMapping(value = "/complexindex/validateandanalysis")
    public ResponseEntity<ResponseResult> complexIndexValidateExpression(@RequestBody ExpressionDTO expressionDTO){
        return ResponseHelper.successful(expressionService.validateComplexIndexExpression(expressionDTO.getExpression()));
    }
}
