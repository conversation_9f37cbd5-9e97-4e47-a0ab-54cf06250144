package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblHouse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (2024-03-11)
 **/
public interface HouseService {

    TblHouse findStationDefaultHouse(Integer stationId);

    boolean createHouse(TblHouse house);

    TblHouse findHouseByHostName(Integer stationId, String houseName);

    // 修改
    boolean updateHouse(TblHouse house);

    // 删除
    boolean deleteHouse(Integer houseId, Integer stationId);

    List<TblHouse> findHouseByStationId(Integer stationId);

    TblHouse findByHouseId(Integer houseId);

    void deleteHouseByStationId(Integer stationId);

    List<TblHouse> findHouseList();

    TblHouse findStationIdAndHouseId(Integer stationId, Integer houseId);

    Integer findHouseIdByStationId(Integer stationId);
}
