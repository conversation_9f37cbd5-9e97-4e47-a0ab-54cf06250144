package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.batchtool.EquipmentSignalPropertyDTO;
import org.siteweb.config.common.entity.TblSignalProperty;
import org.siteweb.config.common.mapper.TblSignalPropertyMapper;
import org.siteweb.config.primary.service.SignalPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SignalPropertyServiceImpl implements SignalPropertyService {
    @Autowired
    private TblSignalPropertyMapper signalPropertyMapper;

    @Override
    public void batchCreateSignalProperty(List<TblSignalProperty> signalPropertyList){
        if (CollUtil.isEmpty(signalPropertyList)) {
            return;
        }
        signalPropertyMapper.insertBatchSomeColumn(signalPropertyList);
    }

    @Override
    public void createSignalProperty(TblSignalProperty signalProperty) {
        signalPropertyMapper.insert(signalProperty);
    }

    @Override
    public List<TblSignalProperty> findByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        return signalPropertyMapper.selectList(new QueryWrapper<TblSignalProperty>()
                .eq("EquipmentTemplateId", equipmentTemplateId)
                .eq("SignalId", signalId));
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        signalPropertyMapper.delete(Wrappers.lambdaQuery(TblSignalProperty.class)
                                            .eq(TblSignalProperty::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void copySignalProperty(Integer originEquipmentTemplateId, Integer destEquipmentTemplateId) {
        List<TblSignalProperty> signalPropertyList = findByEquipmentTemplateId(originEquipmentTemplateId);
        signalPropertyList.forEach(property -> property.setEquipmentTemplateId(destEquipmentTemplateId));
        batchCreateSignalProperty(signalPropertyList);
    }

    @Override
    public void updateSignalProperty(Integer equipmentTemplateId, Integer signalId, List<TblSignalProperty> signalPropertyList) {
        deleteByEquipmentIdAndSignalId(equipmentTemplateId, signalId);
        if (CollUtil.isEmpty(signalPropertyList)) {
            return;
        }
        batchCreateSignalProperty(signalPropertyList);
    }

    @Override
    public void batchInsertLianTongSignalPropertys() {
        signalPropertyMapper.batchInsertLianTongSignalProperties();
    }

    @Override
    public List<EquipmentSignalPropertyDTO> findSignalPropertiesByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return new ArrayList<>();
        }
        return signalPropertyMapper.findSignalPropertiesByEquipmentIdAndSignalIds(equipmentId,signalIds);
    }

    private void deleteByEquipmentIdAndSignalId(Integer equipmentTemplateId, Integer signalId) {
        signalPropertyMapper.delete(Wrappers.lambdaQuery(TblSignalProperty.class)
                                            .eq(TblSignalProperty::getEquipmentTemplateId, equipmentTemplateId)
                                            .eq(TblSignalProperty::getSignalId, signalId));
    }


    private List<TblSignalProperty> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalPropertyMapper.selectList(Wrappers.lambdaQuery(TblSignalProperty.class)
                                                       .eq(TblSignalProperty::getEquipmentTemplateId, equipmentTemplateId));
    }
}
