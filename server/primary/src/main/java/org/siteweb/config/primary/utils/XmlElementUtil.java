package org.siteweb.config.primary.utils;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Element;

/**
 * 由于element默认获取Attribute是String类型的，如果要获取数字类型需要转换比较麻烦，由此类方便转换
 */
@UtilityClass
@Slf4j
public class XmlElementUtil {
    public static Integer getIntegerAttribute(Element element, String name) {
        return getNumberAttribute(element, name, Integer.class, null);
    }

    public static Integer getIntegerAttribute(Element element, String name, Integer defaultValue) {
        return getNumberAttribute(element, name, Integer.class, defaultValue);
    }

    public static Double getDoubleAttribute(Element element, String name) {
        return getNumberAttribute(element, name, Double.class, null);
    }

    public static Long getLongAttribute(Element element, String name) {
        return getNumberAttribute(element, name, Long.class, null);
    }
    public static Long getLongAttribute(Element element, String name, Long defaultValue) {
        return getNumberAttribute(element, name, Long.class, defaultValue);
    }

    public static Short getShortAttribute(Element element, String name) {
        return getNumberAttribute(element, name, Short.class, null);
    }

    public static Boolean getBooleanAttribute(Element element, String name) {
        String value = element.getAttribute(name);
        if (CharSequenceUtil.isBlank(value)) {
            return Boolean.FALSE;
        }
        return Boolean.parseBoolean(value.toLowerCase());
    }

    public static <T extends Number> T getNumberAttribute(Element element, String name, Class<T> clazz, T defaultValue) {
        String value = element.getAttribute(name);
        if (CharSequenceUtil.isBlank(value)) {
            return defaultValue;
        }
        return Convert.convert(clazz, value, defaultValue);
    }
}
