package org.siteweb.config.primary.service;

import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.dto.LogicClassEntryCreateDTO;
import org.siteweb.config.common.dto.LogicClassEntryExportDTO;
import org.siteweb.config.common.entity.TblLogicClassEntry;

import java.util.List;

public interface TblLogicClassEntryService {

    void updateLogicClassByDataItem(int standardType, int entryId);

    List<IdValueDTO<Integer,String>> findLogicClassEntryByEntryCategory(Integer entryCategory);

    TblLogicClassEntry createLogicClassEntry(LogicClassEntryCreateDTO logicClassEntryCreateDTO);

    List<TblLogicClassEntry> findByStandardType(Integer standardType);
    List<LogicClassEntryExportDTO> getLogicClassEntryExport(Integer standardType);

    void saveBatch(List<TblLogicClassEntry> cachedDataList);

    void deleteByStandardType(Integer standardType);
}
