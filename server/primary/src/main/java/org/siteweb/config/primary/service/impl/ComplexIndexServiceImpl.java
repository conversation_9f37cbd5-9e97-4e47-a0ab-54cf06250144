package org.siteweb.config.primary.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.ComplexIndexDTO;
import org.siteweb.config.common.mapper.ComplexIndexMapper;
import org.siteweb.config.primary.service.ComplexIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 */
@Service
@Slf4j
public class ComplexIndexServiceImpl implements ComplexIndexService {
    @Autowired
    ComplexIndexMapper complexIndexMapper;

    @Override
    public List<ComplexIndexDTO> findComplexList(List<Integer> objectIds, Integer objectTypeId){
        return complexIndexMapper.findByObjectIdAndObjectTypeId(objectIds, objectTypeId);
    }
}
