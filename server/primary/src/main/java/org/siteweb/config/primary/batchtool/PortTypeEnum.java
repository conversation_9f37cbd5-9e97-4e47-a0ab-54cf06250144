package org.siteweb.config.primary.batchtool;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum PortTypeEnum {
    /**
     * BACNet端口
     */
    BACNET(32, "BACNet", 2),
    /**
     * SNMP端口
     */
    SNMP(33, "SNMP", 1);

    PortTypeEnum(Integer portType, String portName, Integer driveTemplateType) {
        this.portType = portType;
        this.portName = portName;
        this.driveTemplateType = driveTemplateType;
    }

    /**
     * 端口类型
     */
    private final Integer portType;
    /**
     * 端口名称
     */
    private final String portName;
    /**
     * 默认模板id
     */
    private final Integer driveTemplateType;

    public static Integer getDriveTemplateTypeByType(Integer portType) {
        for (PortTypeEnum value : values()) {
            if (Objects.equals(value.portType, portType)) {
                return value.driveTemplateType;
            }
        }
        return null;
    }

    public static String getPortNameByType(Integer portType) {
        for (PortTypeEnum value : values()) {
            if (Objects.equals(value.portType, portType)) {
                return value.portName;
            }
        }
        return StrUtil.EMPTY;
    }
}