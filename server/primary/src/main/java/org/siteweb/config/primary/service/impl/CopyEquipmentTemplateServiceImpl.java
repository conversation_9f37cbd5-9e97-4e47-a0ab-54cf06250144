package org.siteweb.config.primary.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.dto.CopyEquipmentTemplateDTO;
import org.siteweb.config.common.entity.TblEquipmentTemplate;
import org.siteweb.config.common.mapper.TblEquipmentTemplateMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.primary.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/7
 */
@Service
@Slf4j
public class CopyEquipmentTemplateServiceImpl implements CopyEquipmentTemplateService {

    @Autowired
    private TblEquipmentTemplateMapper equipmentTemplateMapper;

    @Autowired
    private SignalService signalService;

    @Autowired
    private EventService eventService;

    @Autowired
    private ControlService controlService;

    @Autowired
    private ChangeEventService changeEventService;

    @Autowired
    private EquipmentTemplateService equipmentTemplateService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyTemplate(CopyEquipmentTemplateDTO copyEquipmentTemplateDTO) {
        equipmentTemplateService.doesTemplateNameExist(copyEquipmentTemplateDTO.getNewEquipmentTemplateName());
        TblEquipmentTemplate equipmentTemplate = equipmentTemplateMapper.selectById(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId());
        equipmentTemplate.setDescription(copyEquipmentTemplateDTO.getReason());
        equipmentTemplate.setParentTemplateId(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId());
        equipmentTemplate.setEquipmentTemplateName(copyEquipmentTemplateDTO.getNewEquipmentTemplateName());
        equipmentTemplate.setEquipmentTemplateId(null);
        equipmentTemplateService.createEquipmentTemplate(equipmentTemplate);
        signalService.copySignal(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
        eventService.copyEvent(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
        controlService.copyControl(copyEquipmentTemplateDTO.getOriginEquipmentTemplateId(), equipmentTemplate.getEquipmentTemplateId());
        changeEventService.sendCreate(equipmentTemplate);
        return equipmentTemplate.getEquipmentTemplateId();
    }



}
