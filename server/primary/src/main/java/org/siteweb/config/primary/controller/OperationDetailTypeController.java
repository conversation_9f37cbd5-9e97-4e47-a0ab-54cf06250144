package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.OperationDetailTypeService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/operationdetailtype")
public class OperationDetailTypeController {
    @Autowired
    OperationDetailTypeService operationDetailTypeService;

    @GetMapping
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(operationDetailTypeService.findAll());
    }
}
