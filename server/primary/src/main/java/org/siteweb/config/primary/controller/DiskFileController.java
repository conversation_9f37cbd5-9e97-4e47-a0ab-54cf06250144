package org.siteweb.config.primary.controller;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharsetUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.DiskFile;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.DiskFileService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/diskfile")
public class DiskFileController {
    @Autowired
    I18n i18n;
    @Autowired
    DiskFileService diskFileService;

    @PostMapping("/upload")
    public ResponseEntity<ResponseResult> upload(@RequestParam("file") MultipartFile file, @RequestParam("filePath") String filePath, @RequestParam(value = "cover", required = false) Boolean cover) throws IOException {
        if (Objects.isNull(file) || file.isEmpty()) {
            return ResponseHelper.failed(i18n.T("uploadFile.notEmpty"));
        }
        if (diskFileService.diskExist(filePath, file.getOriginalFilename()) && !BooleanUtil.isTrue(cover)) {
            return ResponseHelper.failed(i18n.T("uploadFile.exist.changeName", file.getOriginalFilename()));
        }
        return ResponseHelper.successful(diskFileService.upload(filePath, file.getOriginalFilename(), file.getBytes()));
    }

    @GetMapping(value = "/download", params = "fileId")
    public ResponseEntity<ResponseResult> download(Integer fileId, HttpServletResponse response) throws IOException {
        DiskFile diskFile = diskFileService.findById(fileId);
        return download(response, diskFile);
    }

    @GetMapping(value = "/download", params = {"filePath", "fileName"})
    public ResponseEntity<ResponseResult> download(String filePath,String fileName, HttpServletResponse response) throws IOException {
        DiskFile diskFile = diskFileService.findByFilePathAndFileName(filePath, fileName);
        return download(response, diskFile);
    }

    private ResponseEntity<ResponseResult> download(HttpServletResponse response, DiskFile diskFile) throws IOException {
        if (Objects.isNull(diskFile) || !diskFileService.diskExist(diskFile.getFilePath(), diskFile.getFileName())) {
            return ResponseHelper.failed(i18n.T("uploadFile.notExist"));
        }
        File file = diskFileService.getFile(diskFile.getFilePath(), diskFile.getFileName());
        // 设置响应头
        response.setContentType(Files.probeContentType(file.toPath()));
        //使用URLEncode编码，避免中文乱码
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncodeUtil.encode(file.getName(), CharsetUtil.parse("UTF-8")));
        response.setHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
        // 将文件写入响应输出流
        try (InputStream inputStream = new FileInputStream(file)) {
            FileCopyUtils.copy(inputStream, response.getOutputStream());
        }
        return ResponseHelper.successful();
    }

    @GetMapping(params = "filePath")
    public ResponseEntity<ResponseResult> findByFilePath(String filePath) {
        return ResponseHelper.successful(diskFileService.findByFilePath(filePath));
    }

    @GetMapping(value = "/page",params = "filePath")
    public ResponseEntity<ResponseResult> findPageByFilePath(Page<DiskFile> page, String filePath) {
        return ResponseHelper.successful(diskFileService.findPageByFilePath(page, filePath));
    }

    @DeleteMapping(params = {"filePath", "fileNames"})
    public ResponseEntity<ResponseResult> deleteByFilePathAndNames(String filePath, String fileNames) {
        if (CharSequenceUtil.isBlank(fileNames)) {
            return ResponseHelper.failed(i18n.T("uploadFile.fileName.noEmpty"));
        }
        List<String> fileNameList = CharSequenceUtil.split(fileNames,",");
        return ResponseHelper.successful(diskFileService.deleteFilesFromDiskAndDatabase(filePath, fileNameList));
    }
}
