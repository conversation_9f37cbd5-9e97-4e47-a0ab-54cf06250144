package org.siteweb.config.primary.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.siteweb.config.common.entity.TblEquipmentTemplate;
import org.siteweb.config.common.entity.TslSampler;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;
import org.siteweb.config.common.mapper.TblEquipmentTemplateMapper;
import org.siteweb.config.common.mapper.TslSamplerMapper;
import org.siteweb.config.common.mapper.TslSamplerUnitMapper;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.utils.FileNameUtil;
import org.siteweb.config.common.utils.PathUtil;
import org.siteweb.config.common.vo.SamplerVO;
import org.siteweb.config.primary.enums.ProtocolTypeEnum;
import org.siteweb.config.primary.enums.TableIdentityEnum;
import org.siteweb.config.primary.service.*;
import org.siteweb.config.primary.utils.FileSuffixUtil;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.siteweb.config.toolkit.security.TokenUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (2024-03-11)
 **/
@Slf4j
@Service
public class SamplerServiceImpl implements SamplerService {
    /**
     * 协议so库存储路径
     */
    public static final String PROTOCOL_PATH = "protocol";
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    I18n i18n;
    @Autowired
    TslSamplerMapper samplerMapper;
    @Autowired
    TslSamplerUnitMapper samplerUnitMapper;
    @Autowired
    TblEquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    ChangeEventService changeEventService;
    @Autowired
    DiskFileService diskFileService;
    @Autowired
    EquipmentTemplateService equipmentTemplateService;


    @Override
    public Collection<TslSampler> findAll() {
        return samplerMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public TslSampler findById(Integer samplerId) {
        return samplerMapper.selectById(samplerId);
    }

    @Override
    public List<TslSampler> findByIds(List<Integer> samplerIdList) {
        if (CollUtil.isEmpty(samplerIdList)) {
            return Collections.emptyList();
        }
        return samplerMapper.selectList(Wrappers.lambdaQuery(TslSampler.class)
                .in(TslSampler::getSamplerId, samplerIdList));
    }

    @Override
    public void createSamplersFromXml(TblEquipmentTemplate tblEquipmentTemplate, Element samplersElementList) {
        if (Objects.isNull(tblEquipmentTemplate) || Objects.isNull(samplersElementList)) {
            return;
        }

        String muHOSTEquipment = i18n.T("equipment.MUHOST.equipment");
        // 如果是RMU-MUHOST设备则无需生成Sampler
        if (Objects.equals(tblEquipmentTemplate.getEquipmentTemplateName(), muHOSTEquipment)) {
            return;
        }

        List<Element> samplerElementList = samplersElementList.elements("Sampler");
        if (CollUtil.isEmpty(samplerElementList)) {
            log.warn("没有采集器存在{}", tblEquipmentTemplate);
            return;
        }

        for (Element samplerElement : samplerElementList) {
            TslSampler sampler = parseSamplerXml(samplerElement);
            // 发现协议组发到现场的xml中协议编码和设备模板的协议编码不一致，导致网页版配置工具列表找不到sampler
            // 这边统一以设备模板的协议编码为准
            sampler.setProtocolCode(tblEquipmentTemplate.getProtocolCode());
            create(sampler);
        }
    }

    private TslSampler parseSamplerXml(Element samplerElement) {
        String dllPath = samplerElement.attributeValue("DllPath");
        return TslSampler.builder()
                .samplerName(samplerElement.attributeValue("SamplerName"))
                .samplerType(Short.valueOf(samplerElement.attributeValue("SamplerType")))
                .protocolCode(samplerElement.attributeValue("ProtocolCode"))
                .dllCode(samplerElement.attributeValue("DLLCode"))
                .dllVersion(samplerElement.attributeValue("DLLVersion"))
                .protocolFilePath(samplerElement.attributeValue("ProtocolFilePath"))
                .dllFilePath(samplerElement.attributeValue("DLLFilePath"))
                .dllPath(FileNameUtil.toLowerCaseExtension(dllPath))
                .setting(samplerElement.attributeValue("Setting"))
                .description(samplerElement.attributeValue("Description"))
                .build();
    }

    private boolean isExists(String protocolCode) {
        return samplerMapper.exists(Wrappers.lambdaQuery(TslSampler.class).eq(TslSampler::getProtocolCode, protocolCode));
    }

    @Override
    public void create(TslSampler sampler) {
        if (isExists(sampler.getProtocolCode())) {
            log.warn("采集器protocolCode:{}，已经存在", sampler.getProtocolCode());
            return;
        }
        int samplerId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TSL_SAMPLER, 0);
        sampler.setSamplerId(samplerId);
        // 如果dellcode和dellversion为null，则设置为空字符串
        if (Objects.isNull(sampler.getDllCode())) {
            sampler.setDllCode("");
        }
        if (Objects.isNull(sampler.getDllVersion())) {
            sampler.setDllVersion("");
        }
        if (Objects.isNull(sampler.getProtocolFilePath())) {
            sampler.setProtocolFilePath("");
        }
        if (Objects.isNull(sampler.getDllFilePath())) {
            sampler.setDllFilePath("");
        }
        if (Objects.isNull(sampler.getSoCode())) {
            sampler.setSoCode("");
        }
        if (Objects.isNull(sampler.getSoPath())) {
            sampler.setSoPath("");
        }
        //统一把 dllPath 后缀改成 .so
        sampler.setDllPath(FileSuffixUtil.changeToSo(sampler.getDllPath()));

        samplerMapper.insert(sampler);
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(sampler.getSamplerId()), OperationObjectTypeEnum.SAMPLER, i18n.T("monitor.sampler.name"), i18n.T("add"), "", sampler.getSamplerName());
        changeEventService.sendCreate(sampler);
    }

    @Override
    public void deleteByProtocolCode(String protocolCode) {
        if (CharSequenceUtil.isBlank(protocolCode)) {
            return;
        }
        //设备模板绑定了该采集单元
        Set<String> referenceSamplerNameByProtocolCodes = findReferenceSamplerNameByProtocolCodes(List.of(protocolCode));
        if (CollUtil.isNotEmpty(referenceSamplerNameByProtocolCodes)) {
            return;
        }
        //采集单元绑定了采集器
        Integer samplerId = samplerMapper.findSamplerIdByProtocolCode(protocolCode);
        Set<String> referenceSamplerNameBySamplerIdList = findReferenceSamplerNameBySamplerIdList(List.of(samplerId));
        if (CollUtil.isNotEmpty(referenceSamplerNameBySamplerIdList)) {
            return;
        }
        deleteById(samplerId);
    }

    @Override
    public Set<String> findReferenceSamplerNameBySamplerIdList(List<Integer> samplerIdList) {
        if (CollUtil.isEmpty(samplerIdList)) {
            return Collections.emptySet();
        }
        return samplerUnitMapper.findReferenceSamplerNameBySamplerIdList(samplerIdList);
    }

    @Override
    public TslSampler findByNameAndDllPath(String samplerName, String dllPath) {
        return samplerMapper.findByNameAndDllPath(samplerName, dllPath);
    }

    @Override
    public Set<String> findReferenceSamplerNameByProtocolCodes(List<String> protocolCodes) {
        if (CollUtil.isEmpty(protocolCodes)) {
            return Collections.emptySet();
        }
        return equipmentTemplateMapper.findReferenceSamplerNameByProtocolCodes(protocolCodes);
    }

    @Override
    public Set<String> findReferenceEquipmentNameByProtocolCodes(List<String> protocolCodes){
        if (CollUtil.isEmpty(protocolCodes)) {
            return Collections.emptySet();
        }
        return equipmentTemplateMapper.findReferenceEquipmentNameByProtocolCodes(protocolCodes);
    }

    @Override
    public List<SamplerVO> findAllVo() {
        List<SamplerVO> result = BeanUtil.copyToList(findAll(), SamplerVO.class);
        setEquipmentTemplateName(result);
        return result;
    }

    /**
     * 设置采集器绑定的设备模板
     *
     * @param records 采集器分页记录
     */
    private void setEquipmentTemplateName(List<SamplerVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<String> protocolCodeList = records.stream().map(SamplerVO::getProtocolCode).toList();
        Map<String, String> equipmentTemplateNameMap = equipmentTemplateMapper.findEquipmentTemplateByProtocolCodes(protocolCodeList)
                .stream()
                .collect(Collectors.toMap(SamplerVO::getProtocolCode, SamplerVO::getEquipmentTemplateName, (v1, v2) -> v1));
        records.forEach(sampler -> {
            if (equipmentTemplateNameMap.containsKey(sampler.getProtocolCode())) {
                sampler.setEquipmentTemplateName(equipmentTemplateNameMap.get(sampler.getProtocolCode()));
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Integer> samplerIdList) {
        for (Integer samplerId : samplerIdList) {
            deleteById(samplerId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Integer samplerId) {
        TslSampler sampler = findById(samplerId);
        if (Objects.isNull(sampler)) {
            return false;
        }
        //删除磁盘文件
        diskFileService.deleteFilesFromDiskAndDatabase(PROTOCOL_PATH, List.of(sampler.getProtocolCode()));
        samplerMapper.deleteById(samplerId);
        operationDetailService.recordOperationLog(TokenUserUtil.getLoginUserId(), String.valueOf(samplerId), OperationObjectTypeEnum.SAMPLER, i18n.T("monitor.sampler.name"), i18n.T("delete"), sampler.getSamplerName(), "");
        changeEventService.sendDelete(sampler);
        equipmentTemplateService.deleteByProtocolCode(sampler.getProtocolCode());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(TslSampler sampler) {
        TslSampler oldSampler = findById(sampler.getSamplerId());
        if (Objects.isNull(oldSampler)) {
            log.error("采集器不存在");
            return false;
        }
        handlerDllPath(sampler, oldSampler);
        samplerMapper.updateById(sampler);
        changeEventService.sendUpdate(sampler);
        return true;
    }

    private void handlerDllPath(TslSampler newSampler, TslSampler oldSampler) {
        if (Objects.equals(oldSampler.getDllPath(), newSampler.getDllPath())) {
            return;
        }
        if (CharSequenceUtil.isBlank(oldSampler.getSoPath())) {
            return;
        }
        //更新soPath字段
        String newSoPath = oldSampler.getSoPath().replace(oldSampler.getDllPath(), newSampler.getDllPath());
        newSampler.setSoPath(newSoPath);
        //更新磁盘文件
        for (ProtocolTypeEnum value : ProtocolTypeEnum.values()) {
            String filePath = PathUtil.pathJoin(PROTOCOL_PATH, newSampler.getProtocolCode(), value.getLibPath());
            diskFileService.rename(filePath, oldSampler.getDllPath(), newSampler.getDllPath());
        }
    }

    @Override
    public TslSampler findBySamplerType(int samplerType) {
        return samplerMapper.selectOne(new QueryWrapper<TslSampler>().eq("SamplerType", samplerType));
    }

    @Override
    public TslSampler findByProtocolCode(String protocolCode) {
        return samplerMapper.selectOne(Wrappers.lambdaQuery(TslSampler.class)
                .eq(TslSampler::getProtocolCode, protocolCode));
    }

    @Override
    public void batchInsertLianTongSampler() {
        samplerMapper.batchInsertLianTongSampler();
    }

    @Override
    public TslSampler findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return samplerMapper.findByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadProtocolFile(TslSampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName) {
        // 锁定记录防止并发冲突
        TslSampler lockedSampler = samplerMapper.forUpdateById(sampler.getSamplerId());

        // 处理文件上传操作
        handleFileUpload(lockedSampler, protocolTypeEnum, bytes, fileName);

        // 更新协议配置信息
        String oldConfigValue = updateProtocolConfig(lockedSampler, protocolTypeEnum, fileName);

        // 更新sampler状态
        updateSamplerStatus(lockedSampler);

        // 记录操作日志
        recordUploadOperationLog(lockedSampler.getSamplerId(), oldConfigValue, lockedSampler.getSoPath());
    }

    /**
     * 处理文件上传操作（删除旧文件并上传新文件）
     */
    private void handleFileUpload(TslSampler sampler, ProtocolTypeEnum protocolTypeEnum, byte[] bytes, String fileName) {
        String filePath = PathUtil.pathJoin(PROTOCOL_PATH, sampler.getProtocolCode(), protocolTypeEnum.getLibPath());

        // 删除同名的旧文件
        diskFileService.deleteFilesFromDiskAndDatabase(filePath, List.of(fileName));

        // 上传新文件
        diskFileService.upload(filePath, fileName, bytes);

        log.info("协议文件上传成功, 路径: {}, 文件名: {}", filePath, fileName);
    }

    /**
     * 更新协议配置信息
     */
    private String updateProtocolConfig(TslSampler sampler, ProtocolTypeEnum protocolTypeEnum, String fileName) {
        JSONObject protocolConfig = getJsonObject(sampler.getSoPath());
        String oldConfigValue = protocolConfig.toString();

        // 更新协议文件配置
        protocolConfig.set(protocolTypeEnum.getCode(), fileName);
        sampler.setSoPath(protocolConfig.toString());

        return oldConfigValue;
    }

    /**
     * 更新采样器状态
     */
    private void updateSamplerStatus(TslSampler sampler) {
        sampler.setUploadProtocolFile(true);
        update(sampler);
    }

    /**
     * 记录文件上传操作日志
     */
    private void recordUploadOperationLog(Integer samplerId, String oldValue, String newValue) {
        operationDetailService.recordOperationLog(
                TokenUserUtil.getLoginUserId(),
                String.valueOf(samplerId),
                OperationObjectTypeEnum.SAMPLER,
                i18n.T("monitor.sampler.soPath"),
                i18n.T("modify"),
                oldValue,
                newValue
        );
    }


    private static JSONObject getJsonObject(String soPath) {
        if (CharSequenceUtil.isBlank(soPath)) {
            return new JSONObject();
        }
        return JSONUtil.parseObj(soPath);
    }

    /**
     * 删除协议文件并更新采样器的协议文件状态
     *
     * @param protocolCode 协议代码
     * @param protocolTypeEnum 协议类型枚举
     */
    @Override
    public void deleteProtocolFile(String protocolCode, ProtocolTypeEnum protocolTypeEnum) {
        // 1. 根据协议代码获取采样器信息
        TslSampler sampler = findByProtocolCode(protocolCode);
        if (Objects.isNull(sampler)) {
            log.error("protocolCode:{},protocolTypeEnum:{},不存在对应的采集器", protocolCode, protocolTypeEnum);
            return;
        }
        // 2. 构建协议文件路径并删除指定文件
        JSONObject entries = CharSequenceUtil.isBlank(sampler.getSoPath()) ? new JSONObject() : JSONUtil.parseObj(sampler.getSoPath());
        String fileName = entries.getStr(protocolTypeEnum.getCode());
        if (CharSequenceUtil.isBlank(fileName)) {
            log.info("protocolCode:{},protocolTypeEnum:{},没有上传对应的协议，无需删除", protocolCode, protocolTypeEnum);
            throw new BusinessException("没有上传对应的协议，无需删除");
        }
        String oldValue = entries.toString();
        String protocolFilePath = PathUtil.pathJoin(PROTOCOL_PATH, sampler.getProtocolCode(), protocolTypeEnum.getLibPath());
        diskFileService.deleteFilesFromDiskAndDatabase(protocolFilePath, List.of(fileName));

        // 3. 检查协议是否还有其他文件并更新状态
        entries.remove(protocolTypeEnum.getCode());
        sampler.setSoPath(entries.isEmpty() ? "" : entries.toString());
        sampler.setUploadProtocolFile(!entries.isEmpty());
        // 4. 保存采样器状态
        update(sampler);
        recordUploadOperationLog(sampler.getSamplerId(), oldValue, entries.toString());
    }
}
