package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TslSamplerUnit;

import java.util.List;

/**
 * <AUTHOR> (2024-03-11)
 **/
public interface SamplerUnitService {

    TslSamplerUnit findBySamplerUnitId(Integer samplerUnitId);

    void createSamplerUnit(TslSamplerUnit samplerUnit);

    boolean updateSamplerUnit(TslSamplerUnit samplerUnit);

    boolean deleteSamplerUnit(Integer samplerUnitId);

    List<TslSamplerUnit> selectSamplerUnitWithPort(Integer monitorUnitId);

    List<TslSamplerUnit> findByMonitorUnitId(Integer monitorUnitId);

    List<TslSamplerUnit> findByParentSamplerUnitIdAndSamplerUnitName(Integer parentSamplerUnitId, String samplerUnitName);


    TslSamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName, String portName);

    List<TslSamplerUnit> findByMonitorUnitIdAndPortId(Integer monitorUnitId, Integer portId);

    void deleteByPortId(Integer portId);

    List<TslSamplerUnit> findSamplerUnits();

    // SamplerId
    List<TslSamplerUnit> findBySamplerId(Integer samplerId);

    // MonitorUnitId(), mu.getMonitorUnitId()) && sp.getSamplerType()
    List<TslSamplerUnit> findByMonitorUnitIdAndSamplerType(Integer monitorUnitId, Integer samplerType);

    List<TslSamplerUnit> findByPortId(Integer portId);
}
