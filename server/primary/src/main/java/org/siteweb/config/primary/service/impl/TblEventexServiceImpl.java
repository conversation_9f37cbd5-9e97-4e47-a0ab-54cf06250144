package org.siteweb.config.primary.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.entity.TblEventex;
import org.siteweb.config.common.mapper.TblEventexMapper;
import org.siteweb.config.primary.service.TblEventexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * Creation Date: 2024/4/15
 */
@Service
public class TblEventexServiceImpl implements TblEventexService {
    @Autowired
    private TblEventexMapper eventexMapper;

    @Override
    public void updateEventx(Integer equipmentTemplateId, Integer eventId, Integer turnover) {
        this.deleteByEvent(equipmentTemplateId, eventId);
        if (Objects.isNull(turnover)) {
            return;
        }
        TblEventex eventex = TblEventex.builder()
                                       .equipmentTemplateId(equipmentTemplateId)
                                       .eventId(eventId)
                                       .turnover(turnover)
                                       .build();
        eventexMapper.insert(eventex);
    }

    @Override
    public void deleteByEvent(Integer equipmentTemplateId, Integer eventId) {
        eventexMapper.delete(Wrappers.lambdaUpdate(TblEventex.class).eq(TblEventex::getEquipmentTemplateId, equipmentTemplateId)
                .eq(TblEventex::getEventId, eventId));
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventexMapper.delete(Wrappers.lambdaUpdate(TblEventex.class).eq(TblEventex::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void batchUpdate(List<TblEventex> eventexList) {
        if (CollUtil.isEmpty(eventexList)) {
            return;
        }
        //先删除后添加，避免部分告警还没有生成ex表中的字段
        eventexMapper.batchDelete(eventexList);
        eventexMapper.insertBatchSomeColumn(eventexList);
    }
}
