package org.siteweb.config.primary.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备属性枚举
 * 定义了不同类型的设备属性，包括ID、中文名称和英文名称
 */
@Getter
@AllArgsConstructor
public enum EquipmentPropertyEnum {
    INTELLIGENT(1, "智能设备", "Intelligent Equipment"),
    SUPPRESSABLE(2, "告警可屏蔽", "Suppressable Equipment"),
    CONTROLLABLE(3, "可控设备", "Controllable Equipment"),
    UNUSED(4, "未用", "Unused"),
    IMPORTANT(5, "重要设备", "Important Equipment"),
    BATTERY_24V(6, "24伏蓄电池", "24 Volt Battery"),
    BATTERY_48V(7, "48伏蓄电池", "48 Volt Battery"),
    VIRTUAL(8, "虚拟设备", "Virtual Equipment"),
    CROSS_VIRTUAL_EQUIPMENT(9, "跨站虚拟设备", "Cross Virtual Equipment");

    private final int id;
    private final String chineseName;
    private final String englishName;
}

