package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.FingerPrintService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (2024-09-11)
 **/
@Slf4j
@RestController
@RequestMapping("/fingerprint")
public class FingerPrintController {

    @Autowired
    private FingerPrintService fingerPrintService;


    @GetMapping(value = "/data-list-by-id", params = "fingerPrintId")
    public ResponseEntity<ResponseResult> findDataById(Integer fingerPrintId) {
        return ResponseHelper.successful(fingerPrintService.findDataById(fingerPrintId));
    }


    @GetMapping(value = "/data-list-by-id-no", params = {"fingerPrintId", "fingerPrintNo"})
    public ResponseEntity<ResponseResult> findDataByIdNo(Integer fingerPrintId, Integer fingerPrintNo) {
        return ResponseHelper.successful(fingerPrintService.findDataByIdNo(fingerPrintId, fingerPrintNo));
    }


}
