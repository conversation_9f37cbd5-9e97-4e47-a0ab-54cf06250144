package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 跨站表达式实例类型枚举
 *
 * <AUTHOR>
 * @date 2024/04/14
 */
@Getter
@AllArgsConstructor
public enum InstanceTypeEnum {
    REFERENCED_SAMPLER_UNIT_CONFIG(1,"按引用采集单元配置"),
    REFERENCED_SIGNAL_EXPRESSION_CONFIG(2,"按信号表达式配置"),
    REFERENCED_SAMPLER_UNIT_AND_SIGNAL_EXPRESSION_CONFIG(3,"按引用采集单元与信号表达式配置");
    private final int value;
    private final String describe;
}
