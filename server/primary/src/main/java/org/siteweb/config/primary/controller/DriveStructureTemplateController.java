package org.siteweb.config.primary.controller;

import org.siteweb.config.common.dto.DriveStructureTemplateDTO;
import org.siteweb.config.common.dto.DriveStructureTemplateVO;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.DriveStructureTemplateService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 驱动模板结构控制器
 *
 * <AUTHOR> (2024-05-15 19:57)
 */
@RestController
@RequestMapping
public class DriveStructureTemplateController {

    @Autowired
    private DriveStructureTemplateService driveStructureTemplateService;

    @GetMapping("/drivestructuretemplates")
    public ResponseEntity<ResponseResult> findAllDriveStructureTemplate() {
        return ResponseHelper.successful(driveStructureTemplateService.findAllDriveStructureTemplate());
    }

    /**
     * 根据驱动模板id查询驱动树结构
     */
    @GetMapping("/drivestructuretemplates/findbydrivetemplateid/{drivetemplateid}")
    public ResponseEntity<ResponseResult> findByDriveTemplateId(@PathVariable("drivetemplateid") Integer driveTemplateId) {
        List<DriveStructureTemplateVO> result = driveStructureTemplateService.findByDriveTemplateIds(Collections.singletonList(driveTemplateId));
        return ResponseHelper.successful(driveStructureTemplateService.buildTree(result));
    }

    /**
     * 根据驱动结构id获取某个驱动结构
     */
    @GetMapping("/drivestructuretemplates/{drivestructuretemplateid}")
    public ResponseEntity<ResponseResult> findById(@PathVariable("drivestructuretemplateid") Integer driveStructureTemplateId) {
        return ResponseHelper.successful(driveStructureTemplateService.findById(driveStructureTemplateId));
    }

    @PostMapping(value = "/drivestructuretemplates")
    public ResponseEntity<ResponseResult> create(@RequestBody DriveStructureTemplateDTO driveStructureTemplateDTO) {
        return ResponseHelper.successful(driveStructureTemplateService.create(driveStructureTemplateDTO.build()));
    }

    @PutMapping(value = "/drivestructuretemplates")
    public ResponseEntity<ResponseResult> update(@RequestBody DriveStructureTemplateDTO driveStructureTemplateDTO) {
        return ResponseHelper.successful(driveStructureTemplateService.update(driveStructureTemplateDTO.build()));
    }

    @DeleteMapping(value = "/drivestructuretemplates/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDriveStructureTemplate(@PathVariable Integer id) {
        return ResponseHelper.successful(driveStructureTemplateService.deleteById(id));
    }

    @PostMapping(value = "/adddrivestructuretemplate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addDriveStructureTemplate(@RequestParam("file") MultipartFile file,
                                                                    @RequestParam("filePath") String filePath,
                                                                    @RequestParam("pid") Integer pid,
                                                                    @RequestParam("driveTemplateId") Integer driveTemplateId) throws IOException {
        return ResponseHelper.successful(driveStructureTemplateService.addDriveStructureTemplate(filePath, file, pid, driveTemplateId));
    }

    @GetMapping(value = "drivestructureplaceholders")
    public ResponseEntity<ResponseResult> findDriveStructurePlaceholders() {
        return ResponseHelper.successful(driveStructureTemplateService.getDriveStructurePlaceholder());
    }

}
