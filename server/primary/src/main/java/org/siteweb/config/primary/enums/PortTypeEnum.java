package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PortTypeEnum {
    STANDARD_SERIAL_PORT(1, "标准串口"),
    SNU_PORT(2, "<PERSON><PERSON>口"),
    SNMP_PORT(3, "<PERSON><PERSON><PERSON>口"),
    PSTN_INSPECTION_PORT(4, "PSTN巡检口"),
    VIRTUAL_PORT(5, "虚拟端口"),
    TERMINAL_SERVER_PORT(6, "终端服务器口"),
    PSTN_ALARM_CALLBACK_PORT(7, "PSTN告警回叫口"),
    PSTN_MANUAL_MAINTENANCE_PORT(8, "PSTN手动维护口"),
    OMC_PORT(9, "OMC口"),
    SYSTEM_ACCESS_PORT(10, "系统接入口"),
    ISDN_DIAL_BACKUP_PORT(11, "ISDN拨号备份口"),
    DEDICATED_LINE_BACKUP_PORT(12, "专线备份口"),
    IDU_IP_PORT(13, "IDU-IP口"),
    IDU_SMS_PORT(14, "IDU-SMS口"),
    IDU_IP_SMS_PORT(15, "IDU-IP-SMS口"),
    IDU_SERIAL_PORT(16, "IDU-Serial口"),
    DTU_PORT(17, "DTU口"),
    HOST_PORT(18, "Host口"),
    SIMPLE_LOGIC_CONTROL_PORT(19, "简单逻辑控制口"),
    IP_INSPECTION_PORT(20, "IP巡检口"),
    IP_ALARM_CALLBACK_PORT(21, "IP告警回叫口"),
    IP_MANUAL_MAINTENANCE_PORT(22, "IP手动维护口"),
    GPRS_INSPECTION_PORT(23, "GPRS巡检口"),
    GPRS_ALARM_CALLBACK_PORT(24, "GPRS告警回叫口"),
    GPRS_MANUAL_MAINTENANCE_PORT(25, "GPRS手动维护口"),
    GSM_INSPECTION_PORT(26, "GSM巡检口"),
    GSM_ALARM_CALLBACK_PORT(27, "GSM告警回叫口"),
    GSM_MANUAL_MAINTENANCE_PORT(28, "GSM手动维护口"),
    I2C_PORT(29, "I2C端口"),
    MDU_PORT(30, "MDU端口"),
    MOBILE_B_INTERFACE_ACCESS_CONTROL_PORT(31, "移动B接口门禁透传端口"),
    BACNET_PORT(32, "BACNet端口"),
    SNMP_ENDPOINT_PORT(33, "SNMP端口");
    private final int value;
    private final String describe;
}