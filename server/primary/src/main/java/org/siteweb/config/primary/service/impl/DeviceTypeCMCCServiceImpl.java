package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.entity.TblDeviceTypeCMCC;
import org.siteweb.config.common.mapper.TblDeviceTypeCMCCMapper;
import org.siteweb.config.primary.service.DeviceTypeCMCCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceTypeCMCCServiceImpl implements DeviceTypeCMCCService {
    @Autowired
    TblDeviceTypeCMCCMapper tblDeviceTypeCMCCMapper;

    @Override
    public List<IdValueDTO<Integer, String>> findIdValue() {
        List<TblDeviceTypeCMCC> tblDeviceTypeCMCCS = tblDeviceTypeCMCCMapper.selectList(Wrappers.emptyWrapper());
        return tblDeviceTypeCMCCS.stream()
                                 .map(type -> new IdValueDTO<>(type.getDeviceTypeId(), type.getDeviceTypeName()))
                                 .toList();
    }
}
