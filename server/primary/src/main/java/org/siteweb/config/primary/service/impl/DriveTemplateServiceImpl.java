package org.siteweb.config.primary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.DriveTemplate;
import org.siteweb.config.common.entity.TblDataItem;
import org.siteweb.config.common.mapper.DriveTemplateMapper;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.primary.service.DataItemService;
import org.siteweb.config.primary.service.DriveTemplateService;
import org.siteweb.config.toolkit.I18n.I18n;
import org.siteweb.config.toolkit.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (2024-05-14 16:48)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DriveTemplateServiceImpl implements DriveTemplateService {

    @Autowired
    DriveTemplateMapper driveTemplateMapper;
    @Autowired
    I18n i18n;

    @Autowired
    private DataItemService dataItemService;

    @Override
    public List<DriveTemplate> findAllByType(Integer type) {
        LambdaQueryWrapper<DriveTemplate> wrapper = Wrappers.lambdaQuery();
        if (!(Objects.isNull(type) || type < 0)) {
            wrapper.eq(DriveTemplate::getDriveTemplateType, type);
        }
        return driveTemplateMapper.selectList(wrapper);
    }

    @Override
    public DriveTemplate findById(Integer driveTemplateId) {
        return driveTemplateMapper.selectById(driveTemplateId);
    }

    @Override
    @Transactional
    public DriveTemplate create(DriveTemplate driveTemplate) {
        this.driveTemplateTypeCheck(driveTemplate.getDriveTemplateType());
        handleDefaultTemplate(driveTemplate);
        return driveTemplateMapper.insert(driveTemplate) > 0 ? driveTemplate : null;
    }

    @Override
    @Transactional
    public DriveTemplate update(DriveTemplate driveTemplate) {
        this.driveTemplateTypeCheck(driveTemplate.getDriveTemplateType());
        handleDefaultTemplate(driveTemplate);
        return driveTemplateMapper.updateById(driveTemplate) > 0 ? driveTemplate : null;
    }

    /**
     * 处理默认模板逻辑，将同类型其他模板置为非默认状态
     * @param driveTemplate 驱动模板对象
     */
    private void handleDefaultTemplate(DriveTemplate driveTemplate) {
        // 当模板为默认模板时，则将其他模板类型都修改为非默认状态
        if (Objects.equals(driveTemplate.getIsDefaultTemplate(), 1)) {
            LambdaUpdateWrapper<DriveTemplate> updateWrapper = Wrappers.lambdaUpdate(DriveTemplate.class);
            updateWrapper.set(DriveTemplate::getIsDefaultTemplate, 0);
            updateWrapper.eq(DriveTemplate::getDriveTemplateType, driveTemplate.getDriveTemplateType());
            driveTemplateMapper.update(null, updateWrapper);
        }
    }

    @Override
    public Boolean deleteById(Integer id) {
        DriveTemplate driveTemplate = this.findById(id);
        if (Objects.isNull(driveTemplate)) {
            throw new BusinessException(i18n.T("batchTool.driverTemplate.NotExists"));
        }
        if (Objects.equals(driveTemplate.getIsDefaultTemplate(),1)) {
            throw new BusinessException(i18n.T("batchTool.driverTemplate.DefaultTemplateProhibitDeletion"));
        }
        return driveTemplateMapper.deleteById(id) > 0;
    }

    @Override
    public Integer getDefaultTemplateByType(Integer driveTemplateType) {
        LambdaQueryWrapper<DriveTemplate> wrapper = Wrappers.lambdaQuery(DriveTemplate.class)
                .select(DriveTemplate::getId)
                .eq(DriveTemplate::getIsDefaultTemplate, 1)
                .eq(DriveTemplate::getDriveTemplateType, driveTemplateType);
        DriveTemplate driveTemplate = driveTemplateMapper.selectOne(wrapper);
        return Objects.nonNull(driveTemplate) ? driveTemplate.getId() : null;
    }

    /**
     * 驱动模板类型校验
     */
    private void driveTemplateTypeCheck(Integer driveTemplateType) {
        TblDataItem dataItem = dataItemService.findByEntryIdAndItemId(DataEntryEnum.DRIVE_TEMPLATE_TYPE, driveTemplateType);
        if (Objects.isNull(dataItem)) {
            throw new BusinessException(i18n.T("batchTool.driverTemplateType.NotExists"));
        }
    }
}
