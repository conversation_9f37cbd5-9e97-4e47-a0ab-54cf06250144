package org.siteweb.config.primary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StationGradeEnum {
    VIP_STATION(1, "VIP基站"),
    GENERAL_STATION(2, "普通基站"),
    IMPORTANT_STATION(3, "重要基站"),
    BOUNDARY_STATION(4, "重要基站");


    /**
     * 值
     */
    private final int value;
    /**
     * 描述
     */
    private final String describe;
}
