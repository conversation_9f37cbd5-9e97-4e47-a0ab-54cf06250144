package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.MonitorUnitExtendService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/6/5
 */
@Slf4j
@RestController
@RequestMapping("/monitorunitevent")
public class MonitorUnitExtendController {

    @Autowired
    private MonitorUnitExtendService monitorUnitExtendService;

    // 根据id查询是否已经设置了密码



    @GetMapping(value = "/haspassword")
    public ResponseEntity<ResponseResult> findMonitorUnitPassword(@RequestParam List<Integer> monitorUnitIds) {
        return ResponseHelper.successful(monitorUnitExtendService.findMonitorUnitPassword(monitorUnitIds));
    }

}
