
mvn clean package \
-Dmaven.test.skip=true \
-Dmaven.multiModuleProjectDirectory=$MAVEN_HOME \
-Dmaven.wagon.http.ssl.insecure=true \
-Dmaven.wagon.http.ssl.allowall=true \
-Dmaven.wagon.http.ssl.ignore.validity.dates=true


mvn package

mvn install:install-file -Dfile=client/target/client-1.0.0-release.jar

mvn install:install-file \
    -Dfile=target/api-automation-core-1.0.2.jar \
    -DgroupId=com.github.dylanz666 \
    -DartifactId=api-automation-core \
    -Dversion=1.0.2 \
    -Dpackaging=jar
