package org.siteweb.config.client.provider;

import com.fasterxml.jackson.core.type.TypeReference;
import org.siteweb.config.common.dto.CreateEquipmentDto;
import org.siteweb.config.common.dto.EquipmentDetailDTO;
import org.siteweb.config.common.entity.TblEquipment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> (2024-06-12)
 **/
@Service
public class EquipmentProvider {

    @Autowired
    private HTTPProvider httpProvider;

    public List<TblEquipment> findAll() {
        return httpProvider.get("api/config/equipment/list", new TypeReference<List<TblEquipment>>() {} );
    }

    public TblEquipment findByID(Integer equipmentId) {
        return httpProvider.get("api/config/equipment/config/" + equipmentId, TblEquipment.class);
    }

    public TblEquipment create(CreateEquipmentDto equipment) {
        return httpProvider.post("api/config/equipment/config", equipment, TblEquipment.class);
    }

    public TblEquipment update(EquipmentDetailDTO equipment) {
        return httpProvider.put("api/config/equipment/config", equipment, TblEquipment.class);
    }

    public boolean delete(Integer equipmentId) {
        return httpProvider.delete("api/config/equipment/config/" + equipmentId, boolean.class);
    }

}
