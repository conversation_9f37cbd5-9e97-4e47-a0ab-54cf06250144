package org.siteweb.config.client.provider;

import org.siteweb.config.common.entity.TblCard;
import org.siteweb.config.common.entity.TblFaceData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR> (2024-09-26)
 **/

@Service
public class CardProvider {
    @Autowired
    private HTTPProvider httpProvider;



    public TblCard findCardById(Integer cardId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("cardId", cardId);
        return httpProvider.get("api/config/card/config/by-id", param, TblCard.class);
    }

    public TblCard findCardByCode(String cardCode) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("cardCode", cardCode);
        return httpProvider.get("api/config/card/config/by-code", param, TblCard.class);
    }
}
