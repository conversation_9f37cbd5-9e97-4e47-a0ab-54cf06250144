package org.siteweb.config.client.provider;

import org.apache.commons.lang3.StringUtils;
import org.siteweb.config.common.dto.ControlConfigItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CommandProvider {
    @Autowired
    private HTTPProvider httpProvider;

    public ControlConfigItem create(ControlConfigItem controlConfigItem) {
        return httpProvider.post("api/config/control/create", controlConfigItem, ControlConfigItem.class);
    }

    public Boolean batchdelete(Integer equipmentTemplateId, List<Integer> controlIds) {
        Map<String, Object> delParam = new HashMap<>();
        delParam.put("equipmentTemplateId", equipmentTemplateId);
        delParam.put("controlIds", StringUtils.join(controlIds,","));
        return httpProvider.delete("api/config/control/batchdelete", delParam, Boolean.class);
    }

    public ControlConfigItem update(ControlConfigItem controlConfigItem) {
        return httpProvider.put("api/config/control/update", controlConfigItem, ControlConfigItem.class);
    }

    public ControlConfigItem getControlInfo(Integer equipmentTemplateId, Integer controlId) {
        return httpProvider.get("api/config/control/controlinfo/" + equipmentTemplateId + "/" + controlId, ControlConfigItem.class);
    }


    public ControlConfigItem getControlForEquipment(Integer stationId, Integer equipmentId, Integer controlId) {
        Map<String, Object> params = Map.of(
                "sId", stationId != null ? stationId : "",
                "eqId", equipmentId,
                "cId", controlId
        );
        return httpProvider.get("api/config/control/info/for-equipment", params, ControlConfigItem.class);  // ?sId={stationId}&eqId={equipmentId}&cId={controlId}
    }

}
