package org.siteweb.config.client.provider;

import com.fasterxml.jackson.core.type.TypeReference;
import org.siteweb.config.common.entity.TblFaceData;
import org.siteweb.config.common.entity.TblFingerPrint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> (2024-09-11)
 **/

@Service
public class FingerPrintProvider {
    @Autowired
    private HTTPProvider httpProvider;


    public List<TblFingerPrint> findListById(String fingerPrintId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("fingerPrintId", fingerPrintId);
        return httpProvider.get("api/config/fingerprint/data-list-by-id", param, new TypeReference<List<TblFingerPrint>>() {
        });
    }


    public List<TblFingerPrint> findListById(String fingerPrintId, String fingerPrintNo) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("fingerPrintId", fingerPrintId);
        param.put("fingerPrintNo", fingerPrintNo);
        return httpProvider.get("api/config/fingerprint/data-list-by-id-no", param, new TypeReference<List<TblFingerPrint>>() {
        });
    }

}
