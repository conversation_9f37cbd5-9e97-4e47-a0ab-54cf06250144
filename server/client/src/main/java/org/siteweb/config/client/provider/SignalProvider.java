package org.siteweb.config.client.provider;

import org.apache.commons.lang3.StringUtils;
import org.siteweb.config.common.dto.SignalConfigItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SignalProvider {
    @Autowired
    private HTTPProvider httpProvider;

    public SignalConfigItem create(SignalConfigItem signalConfigItem) {
        return httpProvider.post("api/config/signal/create", signalConfigItem, SignalConfigItem.class);
    }

    public Boolean batchdelete(Integer equipmentTemplateId, List<Integer> signalIdList){
        Map<String,Object> delParam = new HashMap<>();
        delParam.put("eqTemplateId",equipmentTemplateId);
        delParam.put("signalIds", StringUtils.join(signalIdList,","));
        return httpProvider.delete("api/config/signal/batchdelete",delParam,Boolean.class);
    }

    public SignalConfigItem update(SignalConfigItem signalConfigItem) {
        return httpProvider.put("api/config/signal/update", signalConfigItem, SignalConfigItem.class);
    }

    public SignalConfigItem getSignalInfo(Integer equipmentTemplateId,Integer signalId) {
        return httpProvider.get("api/config/signal/signalinfo/"+equipmentTemplateId+"/"+signalId, SignalConfigItem.class);
    }
}
