package org.siteweb.config.client;

import lombok.Getter;

/**
 * 响应结果失败，Code 非0
 *
 * <AUTHOR> (2024-06-12)
 **/

public class ClientException extends RuntimeException {

    @Getter
    private  ExceptionCode code ;


    public ClientException(ExceptionCode code, String message) {
        super(message);
        this.code = code;
    }

    public ClientException(ExceptionCode code, Exception exception) {
        super(exception);
        this.code = code;
    }


    public enum ExceptionCode {

        /**
         * 不确定的，未知的错误
         */
        UNKNOWN(-1),

        /**
         * 服务器连接初始化失败
         */
        SERVER_INITIALIZE_FAILED(1),


        /**
         * 网络异常
         */
        NETWORK_ERROR(2),


        /**
         * 接口业务返回失败
         */
        BUSINESS_FAILED(3);


        ExceptionCode(int code){

        }

    }


}
