package org.siteweb.config.client.provider;

import org.siteweb.config.common.dto.StructureTreeNodeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> (2024-06-12)
 **/
@Service
public class ResourceStructureProvider {

    @Autowired
    private HTTPProvider httpProvider;

    public StructureTreeNodeDTO getTree(Integer structureID, Integer depth, Boolean eqs) {
        Map<String, Object> params = Map.of(
                "rid", structureID != null ? structureID : "",
                "depth", depth != null ? depth : "",
                "eqs", eqs != null ? eqs : ""
        );
        return httpProvider.get("api/config/resource-structure/tree?rid={rid}&depth={depth}&eqs={eqs}", params, StructureTreeNodeDTO.class);
    }

    public StructureTreeNodeDTO getTree() {
        return httpProvider.<StructureTreeNodeDTO>get("api/config/resource-structure/tree", StructureTreeNodeDTO.class);
    }

    public Integer getTreeRootId(){
        return httpProvider.<Integer>get("api/config/resource-structure/tree-root", Integer.class);
    }

    public Boolean reloadTree(){
        return httpProvider.<Boolean>get("api/config/resource-structure/reload", Boolean.class);
    }
}
