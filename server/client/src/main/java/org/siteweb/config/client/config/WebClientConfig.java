package org.siteweb.config.client.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.ClientCodecConfigurer;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.resources.LoopResources;

import java.time.Duration;

/**
 * <AUTHOR> (2024-05-21)
 **/
@Configuration
public class WebClientConfig {

    @Autowired
    private ClientProperties clientProperties;

    @Autowired
    private ObjectMapper objectMapper;



    @Bean("siteweb-config-webclient")
    public WebClient webClientBuilder() {
        // 自定义连接提供者
        ConnectionProvider connectionProvider = ConnectionProvider.builder("config-client")
                .maxConnections(100) // 设置最大连接数
                .maxIdleTime(Duration.ofSeconds(60)) //线程最大空闲60秒
                .pendingAcquireMaxCount(500) // 设置最大等待连接的队列大小
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置连接超时时间
                .build();
        // 自定义事件循环资源
        LoopResources loopResources = LoopResources.create("config-client", 4, true); // 4 是线程池的大小
        // 创建自定义 HttpClient
        HttpClient httpClient = HttpClient.create(connectionProvider).runOn(loopResources);

        // 构建 WebClient
        return WebClient
                .builder()
                .codecs(configure -> configure.defaultCodecs().maxInMemorySize(200 * 1024 * 1024))  // 设置为 200MB
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(clientCodecConfigurer -> {
                    ClientCodecConfigurer.DefaultCodecs defaultCodecs = clientCodecConfigurer.defaultCodecs();
                    defaultCodecs.jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper));
                    defaultCodecs.jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper));
                })

                .baseUrl(clientProperties.getBaseUrl())
                .build();
    }


}
