package org.siteweb.config.client.config;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.siteweb.config.common.change.ChangeMessageListener;
import org.siteweb.config.common.change.ChangeMessageScheduler;
import org.siteweb.config.common.core.ApplicationProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR> (2024-06-12)
 **/
@Slf4j
@Configuration
public class MqttConfigure {
    @Autowired
    private ClientProperties clientProperties;

    @Autowired
    public ChangeMessageScheduler messageScheduler;

    @Bean()
    @Primary
    public MqttAsyncClient mqttClient(ClientProperties clientProperties, ApplicationProperties applicationProperties) throws UnknownHostException, MqttException {
        InetAddress inetAddress = InetAddress.getLocalHost();
        MqttClientPersistence persistence = new MemoryPersistence(); // MemoryPersistence MqttDefaultFilePersistence
        MqttAsyncClient mqttClient = new MqttAsyncClient(applicationProperties.getMqtt().getBroker(), clientProperties.getIdPrefix() + "-" + inetAddress.getHostName(), persistence);
        if (!applicationProperties.isInvalid()) {
            ChangeMessageListener listener = new ChangeMessageListener(mqttClient, messageScheduler);
            mqttClient.setCallback(listener);
            MqttConnectOptions options = new MqttConnectOptions();
            options.setUserName(applicationProperties.getMqtt().getUser());
            options.setPassword(applicationProperties.getMqtt().getPassword().toCharArray());
            options.setAutomaticReconnect(true);
            options.setMaxReconnectDelay(1000);
            options.setConnectionTimeout(100);
            options.setKeepAliveInterval(60);
            options.setCleanSession(false);
            options.setMaxInflight(1024);
            IMqttToken connectToken = mqttClient.connect(options);
            connectToken.waitForCompletion();
            // 订阅变更通知相关topic
            mqttClient.subscribe("gateway/config/+/+/create/+", 2);
            mqttClient.subscribe("gateway/config/+/+/delete/+", 2);
            mqttClient.subscribe("gateway/config/+/+/update/+", 2);
        } else {
            log.warn("Siteweb.Config.Client: 缺失服务器配置，跳过MQTT连接。");
        }
        return mqttClient;
    }
}
