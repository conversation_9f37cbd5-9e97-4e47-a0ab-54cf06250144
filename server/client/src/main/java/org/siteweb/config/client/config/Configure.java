package org.siteweb.config.client.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.core.ApplicationProperties;
import org.siteweb.config.common.utils.EncryptUtil;
import org.siteweb.config.common.utils.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR> (2024-06-12)
 **/
@Slf4j
@Configuration
public class Configure {
    @Autowired
    @Qualifier("siteweb-config-webclient")
    private WebClient webClient;
    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private WebClientConfig webClientConfig;

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public ApplicationProperties applicationProperties() {
        try {
            var result = webClient.get()
                    .uri("api/config/client/properties")
                    .retrieve()
                    .bodyToMono(ResponseResult.class)
                    .block();
            if (result == null || result.getCode() != 0) throw new RuntimeException();
            String textValue = EncryptUtil.decrypt(result.getData().toString(), EncryptUtil.DEFAULT_API_KEY);
            ApplicationProperties properties = objectMapper.readValue(textValue, ApplicationProperties.class);
            return properties;
        } catch (Exception e) {
            log.error("Siteweb.Config.Client: 从服务器加载配置失败。", e);
            return ApplicationProperties.Invalid;
        }
    }


}
