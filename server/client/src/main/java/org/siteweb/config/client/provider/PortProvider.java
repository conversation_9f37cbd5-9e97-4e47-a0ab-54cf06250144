package org.siteweb.config.client.provider;

import org.siteweb.config.common.entity.TslPort;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * @ClassName: PortProvider
 * @descriptions: 采集单元端口业务提供类
 * @author: xsx
 * @date: 2024/8/23 10:01
 **/
@Service
public class PortProvider {
    @Autowired
    private HTTPProvider httpProvider;

    public TslPort create(TslPort tslPort){
        return httpProvider.post("api/config/port/config", tslPort, TslPort.class);
    }

    public Integer getMaxPortByMonitorUnitId(Integer monitorUnitId){
        HashMap<String, Object> param = new HashMap<>();
        param.put("monitorUnitId",monitorUnitId);
        return httpProvider.get("api/config/port/maxportno", param, Integer.class);
    }
}
