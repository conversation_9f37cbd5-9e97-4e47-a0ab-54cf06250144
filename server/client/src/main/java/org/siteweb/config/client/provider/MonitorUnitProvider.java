package org.siteweb.config.client.provider;

import com.fasterxml.jackson.core.type.TypeReference;
import org.siteweb.config.common.dto.CreateMonitorUnitDTO;
import org.siteweb.config.common.dto.MonitorUnitDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> (2024-05-21)
 **/
@Service
public class MonitorUnitProvider {

    @Autowired
    private HTTPProvider httpProvider;

    public List<MonitorUnitDTO> findAll() {
        return httpProvider.get("api/config/monitor-unit", new TypeReference<List<MonitorUnitDTO>>() {});
    }

    public MonitorUnitDTO findByID(Integer monitorUnitId) {
        return httpProvider.get("api/config/monitor-unit/config/" + monitorUnitId, MonitorUnitDTO.class);
    }

    public MonitorUnitDTO update(MonitorUnitDTO monitorUnit) {
        return httpProvider.put("api/config/monitor-unit/config", monitorUnit, MonitorUnitDTO.class);
    }

    public MonitorUnitDTO create(CreateMonitorUnitDTO monitorUnit) {
        return httpProvider.post("api/config/monitor-unit/config", monitorUnit, MonitorUnitDTO.class);
    }


    public boolean delete(Integer monitorUnitId) {
        return httpProvider.delete("api/config/monitor-unit/config/" + monitorUnitId, boolean.class);
    }

    public boolean deleteAndDeleteEqu(Integer monitorUnitId,Boolean isDelEqs) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("isDelEqs",isDelEqs);
        return httpProvider.delete("api/config/monitor-unit/config/" + monitorUnitId,param, boolean.class);
    }

}
