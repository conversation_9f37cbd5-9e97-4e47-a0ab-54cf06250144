package org.siteweb.config.client.provider;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.siteweb.config.common.entity.TslSamplerUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class SamplerUnitProvider {
    @Autowired
    private HTTPProvider httpProvider;

    public List<TslSamplerUnit> selectSamplerUnitWithPort(Integer monitorUnitId){
        Map<String,Object> params = MapUtil.of("monitorUnitId",monitorUnitId);
        return httpProvider.get("api/config/sampler-unit/samplerunitwithport", params, new TypeReference<List<TslSamplerUnit>>(){});
    }

    public TslSamplerUnit create(TslSamplerUnit samplerUnit){
        return httpProvider.post("api/config/sampler-unit/config", samplerUnit, TslSamplerUnit.class);
    }
}
