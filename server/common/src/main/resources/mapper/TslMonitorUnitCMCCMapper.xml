<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TslMonitorUnitCMCCMapper">
    <update id="updateEntity">
        UPDATE tsl_monitorunitcmcc
        SET fsuID       = #{tslMonitorUnitCMCC.fsuID},
            fsuName     = #{tslMonitorUnitCMCC.fsuName},
            roomID      = #{tslMonitorUnitCMCC.roomID},
            roomName    = #{tslMonitorUnitCMCC.roomName},
            userName    = #{tslMonitorUnitCMCC.userName},
            passWord    = #{tslMonitorUnitCMCC.passWord},
            fsuIP       = #{tslMonitorUnitCMCC.fsuIP},
            ftpUserName = #{tslMonitorUnitCMCC.ftpUserName},
            ftpPassWord = #{tslMonitorUnitCMCC.ftpPassWord}
        WHERE stationId = #{tslMonitorUnitCMCC.stationId}
          AND monitorUnitId = #{tslMonitorUnitCMCC.monitorUnitId}
    </update>
    <update id="updateSiteIdByStationId">
        UPDATE TSL_MonitorUnitCMCC
        SET SiteID = #{siteId}
        WHERE StationId = #{stationId}
    </update>
    <update id="updateMonitorUnitNameInSiteWeb">
        UPDATE TSL_MonitorUnit
        SET MonitorUnitName = #{suName},
            IPAddress       = #{suIp}
        WHERE StationId = #{stationId}
          AND MonitorUnitId = #{monitorUnitId}
    </update>
    <select id="findMonitorUnitsByCategory" resultType="org.siteweb.config.common.entity.TslMonitorUnitCMCC">
        SELECT mu.StationId, mu.MonitorUnitId, mu.MonitorUnitName as fsuName, mu.IpAddress as fsuIp
        FROM TSL_MonitorUnit mu
        WHERE NOT EXISTS
            (SELECT *
             FROM TSL_MonitorUnitCMCC muCMCC
             WHERE mu.MonitorUnitId = muCMCC.MonitorUnitId
               AND mu.StationId = muCMCC.StationId)
          AND MonitorUnitCategory = #{categoryId}
    </select>
    <select id="findAll" resultType="org.siteweb.config.common.vo.binterface.MonitorUnitCMCCVO">
        SELECT mu.stationid,
               mu.monitorunitid,
               mu.fsuid,
               mu.fsuname,
               mu.siteid,
               station.sitename,
               mu.roomid,
               mu.roomname,
               mu.username,
               mu.password,
               mu.fsuip,
               mu.ftpusername,
               mu.ftppassword
        FROM TSL_MonitorUnitCMCC mu
                 LEFT JOIN tbl_stationcmcc station ON mu.StationId = station.StationId
    </select>
</mapper>