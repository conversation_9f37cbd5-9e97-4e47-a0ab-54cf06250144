<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblSwatchStationMapper">
    <select id="selectAll" resultType="org.siteweb.config.common.entity.TblSwatchStation">
        select ts.*, ts2.StationName  from tbl_swatchstation ts left join tbl_station ts2 on ts.StationId = ts2.StationId
    </select>


    <insert id="insertEquipmentCMCC">
        insert into tbl_equipmentcmcc (StationId, MonitorUnitId, EquipmentId, DeviceID, DeviceName, FSUID,SiteName, RoomName,
        DeviceType, DeviceSubType, Model, Brand, RatedCapacity, Version, BeginRunTime,ExtendField1)
        select #{stationId}, C.MonitorUnitId, <PERSON>.<PERSON>, '', <PERSON><PERSON>Device<PERSON>ame, '',<PERSON><PERSON>, <PERSON><PERSON>, C.<PERSON>ce<PERSON>ype, C.<PERSON>ce<PERSON>ubType,
        C.<PERSON>, C.<PERSON>, C.RatedCapacity, C.Version, C.BeginRunTime,E.EquipmentName from tbl_equipmentcmcc C,tbl_equipment E where
        C.StationId = E.StationId and C.EquipmentId = E.EquipmentId and  C.StationId = #{swatchStationId} ;
    </insert>
    <update id="updateEquipmentCMCCEquipmentid">
        update tbl_equipmentcmcc e inner join
        tbl_equipment m ON e.stationid = m.stationid and e.ExtendField1 = m.equipmentname and  e.stationid =#{stationId} SET e.equipmentid = m.equipmentid;
    </update>
    <update id="updateEquipmentCMCCMonitorUnitId">
        update tbl_equipmentcmcc e inner join tbl_equipment m ON e.stationid = m.stationid and e.equipmentid = m.equipmentid and  e.stationid =#{stationId} SET e.MonitorUnitId = m.MonitorUnitId;
    </update>
</mapper>