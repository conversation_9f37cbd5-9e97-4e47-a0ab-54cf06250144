<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStandardDicEventMapper">
    <resultMap id="StandardDicEventIniMap" type="org.siteweb.config.common.dto.StandardDicEventIniDTO">
        <result property="standardDicId" column="StandardDicId"/>
        <result property="stationCategory" column="StationCategory"/>
        <result property="eventLogicClass" column="EventLogicClass"/>
        <result property="eventStandardName" column="EventStandardName"/>
        <result property="netManageId" column="NetManageId"/>
        <collection property="baseTypeList" ofType="org.siteweb.config.common.dto.BaseTypeInnerDTO" column="NetManageId" notNullColumn="NetManageId">
            <result property="baseTypeId" column="BaseTypeId"/>
            <result property="baseTypeName" column="BaseTypeName"/>
        </collection>
    </resultMap>

    <update id="updateEntity">
        UPDATE tbl_standarddicevent
        SET
            EquipmentLogicClassId = #{standardDicEvent.equipmentLogicClassId},
            EquipmentLogicClass = #{standardDicEvent.equipmentLogicClass},
            EventLogicClassId = #{standardDicEvent.eventLogicClassId},
            EventLogicClass = #{standardDicEvent.eventLogicClass},
            EventClass = #{standardDicEvent.eventClass},
            EventStandardName = #{standardDicEvent.eventStandardName},
            NetManageId = #{standardDicEvent.netManageId},
            EventSeverity = #{standardDicEvent.eventSeverity},
            CompareValue = #{standardDicEvent.compareValue},
            StartDelay = #{standardDicEvent.startDelay},
            Meanings = #{standardDicEvent.meanings},
            EquipmentAffect = #{standardDicEvent.equipmentAffect},
            BusinessAffect = #{standardDicEvent.businessAffect},
            StationCategory = #{standardDicEvent.stationCategory},
            ModifyType = #{standardDicEvent.modifyType},
            Description = #{standardDicEvent.description},
            ExtendFiled1 = #{standardDicEvent.extendFiled1},
            ExtendFiled2 = #{standardDicEvent.extendFiled2},
            ExtendFiled3 = #{standardDicEvent.extendFiled3}
        WHERE StandardDicId = #{standardDicEvent.standardDicId}
          AND StandardType = #{standardDicEvent.standardType}
    </update>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT StandardDicId as id,EventStandardName as value FROM tbl_standarddicevent WHERE StandardDicId IN
        <foreach collection="standardDicIds" item="standardDicId" open="(" close=")" separator=",">
            #{standardDicId}
        </foreach>
    </select>

    <select id="getStandardDicEventInis" resultMap="StandardDicEventIniMap">
        SELECT
        event.StandardDicId,
        event.StationCategory,
        event.EventLogicClass,
        event.EventStandardName,
        event.NetManageId,
        base.BaseTypeId,
        base.BaseTypeName
        FROM
        TBL_StandardDicEvent event
        LEFT JOIN(
        SELECT
        a.StandardDicId,
        a.StationBaseType,
        a.StandardType,
        a.BaseTypeId,
        b.BaseTypeName
        FROM
        TBL_EventBaseMap a
        LEFT JOIN TBL_EventBaseDic B ON
        a.BaseTypeId = B.BaseTypeId
        ) base
        ON
        base.StandardDicId = event.StandardDicId
        AND base.StationBaseType = event.StationCategory
        AND base.StandardType = event.StandardType
        WHERE
        event.StandardType = #{standardId}
    </select>
</mapper>