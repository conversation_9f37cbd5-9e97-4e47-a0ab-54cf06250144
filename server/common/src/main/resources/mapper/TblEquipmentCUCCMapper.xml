<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEquipmentCUCCMapper">
    <update id="updateEntity">
        UPDATE TBL_EquipmentCUCC
        SET deviceId   = #{tblEquipmentCUCC.deviceId},
            deviceName = #{tblEquipmentCUCC.deviceName},
            deviceRID  = #{tblEquipmentCUCC.deviceRId}
        WHERE stationId = #{tblEquipmentCUCC.stationId}
          AND equipmentId = #{tblEquipmentCUCC.equipmentId}
    </update>
    <update id="updateSUID">
        UPDATE TBL_EquipmentCUCC
        SET SUID = #{suId}
        WHERE StationId = #{stationId}
          AND MonitorUnitId = #{monitorUnitId}
    </update>
    <select id="findVO" resultType="org.siteweb.config.common.vo.binterface.EquipmentCUCCVO">
        SELECT equipment.StationId,
               station.StationName,
               equipment.EquipmentId,
               equipment.DeviceID,
               equipment.DeviceName,
               equipment.DeviceRID,
               equipment.SUID,
               monitorUnit.SUName AS suName
        FROM tbl_equipmentcucc equipment
                 LEFT JOIN tsl_monitorunitcucc monitorUnit ON equipment.SUID = monitorUnit.SUID AND equipment.MonitorUnitId = monitorunit.MonitorUnitId
                 LEFT JOIN TBL_Station station ON equipment.StationId = station.StationId
    </select>
</mapper>