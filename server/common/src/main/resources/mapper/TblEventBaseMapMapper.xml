<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEventBaseMapMapper">


    <select id="getEventBaseMap" resultType="org.siteweb.config.common.dto.EventBaseMapDTO">
        SELECT mapEvent.StandardDicId, mapEvent.StationBaseType, mapEvent.BaseTypeId, sbd.BaseTypeName
        FROM TBL_EventBaseMap mapEvent
        INNER JOIN TBL_EventBaseDic sbd ON sbd.BaseTypeId = mapEvent.BaseTypeId
        WHERE mapEvent.StandardType = #{standardId}
    </select>
    <select id="getEventBaseMapExport" resultType="org.siteweb.config.common.dto.EventBaseMapExportDTO">
        SELECT map.StandardType, st.StandardName, sde.EquipmentLogicClassId, sde.EquipmentLogicClass, sde.EventLogicClassId, sde.EventLogicClass,
        map.StandardDicId, sde.EventStandardName, sde.Meanings, map.StationBaseType,
        sbt.Type, ebt.BaseEquipmentId, ebt.BaseEquipmentName, map.BaseTypeId, ebd.BaseTypeName
        FROM TBL_EventBaseMap map
        INNER JOIN TBL_StandardDicEvent sde ON sde.StandardDicId = map.StandardDicId AND sde.StandardType = #{standardId}
        INNER JOIN TBL_EventBaseDic ebd ON ebd.BaseTypeId = map.BaseTypeId
        INNER JOIN TBL_EquipmentBaseType ebt ON ebt.BaseEquipmentId= ebd.BaseEquipmentId
        INNER JOIN TBL_StationBaseType sbt ON sbt.Id= map.StationBaseType AND sbt.StandardId = #{standardId}
        INNER JOIN TBL_StandardType st ON st.StandardId = map.StandardType
        WHERE map.StandardType = #{standardId}
        ORDER BY sde.EquipmentLogicClassId ASC, sde.EventLogicClassId ASC, map.StandardDicId ASC,
        map.StationBaseType ASC,ebt.BaseEquipmentId ASC,ebd.BaseTypeId ASC
    </select>
</mapper>