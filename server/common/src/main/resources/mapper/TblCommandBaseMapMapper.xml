<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblCommandBaseMapMapper">


    <select id="getControlBaseMap" resultType="org.siteweb.config.common.dto.CommandBaseMapDTO">
        SELECT mapControl.StandardDicId, mapControl.StationBaseType, mapControl.BaseTypeId, sbd.BaseTypeName
        FROM TBL_CommandBaseMap mapControl
        INNER JOIN TBL_CommandBaseDic sbd ON sbd.BaseTypeId = mapControl.BaseTypeId
        WHERE mapControl.StandardType = #{standardId}
    </select>
    <select id="getCommandBaseMapExport" resultType="org.siteweb.config.common.dto.CommandBaseMapExportDTO">
        SELECT map.StandardType, st.StandardName, sdc.EquipmentLogicClassId, sdc.EquipmentLogicClass, sdc.ControlLogicClassId,
        sdc.ControlLogicClass, map.StandardDicId, sdc.ControlStandardName, map.StationBaseType, sbt.Type, ebt.BaseEquipmentId,
        ebt.BaseEquipmentName, map.BaseTypeId, cbd.BaseTypeName
        FROM TBL_CommandBaseMap map
        INNER JOIN TBL_StandardDicControl sdc ON sdc.StandardDicId = map.StandardDicId AND sdc.StandardType = #{standardId}
        INNER JOIN TBL_CommandBaseDic cbd ON cbd.BaseTypeId = map.BaseTypeId
        INNER JOIN TBL_EquipmentBaseType ebt ON ebt.BaseEquipmentId= cbd.BaseEquipmentId
        INNER JOIN TBL_StationBaseType sbt ON sbt.Id= map.StationBaseType AND sbt.StandardId = #{standardId}
        INNER JOIN TBL_StandardType st ON st.StandardId = map.StandardType
        WHERE map.StandardType = #{standardId}
    </select>
</mapper>