<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblDoorTimeGroupMapper">
    <delete id="deleteByEquipmentId">
        <!-- adaptive-multi-pg DELETE修改成通用sql  -->
        DELETE FROM TBL_DoorTimeGroup m
        WHERE EXISTS (
        SELECT 1
        FROM TBL_Door d
        WHERE m.DoorId = d.DoorId
        AND d.EquipmentId = #{equipmentId}
        )
    </delete>
</mapper>