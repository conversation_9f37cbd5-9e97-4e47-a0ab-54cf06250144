<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEventBaseDicMapper">
    <insert id="generateEventBaseDic">
        INSERT INTO TBL_EventBaseDic
        (
            BaseTypeId,
            BaseTypeName,
            BaseEquipmentId,
            EnglishName,
            EventSeverityId,
            ComparedValue,
            BaseLogicCategoryId,
            StartDelay,
            ExtendField1,
            ExtendField2,
            ExtendField3,
            ExtendField4,
            ExtendField5,
            Description,
            BaseNameExt,
            IsSystem
        )
        SELECT
            #{baseTypeId},
            T.BaseTypeName,
            T.BaseEquipmentId,
            T.EnglishName,
            T.EventSeverityId,
            T.ComparedValue,
            T.BaseLogic<PERSON>ategoryId,
            T<PERSON>,

            T.<PERSON>tendField1,
            T<PERSON><PERSON>ield2,
            T<PERSON>ExtendField3,
            <PERSON><PERSON>ExtendField4,
            <PERSON><PERSON>ield5,
            T.Description,
            T.BaseNameExt,
            0
        FROM TBL_EventBaseDic T WHERE T.BaseTypeId=#{sourceId};
    </insert>
    <select id="findEventBaseDic" resultType="org.siteweb.config.common.entity.TblEventBaseDic">
        <!-- adaptive-multi-pg BaseTypeId数字类型pg不支持模糊查询   -->
        SELECT BaseTypeId,BaseTypeName,BaseNameExt FROM TBL_EventBaseDic
        WHERE BaseEquipmentId = (#{eqTypeId} * 100 + #{eqSubTypeId})
        <if test="baseTypeId != null and baseTypeId != ''">
            AND BaseTypeId::varchar LIKE CONCAT('%', #{baseTypeId},'%')
        </if>
        <if test="baseTypeName != null and baseTypeName != ''">
            AND BaseTypeName LIKE CONCAT('%', #{baseTypeName},'%')
        </if>
        <if test="baseNameExt != null and baseNameExt != ''">
            AND BaseNameExt LIKE CONCAT('%', #{baseNameExt},'%')
        </if>
        order by BaseTypeId ASC
    </select>
    <sql id="eventBaseDicSql">
        SELECT
        eb.BaseTypeId,
        eb.BaseTypeName,
        eb.BaseEquipmentId,
        eb.EnglishName,
        eb.EventSeverityId,
        eb.ComparedValue,
        eb.BaseLogicCategoryId,
        eb.StartDelay,
        eb.EndDelay,
        eb.ExtendField1,
        eb.ExtendField2,
        eb.ExtendField3,
        eb.ExtendField4,
        eb.ExtendField5,
        eb.BaseNameExt,
        eb.Description,
        eb.IsSystem,
        teb.BaseEquipmentName,
        tbc.BaseClassName,
        tlb.BaseLogicCategoryName
        FROM
        tbl_eventbasedic eb
        LEFT JOIN tbl_equipmentbasetype teb ON eb.BaseEquipmentId = teb.BaseEquipmentId
        LEFT JOIN tbl_baseclassdic tbc ON teb.EquipmentTypeId = tbc.BaseClassId
        LEFT JOIN tbl_logiccategorybasedic tlb ON eb.BaseLogicCategoryId = tlb.BaseLogicCategoryId
    </sql>
    <select id="findEventBaseDicList" resultType="org.siteweb.config.common.dto.EventBaseDictionaryDTO">
        <include refid="eventBaseDicSql"/>
    </select>
    <select id="findEventBaseDicDetail" resultType="org.siteweb.config.common.dto.EventBaseDictionaryDTO">
        <include refid="eventBaseDicSql"/>
        WHERE eb.BaseTypeId = #{baseTypeId}
    </select>

    <delete id="deleteLianTongByBaseTypeId">
        DELETE FROM TBL_EventBaseDic WHERE substring(RIGHT(BaseTypeId, 6), 1, 1) = #{baseTypeId}
    </delete>

    <insert id="createLianTongEventBaseDic">
        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101720001, '交流输入1线电压Uab超高告警', 101, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101721001, '交流输入1线电压Uab超低告警', 101, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101722001, '交流输入1线电压Ubc超高告警', 101, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101723001, '交流输入1线电压Ubc超低告警', 101, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101724001, '交流输入1线电压Uca超高告警', 101, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101725001, '交流输入1线电压Uca超低告警', 101, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101726001, '交流输入1A相电压超高告警', 101, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'A相电压超高告警', '交流输入{0}A相电压超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101727001, '交流输入1A相电压超低告警', 101, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'A相电压超低告警', '交流输入{0}A相电压超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101728001, '交流输入1B相电压超高告警', 101, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'B相电压超高告警', '交流输入{0}B相电压超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101729001, '交流输入1B相电压超低告警', 101, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'B相电压超低告警', '交流输入{0}B相电压超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101730001, '交流输入1C相电压超高告警', 101, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'C相电压超高告警', '交流输入{0}C相电压超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101731001, '交流输入1C相电压超低告警', 101, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'C相电压超低告警', '交流输入{0}C相电压超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101732001, 'A相电流超高告警', 101, 'Current Ia High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'A相电流大于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101733001, 'B相电流超高告警', 101, 'Current Ib High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'B相电流大于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101734001, 'C相电流超高告警', 101, 'Current Ic High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'C相电流超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101736001, '功率因数超低告警', 101, 'Power Factor Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '三相功率因数低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101737001, '频率超高告警', 101, 'Frequency High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '频率F高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101738001, '频率超低告警', 101, 'Frequency Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '频率F低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (101701001, '零线电流超高告警', 101, 'Neutral Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '零序电流Io大于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (102718001, '合母电压超低告警', 102, 'Power Bus Voltage Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (102719001, '控母电压超低告警', 102, 'Control Bus Voltage Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (102793001, '电池温度超高告警', 102, 'Battery Temperature High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (102702001, '合母电压超高告警', 102, 'Power Bus Voltage High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (102704001, '控母电压超高告警', 102, 'Control Bus Voltage High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201720001, '交流输入1线电压Uab超高告警', 201, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201721001, '交流输入1线电压Uab超低告警', 201, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201722001, '交流输入1线电压Ubc超高告警', 201, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201723001, '交流输入1线电压Ubc超低告警', 201, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201724001, '交流输入1线电压Uca超高告警', 201, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201725001, '交流输入1线电压Uca超低告警', 201, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201707001, '零线电流超高告警', 201, 'Neutral Current High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '零序电流Io高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201708001, '输出分路1相电流Ia超高告警', 201, 'Output Branch Current Ia High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ia高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ia超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201709001, '输出分路1相电流Ib超高告警', 201, 'Output Branch Current Ib High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ib高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ib超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (201710001, '输出分路1相电流Ic超高告警', 201, 'Output Branch Current Ic High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ic高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ic超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (203711001, '交流输入1频率超低告警', 203, 'AC Input Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入{0}频率超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (203712001, '交流输入1频率超高告警', 203, 'AC Input Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入{0}频率超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (204766001, '功率因数超低告警', 204, 'Power Factor Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '功率因数PF低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (204751001, '内部温度超高告警', 204, 'Internal Temperature High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '内部温度高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (205791001, 'IGBT温度超高告警', 205, 'IGBT Temperature High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'IGBT温度高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (205792001, '控制器温度超高告警', 205, 'Controller Temperature High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '控制器温度高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (205701001, '电压畸变超高告警', 205, 'Voltage Distortion High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电压畸变高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (205702001, '电流畸变超高告警', 205, 'Current Distortion High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电流畸变高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301737001, '市电频率超高告警', 301, 'Mains Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '市电频率高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301738001, '市电频率超低告警', 301, 'Mains Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '市电频率低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301750001, '输出线电压Uab超高告警', 301, 'Output Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301751001, '输出线电压Uab超低告警', 301, 'Output Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301752001, '输出线电压Ubc超高告警', 301, 'Output Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301753001, '输出线电压Ubc超低告警', 301, 'Output Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301754001, '输出线电压Uca超高告警', 301, 'Output Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301755001, '输出线电压Uca超低告警', 301, 'Output Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301756001, 'A相输出电压超高告警', 301, 'Output Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301757001, 'A相输出电压超低告警', 301, 'Output Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301758001, 'B相输出电压超高告警', 301, 'Output Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301759001, 'B相输出电压超低告警', 301, 'Output Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301760001, 'C相输出电压超高告警', 301, 'Output Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301761001, 'C相输出电压超低告警', 301, 'Output Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301767001, '输出频率超高告警', 301, 'Output Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301768001, '输出频率超低告警', 301, 'Output Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301721001, '超低油压告警', 301, 'Oil Pressure Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '油机润滑油压力超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301722001, '超低油温告警', 301, 'Oil Temperature Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '油机润滑油温度超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301723001, '水温超低告警', 301, 'Water Temperature Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '水温超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301709001, '超高油温告警', 301, 'Oil Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '油机润滑油温度超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301711001, '水温超高告警', 301, 'Water Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '油机冷却液温度（实际就是水温）超过设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301712001, '超低水位告警', 301, 'Water Level Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '超低水位告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301719001, '燃油油位超低告警', 301, 'Fuel Level Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '燃油液位超低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301724001, '启动电池电压超低告警', 301, 'Starting Battery Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '启动电池电压超低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301725001, '启动电池电压超高告警', 301, 'Starting Battery Voltage High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '启动电池电压超高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (301772001, '输出总视在功率超高告警', 301, 'Output Apparent Power High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302737001, '市电频率超高告警', 302, 'Mains Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '市电频率超高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302738001, '市电频率超低告警', 302, 'Mains Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '市电频率超低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302750001, '输出线电压Uab超高告警', 302, 'Output Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302751001, '输出线电压Uab超低告警', 302, 'Output Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302752001, '输出线电压Ubc超高告警', 302, 'Output Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302753001, '输出线电压Ubc超低告警', 302, 'Output Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302754001, '输出线电压Uca超高告警', 302, 'Output Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302755001, '输出线电压Uca超低告警', 302, 'Output Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302756001, 'A相输出电压超高告警', 302, 'Output Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302757001, 'A相输出电压超低告警', 302, 'Output Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302758001, 'B相输出电压超高告警', 302, 'Output Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302759001, 'B相输出电压超低告警', 302, 'Output Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302760001, 'C相输出电压超高告警', 302, 'Output Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302761001, 'C相输出电压超低告警', 302, 'Output Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302767001, '输出频率超高告警', 302, 'Output Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302768001, '输出频率超低告警', 302, 'Output Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302721001, '超低油压告警', 302, 'Oil Pressure Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '发电机润滑油压力超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302722001, '超低油温告警', 302, 'Oil Temperature Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '发电机润滑油温度超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302723001, '水温超低告警', 302, 'Water Temperature Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '水温低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302709001, '超高油温告警', 302, 'Oil Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '发电机润滑油温度超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302711001, '水温超高告警', 302, 'Water Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '发电机冷却液温度（实际就是水温）超过设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302712001, '超低水位告警', 302, 'Water Level Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '低水位告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302724001, '启动电池电压超低告警', 302, 'Starting Battery Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '启动电池电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302725001, '启动电池电压超高告警', 302, 'Starting Battery Voltage High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '启动电池电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302770001, '控制电池电压超高告警', 302, 'Control Battery Voltage High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302771001, '控制电池电压超低告警', 302, 'Control Battery Voltage Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (302772001, '输出总视在功率超高告警', 302, 'Output Apparent Power High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401720001, '交流输入1线电压Uab超高告警', 401, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401721001, '交流输入1线电压Uab超低告警', 401, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401722001, '交流输入1线电压Ubc超高告警', 401, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401723001, '交流输入1线电压Ubc超低告警', 401, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401724001, '交流输入1线电压Uca超高告警', 401, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401725001, '交流输入1线电压Uca超低告警', 401, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401726001, '交流输入1相电压Ua超高告警', 401, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超高告警', '交流输入{0}相电压Ua超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401727001, '交流输入1相电压Ua超低告警', 401, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超低告警', '交流输入{0}相电压Ua超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401728001, '交流输入1相电压Ub超高告警', 401, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超高告警', '交流输入{0}相电压Ub超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401729001, '交流输入1相电压Ub超低告警', 401, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超低告警', '交流输入{0}相电压Ub超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401730001, '交流输入1相电压Uc超高告警', 401, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超高告警', '交流输入{0}相电压Uc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401731001, '交流输入1相电压Uc超低告警', 401, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超低告警', '交流输入{0}相电压Uc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401737001, '交流输入1频率超高告警', 401, 'AC Input Frequency High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401738001, '交流输入1频率超低告警', 401, 'AC Input Frequency Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401710001, '直流输出电压超高告警', 401, 'DC Output Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401711001, '直流输出电压超低告警', 401, 'DC Output Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401713001, '负载电流超高告警', 401, 'Load Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '遥测值,若负载总电流高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401793001, '电池组1温度超高告警', 401, 'Battery String Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组温度高于设定告警阈值', '电池组{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401702001, '电池组1充电超流告警', 401, 'Battery String Overcharging Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池充电超流告警状态', '电池组{0}充电超流告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (401749001, '模块1温度超高告警', 401, 'Rectifier Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '模块超温告警状态', '模块{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403720001, '交流输入1线电压Uab超高告警', 403, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403721001, '交流输入1线电压Uab超低告警', 403, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403722001, '交流输入1线电压Ubc超高告警', 403, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403723001, '交流输入1线电压Ubc超低告警', 403, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403724001, '交流输入1线电压Uca超高告警', 403, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403725001, '交流输入1线电压Uca超低告警', 403, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403726001, '交流输入1相电压Ua超高告警', 403, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超高告警', '交流输入{0}相电压Ua超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403727001, '交流输入1相电压Ua超低告警', 403, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超低告警', '交流输入{0}相电压Ua超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403728001, '交流输入1相电压Ub超高告警', 403, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超高告警', '交流输入{0}相电压Ub超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403729001, '交流输入1相电压Ub超低告警', 403, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超低告警', '交流输入{0}相电压Ub超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403730001, '交流输入1相电压Uc超高告警', 403, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超高告警', '交流输入{0}相电压Uc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403731001, '交流输入1相电压Uc超低告警', 403, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超低告警', '交流输入{0}相电压Uc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403737001, '交流输入1频率超高告警', 403, 'AC Input Frequency High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (403738001, '交流输入1频率超低告警', 403, 'AC Input Frequency Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (404729001, '模块1温度超高告警', 404, 'Rectifier Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '模块超温告警状态', '模块{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (405710001, '直流输出电压超高告警', 405, 'DC Output Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (405711001, '直流输出电压超低告警', 405, 'DC Output Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (405713001, '负载电流超高告警', 405, 'Load Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '遥测值,若负载总电流高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (405793001, '电池组1温度超高告警', 405, 'Battery String Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组温度高于设定告警阈值', '电池组{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (405702001, '电池组1充电超过流告警', 405, 'Battery String Overcharging Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池充电超流告警状态', '电池组{0}充电超过流告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407720001, '交流输入1线电压Uab超高告警', 407, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407721001, '交流输入1线电压Uab超低告警', 407, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407722001, '交流输入1线电压Ubc超高告警', 407, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407723001, '交流输入1线电压Ubc超低告警', 407, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407724001, '交流输入1线电压Uca超高告警', 407, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407725001, '交流输入1线电压Uca超低告警', 407, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407726001, '交流输入1相电压Ua超高告警', 407, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超高告警', '交流输入{0}相电压Ua超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407727001, '交流输入1相电压Ua超低告警', 407, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超低告警', '交流输入{0}相电压Ua超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407728001, '交流输入1相电压Ub超高告警', 407, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超高告警', '交流输入{0}相电压Ub超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407729001, '交流输入1相电压Ub超低告警', 407, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超低告警', '交流输入{0}相电压Ub超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407730001, '交流输入1相电压Uc超高告警', 407, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超高告警', '交流输入{0}相电压Uc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407731001, '交流输入1相电压Uc超低告警', 407, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超低告警', '交流输入{0}相电压Uc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407737001, '交流输入1频率超高告警', 407, 'AC Input Frequency High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (407738001, '交流输入1频率超低告警', 407, 'AC Input Frequency Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入频率越限告警', '交流输入{0}频率超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (408729001, '模块1温度超高告警', 408, 'Rectifier Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '模块超温告警状态', '模块{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (409710001, '直流输出电压超高告警', 409, 'DC Output Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (409711001, '直流输出电压超低告警', 409, 'DC Output Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (409713001, '负载电流超高告警', 409, 'Load Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '遥测值,若负载总电流高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (409793001, '电池组1温度超高告警', 409, 'Battery String Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组温度高于设定告警阈值', '电池组{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (409702001, '电池组1充电超过流告警', 409, 'Battery String Overcharging Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池充电超流告警状态', '电池组{0}充电超过流告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501720001, '交流输入1线电压Uab超高告警', 501, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501721001, '交流输入1线电压Uab超低告警', 501, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501722001, '交流输入1线电压Ubc超高告警', 501, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501723001, '交流输入1线电压Ubc超低告警', 501, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501724001, '交流输入1线电压Uca超高告警', 501, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501725001, '交流输入1线电压Uca超低告警', 501, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501726001, '交流输入1相电压Ua超高告警', 501, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超高告警', '交流输入{0}相电压Ua超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501727001, '交流输入1相电压Ua超低告警', 501, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超低告警', '交流输入{0}相电压Ua超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501728001, '交流输入1相电压Ub超高告警', 501, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超高告警', '交流输入{0}相电压Ub超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501729001, '交流输入1相电压Ub超低告警', 501, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超低告警', '交流输入{0}相电压Ub超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501730001, '交流输入1相电压Uc超高告警', 501, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超高告警', '交流输入{0}相电压Uc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501731001, '交流输入1相电压Uc超低告警', 501, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超低告警', '交流输入{0}相电压Uc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501737001, '交流输入1频率超高告警', 501, 'AC Input Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流交流输入频率超过设定阈值范围', '交流输入{0}频率超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501738001, '交流输入1频率超低告警', 501, 'AC Input Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流交流输入频率超过设定阈值范围', '交流输入{0}频率超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501750001, '输出线电压Uab超高告警', 501, 'Output Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501751001, '输出线电压Uab超低告警', 501, 'Output Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501752001, '输出线电压Ubc超高告警', 501, 'Output Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501753001, '输出线电压Ubc超低告警', 501, 'Output Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501754001, '输出线电压Uca超高告警', 501, 'Output Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501755001, '输出线电压Uca超低告警', 501, 'Output Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501756001, '输出相电压Ua超高告警', 501, 'Output Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501757001, '输出相电压Ua超低告警', 501, 'Output Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501758001, '输出相电压Ub超高告警', 501, 'Output Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501759001, '输出相电压Ub超低告警', 501, 'Output Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501760001, '输出相电压Uc超高告警', 501, 'Output Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501761001, '输出相电压Uc超低告警', 501, 'Output Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501767001, '输出频率超高告警', 501, 'Output Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501768001, '输出频率超低告警', 501, 'Output Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501771001, '输出电流超过载告警', 501, 'Output Current OverLoad Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电流超过设备额定容量，本机过载告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501780001, '旁路线电压Uab超高告警', 501, 'Bypass Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501781001, '旁路线电压Uab超低告警', 501, 'Bypass Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501782001, '旁路线电压Ubc超高告警', 501, 'Bypass Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501783001, '旁路线电压Ubc超低告警', 501, 'Bypass Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501784001, '旁路线电压Uca超高告警', 501, 'Bypass Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501785001, '旁路线电压Uca超低告警', 501, 'Bypass Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501786001, '旁路相电压Ua超高告警', 501, 'Bypass Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501787001, '旁路相电压Ua超低告警', 501, 'Bypass Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501788001, '旁路相电压Ub超高告警', 501, 'Bypass Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501789001, '旁路相电压Ub超低告警', 501, 'Bypass Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501790001, '旁路相电压Uc超高告警', 501, 'Bypass Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501791001, '旁路相电压Uc超低告警', 501, 'Bypass Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501797001, '旁路输入频率超高告警', 501, 'Bypass Input Frequency High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路交流输入频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501798001, '旁路输入频率超低告警', 501, 'Bypass Input Frequency Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路交流输入频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501799001, '温度超高告警', 501, 'Temperature High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '设备整流器、逆变器、变压器等功率器件运行温度超过设定阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501772001, '电池组总电压超低告警', 501, 'Battery String Total Voltage Low Alarm', 3, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '放电过程中后备电池组总电压低于设定阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501718001, '逆变器交流电压超高告警', 501, 'Inverter AC Voltage High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '逆变器交流电压超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501719001, '逆变器交流电压超低告警', 501, 'Inverter AC Voltage Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '逆变器交流电压超低告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (501710001, '电池组总电压超高告警', 501, 'Battery String Total Voltage High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (503772001, '电池总电压超低告警', 503, 'Battery Total Voltage Low Alarm', 3, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (503793001, '电池组温度1超高告警', 503, 'Battery String Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (503770001, '电池组总电压超高告警', 503, 'Battery String Total Voltage High Alarm', 1, 57, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (503781001, '电池单体1电压超高告警', 503, 'Battery Cell Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池单体{0}电压超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (503782001, '电池单体1电压超低告警', 503, 'Battery Cell Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池单体{0}电压超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504720001, '交流输入1线电压Uab超高告警', 504, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab高于设定告警阈值', '交流输入{0}线电压Uab超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504721001, '交流输入1线电压Uab超低告警', 504, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uab低于设定告警阈值', '交流输入{0}线电压Uab超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504722001, '交流输入1线电压Ubc超高告警', 504, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc高于设定告警阈值', '交流输入{0}线电压Ubc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504723001, '交流输入1线电压Ubc超低告警', 504, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Ubc低于设定告警阈值', '交流输入{0}线电压Ubc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504724001, '交流输入1线电压Uca超高告警', 504, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca高于设定告警阈值', '交流输入{0}线电压Uca超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504725001, '交流输入1线电压Uca超低告警', 504, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输入线电压Uca低于设定告警阈值', '交流输入{0}线电压Uca超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504726001, '交流输入1相电压Ua超高告警', 504, 'AC Input Voltage Ua High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超高告警', '交流输入{0}相电压Ua超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504727001, '交流输入1相电压Ua超低告警', 504, 'AC Input Voltage Ua Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ua超低告警', '交流输入{0}相电压Ua超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504728001, '交流输入1相电压Ub超高告警', 504, 'AC Input Voltage Ub High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超高告警', '交流输入{0}相电压Ub超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504729001, '交流输入1相电压Ub超低告警', 504, 'AC Input Voltage Ub Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Ub超低告警', '交流输入{0}相电压Ub超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504730001, '交流输入1相电压Uc超高告警', 504, 'AC Input Voltage Uc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超高告警', '交流输入{0}相电压Uc超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504731001, '交流输入1相电压Uc超低告警', 504, 'AC Input Voltage Uc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电压Uc超低告警', '交流输入{0}相电压Uc超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504737001, '交流输入频率超高告警', 504, 'AC Input Frequency High Alarm', 1, NULL, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504738001, '交流输入频率超低告警', 504, 'AC Input Frequency Low Alarm', 1, NULL, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504750001, '输出线电压Uab超高告警', 504, 'Output Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504751001, '输出线电压Uab超低告警', 504, 'Output Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504752001, '输出线电压Ubc超高告警', 504, 'Output Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504753001, '输出线电压Ubc超低告警', 504, 'Output Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504754001, '输出线电压Uca超高告警', 504, 'Output Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504755001, '输出线电压Uca超低告警', 504, 'Output Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504756001, '输出相电压Ua超高告警', 504, 'Output Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504757001, '输出相电压Ua超低告警', 504, 'Output Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504758001, '输出相电压Ub超高告警', 504, 'Output Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504759001, '输出相电压Ub超低告警', 504, 'Output Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504760001, '输出相电压Uc超高告警', 504, 'Output Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504761001, '输出相电压Uc超低告警', 504, 'Output Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '输出电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504767001, '输出频率超高告警', 504, 'Output Frequency High Alarm', 1, NULL, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504768001, '输出频率超低告警', 504, 'Output Frequency Low Alarm', 1, NULL, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504769001, '输出电流超过载告警', 504, 'Output Current OverLoad Alarm', 2, NULL, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504780001, '旁路线电压Uab超高告警', 504, 'Bypass Voltage Uab High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504781001, '旁路线电压Uab超低告警', 504, 'Bypass Voltage Uab Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504782001, '旁路线电压Ubc超高告警', 504, 'Bypass Voltage Ubc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504783001, '旁路线电压Ubc超低告警', 504, 'Bypass Voltage Ubc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504784001, '旁路线电压Uca超高告警', 504, 'Bypass Voltage Uca High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504785001, '旁路线电压Uca超低告警', 504, 'Bypass Voltage Uca Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504786001, '旁路相电压Ua超高告警', 504, 'Bypass Voltage Ua High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504787001, '旁路相电压Ua超低告警', 504, 'Bypass Voltage Ua Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504788001, '旁路相电压Ub超高告警', 504, 'Bypass Voltage Ub High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504789001, '旁路相电压Ub超低告警', 504, 'Bypass Voltage Ub Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504790001, '旁路相电压Uc超高告警', 504, 'Bypass Voltage Uc High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压高', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504791001, '旁路相电压Uc超低告警', 504, 'Bypass Voltage Uc Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '旁路电压低', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504797001, '旁路输入频率超高告警', 504, 'Bypass Input Frequency High Alarm', 1, NULL, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504798001, '旁路输入频率超低告警', 504, 'Bypass Input Frequency Low Alarm', 1, NULL, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504770001, '温度超高告警', 504, 'Temperature High Alarm', 1, NULL, 1, 30, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504771001, '电池总电压超低告警', 504, 'Battery Total Voltage Low Alarm', 3, NULL, 1, 30, NULL, NULL, NULL, NULL, NULL, NULL, '总电压', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504749001, '模块温度超高告警', 504, 'Module Temperature High Alarm', 1, NULL, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504774001, '逆变器交流电压超高告警', 504, 'Inverter AC Voltage High Alarm', 1, NULL, 1, 30, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504775001, '逆变器交流电压超低告警', 504, 'Inverter AC Voltage Low Alarm', 1, NULL, 1, 30, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (504776001, '电池总电压超高告警', 504, 'Battery Total Voltage High Alarm', 1, NULL, 1, 30, NULL, NULL, NULL, NULL, NULL, NULL, '总电压', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601701001, 'A路输入电压超高告警', 601, 'Input A Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601702001, 'A路输入电压超低告警', 601, 'Input A Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601703001, 'A路输入电流超高告警', 601, 'Input A Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601705001, 'B路输入电压超高告警', 601, 'Input B Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601706001, 'B路输入电压超低告警', 601, 'Input B Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (601707001, 'B路输入电流超高告警', 601, 'Input B Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (602710001, '输出电压超高告警', 602, 'Output Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (602711001, '输出电压超低告警', 602, 'Output Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (602722001, '输入电压超高告警', 602, 'Input Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (602723001, '输入电压超低告警', 602, 'Input Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (602725001, '输出电流超高告警', 602, 'Output Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (603701001, '输入电压超高告警', 603, 'Input Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (603702001, '输入电压超低告警', 603, 'Input Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604710001, '输出电压超高告警', 604, 'Output Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604711001, '输出电压超低告警', 604, 'Output Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604722001, '输入电压超高告警', 604, 'Input Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604723001, '输入电压超低告警', 604, 'Input Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604725001, '输出电流超高告警', 604, 'Output Current High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (604751001, '设备温度超高告警', 604, 'Device Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (701706001, '压缩机超高压告警', 701, 'Compressor Pressure High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (701707001, '压缩机超低压告警', 701, 'Compressor Pressure Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702701001, '回风1超高温告警', 702, 'Return Air Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超高温告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702702001, '回风1超低温告警', 702, 'Return Air Temperature Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超低温告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702703001, '回风1超高湿告警', 702, 'Return Air Humidity High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超高湿告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702704001, '回风1超低湿告警', 702, 'Return Air Humidity Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超低湿告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702706001, '压缩机1超高压告警', 702, 'Compressor Pressure High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '压缩机{0}超高压告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (702707001, '压缩机1超低压告警', 702, 'Compressor Pressure Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '压缩机{0}超低压告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704701001, '回风1超高温告警', 704, 'Return Air Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超高温告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704702001, '回风1超低温告警', 704, 'Return Air Temperature Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超低温告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704703001, '回风1超高湿告警', 704, 'Return Air Humidity High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超高湿告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704704001, '回风1超低湿告警', 704, 'Return Air Humidity Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '回风{0}超低湿告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704724001, '冷冻水进水1温度超高告警', 704, 'Inlet Chilled Water Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷冻水进水{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (704725001, '冷冻水进水1压力超低告警', 704, 'Inlet Chilled Water Temperature Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷冻水进水{0}压力超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705726001, '冷却塔1水位超低告警', 705, 'Cooling Tower Water Level Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷却塔{0}水位超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705727001, '冷却塔1水位超高告警', 705, 'Cooling Tower Water Level High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷却塔{0}水位超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705731001, '排气1压力超高告警', 705, 'Outlet Air Pressure High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '压缩机高压告警', '排气{0}压力超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705733001, '排气1温度超高告警', 705, 'Outlet Air Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '排气{0}温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705734001, '冷却水1出水温度超高告警', 705, 'Outlet Cooling Water Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷却水{0}出水温度超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705735001, '冷冻水1出水温度超低告警', 705, 'Outlet Chilled Water Temperature Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '冷冻水{0}出水温度超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705736001, '油压差超低告警', 705, 'Oil Differential Pressure Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705742001, '压缩机1电流超高告警', 705, 'Compressor Current High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '压缩机过载告警', '压缩机{0}电流超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705743001, '压缩机1电流超低告警', 705, 'Compressor Current Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '压缩机{0}电流超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (705764001, '吸气压力超低告警', 705, 'Suction Pressure Low Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (804751001, '柜内温度超高告警', 804, 'Cabinet Temperature High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '温控柜内温度超高告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101780001, '电池组1总电压(49V)超低告警', 1101, 'Battery String Total Voltage(48V) Low Alarm', 2, 46, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组{0}总电压(49V)超低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101781001, '电池组1温度1超高告警', 1101, 'Battery String 1 Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组1温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101784001, '电池组2温度1超高告警', 1101, 'Battery String 2 Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组2温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101787001, '电池组3温度1超高告警', 1101, 'Battery String 3 Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组3温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101790001, '电池组4温度1超高告警', 1101, 'Battery String 4 Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组4温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101791001, '单体1电压超低告警(12V)', 1101, 'Cell Voltage Low Alarm(12v)', 0, 11.5, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超低告警(12V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101792001, '单体1电压超低告警(2V)', 1101, 'Cell Voltage Low Alarm(2v)', 0, 1.8500000000000001, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超低告警(2V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101752001, '单体1电压超高告警(12V)', 1101, 'Cell Voltage High Alarm(12v)', 0, 14.5, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超高告警(12V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101753001, '单体1电压超高告警(6V)', 1101, 'Cell Voltage High Alarm(6v)', 0, 7.25, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超高告警(6V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101754001, '单体1电压超低告警(6V)', 1101, 'Cell Voltage Low Alarm(6v)', 0, 5.75, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超低告警(6V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101755001, '单体1电压超高告警(2V)', 1101, 'Cell Voltage High Alarm(2v)', 0, 2.3799999999999999, 1, 180, NULL, NULL, NULL, NULL, NULL, NULL, '当只有一组时，使用本编码，多组时，使用下面331～338部分。', '单体{0}电压超高告警(2V)', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1101770001, '电池组1总电压(49V)超高告警', 1101, 'Battery String Total Voltage(48V) High Alarm', 2, 57, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组{0}总电压(49V)超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1103772001, '总电压超低告警', 1103, 'Battery String Total Voltage Low Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1103792001, '电池单体1电压超低告警', 1103, 'Battery Cell Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池单体{0}电压低告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1103732001, '电池组温度1超高告警', 1103, 'Battery String Temperature High Alarm', 1, 35, 1, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池组温度{0}超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1103752001, '电池单体1电压超高告警', 1103, 'Battery Cell Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '电池单体{0}电压超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1103770001, '总电压超高告警', 1103, 'Battery String Total Voltage High Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1601751001, '超高温告警', 1601, 'Over Temperature Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '变压器超高温信号', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701708001, '输出分路1相电流Ia超高告警', 1701, 'Output Branch Current Ia High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ia高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ia超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701709001, '输出分路1相电流Ib超高告警', 1701, 'Output Branch Current Ib High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ib高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ib超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701710001, '输出分路1相电流Ic超高告警', 1701, 'Output Branch Current Ic High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '出线相电流Ic高于设定告警阈值,告警阈值以系统或线路容量', '输出分路{0}相电流Ic超高告警', 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701720001, '交流输入线电压Uab超高告警', 1701, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701721001, '交流输入线电压Uab超低告警', 1701, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701722001, '交流输入线电压Ubc超高告警', 1701, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701723001, '交流输入线电压Ubc超低告警', 1701, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701724001, '交流输入线电压Uca超高告警', 1701, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1701725001, '交流输入线电压Uca超低告警', 1701, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702720001, '交流输入线电压Uab超高告警', 1702, 'AC Input Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702721001, '交流输入线电压Uab超低告警', 1702, 'AC Input Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702722001, '交流输入线电压Ubc超高告警', 1702, 'AC Input Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702723001, '交流输入线电压Ubc超低告警', 1702, 'AC Input Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702724001, '交流输入线电压Uca超高告警', 1702, 'AC Input Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1702725001, '交流输入线电压Uca超低告警', 1702, 'AC Input Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1801701001, '直流超过压告警', 1801, 'DC OverVoltage Alarm', 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '直流超过压', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (1801715001, '直流电流超高告警', 1801, 'DC Current High Alarm', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109750001, '交流输出线电压Uab超高告警', 2109, 'AC Ouput Voltage Uab High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109751001, '交流输出线电压Uab超低告警', 2109, 'AC Ouput Voltage Uab Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uab低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109752001, '交流输出线电压Ubc超高告警', 2109, 'AC Ouput Voltage Ubc High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109753001, '交流输出线电压Ubc超低告警', 2109, 'AC Ouput Voltage Ubc Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Ubc低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109754001, '交流输出线电压Uca超高告警', 2109, 'AC Ouput Voltage Uca High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca高于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109755001, '交流输出线电压Uca超低告警', 2109, 'AC Ouput Voltage Uca Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '母线电压Uca低于设定告警阈值', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109762001, 'A相电流超高告警', 2109, 'Current Ia High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电流Ia高于设定告警阈值,告警阈值以系统或线路容量', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109763001, 'B相电流超高告警', 2109, 'Current Ib High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电流Ib高于设定告警阈值,告警阈值以系统或线路容量', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109764001, 'C相电流超高告警', 2109, 'Current Ic High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '相电流Ic高于设定告警阈值,告警阈值以系统或线路容量', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109767001, '交流输出频率超高告警', 2109, 'AC Ouput Frequency High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输出频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109768001, '交流输出频率超低告警', 2109, 'AC Ouput Frequency Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '交流输出频率超过设定阈值范围', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109722001, '直流输入电压超高告警', 2109, 'DC Input Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109723001, '直流输入电压超低告警', 2109, 'DC Input Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109707001, '交流输出线电压超高告警', 2109, 'AC Line Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '任意一相电压超高产生告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109708001, '交流输出线电压超低告警', 2109, 'AC Line Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '任意一相电压超低产生告警', NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109709001, '交流输出相电压超高告警', 2109, 'AC Phase Voltage High Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

        INSERT INTO TBL_EventBaseDic (BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, EventSeverityId, ComparedValue, BaseLogicCategoryId, StartDelay, EndDelay, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5, Description, BaseNameExt, IsSystem)
        VALUES (2109710001, '交流输出相电压超低告警', 2109, 'AC Phase Voltage Low Alarm', 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
    </insert>

</mapper>
