<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblSignalMapper">
    <resultMap id="signalConfigItemMap" type="org.siteweb.config.common.dto.SignalConfigItem">
        <id column="id" property="id"/>
        <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="SignalId" property="signalId"/>
        <result column="Enable" property="enable"/>
        <result column="Visible" property="visible"/>
        <result column="Description" property="description"/>
        <result column="SignalName" property="signalName"/>
        <result column="SignalCategory" property="signalCategory"/>
        <result column="SignalType" property="signalType"/>
        <result column="ChannelNo" property="channelNo"/>
        <result column="ChannelType" property="channelType"/>
        <result column="Expression" property="expression"/>
        <result column="DataType" property="dataType"/>
        <result column="ShowPrecision" property="showPrecision"/>
        <result column="Unit" property="unit"/>
        <result column="StoreInterval" property="storeInterval"/>
        <result column="AbsValueThreshold" property="absValueThreshold"/>
        <result column="PercentThreshold" property="percentThreshold"/>
        <result column="StaticsPeriod" property="staticsPeriod"/>
        <result column="BaseTypeId" property="baseTypeId"/>
        <result column="ChargeStoreInterVal" property="chargeStoreInterVal"/>
        <result column="ChargeAbsValue" property="chargeAbsValue"/>
        <result column="DisplayIndex" property="displayIndex"/>
        <result column="MDBSignalId" property="mDBSignalId"/>
        <result column="ModuleNo" property="moduleNo"/>
        <result column="baseTypeName" property="baseTypeName"/>
        <result column="baseStatusId" property="baseStatusId"/>
        <result column="baseNameExt" property="baseNameExt"/>
        <collection property="signalMeaningsList" ofType="org.siteweb.config.common.entity.TblSignalMeanings" column="equipmentTemplateId,signalId" notNullColumn="meaningId">
            <id column="meaningId" property="id"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="SignalId" property="signalId"/>
            <result column="stateValue" property="stateValue"/>
            <result column="basecondid" property="baseCondId"/>
            <result column="meanings" property="meanings"/>
        </collection>
        <collection property="signalPropertyList" ofType="org.siteweb.config.common.entity.TblSignalProperty" column="equipmentTemplateId,signalId" notNullColumn="propertyId">
            <id column="propertyId" property="id"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="SignalId" property="signalId"/>
            <result column="SignalPropertyId" property="signalPropertyId"/>
        </collection>
    </resultMap>
    <sql id="findSignalItemConfigSql">
        SELECT a.id,
               a.equipmenttemplateid,
               a.signalid,
               a.enable,
               a.visible,
               a.description,
               a.signalname,
               a.signalcategory,
               a.signaltype,
               a.channelno,
               a.channeltype,
               a.expression,
               a.datatype,
               a.showprecision,
               a.unit,
               a.storeinterval,
               a.absvaluethreshold,
               a.percentthreshold,
               a.staticsperiod,
               a.basetypeid,
               a.chargestoreinterval,
               a.chargeabsvalue,
               a.displayindex,
               a.mdbsignalid,
               a.moduleno,
               b.Id as meaningId,
               b.statevalue,
               b.meanings,
               b.basecondid,
               c.Id as propertyId,
               c.SignalPropertyId,
               d.BaseTypeName,
               d.baseStatusId,
               d.baseNameExt
        FROM tbl_signal a
                 LEFT JOIN tbl_signalmeanings b
                           ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.SignalId = b.SignalId
                 LEFT JOIN tbl_signalproperty c
                           ON a.EquipmentTemplateId = c.EquipmentTemplateId AND a.SignalId = c.SignalId
                 LEFT JOIN tbl_signalbasedic d ON d.BaseTypeId = a.BaseTypeId
    </sql>
    <select id="findByEquipmentTemplateId" resultType="org.siteweb.config.common.entity.TblSignal">
        SELECT Id, EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName,
        SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision,
        Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId,
        ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo FROM TBL_Signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findMaxSignalIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(SignalId)
        FROM TBL_Signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findMaxSignalByEquipmentTemplateId" resultMap="signalConfigItemMap">
        <include refid="findSignalItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId}
        ORDER BY a.SignalId DESC
        LIMIT 1;
    </select>
    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(DisplayIndex)
        FROM TBL_Signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT t.BaseTypeId
        FROM TBL_Signal t
        WHERE t.EquipmentTemplateId = #{equipmentTemplateId}
        AND t.BaseTypeId IS NOT NULL
        AND t.BaseTypeId != 0
        AND t.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_SignalBaseDic);
    </select>
    <select id="findAllSignalItem" resultMap="signalConfigItemMap">
       <include refid="findSignalItemConfigSql"/>
    </select>
    <select id="findSignalItemByEquipmentTemplateId" resultMap="signalConfigItemMap">
        <include refid="findSignalItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findByEquipmentTemplateIdAndSignalId"  resultMap="signalConfigItemMap">
        <include refid="findSignalItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId}  and a.signalId = #{signalId}
    </select>
    <select id="diffSignal" resultType="org.siteweb.config.common.entity.TblSignal">
        SELECT SignalId, ChannelNo,SignalName
        FROM TBL_Signal
        WHERE EquipmentTemplateId = #{originTemplateId}
          AND SignalId NOT IN (SELECT SignalId FROM TBL_Signal WHERE EquipmentTemplateId = #{destTemplateId})
    </select>
    <select id="findSameVirtualSignals" resultType="org.siteweb.config.common.entity.TblSignal">
        SELECT SignalId, SignalName
        FROM TBL_Signal
        WHERE EquipmentTemplateId = #{oldEquipmentTemplateId}
          AND ChannelNo = -2
          AND SignalId IN (SELECT SignalId FROM TBL_Signal WHERE EquipmentTemplateId = #{newEquipmentTemplateId})
    </select>
    <delete id="deleteSignal">
        DELETE FROM TSL_MonitorUnitSignal t WHERE t.SignalId=#{signalId} AND t.EquipmentId IN(SELECT te.EquipmentId FROM
        TBL_Equipment te WHERE te.EquipmentTemplateId=#{equipmentTemplateId});
        DELETE FROM TBL_SignalProperty t WHERE t.SignalId=#{signalId} AND t.EquipmentTemplateId=#{equipmentTemplateId};
        DELETE FROM TBL_SignalMeanings t WHERE t.SignalId=#{signalId} AND t.EquipmentTemplateId=#{equipmentTemplateId};
        DELETE FROM TBL_Signal t WHERE t.SignalId=#{signalId} AND t.EquipmentTemplateId=#{equipmentTemplateId};
    </delete>
    <update id="updateWorkStationSignalName">
        UPDATE tbl_signal
        SET SignalName = CONCAT(#{prefix}, SignalName)
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>
    <update id="updateSelfDiagnosisSignal">
        UPDATE tbl_signal
        SET SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
        UPDATE tbl_signalmeanings
        SET SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
    </update>

    <insert id="batchInsertLianTongSignal">
        <!-- adaptive-multi-pg 去除反引号  -->
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010113,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','0','0',null,'0',102026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010114,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','0','0',null,'0',102028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010115,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','0','0',null,'0',102030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010116,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','0','0',null,'0',102020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010117,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','0','0',null,'0',102022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010118,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','0','0',null,'0',102024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010119,1,1,'电源交流输入电流只会测正在供电的这路，如果针对交流屏则是输出电流。','交流输入电流Ia',1,1,6,1,'',0,'0.0','A','0','0',null,'0',102032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010120,1,1,'电源交流输入电流只会测正在供电的这路，如果针对交流屏则是输出电流。','交流输入电流Ib',1,1,7,1,'',0,'0.0','A','0','0',null,'0',102033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010121,1,1,'电源交流输入电流只会测正在供电的这路，如果针对交流屏则是输出电流。','交流输入电流Ic',1,1,8,1,'',0,'0.0','A','0','0',null,'0',102034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010122,1,1,'波动较小，一般由直流屏变出DC220V，对操作回路供电，提供较小的电流。','控母电压',1,1,9,1,'',0,'0.0','V','28800','0',null,'24',102119001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000069,100010123,1,1,'波动较大，一般由直流屏变出DC240V，对断路器进线分合闸使用的电源。提供合闸瞬间较大电流。','合母电压',1,1,10,1,'',0,'0.0','V','28800','0',null,'24',102118001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010114,1,1,' ','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','0',null,'24',1601026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010115,1,1,' ','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','0',null,'24',1601028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010116,1,1,' ','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','0',null,'24',1601030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010117,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','0',null,'24',1601020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010118,1,1,' ','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','0',null,'24',1601022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010119,1,1,' ','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','0',null,'24',1601024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010120,1,1,'','交流输入电流Ia',1,1,6,1,'',0,'0.0','A','28800','0',null,'0',1601032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010121,1,1,'','交流输入电流Ib',1,1,7,1,'',0,'0.0','A','28800','0',null,'0',1601033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010122,1,1,'','交流输入电流Ic',1,1,8,1,'',0,'0.0','A','28800','0',null,'0',1601034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010123,1,1,'','输出相电压Ua',1,1,9,1,'',0,'0','V','28800','0',null,'24',1601056001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010124,1,1,'','输出相电压Ub',1,1,10,1,'',0,'0','V','28800','0',null,'24',1601058001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010125,1,1,'','输出相电压Uc',1,1,11,1,'',0,'0','V','28800','0',null,'24',1601060001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010126,1,1,'','输出线电压Uab',1,1,12,1,'',0,'0','V','28800','0',null,'24',1601050001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010127,1,1,'','输出线电压Ubc',1,1,13,1,'',0,'0','V','28800','0',null,'24',1601052001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010128,1,1,'','输出线电压Uca',1,1,14,1,'',0,'0','V','28800','0',null,'24',1601054001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010129,1,1,'','交流输出电流Ia',1,1,15,1,'',0,'0.0','A','28800','0',null,'0',1601062001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010130,1,1,'','交流输出电流Ib',1,1,16,1,'',0,'0.0','A','28800','0',null,'0',1601063001,null,null,17,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010131,1,1,'','交流输出电流Ic',1,1,17,1,'',0,'0.0','A','28800','0',null,'0',1601064001,null,null,18,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000070,100010132,1,1,'绕组温度，不区分A相、B相、C相','设备温度',1,1,18,1,'',0,'0.0','℃','28800','5',null,'24',1601151001,null,null,19,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010112,1,1,'','输出相电压Ua',1,1,0,1,'',0,'0.0','kV','28800','0',null,'24',101026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010113,1,1,'','输出相电压Ub',1,1,1,1,'',0,'0.0','kV','28800','0',null,'24',101028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010114,1,1,'','输出相电压Uc',1,1,2,1,'',0,'0.0','kV','28800','0',null,'24',101030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010115,1,1,'','输出线电压Uab',1,1,3,1,'',0,'0.0','kV','28800','0',null,'24',101020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010116,1,1,'','输出线电压Ubc',1,1,4,1,'',0,'0.0','kV','28800','0',null,'24',101022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010117,1,1,'','输出线电压Uca',1,1,5,1,'',0,'0.0','kV','28800','0',null,'24',101024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010118,1,1,'','输出相电流Ia',1,1,6,1,'',0,'0.0','A','28800','0',null,'24',101032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010119,1,1,'','输出相电流Ib',1,1,7,1,'',0,'0.0','A','28800','0',null,'24',101033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010120,1,1,'','输出相电流Ic',1,1,8,1,'',0,'0.0','A','28800','0',null,'24',101034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010121,1,1,'','输出频率',1,1,9,1,'',0,'0.0','Hz','86400','0',null,'24',101037001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010122,1,1,'','输出功率因数',1,1,10,1,'',0,'0.0','','86400','0',null,'0',101036001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010123,1,1,'','输出总有功功率',1,1,11,1,'',0,'0','kW','86400','0',null,'24',101035001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010124,1,1,'','输出总视在功率',1,1,12,1,'',0,'0.0','kVA','86400','0',null,'0',101315001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010125,1,1,'','输出总有功电度',1,1,13,1,'',0,'0','kWh','86400','0',null,'24',101039001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010126,1,1,'','输出总谐波电流',1,1,14,1,'',0,'0.0','A','0','0',null,'0',101316001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000071,100010127,1,1,'','断路器状态',2,1,15,2,'',0,'0','','0','1',null,'0',101156001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010115,1,1,'','输出相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',201026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010116,1,1,'','输出相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',201028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010117,1,1,'','输出相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',201030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010118,1,1,'','输出线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',201020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010119,1,1,'','输出线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',201022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010120,1,1,'','输出线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',201024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010121,1,1,'','输出相电流Ia',1,1,6,1,'',0,'0.0','A','28800','0',null,'0',201302001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010122,1,1,'','输出相电流Ib',1,1,7,1,'',0,'0.0','A','28800','0',null,'0',201303001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010123,1,1,'','输出相电流Ic',1,1,8,1,'',0,'0.0','A','28800','0',null,'0',201304001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010124,1,1,'','输出频率',1,1,9,1,'',0,'0.0','Hz','86400','0',null,'0',201037001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010125,1,1,'','输出功率因数',1,1,10,1,'',0,'0.0','','86400','0',null,'0',201036001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010126,1,1,'','输出总有功功率',1,1,11,1,'',0,'0','kW','86400','0',null,'24',201035001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010127,1,1,'','输出总视在功率',1,1,12,1,'',0,'0.0','kVA','86400','0',null,'0',201320001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010128,1,1,'','输出总有功电度',1,1,13,1,'',0,'0','kWh','86400','0',null,'24',201313001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010129,1,1,'','输出总谐波电流',1,1,14,1,'',0,'0.0','A','0','0',null,'0',201321001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000072,100010130,1,1,'','断路器状态',2,1,15,2,'',0,'0','','0','1',null,'0',201156001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000073,100010116,1,1,' ','输入功率因数',1,1,0,1,'',0,'0.0','','28800','0.1',null,'0',204066001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000074,100010117,1,1,'','输入总谐波电流',1,1,0,1,'',0,'0','A','28800','0',null,'24',205041001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000074,100010118,1,1,'','输入总谐波电压',1,1,1,1,'',0,'0.0','V','0','0',null,'0',205303001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010118,1,1,'','主用相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',203026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010119,1,1,'','主用相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',203028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010120,1,1,'','主用相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',203030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010121,1,1,'','主用线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',203020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010122,1,1,'','主用线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',203022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010123,1,1,'','主用线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',203024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000075,100010124,1,1,'频率1用于主路；频率2用于备用路','主用电源频率',1,1,6,1,'',0,'0.0','Hz','86400','0',null,'0',203037001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010119,1,1,'','输出相电压Ua',1,1,0,1,'',0,'0','V','86400','20',null,'0',301056001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010120,1,1,'','输出相电压Ub',1,1,1,1,'',0,'0','V','86400','20',null,'0',301058001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010121,1,1,'','输出相电压Uc',1,1,2,1,'',0,'0','V','86400','20',null,'0',301060001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010122,1,1,'','输出线电压Uab',1,1,3,1,'',0,'0','V','86400','35',null,'0',301050001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010123,1,1,'','输出线电压Ubc',1,1,4,1,'',0,'0','V','86400','35',null,'0',301052001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010124,1,1,'','输出线电压Uca',1,1,5,1,'',0,'0','V','86400','35',null,'0',301054001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010125,1,1,'','交流输出电流Ia',1,1,6,1,'',0,'0.0','A','86400','0',10,'0',301062001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010126,1,1,'','交流输出电流Ib',1,1,7,1,'',0,'0.0','A','86400','0',10,'0',301063001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010127,1,1,'','交流输出电流Ic',1,1,8,1,'',0,'0.0','A','86400','0',10,'0',301064001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010128,1,1,'','输出频率',1,1,9,1,'',0,'0.0','Hz','86400','0.2',null,'0',301067001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010129,1,1,'普通发电机转速','转速',1,1,10,1,'',0,'0','r/min','86400','0',10,'0',301220001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010130,1,1,'润滑油','润滑油油压',1,1,11,1,'',0,'0','kPa','86400','0',10,'0',301221001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010131,1,1,'','缸温/水温',1,1,12,1,'',0,'0.0','℃','86400','0',10,'0',301223001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010132,1,1,'润滑油','润滑油油温',1,1,13,1,'',0,'0.0','℃','86400','0',10,'0',301222001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010133,1,1,'智能油机自采集油箱油量即内部油箱','燃油液位',1,1,14,1,'',0,'0','L','86400','3',null,'24',301228001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010134,1,1,'','启动电池电压12V',1,1,15,1,'',0,'0.0','V','86400','3',null,'24',301225001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010136,1,1,'由于市电停或者油机测试，油机开始工作','开关机状态',2,1,17,2,'',0,'0','','0','1',null,'0',301155001,null,null,18,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010138,1,1,'','手自动状态',2,1,19,2,'',0,'0','','0','0',null,'0',301154001,null,null,20,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010140,1,1,'','主备用状态',2,1,20,2,'',0,'0','','0','1',null,'0',301309001,null,null,22,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000076,100010141,1,1,'','输出总视在功率',1,1,21,1,'',0,'0','kVA','86400','0',null,'0',301304001,null,null,23,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010120,1,1,'','输出相电压Ua',1,1,0,1,'',0,'0','V','86400','20',null,'0',302056001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010121,1,1,'','输出相电压Ub',1,1,1,1,'',0,'0','V','86400','20',null,'0',302058001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010122,1,1,'','输出相电压Uc',1,1,2,1,'',0,'0','V','86400','20',null,'0',302060001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010123,1,1,'','输出线电压Uab',1,1,3,1,'',0,'0','V','86400','35',null,'0',302050001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010124,1,1,'','输出线电压Ubc',1,1,4,1,'',0,'0','V','86400','35',null,'0',302052001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010125,1,1,'','输出线电压Uca',1,1,5,1,'',0,'0','V','86400','35',null,'0',302054001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010126,1,1,'','交流输出电流Ia',1,1,6,1,'',0,'0.0','A','86400','0',10,'0',302062001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010127,1,1,'','交流输出电流Ib',1,1,7,1,'',0,'0.0','A','86400','0',10,'0',302063001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010128,1,1,'','交流输出电流Ic',1,1,8,1,'',0,'0.0','A','86400','0',10,'0',302064001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010129,1,1,'','输出频率',1,1,9,1,'',0,'0.0','Hz','86400','0.2',null,'0',302067001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010130,1,1,'普通发电机转速','转速',1,1,10,1,'',0,'0','r/min','86400','0',10,'0',302220001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010131,1,1,'','排气温度',1,1,11,1,'',0,'0.0','℃','86400','0',null,'0',302311001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010132,1,1,'','进气温度',1,1,12,1,'',0,'0.0','℃','86400','0',null,'0',302310001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010133,1,1,'','润滑油油压',1,1,13,1,'',0,'0','kPa','86400','0',10,'0',302221001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010134,1,1,'','缸温/水温',1,1,14,1,'',0,'0.0','℃','86400','0',10,'0',302223001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010135,1,1,'','润滑油油温',1,1,15,1,'',0,'0.0','℃','86400','0',10,'0',302222001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010136,1,1,'','启动电池电压',1,1,16,1,'',0,'0.0','V','86400','3',null,'24',302225001,null,null,17,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010138,1,1,'由于市电停或者发电机测试，发电机开始工作','开关机状态',2,1,18,2,'',0,'0','','0','1',null,'0',302155001,null,null,19,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010140,1,1,'','手自动状态',2,1,19,2,'',0,'0','','0','0',null,'0',302154001,null,null,21,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000077,100010142,1,1,'','主备用状态',2,1,20,2,'',0,'0','','0','1',null,'0',302309001,null,null,23,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010121,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',501026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010122,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',501028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010123,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',501030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010124,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',501020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010125,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',501022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010126,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',501024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010127,1,1,'','输出相电压Ua',1,1,6,1,'',0,'0','V','28800','20',null,'24',501056001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010128,1,1,'','输出相电压Ub',1,1,7,1,'',0,'0','V','28800','20',null,'24',501058001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010129,1,1,'','输出相电压Uc',1,1,8,1,'',0,'0','V','28800','20',null,'24',501060001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010130,1,1,'','输出线电压Uab',1,1,9,1,'',0,'0','V','28800','35',null,'24',501050001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010131,1,1,'','输出线电压Ubc',1,1,10,1,'',0,'0','V','28800','35',null,'24',501052001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010132,1,1,'','输出线电压Uca',1,1,11,1,'',0,'0','V','28800','35',null,'24',501054001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010133,1,1,'','输出相电流Ia',1,1,12,1,'',0,'0.0','A','28800','0',null,'0',501062001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010134,1,1,'','输出相电流Ib',1,1,13,1,'',0,'0.0','A','28800','0',null,'0',501063001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010135,1,1,'','输出相电流Ic',1,1,14,1,'',0,'0.0','A','28800','0',null,'0',501064001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010136,1,1,'单位 HZ','输出频率',1,1,15,1,'',0,'0.0','Hz','86400','0',null,'0',501067001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010137,1,1,'直流输入电压','直流输入电压',1,1,16,1,'',0,'0.0','V','43200','0',null,'0',501117001,null,null,17,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010138,1,1,'12V','蓄电池单体电压',1,1,17,1,'',0,'0.0','V','43200','0',null,'0',501179001,null,null,18,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010139,1,1,'内置温度和表温一般只会使用一个。','蓄电池单体温度',1,1,18,1,'',0,'0.0','℃','28800','0',null,'24',501181001,null,null,19,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000078,100010140,1,1,'','输出总有功功率',1,1,63,1,'',0,'0.0','KW','28800','0',null,'24',501350001,null,null,63,0,0);

        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010122,1,1,'A相输入电压','输入相电压Ua',1,1,0,1,'',0,'0','V','86400','5',null,'0',504026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010123,1,1,'B相输入电压','输入相电压Ub',1,1,1,1,'',0,'0','V','86400','5',null,'0',504028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010124,1,1,'C相输入电压','输入相电压Uc',1,1,2,1,'',0,'0','V','86400','5',null,'0',504030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010125,1,1,'交流输入线电压AB','输入线电压Uab',1,1,3,1,'',0,'0','V','0','0',null,'0',504020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010126,1,1,'交流输入线电压BC','输入线电压Ubc',1,1,4,1,'',0,'0','V','0','0',null,'0',504022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010127,1,1,'交流输入线电压CA','输入线电压Uca',1,1,5,1,'',0,'0','V','0','0',null,'0',504024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010128,1,1,'A相输出电压','输出相电压Ua',1,1,6,1,'',0,'0','V','86400','5',null,'0',504056001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010129,1,1,'B相输出电压','输出相电压Ub',1,1,7,1,'',0,'0','V','86400','5',null,'0',504058001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010130,1,1,'C相输出电压','输出相电压Uc',1,1,8,1,'',0,'0','V','86400','5',null,'0',504060001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010131,1,1,'交流输出线电压AB','输出线电压Uab',1,1,9,1,'',0,'0','V','0','0',null,'0',504050001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010132,1,1,'交流输出线电压BC','输出线电压Ubc',1,1,10,1,'',0,'0','V','0','0',null,'0',504052001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010133,1,1,'交流输出线电压CA','输出线电压Uca',1,1,11,1,'',0,'0','V','0','0',null,'0',504054001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010134,1,1,'A相输出电流','输出相电流Ia',1,1,12,1,'',0,'0.0','A','86400','0',20,'0',504062001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010135,1,1,'B相输出电流','输出相电流Ib',1,1,13,1,'',0,'0.0','A','86400','0',20,'0',504063001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010136,1,1,'C相输出电流','输出相电流Ic',1,1,14,1,'',0,'0.0','A','86400','0',20,'0',504064001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010137,1,1,'输出频率(三相一致);','输出频率',1,1,15,1,'',0,'0.0','Hz','86400','0.5',null,'0',504067001,null,null,16,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010138,1,1,'直流输入电压','直流输入电压',1,1,16,1,'',0,'0.0','V','0','0',null,'0',504117001,null,null,17,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010139,1,1,'12V','蓄电池单体电压',1,1,17,1,'',0,'0.0','V','43200','0',null,'0',504179001,null,null,18,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010140,1,1,'内置温度和表温一般只会使用一个。','蓄电池单体温度',1,1,18,1,'',0,'0.0','℃','28800','5',null,'24',504181001,null,null,19,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000079,100010141,1,1,'输出总有功功率','输出总有功功率',1,1,19,1,'',0,'0.0','kW','86400','0',null,'24',504350001,null,null,20,0,0);


        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010123,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',1701026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010124,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',1701028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010125,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',1701030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010126,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',1701020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010127,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',1701022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010128,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',1701024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010129,1,1,'','交流输出电流Ia',1,1,6,1,'',0,'0.0','A','28800','0',null,'0',1701032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010130,1,1,'','交流输出电流Ib',1,1,7,1,'',0,'0.0','A','28800','0',null,'0',1701033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010131,1,1,'','交流输出电流Ic',1,1,8,1,'',0,'0.0','A','28800','0',null,'0',1701034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010132,1,1,'','输出总有功电度',1,1,9,1,'',0,'0','kWh','86400','0',null,'24',1701039001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000080,100010133,1,1,'','输入频率',1,1,10,1,'',0,'0.0','Hz','86400','0',null,'0',1701037001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010124,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',1702026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010125,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',1702028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010126,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',1702030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010127,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',1702020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010128,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',1702022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010129,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',1702024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010130,1,1,'','输出总有功电度',1,1,6,1,'',0,'0','kWh','86400','0',null,'24',1702039001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000081,100010131,1,1,'','输入频率',1,1,7,1,'',0,'0.0','Hz','86400','0',null,'0',1702037001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010125,1,1,'','冷冻水进水温度',1,1,0,1,'',0,'0.0','℃','0','0',null,'0',705211001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010126,1,1,'','冷冻水出水温度',1,1,1,1,'',0,'0.0','℃','0','0',null,'0',705210001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010127,1,1,'','冷却水进水温度',1,1,2,1,'',0,'0.0','℃','0','0',null,'0',705209001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010129,1,1,'','冷却水泵电流',1,1,4,1,'',0,'0.0','A','0','0',null,'0',705378001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010130,1,1,'','冷冻水泵电流',1,1,5,1,'',0,'0.0','A','0','0',null,'0',705379001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010131,1,1,'','压缩机电流',1,1,6,1,'',0,'0.0','A','0','0',null,'0',705328001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010132,1,1,'','压缩机吸气压力',1,1,7,1,'',0,'0.0','kPa','0','0',null,'0',705326001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010133,1,1,'','压缩机排气压力',1,1,8,1,'',0,'0.0','kPa','0','0',null,'0',705327001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010134,1,1,'','冷却塔水位',1,1,9,1,'',0,'0.0','m','0','0',null,'0',705380001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000082,100010135,1,1,'','冷却水出水温度',1,1,10,1,'',0,'0.0','℃','0','0',null,'0',705208001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000083,100010153,1,1,'','雷击次数',1,1,0,1,'',0,'','pcs','0','0',null,'0',2105295001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000084,100010152,1,1,'','室内环境温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'24',1004001001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000084,100010153,1,1,'','室内环境湿度',1,1,1,1,'',0,'0.0','%RH','28800','10',null,'24',1004003001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010129,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',403026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010130,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',403028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010131,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',403030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010132,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',403020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010133,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',403022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010134,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',403024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010135,1,1,'','交流输出电流Ia',1,1,6,1,'',0,'0.0','A','0','0',null,'0',403032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010136,1,1,'','交流输出电流Ib',1,1,7,1,'',0,'0.0','A','0','0',null,'0',403033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010137,1,1,'','交流输出电流Ic',1,1,8,1,'',0,'0.0','A','0','0',null,'0',403034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000085,100010138,1,1,'','输入频率',1,1,9,1,'',0,'0.0','Hz','0','0',null,'0',403037001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000086,100010144,1,1,'','空调送风温度',1,1,0,1,'',0,'0.0','℃','0','0',null,'0',704330001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000086,100010145,1,1,'','空调送风湿度',1,1,1,1,'',0,'0.0','%RH','0','0',null,'0',704331001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000086,100010146,1,1,'','空调回风温度',1,1,2,1,'',0,'0.0','℃','28800','5',null,'24',704207001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000086,100010147,1,1,'','空调回风湿度',1,1,3,1,'',0,'0.0','%RH','28800','10',null,'24',704317001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000086,100010148,1,1,'','开关机状态',2,1,4,2,'',0,'0','','0','1',null,'0',704155001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000087,100010150,1,1,'','门开关状态',2,1,0,2,'',0,'0','','0','0',null,'0',1001303001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010128,1,1,'','直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0',null,'24',2109122001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010129,1,1,'','输出相电压Ua',1,1,1,1,'',0,'0','V','28800','0',null,'24',2109056001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010130,1,1,'','输出相电压Ub',1,1,2,1,'',0,'0','V','28800','0',null,'24',2109058001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010131,1,1,'','输出相电压Uc',1,1,3,1,'',0,'0','V','28800','0',null,'24',2109060001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010132,1,1,'','输出线电压Uab',1,1,4,1,'',0,'0','V','28800','0',null,'24',2109050001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010133,1,1,'','输出线电压Ubc',1,1,5,1,'',0,'0','V','28800','0',null,'24',2109052001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010134,1,1,'','输出线电压Uca',1,1,6,1,'',0,'0','V','28800','0',null,'24',2109054001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010135,1,1,'','交流输出电流Ia',1,1,7,1,'',0,'0.0','A','28800','0',null,'0',2109062001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010136,1,1,'','交流输出电流Ib',1,1,8,1,'',0,'0.0','A','28800','0',null,'0',2109063001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010137,1,1,'','交流输出电流Ic',1,1,9,1,'',0,'0.0','A','28800','0',null,'0',2109064001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000088,100010138,1,1,'','输出频率',1,1,10,1,'',0,'0.0','Hz','86400','0',null,'0',2109067001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000089,100010147,1,1,'','室内环境温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'0',806001001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000089,100010148,1,1,'','室外环境温度',1,1,1,1,'',0,'0.0','℃','28800','5',null,'0',806012001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000089,100010149,1,1,'','开关机状态',2,1,2,2,'',0,'','','0','1',null,'0',806155001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000090,100010148,1,1,'','室内环境温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'0',805001001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000090,100010149,1,1,'','室外环境温度',1,1,1,1,'',0,'0.0','℃','28800','5',null,'0',805012001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000090,100010150,1,1,'','开关机状态',2,1,2,2,'',0,'','','0','1',null,'0',805301001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010146,1,1,'','室内环境温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'24',807001001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010147,1,1,'','室外环境温度',1,1,1,1,'',0,'0.0','℃','28800','5',null,'24',807012001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010148,1,1,'','室内环境湿度',1,1,2,1,'',0,'0.0','%RH','28800','5',null,'24',807003001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010149,1,1,'','室外环境湿度',1,1,3,1,'',0,'0.0','%RH','28800','5',null,'24',807305001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010150,1,1,'','输入相电压Ua',1,1,4,1,'',0,'0.0','V','0','0',null,'0',807321001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010151,1,1,'','输入相电压Ub',1,1,5,1,'',0,'0.0','V','0','0',null,'0',807322001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010152,1,1,'','输入相电压Uc',1,1,6,1,'',0,'0.0','V','0','0',null,'0',807323001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010153,1,1,'','输入线电压Uab',1,1,7,1,'',0,'0.0','V','0','0',null,'0',807318001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010154,1,1,'','输入线电压Ubc',1,1,8,1,'',0,'0.0','V','0','0',null,'0',807319001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010155,1,1,'','输入线电压Uca',1,1,9,1,'',0,'0.0','V','0','0',null,'0',807320001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000091,100010156,1,1,'','开关机状态',2,1,10,2,'',0,'0','','0','1',null,'0',807155001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010143,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0.0','V','0','0',null,'0',701026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010144,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0.0','V','0','0',null,'0',701028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010145,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0.0','V','0','0',null,'0',701030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010146,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0.0','V','0','0',null,'0',701020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010147,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0.0','V','0','0',null,'0',701022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010148,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0.0','V','0','0',null,'0',701024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010149,1,1,'','工作电流',1,1,6,1,'',0,'0.0','A','0','0',null,'0',701303001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010150,1,1,'','空调回风温度',1,1,7,1,'',0,'0.0','℃','28800','5',null,'24',701001001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010151,1,1,'','空调回风湿度',1,1,8,1,'',0,'0.0','%RH','28800','10',null,'24',701003001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000092,100010152,1,1,'','开关机状态',2,1,9,2,'',0,'0','','0','1',null,'0',701155001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000093,100010136,1,1,'','直流输出电压',1,1,0,1,'',0,'0.0','V','0','0',null,'0',1801277001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000093,100010137,1,1,'','直流输出电流',1,1,1,1,'',0,'0.0','A','0','0',null,'0',1801278001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000093,100010138,1,1,'','方阵工作状态',2,1,2,2,'',0,'','','0','0',null,'0',1801301001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010145,1,1,'','室内环境温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'24',801001001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010146,1,1,'','室外环境温度',1,1,1,1,'',0,'0.0','℃','28800','5',null,'24',801012001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010147,1,1,'','室内环境湿度',1,1,2,1,'',0,'0.0','%RH','28800','5',null,'24',801003001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010148,1,1,'','室外环境湿度',1,1,3,1,'',0,'0.0','%RH','28800','5',null,'24',801305001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010149,1,1,'','输入相电压Ua',1,1,4,1,'',0,'0.0','V','0','0',null,'0',801026001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010150,1,1,'','输入相电压Ub',1,1,5,1,'',0,'0.0','V','0','0',null,'0',801028001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010151,1,1,'','输入相电压Uc',1,1,6,1,'',0,'0.0','V','0','0',null,'0',801030001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010152,1,1,'','输入线电压Uab',1,1,7,1,'',0,'0.0','V','0','0',null,'0',801020001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010153,1,1,'','输入线电压Ubc',1,1,8,1,'',0,'0.0','V','0','0',null,'0',801022001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010154,1,1,'','输入线电压Uca',1,1,9,1,'',0,'0.0','V','0','0',null,'0',801024001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010155,1,1,'','输入直流电压',1,1,10,1,'',0,'0.0','V','0','0',null,'0',801324001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000094,100010156,1,1,'','开关机状态',2,1,11,2,'',0,'0','','0','1',null,'0',801155001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000095,100010149,1,1,'','蓄电池区域温度',1,1,0,1,'',0,'0.0','℃','28800','5',null,'0',804151001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000095,100010150,1,1,'','室内环境温度',1,1,1,1,'',0,'0.0','℃','28800','5',null,'0',804001001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000095,100010151,1,1,'','开关机状态',2,1,2,2,'',0,'','','0','1',null,'0',804302001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000096,100010130,1,1,'','直流输出电压',1,1,0,1,'',0,'0.0','V','28800','0.5',null,'24',404110001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000096,100010131,1,1,'','单个整流模块输出电流',1,1,1,1,'',0,'0.0','A','43200','0',null,'0',404114001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000097,100010131,1,1,'','直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0.5',null,'24',405110001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000097,100010132,1,1,'','直流输入电流',1,1,1,1,'',0,'0.0','A','28800','0',10,'24',405125001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000097,100010133,1,1,'','直流分路电流',1,1,2,1,'',0,'0.0','A','0','0',null,'0',405318001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000097,100010134,1,1,'','蓄电池充电电流',1,1,3,1,'',0,'0.0','A','0','0',null,'0',405174001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000098,100010141,1,1,'','直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0',null,'24',604122001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000098,100010142,1,1,'','直流输入电流',1,1,1,1,'',0,'0.0','A','28800','0',null,'0',604124001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000098,100010143,1,1,'','直流输出电压',1,1,2,1,'',0,'0.0','V','28800','0',null,'24',604110001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000098,100010144,1,1,'','直流输出电流',1,1,3,1,'',0,'0.0','A','28800','0',null,'0',604125001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000099,100010137,1,1,'','直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0',null,'24',602122001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000099,100010138,1,1,'','直流输入电流',1,1,1,1,'',0,'0.0','A','28800','0',null,'0',602124001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000099,100010139,1,1,'','直流输出电压',1,1,2,1,'',0,'0.0','V','28800','0',null,'24',602110001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000099,100010140,1,1,'','直流输出电流',1,1,3,1,'',0,'0.0','A','28800','0',null,'0',602125001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000100,100010142,1,1,'','空调送风温度',1,1,0,1,'',0,'0.0','℃','0','0',null,'0',702330001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000100,100010143,1,1,'','空调送风湿度',1,1,1,1,'',0,'0.0','%RH','0','0',null,'0',702331001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000100,100010144,1,1,'','空调回风温度',1,1,2,1,'',0,'0.0','℃','28800','5',null,'24',702001001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000100,100010145,1,1,'','空调回风湿度',1,1,3,1,'',0,'0.0','%RH','28800','10',null,'24',702003001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000100,100010146,1,1,'','开关机状态',2,1,5,2,'',0,'0','','0','1',null,'0',702155001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010132,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',401026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010133,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',401028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010134,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',401030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010135,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',401020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010136,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',401022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010137,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',401024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010138,1,1,'','交流输入电流Ia',1,1,6,1,'',0,'0.0','A','0','0',null,'0',401032001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010139,1,1,'','交流输入电流Ib',1,1,7,1,'',0,'0.0','A','0','0',null,'0',401033001,null,null,8,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010140,1,1,'','交流输入电流Ic',1,1,8,1,'',0,'0.0','A','0','0',null,'0',401034001,null,null,9,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010141,1,1,'','输入频率',1,1,9,1,'',0,'0.0','Hz','0','0',null,'0',401037001,null,null,10,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010142,1,1,'','直流输出电压',1,1,10,1,'',0,'0.0','V','28800','0.5',null,'24',401110001,null,null,11,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010143,1,1,'','单个整流模块输出电流',1,1,11,1,'',0,'0.0','A','43200','0',null,'0',401114001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010144,1,1,'','直流输出电流',1,1,12,1,'',0,'0.0','A','28800','0',10,'24',401125001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010145,1,1,'','直流分路电流',1,1,13,1,'',0,'0.0','A','0','0',null,'0',401318001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010146,1,1,'','蓄电池充电电流',1,1,14,1,'',0,'0.0','A','0','0',null,'0',401174001,null,null,15,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000101,100010147,1,1,'','直流输出总功率',1,1,31,1,'',0,'0.0','kW','28800','0',null,'24',401343001,null,null,32,0,0);

        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000102,100010126,1,1,'','A路直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0',null,'24',601301001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000102,100010128,1,1,'','A路直流输入电流',1,1,2,1,'',0,'0.0','A','28800','0',null,'0',601302001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000102,100010130,1,1,'','A路直流分路电流',1,1,4,1,'',0,'0.0','A','28800','0',null,'0',601316001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000103,100010127,1,1,'','直流输入电压',1,1,0,1,'',0,'0.0','V','28800','0',null,'24',603301001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000103,100010128,1,1,'','直流输入电流',1,1,1,1,'',0,'0.0','A','28800','0',null,'0',603302001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000105,100010139,1,1,'','蓄电池组总电压',1,1,0,1,'',0,'0.0','V','43200','0',null,'24',503172001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000105,100010140,1,1,'','蓄电池单体电压',1,1,1,1,'',0,'0.000','V','28800','0.2',null,'0',503179001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000105,100010141,1,1,'','蓄电池单体温度',1,1,2,1,'',0,'0.0','℃','28800','0',10,'0',503181001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000105,100010142,1,1,'','蓄电池充电电流',1,1,3,1,'',0,'0.0','A','0','0',null,'0',503174001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000105,100010143,1,1,'','蓄电池组容量',1,1,4,1,'',0,'0','Ah','0','0',null,'0',503400001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000106,100010151,1,1,'','CPU使用率',1,1,0,1,'',0,'0.0','%','0','0',null,'0',1301301001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000106,100010152,1,1,'','内存使用率',1,1,1,1,'',0,'0.0','%','0','0',null,'0',1301302001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010140,1,1,'','蓄电池组总电压',1,1,0,1,'',0,'0','V','0','0',null,'0',1103172001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010141,1,1,'','蓄电池单体电压',1,1,1,1,'',0,'0','V','0','0',null,'0',1103192001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010142,1,1,'','蓄电池单体温度',1,1,2,1,'',0,'0.0','℃','0','0',null,'0',1103193001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010143,1,1,'','蓄电池单体内阻',1,1,3,1,'',0,'0.0','mΩ','0','0',null,'0',1103310001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010144,1,1,'','蓄电池充电电流',1,1,4,1,'',0,'0.0','A','0','0',null,'0',1103173001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000107,100010145,1,1,'','蓄电池组容量',1,1,5,1,'',0,'0','Ah','0','0',null,'0',1103318001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010133,1,1,'','输入相电压Ua',1,1,0,1,'',0,'0','V','28800','20',null,'24',407026001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010134,1,1,'','输入相电压Ub',1,1,1,1,'',0,'0','V','28800','20',null,'24',407028001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010135,1,1,'','输入相电压Uc',1,1,2,1,'',0,'0','V','28800','20',null,'24',407030001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010136,1,1,'','输入线电压Uab',1,1,3,1,'',0,'0','V','28800','35',null,'24',407020001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010137,1,1,'','输入线电压Ubc',1,1,4,1,'',0,'0','V','28800','35',null,'24',407022001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010138,1,1,'','输入线电压Uca',1,1,5,1,'',0,'0','V','28800','35',null,'24',407024001,null,null,6,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000108,100010139,1,1,'','输入频率',1,1,6,1,'',0,'0.0','Hz','0','0',null,'0',407037001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000109,100010134,1,1,'','直流输出电压',1,1,0,1,'',0,'0.0','V','28800','0.5',null,'24',408110001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000109,100010135,1,1,'','单个整流模块输出电流',1,1,1,1,'',0,'0.0','A','43200','0',null,'0',408114001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000110,100010135,1,1,'','直流分路电流',1,1,0,1,'',0,'0.0','A','28800','0',10,'24',409125001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000110,100010136,1,1,'','蓄电池充电电流',1,1,1,1,'',0,'0.0','A','0','0',null,'0',409174001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010138,1,1,'','蓄电池组总电压',1,1,1,1,'',0,'0.0','V','43200','0.5',null,'24',1101170001,null,null,1,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010141,1,1,'','蓄电池单体电压',1,1,5,1,'',0,'0.000','V','43200','0.05',null,'0',1101180001,null,null,2,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010152,1,1,'','蓄电池单体温度',1,1,92,1,'',0,'0.0','℃','43200','5',null,'24',1101181001,null,null,3,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010156,1,1,'','蓄电池单体内阻',1,1,31,1,'',0,'0.0','mΩ','0','0',null,'0',1101302001,null,null,4,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010160,1,1,'','蓄电池充电电流',1,1,4,1,'',0,'0.0','A','0','0',null,'0',1101174001,null,null,5,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010167,1,1,'','蓄电池组已放电时间',1,1,58,1,'',0,'0.0','分钟','0','0',null,'0',1101324001,null,null,12,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010168,1,1,'','蓄电池组剩余放电时间',1,1,3,1,'',0,'0.0','分钟','0','0',null,'0',1101333001,null,null,13,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010170,1,1,'','蓄电池组实际容量',1,1,84,1,'',0,'0','Ah','0','0',null,'0',1101195001,null,null,14,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010173,1,1,'','蓄电池组剩余容量',1,1,55,1,'',0,'0','Ah','0','0',null,'0',1101316001,null,null,17,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010174,1,1,'','蓄电池组剩余容量（%）',1,1,61,1,'',0,'0','%','0','0',null,'0',1101322001,null,null,18,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010175,1,1,'','蓄电池健康度',1,1,79,1,'',0,'0.0','%','0','0',null,'0',1101194001,null,null,19,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010176,1,1,'','蓄电池放电电流',1,1,2,1,'',0,'0','A','0','0',null,'0',1101199001,null,null,7,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010177,1,1,'','蓄电池组工作状态',2,1,0,1,'',0,'0','','0','0',null,'0',1101155001,null,null,20,0,0);
        INSERT INTO TBL_Signal (EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory, SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit, StoreInterval, AbsValueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId, ChargeStoreInterVal, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)	VALUES (123000104,100010178,1,1,'','蓄电池组保障时间',1,1,91,1,'',0,'0','分钟','0','0',null,'0',1101196001,null,null,21,0,0);
    </insert>

    <select id="findSignalsByEquipmentIdAndSignalIds" resultType="org.siteweb.config.common.dto.batchtool.EquipmentSignalDTO">
        SELECT ts.id, ts.equipmenttemplateid, ts.signalid, ts.enable, ts.visible, ts.description, ts.signalname,
        ts.signalcategory, ts.signaltype, ts.channelno, ts.channeltype, ts.expression, ts.datatype, ts.showprecision,
        ts.unit, ts.storeinterval, ts.absvaluethreshold, ts.percentthreshold, ts.staticsperiod, ts.basetypeid,
        ts.chargestoreinterval, ts.chargeabsvalue, ts.displayindex, ts.mdbsignalid, ts.moduleno,te.EquipmentId,te.EquipmentName
        FROM TBL_Signal ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId} AND
        ts.SignalId IN
        <foreach collection="signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSignalEvent" resultType="org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO">
        SELECT b.signalId,
               b.SignalName,
               c.EventId,
               c.EventName,
               a.EquipmentId
        FROM TBL_Equipment a
                 LEFT JOIN tbl_signal b ON b.EquipmentTemplateId = a.EquipmentTemplateId
                 LEFT JOIN tbl_event c ON c.EquipmentTemplateId = a.EquipmentTemplateid AND b.SignalId = c.SignalId
        WHERE a.EquipmentId = #{equipmentId}
        ORDER BY b.signalId
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT a.EquipmentTemplateId || '.' || a.SignalId as id,
        b.EquipmentTemplateName || ',' || a.SignalName as value
        FROM tbl_signal a
        INNER JOIN tbl_equipmenttemplate b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE (a.EquipmentTemplateId, a.SignalId) IN (
        <foreach item="item" collection="signalUniqueIds" separator="," open="" close="">
            (#{item.equipmentTemplateId}::integer, #{item.signalId}::integer)
        </foreach>
        )
    </select>
    <select id="findSignalProgressList" resultType="org.siteweb.config.common.dto.SignalProgressDTO">
        SELECT
        s.EquipmentTemplateId,
        s.SignalId,
        s.SignalCategory,
        s.BaseTypeId,
        c.BaseCondId,
        c.StateValue
        FROM
        TBL_Signal s
        LEFT JOIN TBL_SignalMeanings c ON s.EquipmentTemplateId = c.EquipmentTemplateId
        AND s.SignalId = c.SignalId
        ORDER BY
        s.EquipmentTemplateId
    </select>
    <sql id="findSignalBaseClassDetailsSql">
        SELECT
        s.EquipmentTemplateId,
        e.EquipmentTemplateName,
        e.ParentTemplateId,
        (
        SELECT
        EquipmentTemplateName
        FROM
        TBL_EquipmentTemplate
        WHERE
        EquipmentTemplateId = e.ParentTemplateId
        LIMIT 1) AS ParentTemplateName,
        e.ProtocolCode,
        s.SignalId,
        s.SignalName,
        c.Meanings,
        s.SignalCategory,
        s.SignalType,
        s.ChannelNo,
        s.Unit,
        s.BaseTypeId,
        d.BaseTypeName,
        d.baseNameExt,
        c.BaseCondId,
        e.EquipmentBaseType AS CategoryOrBaseType,
        e.EquipmentBaseType,
        c.StateValue
        FROM
        TBL_EquipmentTemplate e
        INNER JOIN TBL_Signal s ON	e.EquipmentTemplateId = s.EquipmentTemplateId
        LEFT JOIN TBL_SignalMeanings c ON s.EquipmentTemplateId = c.EquipmentTemplateId	AND s.SignalId = c.SignalId
        LEFT JOIN tbl_signalbasedic d ON d.BaseTypeId = s.BaseTypeId
    </sql>
    <select id="findSignalBaseClass" resultType="org.siteweb.config.common.dto.SignalBaseClassDetailDTO">
        <include refid="findSignalBaseClassDetailsSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
    </select>
    <select id="findSignalBaseClassDetails"  resultType="org.siteweb.config.common.dto.SignalBaseClassDetailDTO">
        <include refid="findSignalBaseClassDetailsSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
        <if test="signalName != null and signalName != ''">
            AND s.signalName = #{signalName}
        </if>
        <choose>
            <when test="meanings != null and meanings != ''">
                AND c.Meanings = #{meanings}
            </when>
            <otherwise>
                AND c.Meanings IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="getApplyStandards" resultType="org.siteweb.config.common.dto.SignalApplyStandardDTO">
        SELECT
        mysignal.id,
        COALESCE(
        CASE
        WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 1) = 1
        THEN REPLACE(dic.SignalStandardName, 'XX', CAST(mysignal.BaseTypeId - FLOOR(mysignal.BaseTypeId / 1000) * 1000 AS TEXT))
        ELSE NULL
        END,
        mysignal.SignalName
        ) AS SignalName,
        COALESCE(
        CASE
        WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 2) = 1
        THEN dic.StoreInterval
        ELSE NULL
        END,
        mysignal.StoreInterval
        ) AS StoreInterval,
        COALESCE(
        CASE
        WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 3) = 1
        THEN dic.AbsValueThreshold
        ELSE NULL
        END,
        mysignal.AbsValueThreshold
        ) AS AbsValueThreshold,
        COALESCE(
        CASE
        WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 4) = 1
        THEN dic.PercentThreshold
        ELSE NULL
        END,
        mysignal.PercentThreshold
        ) AS PercentThreshold
        FROM
        TBL_Signal mysignal
        INNER JOIN TBL_EquipmentTemplate template ON template.EquipmentTemplateId = mysignal.EquipmentTemplateId
        INNER JOIN TBL_SignalBaseMap map ON FLOOR(map.BaseTypeId / 1000) = FLOOR(mysignal.BaseTypeId / 1000)
        INNER JOIN TBL_StandardDicSig dic ON dic.StandardDicId = map.StandardDicId
        AND dic.StationCategory = map.StationBaseType
        AND dic.StandardType = map.StandardType
        WHERE
        dic.StationCategory = template.StationCategory
        AND dic.StandardType = #{standardId}
        <if test="equipmentTemplateIds != null and equipmentTemplateIds.size > 0">
            AND mysignal.EquipmentTemplateId in
            <foreach collection="equipmentTemplateIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="restoreStandard">
        UPDATE TBL_Signal
        SET SignalName = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 1) = 1 THEN bak.SignalName ELSE TBL_Signal.SignalName END,
        StoreInterval = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 2) = 1 THEN bak.StoreInterval ELSE TBL_Signal.StoreInterval END,
        AbsValueThreshold = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 3) = 1 THEN bak.AbsValueThreshold ELSE TBL_Signal.AbsValueThreshold END,
        PercentThreshold = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 4) = 1 THEN bak.PercentThreshold ELSE TBL_Signal.PercentThreshold END
        FROM TBL_StandardBack bak
        WHERE bak.EquipmentTemplateId = TBL_Signal.EquipmentTemplateId
        AND bak.EntryId = TBL_Signal.SignalId
        AND bak.EntryCategory = 1
    </update>
    <update id="batchUpdateField">
        <foreach collection="signalList" item="signal" separator=";">
            UPDATE tbl_signal
            <set>
                <if test="signal.equipmentTemplateId != null">
                    EquipmentTemplateId = #{signal.equipmentTemplateId},
                </if>
                <if test="signal.signalId != null">
                    SignalId = #{signal.signalId},
                </if>
                <if test="signal.enable != null">
                    Enable = #{signal.enable},
                </if>
                <if test="signal.visible != null">
                    Visible = #{signal.visible},
                </if>
                <if test="signal.description != null">
                    Description = #{signal.description},
                </if>
                <if test="signal.signalName != null">
                    SignalName = #{signal.signalName},
                </if>
                <if test="signal.signalCategory != null">
                    SignalCategory = #{signal.signalCategory},
                </if>
                <if test="signal.signalType != null">
                    SignalType = #{signal.signalType},
                </if>
                <if test="signal.channelNo != null">
                    ChannelNo = #{signal.channelNo},
                </if>
                <if test="signal.channelType != null">
                    ChannelType = #{signal.channelType},
                </if>
                <if test="signal.expression != null">
                    Expression = #{signal.expression},
                </if>
                <if test="signal.dataType != null">
                    DataType = #{signal.dataType},
                </if>
                <if test="signal.showPrecision != null">
                    ShowPrecision = #{signal.showPrecision},
                </if>
                <if test="signal.unit != null">
                    Unit = #{signal.unit},
                </if>
                <if test="signal.storeInterval != null">
                    StoreInterval = #{signal.storeInterval},
                </if>
                <if test="signal.absValueThreshold != null">
                    AbsValueThreshold = #{signal.absValueThreshold},
                </if>
                <if test="signal.percentThreshold != null">
                    PercentThreshold = #{signal.percentThreshold},
                </if>
                <if test="signal.staticsPeriod != null">
                    StaticsPeriod = #{signal.staticsPeriod},
                </if>
                <if test="signal.baseTypeId != null">
                    BaseTypeId = #{signal.baseTypeId},
                </if>
                <if test="signal.chargeStoreInterVal != null">
                    ChargeStoreInterVal = #{signal.chargeStoreInterVal},
                </if>
                <if test="signal.chargeAbsValue != null">
                    ChargeAbsValue = #{signal.chargeAbsValue},
                </if>
                <if test="signal.displayIndex != null">
                    DisplayIndex = #{signal.displayIndex},
                </if>
                <if test="signal.mDBSignalId != null">
                    MDBSignalId = #{signal.mDBSignalId},
                </if>
                <if test="signal.moduleNo != null">
                    ModuleNo = #{signal.moduleNo},
                </if>
            </set>
            WHERE equipmentTemplateId = #{signal.equipmentTemplateId} and signalId = #{signal.signalId}
        </foreach>
    </update>
    <select id="getStandardCompareData" resultType="org.siteweb.config.common.dto.StandardSignalCompareDTO">
        SELECT
        bak.EquipmentTemplateId AS EquipmentTemplateIdBefore,
        template.EquipmentTemplateName AS EquipmentTemplateNameBefore,
        bak.EntryId AS SignalIdBefore,
        bak.SignalName AS SignalNameBefore,
        bak.StoreInterval AS StoreIntervalBefore,
        bak.AbsValueThreshold AS AbsValueThresholdBefore,
        bak.PercentThreshold AS PercentThresholdBefore,
        sig.EquipmentTemplateId AS EquipmentTemplateIdAfter,
        template.EquipmentTemplateName AS EquipmentTemplateNameAfter,
        sig.SignalId AS SignalIdAfter,
        sig.SignalName AS SignalNameAfter,
        sig.StoreInterval AS StoreIntervalAfter,
        sig.AbsValueThreshold AS AbsValueThresholdAfter,
        sig.PercentThreshold AS PercentThresholdAfter
        FROM
        TBL_StandardBack bak
        LEFT JOIN TBL_EquipmentTemplate template ON
        template.EquipmentTemplateId = bak.EquipmentTemplateId
        LEFT JOIN TBL_Signal sig ON
        bak.EquipmentTemplateId = sig.EquipmentTemplateId
        AND bak.EntryId = sig.SignalId
        WHERE
        bak.EntryCategory = 1
    </select>
    <select id="getSignalStandardApplyCheckData" resultType="org.siteweb.config.common.dto.StandardApplySignalCheckDTO">
        SELECT
        (CASE
        WHEN (
        SELECT
        COUNT(*)
        FROM
        TBL_StandardBack sb
        WHERE
        sb.EntryCategory = 1
        AND sb.EquipmentTemplateId = sig.EquipmentTemplateId
        AND sb.EntryId = sig.SignalId) >= 1 THEN 1
        ELSE 0
        END) AS Standarded,
        sig.EquipmentTemplateId,
        et.EquipmentTemplateName,
        sig.SignalId,
        sig.SignalName,
        sig.StoreInterval,
        sig.AbsValueThreshold,
        sig.PercentThreshold,
        sig.StaticsPeriod,
        sig.BaseTypeId,
        sbd.BaseTypeName,
        sds.StandardDicId,
        sds.SignalStandardName,
        sds.EquipmentLogicClassId,
        sds.EquipmentLogicClass,
        ebt.BaseEquipmentId,
        ebt.BaseEquipmentName
        FROM
        TBL_Signal sig
        LEFT JOIN TBL_EquipmentTemplate et ON
        et.EquipmentTemplateId = sig.EquipmentTemplateId
        LEFT JOIN TBL_SignalBaseMap sbm ON
        sbm.BaseTypeId = sig.BaseTypeId
        AND et.StationCategory = sbm.StationBaseType
        AND sbm.StandardType =  #{standardId}
        LEFT JOIN TBL_StandardDicSig sds ON
        sds.StandardDicId = sbm.StandardDicId
        AND sds.StandardType = sbm.StandardType
        AND sds.StationCategory = sbm.StationBaseType
        AND sds.StandardType =  #{standardId}
        LEFT JOIN TBL_SignalBaseDic sbd ON
        sbd.BaseTypeId = sig.BaseTypeId
        LEFT JOIN TBL_EquipmentBaseType ebt ON
        ebt.BaseEquipmentId = sbd.BaseEquipmentId
    </select>

    <select id="getSignalStandardMappingCheck"  resultType="org.siteweb.config.common.dto.StandardMappingSignalCheckDTO">
        SELECT
        DISTINCT sig.SignalId,
        sig.SignalName,
        (CASE
        WHEN dicSignal.StandardDicId IS NULL THEN 0
        ELSE 1
        END) AS Standarded,
        station.StationId,
        station.StationName,
        station.StationCategory,
        equip.EquipmentId,
        equip.EquipmentName,
        equip.EquipmentCategory,
        eq.EquipmentTemplateId,
        eq.EquipmentTemplateName,
        dic.BaseTypeId,
        dic.BaseTypeName,
        dicSignal.StandardDicId,
        REPLACE(dicSignal.SignalStandardName, 'XX', RIGHT(concat('0', (sig.BaseTypeId - floor(sig.BaseTypeId / 1000)* 1000)::varchar), 2)) AS SignalStandardName
        FROM
        TBL_Station station
        LEFT JOIN TBL_Equipment equip ON
        station.StationId = equip.StationId
        LEFT JOIN TBL_EquipmentTemplate eq ON
        eq.EquipmentTemplateId = equip.EquipmentTemplateId
        LEFT JOIN TBL_Signal sig ON
        eq.EquipmentTemplateId = sig.EquipmentTemplateId
        LEFT JOIN TBL_SignalBaseDic dic ON
        dic.BaseTypeId = sig.BaseTypeId
        LEFT JOIN TBL_StationBaseMap stationMap ON
        stationMap.StationCategory = station.StationCategory
        AND stationMap.StandardType = #{standardId}
        LEFT JOIN TBL_SignalBaseMap MAP ON
        FLOOR(map.BaseTypeId / 1000)= FLOOR(dic.BaseTypeId / 1000)
        AND map.StationBaseType = stationMap.StationBaseType
        AND map.StandardType = #{standardId}
        LEFT JOIN TBL_StandardDicSig dicSignal ON
        dicSignal.StandardDicId = map.StandardDicId
        AND (dicSignal.StationCategory = map.StationBaseType
        OR dicSignal.StationCategory = 0)
        AND dicSignal.StandardType = #{standardId}
        WHERE
        sig.SignalId IS NOT NULL
        <if test="equipmentCategory != null">
            AND equip.EquipmentCategory = #{equipmentCategory}
        </if>
        ORDER BY StationId
    </select>
    <select id="findByEquipmentCategory" resultType="org.siteweb.config.common.entity.TblSignal">
        SELECT
        s.EquipmentTemplateId,
        s.SignalId,
        s.SignalName,
        s.Description
        FROM
        tbl_signal s
        INNER JOIN tbl_equipmenttemplate eq ON s.EquipmentTemplateId = eq.EquipmentTemplateId
        WHERE
        eq.EquipmentCategory = #{equipmentCategory}
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="org.siteweb.config.common.dto.excel.SignalExcel">
        SELECT d.stationId,
               d.StationName,
               c.HouseId,
               c.HouseName,
               b.EquipmentId,
               b.EquipmentName,
               a.EquipmentTemplateId,
               a.EquipmentTemplateName,
               e.SignalId,
               e.SignalName,
               e.Expression,
               e.ShowPrecision,
               e.Unit,
               e.StoreInterval,
               e.AbsvalueThreshold
        FROM tbl_equipmentTemplate a
                 LEFT JOIN
             tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
                 LEFT JOIN
             tbl_house c ON b.StationId = c.StationId AND b.HouseId = c.HouseId
                 LEFT JOIN
             tbl_station d ON d.StationId = c.StationId
                 LEFT JOIN
             tbl_signal e ON e.EquipmentTemplateId = a.EquipmentTemplateId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>