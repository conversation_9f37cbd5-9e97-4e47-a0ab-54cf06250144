<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TslMonitorUnitMapper">


    <insert id="insertDto">
        INSERT INTO TSL_MonitorUnit (MonitorUnitId, MonitorUnitName, MonitorUnitCategory, MonitorUnitCode,
        WorkStationId, StationId, IpAddress, RunMode, ConfigFileCode,
        ConfigUpdateTime, SampleConfigCode, SoftwareVersion, Description,
        StartTime, HeartbeatTime, ConnectState, UpdateTime, IsSync,
        SyncTime, IsConfigOK, ConfigFileCode_Old, SampleConfigCode_Old, AppCongfigId,
        CanDistribute, Enable, ProjectName, ContractNo, InstallTime, FSU)
        VALUES (#{monitorUnitId}, #{monitorUnitName}, #{monitorUnitCategory}, #{monitorUnitCode},
        #{workStationId}, #{stationId}, #{ipAddress}, #{runMode}, #{configFileCode},
        #{configUpdateTime}, #{sampleConfigCode}, #{softwareVersion}, #{description},
        #{startTime}, #{heartbeatTime}, #{connectState}, #{updateTime}, #{isSync},
        #{syncTime}, #{isConfigOK}, #{configFileCode_Old}, #{sampleConfigCode_Old}, #{appConfigId},
        #{canDistribute}, #{enable}, #{rdsServer}, #{dataServer}, #{installTime}, #{fsu});

        INSERT INTO TBL_MonitorUnitProjectInfo (StationId, MonitorUnitId, ProjectName, ContractNo, InstallTime) VALUES
        (#{stationId}, #{monitorUnitId}, #{projectName}, #{contractNo}, #{installTime});
    </insert>
    <update id="updateDto">
        UPDATE TSL_MonitorUnit SET
                                   MonitorUnitName = COALESCE(#{monitorUnitName}, MonitorUnitName),
                                   MonitorUnitCategory = COALESCE(#{monitorUnitCategory}, MonitorUnitCategory),
                                   MonitorUnitCode = COALESCE(#{monitorUnitCode}, MonitorUnitCode),
                                   WorkStationId = COALESCE(#{workStationId}, WorkStationId),
                                   StationId = COALESCE(#{stationId}, StationId),
                                   IpAddress = COALESCE(#{ipAddress}, IpAddress),
                                   RunMode = COALESCE(#{runMode}, RunMode),
                                   ConfigFileCode = COALESCE(#{configFileCode}, ConfigFileCode),
                                   ConfigUpdateTime = COALESCE(#{configUpdateTime}, ConfigUpdateTime),
                                   SampleConfigCode = COALESCE(#{sampleConfigCode}, SampleConfigCode),
                                   SoftwareVersion = COALESCE(#{softwareVersion}, SoftwareVersion),
                                   Description = COALESCE(#{description}, Description),
                                   StartTime = COALESCE(#{startTime}, StartTime),
                                   HeartbeatTime = COALESCE(#{heartbeatTime}, HeartbeatTime),
                                   ConnectState = COALESCE(#{connectState}, ConnectState),
                                   UpdateTime = COALESCE(#{updateTime}, UpdateTime),
                                   IsSync = COALESCE(#{isSync}, IsSync),
                                   SyncTime = COALESCE(#{syncTime}, SyncTime),
                                   IsConfigOK = COALESCE(#{isConfigOK}, IsConfigOK),
                                   ConfigFileCode_Old = COALESCE(#{configFileCode_Old}, ConfigFileCode_Old),
                                   SampleConfigCode_Old = COALESCE(#{sampleConfigCode_Old}, SampleConfigCode_Old),
                                   AppCongfigId = COALESCE(#{appConfigId}, AppCongfigId),
                                   CanDistribute = COALESCE(#{canDistribute}, CanDistribute),
                                   Enable = COALESCE(#{enable}, Enable),
                                   ProjectName = COALESCE(#{rdsServer}, ProjectName),
                                   ContractNo = COALESCE(#{dataServer}, ContractNo),
                                   InstallTime = COALESCE(#{installTime}, InstallTime),
                                   FSU = COALESCE(#{fsu}, FSU)
        WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId};

        UPDATE TBL_MonitorUnitProjectInfo SET
                                              StationId = COALESCE(#{stationId}, StationId),
                                              ProjectName = COALESCE(#{projectName}, ProjectName),
                                              ContractNo = COALESCE(#{contractNo}, ContractNo),
                                              InstallTime = COALESCE(#{installTime}, InstallTime)
        WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId};
    </update>
    <delete id="deleteDto">
        DELETE FROM TSL_MonitorUnit WHERE MonitorUnitId = #{monitorUnitId};

        DELETE FROM TBL_MonitorUnitProjectInfo WHERE MonitorUnitId = #{monitorUnitId};
    </delete>
    <sql id="MonitorUnitSql">
        SELECT t.* FROM(
            SELECT
            mu.MonitorUnitId,
            mu.MonitorUnitName,
            mu.MonitorUnitCategory,
            mu.MonitorUnitCode,
            mu.WorkStationId,
            mu.StationId,
            ts.stationName,
            mu.IpAddress,
            mu.RunMode,
            mu.ConfigFileCode,
            mu.ConfigUpdateTime,
            mu.SampleConfigCode,
            mu.SoftwareVersion,
            mu.Description,
            mu.StartTime,
            mu.HeartbeatTime,
            mu.ConnectState,
            mu.UpdateTime,
            mu.IsSync,
            mu.SyncTime,
            mu.IsConfigOK,
            mu.ConfigFileCode_Old,
            mu.SampleConfigCode_Old,
            mu.AppCongfigId as appConfigId,
            mu.CanDistribute,
            mu.Enable,
            mu.ProjectName as rdsServer,
            mu.ContractNo as dataServer,
            mu.FSU,
            pi.ProjectName,
            pi.ContractNo,
            pi.InstallTime,
            ws.WorkStationName,
            port.portNos,
            ROW_NUMBER() over(PARTITION BY mu.MonitorUnitId ORDER BY tp.PortNo ASC) AS row
            FROM
            TSL_MonitorUnit mu left join TBL_MonitorUnitProjectInfo pi on mu.MonitorUnitId = pi.MonitorUnitId
            left join tbl_station ts on mu.StationId = ts.StationId
            left join tsl_port tp on mu.MonitorUnitId = tp.MonitorUnitId
            left join tbl_workstation ws on mu.WorkStationId = ws.WorkStationId
            LEFT JOIN (
                SELECT
                mu.MonitorUnitId,
                STRING_AGG(tp.PortNo::text, ',' ORDER BY tp.PortNo ASC) AS portNos
                FROM
                TSL_MonitorUnit mu left join TBL_MonitorUnitProjectInfo pi on mu.MonitorUnitId = pi.MonitorUnitId
                left join tsl_port tp on mu.MonitorUnitId = tp.MonitorUnitId
                left join tbl_workstation ws on mu.WorkStationId = ws.WorkStationId
                GROUP BY
                mu.MonitorUnitId
            ) port ON mu.MonitorUnitId = port.MonitorUnitId
        ) t
        WHERE t.row = 1
    </sql>
    <select id="selectAll" resultType="org.siteweb.config.common.dto.MonitorUnitDTO">
        <!-- adaptive-multi-pg 修改GROUP_CONCAT函数  -->
       <include refid="MonitorUnitSql"/>
    </select>

    <select id="selectByMonitorUnitId" resultType="org.siteweb.config.common.dto.MonitorUnitDTO">
        <!-- adaptive-multi-pg 修改GROUP_CONCAT函数  -->
        <include refid="MonitorUnitSql"/>
        AND  t.MonitorUnitId = #{monitorUnitId}
    </select>

    <select id="selectByStationId" resultType="org.siteweb.config.common.dto.MonitorUnitDTO">
        <!-- adaptive-multi-pg 修改GROUP_CONCAT函数  -->
        <include refid="MonitorUnitSql"/>
        AND  t.StationId = #{stationId}
    </select>

    <select id="selectByWorkStationId" resultType="org.siteweb.config.common.dto.MonitorUnitDTO">
        <!-- adaptive-multi-pg 修改GROUP_CONCAT函数  -->
        <include refid="MonitorUnitSql"/>
        AND  t.WorkStationId = #{workStationId}
    </select>

    <select id="selectByMonitorIds" resultType="org.siteweb.config.common.dto.MonitorUnitDTO">
        <!-- adaptive-multi-pg 修改GROUP_CONCAT函数  -->
        <include refid="MonitorUnitSql"/>
        AND
        t.MonitorUnitId IN
        <foreach collection="monitorUnitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>


    <select id="findMonitorUnitStationVOs" resultType="org.siteweb.config.common.vo.MonitorUnitStationVO">
        SELECT
        a.StationId,
        a.MonitorUnitId,
        a.MonitorUnitName,
        a.IpAddress,
        b.StationName
        FROM
        TSL_MonitorUnit a
        INNER JOIN
        TBL_Station b
        ON
        a.StationId = b.StationId
        WHERE
        (a.MonitorUnitCategory = 1 OR a.MonitorUnitCategory = 24)
        AND a.WorkStationId = #{workStationId};
    </select>

    <select id="findMonitorUnitDetails" resultType="org.siteweb.config.common.vo.MonitorUnitDetails">
        SELECT DISTINCT
            mu.MonitorUnitName,
            mu.MonitorUnitId,
            sp.PhoneNumber,
            sp.Description AS ReportIntervalTime,
            sp.Address,
            port.Setting,
            sp.SamplerUnitId
        FROM
            TSL_MonitorUnit mu,
            TSL_SamplerUnit sp,
            TSL_Port port,
            TBL_Equipment eq,
            TBL_EquipmentTemplate eqt
        WHERE
            mu.MonitorUnitId = sp.MonitorUnitId
            AND mu.MonitorUnitId = port.MonitorUnitId
            AND port.LinkSamplerUnitId = 0
            AND sp.SamplerType = 29
            AND mu.MonitorUnitId = eq.MonitorUnitId
            AND eq.EquipmentTemplateId = eqt.EquipmentTemplateId
            AND mu.WorkStationId = #{workstationId}
    </select>

    <select id="findStartCompareValue" resultType="java.lang.Double">
        SELECT
        evcond.StartCompareValue
        FROM
        TSL_MonitorUnit mu,
        TSL_SamplerUnit sp,
        TSL_Port port,
        TBL_Equipment eq,
        TBL_EquipmentTemplate eqt,
        TBL_Event event,
        TBL_EventCondition evcond
        WHERE
        mu.MonitorUnitId = sp.MonitorUnitId
        AND mu.MonitorUnitId = port.MonitorUnitId
        AND port.LinkSamplerUnitId = 0
        AND sp.SamplerType = 29
        AND mu.MonitorUnitId = eq.MonitorUnitId
        AND eq.EquipmentTemplateId = eqt.EquipmentTemplateId
        AND event.EventName = '温度'
        AND event.EquipmentTemplateId = eqt.EquipmentTemplateId
        AND event.EventId = evcond.EventId
        AND mu.MonitorUnitId = #{monitorUnitId}
        ORDER BY
        evcond.StartCompareValue
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT MonitorUnitId as id,MonitorUnitName as value FROM tsl_monitorunit WHERE MonitorUnitId in
        <foreach collection="monitorUnitIds" item="monitorUnitId" open="(" close=")" separator=",">
            #{monitorUnitId}
        </foreach>
    </select>
</mapper>