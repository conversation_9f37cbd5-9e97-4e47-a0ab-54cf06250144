<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblLogicClassEntryMapper">

    <update id="updateLogicClassByDataItem" parameterType="java.util.Map">
        <!-- adaptive-multi-pg UPDATE修改成通用sql  -->
       UPDATE TBL_LogicClassEntry
        SET LogicClass = (
            SELECT ItemValue
            FROM TBL_DataItem
            WHERE TBL_DataItem.EntryId = #{entryId}
              AND TBL_LogicClassEntry.Description = TBL_DataItem.ExtendField2
        )
        WHERE StandardType = #{standardType}
          AND EXISTS (
            SELECT 1
            FROM TBL_DataItem
            WHERE TBL_DataItem.EntryId = #{entryId}
              AND TBL_LogicClassEntry.Description = TBL_DataItem.ExtendField2
        )
    </update>
    <select id="maxEntryId" resultType="java.lang.Integer">
        SELECT max(EntryId)  from TBL_LogicClassEntry;
    </select>
    <select id="maxLogicClassId" resultType="java.lang.Integer">
        SELECT max(LogicClassId) FROM TBL_LogicClassEntry WHERE StandardType = #{standardType} AND EntryCategory = #{entryCategory}
    </select>
</mapper>

