<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblControlMapper">
    <resultMap id="findVoMap" type="org.siteweb.config.common.vo.ControlVO">
        <id column="id" property="id"/>
        <result column="equipmenttemplateid" property="equipmentTemplateId"/>
        <result column="controlid" property="controlId"/>
        <result column="controlname" property="controlName"/>
        <result column="controlcategory" property="controlCategory"/>
        <result column="cmdtoken" property="cmdToken"/>
        <result column="basetypeid" property="baseTypeId"/>
        <result column="controlseverity" property="controlSeverity"/>
        <result column="signalid" property="signalId"/>
        <result column="timeout" property="timeOut"/>
        <result column="retry" property="retry"/>
        <result column="description" property="description"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="displayindex" property="displayIndex"/>
        <result column="commandtype" property="commandType"/>
        <result column="controltype" property="controlType"/>
        <result column="datatype" property="dataType"/>
        <result column="maxvalue" property="maxValue"/>
        <result column="minvalue" property="minValue"/>
        <result column="defaultvalue" property="defaultValue"/>
        <result column="moduleno" property="moduleNo"/>
        <collection property="controlMeaningsList" ofType="org.siteweb.config.common.entity.TblControlMeanings" column="equipmenttemplateid,controlid" notNullColumn="meaningsId">
            <id column="meaningsId" property="id"/>
            <result column="equipmenttemplateid" property="equipmentTemplateId"/>
            <result column="controlid" property="controlId"/>
            <result column="ParameterValue" property="parameterValue"/>
            <result column="Meanings" property="meanings"/>
            <result column="BaseCondId" property="baseCondId"/>
        </collection>
    </resultMap>
    <resultMap id="controlConfigItemMap" type="org.siteweb.config.common.dto.ControlConfigItem">
        <id column="id" property="id"/>
        <result column="equipmenttemplateid" property="equipmentTemplateId"/>
        <result column="controlid" property="controlId"/>
        <result column="controlname" property="controlName"/>
        <result column="controlcategory" property="controlCategory"/>
        <result column="cmdtoken" property="cmdToken"/>
        <result column="basetypeid" property="baseTypeId"/>
        <result column="controlseverity" property="controlSeverity"/>
        <result column="signalid" property="signalId"/>
        <result column="timeout" property="timeOut"/>
        <result column="retry" property="retry"/>
        <result column="description" property="description"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="displayindex" property="displayIndex"/>
        <result column="commandtype" property="commandType"/>
        <result column="controltype" property="controlType"/>
        <result column="datatype" property="dataType"/>
        <result column="maxvalue" property="maxValue"/>
        <result column="minvalue" property="minValue"/>
        <result column="defaultvalue" property="defaultValue"/>
        <result column="moduleno" property="moduleNo"/>
        <result column="BaseTypeName" property="baseTypeName"/>
        <result column="baseStatusId" property="baseStatusId"/>
        <result column="baseNameExt" property="baseNameExt"/>
        <collection property="controlMeaningsList" ofType="org.siteweb.config.common.entity.TblControlMeanings" column="equipmenttemplateid,controlid" notNullColumn="meaningsId">
            <id column="meaningsId" property="id"/>
            <result column="equipmenttemplateid" property="equipmentTemplateId"/>
            <result column="controlid" property="controlId"/>
            <result column="ParameterValue" property="parameterValue"/>
            <result column="Meanings" property="meanings"/>
            <result column="BaseCondId" property="baseCondId"/>
        </collection>
    </resultMap>
    <sql id="findControlItemConfigSql">
        <!-- adaptive-multi-pg 去除反引号   -->
        SELECT a.id,
        a.equipmenttemplateid,
        a.controlid,
        a.controlname,
        a.controlcategory,
        a.cmdtoken,
        a.basetypeid,
        a.controlseverity,
        a.signalid,
        a.timeout,
        a.retry,
        a.description,
        a.enable,
        a.visible,
        a.displayindex,
        a.commandtype,
        a.controltype,
        a.datatype,
        a."maxvalue",
        a."minvalue",
        a.defaultvalue,
        a.moduleno,
        b.Id AS meaningsId,
        b.ParameterValue,
        b.Meanings,
        b.BaseCondId,
        c.BaseTypeName,
        c.baseStatusId,
        c.baseNameExt
        FROM tbl_control a
        LEFT JOIN tbl_controlmeanings b
            ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.ControlId = b.ControlId
        LEFT JOIN tbl_commandbasedic c
            ON a.BaseTypeId = c.BaseTypeId
    </sql>

    <insert id="createControl">
        INSERT INTO tbl_control(equipmenttemplateid, controlid, controlname, controlcategory, cmdtoken, basetypeid,
                                controlseverity, signalid, timeout, retry, description, enable, visible, displayindex,
                                commandtype, controltype, datatype, "maxvalue", "minvalue", defaultvalue, moduleno)
        VALUES (#{control.equipmentTemplateId}, #{control.controlId}, #{control.controlName}, #{control.controlCategory},
                #{control.cmdToken}, #{control.baseTypeId}, #{control.controlSeverity}, #{control.signalId},
                #{control.timeOut}, #{control.retry}, #{control.description}, #{control.enable}, #{control.visible},
                #{control.displayIndex}, #{control.commandType}, #{control.controlType}, #{control.dataType},
                #{control.maxValue}, #{control.minValue}, #{control.defaultValue}, #{control.moduleNo})
    </insert>
    <insert id="batchInsertControl">
        INSERT INTO tbl_control(equipmenttemplateid, controlid, controlname, controlcategory, cmdtoken, basetypeid,
        controlseverity, signalid, timeout, retry, description, enable, visible, displayindex,
        commandtype, controltype, datatype, "maxvalue", "minvalue", defaultvalue, moduleno) VALUES
        <foreach collection="controlList" item="control" separator=",">
            (#{control.equipmentTemplateId}, #{control.controlId}, #{control.controlName}, #{control.controlCategory},
            #{control.cmdToken}, #{control.baseTypeId}, #{control.controlSeverity}, #{control.signalId},
            #{control.timeOut}, #{control.retry}, #{control.description}, #{control.enable}, #{control.visible},
            #{control.displayIndex}, #{control.commandType}, #{control.controlType}, #{control.dataType},
            #{control.maxValue}, #{control.minValue}, #{control.defaultValue}, #{control.moduleNo})
        </foreach>
    </insert>
    <delete id="deleteControl">
        DELETE FROM TBL_ControlMeanings t WHERE t.ControlId=#{controlId} AND t.EquipmentTemplateId=#{equipmentTemplateId};
        DELETE FROM TBL_Control t WHERE t.ControlId=#{controlId} AND t.EquipmentTemplateId=#{equipmentTemplateId};
    </delete>
    <select id="findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT t.BaseTypeId
        FROM tbl_control t
        WHERE t.EquipmentTemplateId = #{equipmentTemplateId}
          AND t.BaseTypeId IS NOT NULL
          AND t.BaseTypeId != 0
          AND t.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_CommandBaseDic)
    </select>
    <select id="findMaxControlIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(ControlId)
        FROM TBL_Control
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(DisplayIndex)
        FROM TBL_Control
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findByEquipmentTemplateId" resultType="org.siteweb.config.common.entity.TblControl">
        <!-- adaptive-multi-pg 去除反引号  -->
        SELECT id,
               equipmenttemplateid,
               controlid,
               controlname,
               controlcategory,
               cmdtoken,
               basetypeid,
               controlseverity,
               signalid,
               timeout,
               retry,
               description,
               enable,
               visible,
               displayindex,
               commandtype,
               controltype,
               datatype,
               "maxvalue",
               "minvalue",
               defaultvalue,
               moduleno
        FROM tbl_control
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findVoByEquipmentTemplateId" resultMap="findVoMap">
        <!-- adaptive-multi-pg 去除反引号  -->
        SELECT a.id,
               a.equipmenttemplateid,
               a.controlid,
               a.controlname,
               a.controlcategory,
               a.cmdtoken,
               a.basetypeid,
               a.controlseverity,
               a.signalid,
               a.timeout,
               a.retry,
               a.description,
               a.enable,
               a.visible,
               a.displayindex,
               a.commandtype,
               a.controltype,
               a.datatype,
               a."maxvalue",
               a."minvalue",
               a.defaultvalue,
               a.moduleno,
               b.Id AS meaningsId,
               b.ParameterValue,
               b.Meanings,
               b.BaseCondId
        FROM tbl_control a
        LEFT JOIN tbl_controlmeanings b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.ControlId = b.ControlId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findAllControlItem" resultMap="controlConfigItemMap">
        <include refid="findControlItemConfigSql"/>
    </select>

    <select id="findControlItemByEquipmentTemplateId" resultMap="controlConfigItemMap">
        <include refid="findControlItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findControlItemByEquipmentTemplateIds" resultMap="controlConfigItemMap">
        <include refid="findControlItemConfigSql"/>
        where a.equipmentTemplateId IN
        <foreach item="item" collection="equipmentTemplateIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByEquipmentTemplateIdAndControlId" resultMap="controlConfigItemMap">
        <include refid="findControlItemConfigSql"/> where a.equipmentTemplateId = #{equipmentTemplateId} AND a.controlId = #{controlId}
    </select>

    <select id="diffControl" resultType="org.siteweb.config.common.entity.TblControl">
        SELECT ControlId,ControlName
        FROM TBL_Control
        WHERE EquipmentTemplateId = #{oldEquipmentTemplateId}
          AND ControlId NOT IN (SELECT ControlId FROM TBL_Control WHERE EquipmentTemplateId = #{newEquipmentTemplateId})
    </select>
    <select id="getControlProgressList" resultType="org.siteweb.config.common.dto.ControlProgressDTO">
        SELECT
        s.EquipmentTemplateId,
        s.ControlId,
        s.CommandType,
        s.BaseTypeId,
        c.BaseCondId,
        c.ParameterValue
        FROM
        TBL_Control s
        LEFT JOIN TBL_ControlMeanings c ON
        s.EquipmentTemplateId = c.EquipmentTemplateId
        AND s.ControlId = c.ControlId
        ORDER BY
        s.EquipmentTemplateId
    </select>
    <sql id="findControlBaseClassDetailsSql">
        SELECT
        s.EquipmentTemplateId,
        e.EquipmentTemplateName,
        e.ParentTemplateId,
        (
        SELECT
        EquipmentTemplateName
        FROM
        TBL_EquipmentTemplate
        WHERE
        EquipmentTemplateId = e.ParentTemplateId
        LIMIT 1) AS ParentTemplateName,
        e.ProtocolCode,
        s.ControlId,
        s.ControlName,
        c.Meanings,
        s.ControlCategory,
        s.CommandType,
        s.CmdToken,
        s.BaseTypeId,
        command.BaseTypeName,
        command.baseNameExt,
        c.BaseCondId,
        e.EquipmentBaseType AS CategoryOrBaseType,
        e.EquipmentBaseType,
        c.ParameterValue
        FROM
        TBL_EquipmentTemplate e
        INNER JOIN TBL_Control s ON  e.EquipmentTemplateId = s.EquipmentTemplateId
        LEFT JOIN TBL_ControlMeanings c ON 	s.EquipmentTemplateId = c.EquipmentTemplateId AND s.ControlId = c.ControlId
        LEFT JOIN tbl_commandbasedic command  ON s.BaseTypeId = command.BaseTypeId
    </sql>
    <select id="findControlBaseClassDetails"  resultType="org.siteweb.config.common.dto.ControlBaseClassDetailDTO">
        <include refid="findControlBaseClassDetailsSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
        <if test="controlName != null and controlName != ''">
            AND s.ControlName = #{controlName}
        </if>
        <choose>
            <when test="meanings != null and meanings != ''">
                AND c.Meanings = #{meanings}
            </when>
            <otherwise>
                AND c.Meanings IS NULL
            </otherwise>
        </choose>
    </select>
    <select id="findControlBaseClass" resultType="org.siteweb.config.common.dto.ControlBaseClassDetailDTO">
        <include refid="findControlBaseClassDetailsSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT concat(a.EquipmentTemplateId,'.',a.ControlId) as id,concat(b.EquipmentTemplateName,'.',a.ControlName) as value
        FROM tbl_control a inner join tbl_equipmentTemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE
        (a.EquipmentTemplateId, a.ControlId) IN
        <foreach item="item" collection="controlUniqueIds" separator="," open="(" close=")">
            (#{item.equipmentTemplateId}, #{item.controlId})
        </foreach>
    </select>
    <insert id="batchInsertLianTongControls">
        <!-- adaptive-multi-pg 去除反引号  -->
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000072,100010132,'遥控断路器开关',1,'','201019001',1,100010130,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000076,100010142,'遥控开关机',1,'10','301003001',1,100010136,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000076,100010143,'遥控紧急停机',1,'','301102001',1,100010136,NULL,NULL,'',1,1,2,2,1,0,0,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000076,100010145,'遥控手自动模式',1,'10','301009001',1,100010138,NULL,NULL,'',1,1,4,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000076,100010147,'遥控主备用模式',1,'10','301307001',1,100010140,NULL,NULL,'',1,1,6,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000077,100010144,'遥控开关机',1,'10','302003001',1,100010138,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000077,100010145,'遥控紧急停机',1,'','302102001',1,100010138,NULL,NULL,'',1,1,2,2,1,0,0,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000077,100010147,'遥控自动模式',1,'10','302009001',1,100010140,NULL,NULL,'',1,1,4,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000077,100010149,'遥控主备用模式',1,'10','302307001',1,100010142,NULL,NULL,'',1,1,6,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000086,100010151,'设定温度',1,'','704053001',1,100010146,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000086,100010152,'遥控开关机',1,'','704003001',1,null,NULL,NULL,'',1,1,3,2,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000087,100010154,'遥控开门命令',1,'10','1001010001',1,100010150,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010151,'开机温度(室内);参数',1,'','806306001',1,100010147,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010152,'关机温度(室内);参数',1,'','806303001',1,100010147,NULL,NULL,'',1,1,3,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010153,'开机温度(室外);参数',1,'','806311001',1,100010148,NULL,NULL,'',1,1,4,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010154,'关机温度(室外);参数',1,'','806312001',1,100010148,NULL,NULL,'',1,1,5,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010155,'室内外温差参数',1,'','806304001',1,null,NULL,NULL,'',1,1,6,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010156,'遥控开机',1,'','806001001',1,100010149,NULL,NULL,'',1,1,7,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000089,100010157,'遥控关机',1,'','806002001',1,100010149,NULL,NULL,'',1,1,8,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010151,'遥控开关机',1,'','805001001',1,100010150,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010152,'开机温度(室内);参数',1,'','805306001',1,100010148,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010153,'关机温度(室内);参数',1,'','805303001',1,100010148,NULL,NULL,'',1,1,3,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010154,'开机温度(室外);参数',1,'','805311001',1,null,NULL,NULL,'',1,1,4,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010155,'关机温度(室外);参数',1,'','805312001',1,null,NULL,NULL,'',1,1,5,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000090,100010156,'室内外温差参数',1,'','805304001',1,null,NULL,NULL,'',1,1,6,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010158,'远程开关机',1,'10','807003001',1,100010156,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010159,'开机温度(室内);参数',1,'','807302001',1,100010146,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010160,'关机温度(室内);参数',1,'','807303001',1,100010146,NULL,NULL,'',1,1,3,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010161,'开机湿度(室内);参数',1,'','807309001',1,100010148,NULL,NULL,'',1,1,4,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010162,'关机湿度(室内);参数',1,'','807310001',1,100010148,NULL,NULL,'',1,1,5,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010163,'开机温度(室外);参数',1,'','807311001',1,100010147,NULL,NULL,'',1,1,6,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010164,'关机温度(室外);参数',1,'','807312001',1,100010147,NULL,NULL,'',1,1,7,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010165,'开机湿度(室外);参数',1,'','807313001',1,100010149,NULL,NULL,'',1,1,8,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010166,'关机湿度(室外);参数',1,'','807314001',1,100010149,NULL,NULL,'',1,1,9,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000091,100010167,'室内外温差参数',1,'','807304001',1,null,NULL,NULL,'',1,1,10,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000092,100010154,'远程开关机',1,'10','701003001',1,100010152,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000092,100010155,'温度设定',1,'12','701053001',1,100010150,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000093,100010140,'遥控方阵投入撤出',1,'10','902003001',1,100010138,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010158,'远程开关机',1,'10','801003001',1,100010156,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010159,'开机温度(室内);参数',1,'','801302001',1,100010145,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010160,'关机温度(室内);参数',1,'','801303001',1,100010145,NULL,NULL,'',1,1,3,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010161,'开机湿度(室内);参数',1,'','801309001',1,100010147,NULL,NULL,'',1,1,4,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010162,'关机湿度(室内);参数',1,'','801310001',1,100010147,NULL,NULL,'',1,1,5,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010163,'开机温度(室外);参数',1,'','801311001',1,100010146,NULL,NULL,'',1,1,6,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010164,'关机温度(室外);参数',1,'','801312001',1,100010146,NULL,NULL,'',1,1,7,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010165,'开机湿度(室外);参数',1,'','801313001',1,100010148,NULL,NULL,'',1,1,8,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010166,'关机湿度(室外);参数',1,'','801314001',1,100010148,NULL,NULL,'',1,1,9,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000094,100010167,'室内外温差参数',1,'','801304001',1,null,NULL,NULL,'',1,1,10,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000095,100010152,'设定温度参数',1,'','804053001',1,100010149,NULL,NULL,'',1,1,1,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000096,100010132,'远程开关机',1,'10','404003001',1,null,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000096,100010134,'遥控均浮充',1,'10','404006001',1,null,NULL,NULL,'',1,1,3,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000096,100010135,'遥控测试',1,'','404107001',1,null,NULL,NULL,'',1,1,4,2,1,0,0,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000100,100010148,'远程开关机',1,'10','702003001',1,100010146,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000100,100010149,'温度设定',1,'','702302001',1,100010144,NULL,NULL,'',1,1,2,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000100,100010150,'湿度设定',1,'','702054001',1,100010145,NULL,NULL,'',1,1,3,1,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000101,100010147,'远程开关机',1,'10','401003001',1,null,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000101,100010149,'遥控均浮充',1,'10','401006001',1,null,NULL,NULL,'',1,1,3,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000101,100010150,'遥控放电测试',1,'','401107001',1,null,NULL,NULL,'',1,1,4,2,1,0,0,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000109,100010136,'远程开关机',1,'10','408003001',1,null,NULL,NULL,'',1,1,1,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000109,100010138,'遥控均浮充',1,'10','408006001',1,null,NULL,NULL,'',1,1,3,2,1,0,1,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000109,100010139,'遥控测试',1,'0','408107001',1,null,NULL,NULL,'',1,1,4,2,1,0,0,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000104,100010177,'遥控测试放电',1,'','1101303001',1,null,NULL,NULL,'',1,1,1,2,1,0,99,0,NULL,0);
        INSERT INTO TBL_Control (EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, CommandType, ControlType, DataType, "maxvalue", "minvalue", DefaultValue, ModuleNo) VALUES (123000104,100010180,'蓄电池健康度下限',1,'','1101336001',1,null,NULL,NULL,'',1,1,4,1,1,0,99,0,NULL,0);
    </insert>
    <insert id="insertControl">
        <!-- adaptive-multi-pg 去除反引号  -->
        INSERT INTO tbl_control (
        equipmentTemplateId,
        ControlId,
        controlName,
        controlCategory,
        cmdToken,
        baseTypeId,
        controlSeverity,
        signalId,
        timeOut,
        retry,
        description,
        enable,
        visible,
        displayIndex,
        commandType,
        controlType,
        dataType,
        "maxvalue",
        "minvalue",
        defaultValue,
        moduleNo
        ) VALUES (
        #{equipmentTemplateId},
        #{controlId},
        #{controlName},
        #{controlCategory},
        #{cmdToken},
        #{baseTypeId},
        #{controlSeverity},
        #{signalId},
        #{timeOut},
        #{retry},
        #{description},
        #{enable},
        #{visible},
        #{displayIndex},
        #{commandType},
        #{controlType},
        #{dataType},
        #{maxValue},
        #{minValue},
        #{defaultValue},
        #{moduleNo}
        )
    </insert>
    <update id="updateControl">
        <!-- adaptive-multi-pg 去除反引号  -->
        UPDATE tbl_control
        <set>
            <if test="controlName != null">controlName = #{controlName},</if>
            <if test="controlCategory != null">controlCategory = #{controlCategory},</if>
            <if test="cmdToken != null">cmdToken = #{cmdToken},</if>
            baseTypeId = #{baseTypeId},
            <if test="controlSeverity != null">controlSeverity = #{controlSeverity},</if>
            <if test="signalId != null">signalId = #{signalId},</if>
            <if test="timeOut != null">timeOut = #{timeOut},</if>
            <if test="retry != null">retry = #{retry},</if>
            <if test="description != null">description = #{description},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="displayIndex != null">displayIndex = #{displayIndex},</if>
            <if test="commandType != null">commandType = #{commandType},</if>
            <if test="controlType != null">controlType = #{controlType},</if>
            <if test="dataType != null">dataType = #{dataType},</if>
            <if test="maxValue != null">"maxvalue" = #{maxValue},</if>
            <if test="minValue != null">"minvalue" = #{minValue},</if>
            <if test="defaultValue != null">defaultValue = #{defaultValue},</if>
            <if test="moduleNo != null">moduleNo = #{moduleNo}</if>
        </set>
        WHERE
        equipmentTemplateId = #{equipmentTemplateId}
        AND controlId = #{controlId}
    </update>

    <select id="getApplyStandards" resultType="org.siteweb.config.common.dto.ControlApplyStandardDTO">
        SELECT control.id,
        COALESCE(CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 10) = 1 THEN replace(dic.ControlStandardName,'XX',(control.BaseTypeId - floor(control.BaseTypeId / 1000)*1000)::VARCHAR ) ELSE NULL END, control.ControlName) AS ControlName
        FROM
        TBL_Control control
        INNER JOIN TBL_EquipmentTemplate template ON template.EquipmentTemplateId = control.EquipmentTemplateId
        INNER JOIN TBL_CommandBaseMap map ON FLOOR(map.BaseTypeId / 1000) = FLOOR(control.BaseTypeId / 1000)
        INNER JOIN TBL_StandardDicControl dic ON dic.StandardDicId = map.StandardDicId AND dic.StationCategory = map.StationBaseType AND dic.StandardType = map.StandardType
        WHERE dic.StationCategory = template.StationCategory
        AND dic.StandardType = #{standardId}
        <if test="equipmentTemplateIds != null and equipmentTemplateIds.size > 0">
            AND control.EquipmentTemplateId in
            <foreach collection="equipmentTemplateIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="restoreStandard">
        UPDATE TBL_Control
        SET ControlName = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 10) = 1 THEN bak.ControlName ELSE TBL_Control.ControlName END
        FROM TBL_StandardBack bak
        WHERE bak.EquipmentTemplateId = TBL_Control.EquipmentTemplateId
        AND bak.EntryId= TBL_Control.ControlId
        AND bak.EntryCategory = 3
    </update>
    <update id="batchUpdateField">
        <foreach collection="controlList" item="control" separator=";">
            UPDATE tbl_control
            <set>
                <if test="control.equipmentTemplateId != null">
                    EquipmentTemplateId = #{control.equipmentTemplateId},
                </if>
                <if test="control.controlName != null">
                    ControlName = #{control.controlName},
                </if>
                <if test="control.controlCategory != null">
                    ControlCategory = #{control.controlCategory},
                </if>
                <if test="control.cmdToken != null">
                    CmdToken = #{control.cmdToken},
                </if>
                <if test="control.cmdToken != control.baseTypeId">
                    BaseTypeId = #{control.baseTypeId},
                </if>
                <if test="control.controlSeverity != null">
                    ControlSeverity = #{control.controlSeverity},
                </if>
                <if test="control.signalId != null">
                    SignalId = #{control.signalId},
                </if>
                <if test="control.timeOut != null">
                    TimeOut = #{control.timeOut},
                </if>
                <if test="control.retry != null">
                    Retry = #{control.retry},
                </if>
                <if test="control.description != null">
                    Description = #{control.description},
                </if>
                <if test="control.enable != null">
                    Enable = #{control.enable},
                </if>
                <if test="control.visible != null">
                    Visible = #{control.visible},
                </if>
                <if test="control.displayIndex != null">
                    DisplayIndex = #{control.displayIndex},
                </if>
                <if test="control.commandType != null">
                    CommandType = #{control.commandType},
                </if>
                <if test="control.controlType != null">
                    ControlType = #{control.controlType},
                </if>
                <if test="control.dataType != null">
                    DataType = #{control.dataType},
                </if>
                <if test="control.maxValue != null">
                    "maxvalue" = #{control.maxValue},
                </if>
                <if test="control.minValue != null">
                    "minvalue" = #{control.minValue},
                </if>
                <if test="control.defaultValue != null">
                    DefaultValue = #{control.defaultValue},
                </if>
                <if test="control.moduleNo != null">
                    ModuleNo = #{control.moduleNo},
                </if>
            </set>
            WHERE EquipmentTemplateId = #{control.equipmentTemplateId} and ControlId = #{control.controlId}
        </foreach>
    </update>

    <select id="getStandardCompareData" resultType="org.siteweb.config.common.dto.StandardControlCompareDTO">
        SELECT
        bak.EquipmentTemplateId AS equipmentTemplateIdBefore,
        template.EquipmentTemplateName AS equipmentTemplateNameBefore,
        bak.EntryId AS controlIdBefore,
        bak.ControlName AS controlNameBefore,
        control.EquipmentTemplateId AS equipmentTemplateIdAfter,
        template.EquipmentTemplateName AS equipmentTemplateNameAfter,
        control.ControlId AS controlIdAfter,
        control.ControlName AS controlNameAfter
        FROM
        TBL_StandardBack bak
        LEFT JOIN TBL_EquipmentTemplate template ON
        template.EquipmentTemplateId = bak.EquipmentTemplateId
        LEFT JOIN TBL_Control control ON
        bak.EquipmentTemplateId = control.EquipmentTemplateId
        AND bak.EntryId = control.ControlId
        WHERE
        bak.EntryCategory = 3
    </select>

    <select id="getControlStandardApplyCheckData" resultType="org.siteweb.config.common.dto.StandardApplyControlCheckDTO">
        SELECT
        (CASE
        WHEN (
        SELECT
        COUNT(*)
        FROM
        TBL_StandardBack sb
        WHERE
        sb.EntryCategory = 3
        AND sb.EquipmentTemplateId = control.EquipmentTemplateId
        AND sb.EntryId = control.ControlId) >= 1 THEN 1
        ELSE 0
        END) AS Standarded,
        control.EquipmentTemplateId,
        et.EquipmentTemplateName,
        control.ControlId,
        control.ControlName,
        control.BaseTypeId,
        cbd.BaseTypeName,
        sdc.StandardDicId,
        sdc.ControlStandardName,
        sdc.EquipmentLogicClassId,
        sdc.EquipmentLogicClass,
        ebt.BaseEquipmentId,
        ebt.BaseEquipmentName
        FROM
        TBL_Control control
        LEFT JOIN TBL_EquipmentTemplate et ON
        et.EquipmentTemplateId = control.EquipmentTemplateId
        LEFT JOIN TBL_CommandBaseMap cbm ON
        cbm.BaseTypeId = control.BaseTypeId
        AND et.StationCategory = cbm.StationBaseType
        AND cbm.StandardType = #{standardId}
        LEFT JOIN TBL_StandardDicControl sdc ON
        sdc.StandardDicId = cbm.StandardDicId
        AND sdc.StandardType = cbm.StandardType
        AND sdc.StationCategory = cbm.StationBaseType
        AND sdc.StandardType = #{standardId}
        LEFT JOIN TBL_CommandBaseDic cbd ON
        cbd.BaseTypeId = control.BaseTypeId
        LEFT JOIN TBL_EquipmentBaseType ebt ON
        ebt.BaseEquipmentId = cbd.BaseEquipmentId
    </select>

    <select id="getControStandardMappingCheck" resultType="org.siteweb.config.common.dto.StandardMappingControlCheckDTO">
        SELECT
        DISTINCT control.ControlId,
        control.ControlName,
        (
        CASE
        WHEN dicControl.StandardDicId IS NULL THEN 0
        ELSE 1
        END
        ) AS Standarded,
        station.StationId,
        station.StationName,
        station.StationCategory,
        equip.EquipmentId,
        equip.EquipmentName,
        equip.EquipmentCategory,
        eq.EquipmentTemplateId,
        eq.EquipmentTemplateName,
        dic.BaseTypeId,
        dic.BaseTypeName,
        dicControl.StandardDicId,
        REPLACE(dicControl.ControlStandardName, 'XX', RIGHT(concat('0', (control.BaseTypeId - floor(control.BaseTypeId / 1000)* 1000)::VARCHAR), 2)) AS ControlStandardName
        FROM
        TBL_Station station
        LEFT JOIN TBL_Equipment equip ON
        station.StationId = equip.StationId
        LEFT JOIN TBL_EquipmentTemplate eq ON
        eq.EquipmentTemplateId = equip.EquipmentTemplateId
        LEFT JOIN TBL_Control control ON
        eq.EquipmentTemplateId = control.EquipmentTemplateId
        LEFT JOIN TBL_CommandBaseDic dic ON
        dic.BaseTypeId = control.BaseTypeId
        LEFT JOIN TBL_StationBaseMap stationMap ON
        stationMap.StationCategory = station.StationCategory
        AND stationMap.StandardType = #{standardId}
        LEFT JOIN TBL_CommandBaseMap MAP ON
        FLOOR(map.BaseTypeId / 1000)= FLOOR(dic.BaseTypeId / 1000)
        AND map.StationBaseType = stationMap.StationBaseType
        AND map.StandardType = #{standardId}
        LEFT JOIN TBL_StandardDicControl dicControl ON
        dicControl.StandardDicId = map.StandardDicId
        AND (
        dicControl.StationCategory = map.StationBaseType
        OR dicControl.StationCategory = 0
        )
        AND dicControl.StandardType = #{standardId}
        WHERE
        control.ControlId IS NOT NULL
        <if test="equipmentCategory != null">
            AND equip.EquipmentCategory = #{equipmentCategory}
        </if>
        ORDER BY StationId
    </select>
    <select id="findByEquipmentCategory" resultType="org.siteweb.config.common.entity.TblControl">
        SELECT
        s.EquipmentTemplateId,
        s.ControlId ,
        s.ControlName ,
        s.Description
        FROM
        tbl_control s
        INNER JOIN tbl_equipmenttemplate eq ON s.EquipmentTemplateId = eq.EquipmentTemplateId
        WHERE
        eq.EquipmentCategory = #{equipmentCategory}
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="org.siteweb.config.common.dto.excel.ControlExcel">
        SELECT b.EquipmentTemplateId,
               a.EquipmentId,
               a.EquipmentName,
               b.ControlId,
               b.ControlName,
               c.ItemValue,
               b.cmdToken,
               b."maxvalue",
               b."minvalue",
               d.ParameterValue,
               d.Meanings
        FROM tbl_control b
                 LEFT JOIN
             tbl_equipment a
             ON a.EquipmentTemplateId = b.EquipmentTemplateId
                 LEFT JOIN
             tbl_dataitem c ON c.EntryId = 31
                 AND c.ItemId = b.ControlCategory
                 LEFT JOIN
             tbl_controlmeanings d
             ON d.EquipmentTemplateId = b.EquipmentTemplateId
                 AND d.ControlId = b.ControlId
        WHERE b.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>