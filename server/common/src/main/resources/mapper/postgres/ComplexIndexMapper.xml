<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.ComplexIndexMapper">

    <select id="findByObjectIdAndObjectTypeId" resultType="org.siteweb.config.common.dto.ComplexIndexDTO">
        SELECT
        c.ComplexIndexId,
        c.ComplexIndexName,
        c.objectId,
        c.objectTypeId,
        cb.BusinessTypeName,
        CASE
        WHEN c.ObjectTypeId = 7 THEN te.EquipmentName
        WHEN c.ObjectTypeId = 8 THEN co.commonobjectname
        WHEN c.ObjectTypeId = 9 THEN crack.ComputerRackName
        WHEN c.ObjectTypeId = 10 THEN i.ITDeviceName
        ELSE rs.ResourceStructureName
        END AS ObjectName
        FROM
        complexindex c
        INNER JOIN
        complexindexbusinesstype cb ON c.BusinessTypeId = cb.BusinessTypeId
        LEFT JOIN
        tbl_equipment te ON te.EquipmentId = c.ObjectId
        LEFT JOIN
        itdevice i ON i.ITDeviceId = c.ObjectId
        LEFT JOIN
        resourcestructure rs ON rs.ResourceStructureId = c.ObjectId
        LEFT JOIN
        commonobject co ON co.commonobjectid = c.ObjectId
        LEFT JOIN
        computerrack crack ON crack.ComputerRackId = c.ObjectId
        <where>
            <if test="objectId != null and objectId.size > 0">
                c.ObjectId IN
                <foreach collection="objectId" item="id" open="(" separator="," close=")">
                    #{id, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="objectTypeId != null">
                c.ObjectTypeId = #{objectTypeId,jdbcType=INTEGER}
            </if>
        </where>
    </select>


</mapper>