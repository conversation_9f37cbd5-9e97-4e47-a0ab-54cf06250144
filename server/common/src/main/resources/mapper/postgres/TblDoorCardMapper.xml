<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblDoorCardMapper">
    <delete id="deleteByEquipmentId">
        <!-- adaptive-multi-pg DELETE修改成通用sql  -->
        DELETE FROM TBL_DoorCard m
        WHERE EXISTS (
        SELECT 1
        FROM TBL_DoorTimeGroup d
        INNER JOIN TBL_Door e ON d.DoorId = e.DoorId
        WHERE m.DoorId = d.DoorId
        AND m.TimeGroupId = d.TimeGroupId
        AND e.EquipmentId = #{equipmentId}
        )
    </delete>
    <select id="findCardIdByEquipmentId" resultType="java.lang.Integer">
        SELECT doorCard.CardId
        FROM tbl_doorcard doorCard
                 INNER JOIN tbl_door door ON door.DoorId = doorCard.DoorId
        WHERE door.EquipmentId = #{equipmentId}
    </select>
</mapper>