<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEquipmentTemplateMapper">
    <select id="findTree" resultType="org.siteweb.config.common.dto.EquipmentTemplateTreeDTO">
        SELECT ParentTemplateId      AS parentId,
               EquipmentTemplateId   AS id,
               EquipmentTemplateName AS name,
               equipmentCategory     AS equipmentCategory
        FROM tbl_equipmenttemplate
    </select>
    <select id="findReferenceSamplerNameByProtocolCodes" resultType="java.lang.String">
        SELECT b.SamplerName FROM tbl_equipmenttemplate a inner JOIN tsl_sampler b on a.protocolcode = b.ProtocolCode
        WHERE b.ProtocolCode IN
        <foreach collection="protocolCodeList" item="protocolCode" open="(" close=")" separator=",">
            #{protocolCode}
        </foreach>
    </select>
    <select id="findEquipmentTemplateByProtocolCodes" resultType="org.siteweb.config.common.vo.SamplerVO">
        SELECT a.protocolcode,a.EquipmentTemplateName FROM tbl_equipmenttemplate a
        WHERE a.ProtocolCode IN
        <foreach collection="protocolCodeList" item="protocolCode" open="(" close=")" separator=",">
            #{protocolCode}
        </foreach>
        AND ParentTemplateId = 0 AND a.protocolcode is not null
    </select>
    <select id="findTreeByEquipmentCategory" resultType="org.siteweb.config.common.dto.EquipmentTemplateTreeDTO">
        SELECT ParentTemplateId AS parentId,
        EquipmentTemplateId AS id,
        EquipmentTemplateName AS name,
        equipmentCategory
        AS equipmentCategory
        FROM tbl_equipmenttemplate
        <where>
            <if test="equipmentCategory != null">
                EquipmentCategory = #{equipmentCategory}
            </if>
        </where>
    </select>
    <select id="findVoByEquipmentTemplateId" resultType="org.siteweb.config.common.vo.EquipmentTemplateVO">
        SELECT a.equipmenttemplateid,
               a.equipmenttemplatename,
               a.parenttemplateid,
               a.memo,
               a.protocolcode,
               a.equipmentcategory,
               a.equipmenttype,
               a.property,
               a.description,
               a.equipmentstyle,
               a.unit,
               a.vendor,
               a.equipmentbasetype,
               a.stationcategory,
               a.photo,
               b.SamplerName
        FROM tbl_equipmenttemplate a
                 LEFT JOIN tsl_sampler b ON a.protocolcode = b.ProtocolCode
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <update id="updateEquipmentCategoryByCategoryIdMap" parameterType="java.util.Map">
        <!-- adaptive-multi-dm UPDATE修改成通用sql  -->
        UPDATE TBL_EquipmentTemplate
        SET EquipmentCategory = (
            SELECT BusinessCategoryId
            FROM TBL_CategoryIdMap
            WHERE BusinessId = #{businessId}
              AND CategoryTypeId = #{categoryTypeId}
              AND OriginalCategoryId = TBL_EquipmentTemplate.EquipmentCategory
        )
        WHERE EXISTS (
            SELECT 1
            FROM TBL_CategoryIdMap
            WHERE BusinessId = #{businessId}
              AND CategoryTypeId = #{categoryTypeId}
              AND OriginalCategoryId = TBL_EquipmentTemplate.EquipmentCategory
        )
    </update>

    <select id="findEquipmentTemplateIdByEquipmentTemplateIdDiv" resultType="java.lang.Integer">
        SELECT EquipmentTemplateId FROM TBL_EquipmentTemplate
        WHERE FLOOR(TBL_EquipmentTemplate.EquipmentTemplateId/ 1000000)  = #{equipmentTemplateIdDiv}
    </select>

    <insert id="batchInsertLianTongEquipmentTemplate">
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000069,'temp直流操作电源柜',0,'联通预制模板','VirtualSampler_755000013',17,1,'','','','','',102,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000070,'temp10KV/400V',0,'联通预制模板','VirtualSampler_755000014',18,1,'','','','','',1601,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000071,'temp10KV配电设备',0,'联通预制模板','VirtualSampler_755000015',11,1,'','','','','',101,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000072,'temp低压配电柜',0,'联通预制模板','VirtualSampler_755000016',12,1,'','','','','',201,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000073,'temp电容补偿柜',0,'联通预制模板','VirtualSampler_755000017',27,1,'','','','','',204,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000074,'temp谐波抑制柜',0,'联通预制模板','VirtualSampler_755000018',28,1,'','','','','',205,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000075,'temp备用电源投入装置',0,'联通预制模板','VirtualSampler_755000019',30,1,'','','','','',203,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000076,'temp柴油发电机组',0,'联通预制模板','VirtualSampler_755000020',13,1,'','','','','',301,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000077,'temp燃气轮发电机组',0,'联通预制模板','VirtualSampler_755000021',65,1,'','','','','',302,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000078,'tempUPS',0,'联通预制模板','VirtualSampler_755000022',31,1,'','','','','',501,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000079,'temp模块化UPS',0,'联通预制模板','VirtualSampler_755000023',35,1,'','','','','',504,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000080,'temp交流配电屏',0,'联通预制模板','VirtualSampler_755000024',37,1,'','','','','',1701,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000081,'temp交流配电箱',0,'联通预制模板','VirtualSampler_755000025',38,1,'','','','','',1702,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000082,'temp冷冻系统',0,'联通预制模板','VirtualSampler_755000026',42,1,'','','','','',705,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000083,'temp防雷箱',0,'联通预制模板','VirtualSampler_755000027',72,1,'','','','','',2105,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000084,'temp环境',0,'联通预制模板','VirtualSampler_755000028',51,1,'','','','','',1004,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000085,'temp48V交流屏',0,'联通预制模板','VirtualSampler_755000031',21,1,'','','','','',1701,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000086,'temp空调系统',0,'联通预制模板','VirtualSampler_755000032',42,1,'','','','','',704,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000087,'temp门禁系统',0,'联通预制模板','VirtualSampler_755000034',82,1,'','','','','',1001,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000088,'temp逆变器',0,'联通预制模板','VirtualSampler_755000036',41,1,'','','','','',2109,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000089,'temp热管设备',0,'联通预制模板','VirtualSampler_755000038',69,1,'','','','','',806,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000090,'temp热交换设备',0,'联通预制模板','VirtualSampler_755000039',68,1,'','','','','',805,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000091,'temp湿膜新风系统',0,'联通预制模板','VirtualSampler_755000040',88,1,'','','','','',807,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000092,'temp舒适性空调',0,'联通预制模板','VirtualSampler_755000041',45,1,'','','','','',701,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000093,'temp太阳能供电',0,'联通预制模板','VirtualSampler_755000042',33,1,'','','','','',1801,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000094,'temp新风系统',0,'联通预制模板','VirtualSampler_755000044',86,1,'','','','','',801,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000095,'temp蓄电池分区温控',0,'联通预制模板','VirtualSampler_755000045',67,1,'','','','','',804,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000096,'temp48V整流屏',0,'联通预制模板','VirtualSampler_755000046',20,1,'','','','','',404,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000097,'temp48V直流屏',0,'联通预制模板','VirtualSampler_755000048',23,1,'','','','','',405,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000098,'temp直流远供局端电源',0,'联通预制模板','VirtualSampler_755000049',32,1,'','','','','',604,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000099,'temp直流直流变换器',0,'联通预制模板','VirtualSampler_755000050',34,1,'','','','','',602,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000100,'temp专用空调（风冷）',0,'联通预制模板','VirtualSampler_755000051',43,1,'','','','','',702,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000101,'temp48V组合开关电源',0,'联通预制模板','VirtualSampler_755000052',22,1,'','','','','',401,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000102,'temp48V配电屏',0,'联通预制模板','VirtualSampler_755000055',39,1,'','','','','',601,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000103,'temp48V配电箱',0,'联通预制模板','VirtualSampler_755000056',40,1,'','','','','',603,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000105,'tempUPS铅酸阀控蓄电池组',0,'联通预制模板','VirtualSampler_755000059',36,1,'','','','','',503,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000106,'temp动环监控系统',0,'联通预制模板','VirtualSampler_755000060',99,1,'','','','','',1301,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000107,'temp240V铅酸阀控蓄电池组',0,'联通预制模板','VirtualSampler_755000061',26,1,'','','','','',1103,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000108,'temp240V交流屏',0,'联通预制模板','VirtualSampler_755000062',14,1,'','','','','',407,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000109,'temp240V整流屏',0,'联通预制模板','VirtualSampler_755000063',15,1,'','','','','',408,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000110,'temp240V直流屏',0,'联通预制模板','VirtualSampler_755000064',16,1,'','','','','',409,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000111,'temp低压联络柜',0,'联通预制模板','VirtualSampler_755000065',29,1,'','','','','',208,NULL);
        INSERT INTO TBL_EquipmentTemplate (EquipmentTemplateId, EquipmentTemplateName, ParentTemplateId, Memo, ProtocolCode, EquipmentCategory, EquipmentType, Property, Description, EquipmentStyle, Unit, Vendor, EquipmentBaseType, StationCategory) VALUES (123000104,'temp48V铅酸阀控蓄电池组',0,'联通预制模板','VirtualSampler_755000057',24,1,'','','','','',1101,NULL);
    </insert>

    <select id="findByMonitorUnitId" resultType="org.siteweb.config.common.entity.TblEquipmentTemplate">
        SELECT *
        FROM tbl_equipmenttemplate a
        WHERE a.EquipmentTemplateId IN
        (SELECT EquipmentTemplateId FROM TBL_Equipment WHERE MonitorUnitId=#{monitorUnitId})
    </select>
    <select id="getAutoSetEquipmentBaseTypeList"
            resultType="org.siteweb.config.common.dto.AutoEquipmentBaseTypeDTO">
        SELECT
            t.EquipmentTemplateId,
            m.BaseEquipmentID
        FROM
            TBL_EquipmentTemplate t
        INNER JOIN TBL_BaseEquipmentCategoryMap m ON  t.EquipmentCategory = m.EquipmentCategory
        WHERE
        m.EquipmentCategory IN (
            SELECT
            aa.EquipmentCategory
            FROM
            TBL_BaseEquipmentCategoryMap aa
            GROUP BY
            aa.EquipmentCategory
            HAVING
            (count(1) = 1)
            )
        AND t.EquipmentBaseType IS NULL
    </select>
    <select id="findByName" resultType="org.siteweb.config.common.entity.TblEquipmentTemplate">
        SELECT equipmenttemplateid,
               equipmenttemplatename,
               parenttemplateid,
               memo,
               protocolcode,
               equipmentcategory,
               equipmenttype,
               property,
               description,
               equipmentstyle,
               unit,
               vendor,
               equipmentbasetype,
               stationcategory,
               photo
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateName = #{equipmentTemplateName}
        LIMIT 1;
    </select>
    <select id="findAllChildId" resultType="java.lang.Integer">
        WITH RECURSIVE cte (EquipmentTemplateId, ParentTemplateId) AS (
            SELECT EquipmentTemplateId, ParentTemplateId
            FROM tbl_equipmenttemplate
            WHERE EquipmentTemplateId = #{parentTemplateId}
            UNION ALL
            SELECT t.EquipmentTemplateId, t.ParentTemplateId
            FROM tbl_equipmenttemplate t
                     INNER JOIN cte ON t.ParentTemplateId = cte.EquipmentTemplateId
        )
        SELECT EquipmentTemplateId FROM cte;
    </select>
    <select id="findByEquipmentCategoryAndProtocolCode" resultType="org.siteweb.config.common.entity.TblEquipmentTemplate">
        SELECT EquipmentTemplateId,ParentTemplateId,EquipmentTemplateName,EquipmentCategory FROM tbl_equipmenttemplate
        <where>
            <if test="equipmentCategory != null">
                and equipmentCategory = #{equipmentCategory}
            </if>
            <if test="protocolCode != null and protocolCode != ''">
                and protocolCode = #{protocolCode}
            </if>
            <if test="equipmentTemplateName != null and equipmentTemplateName != ''">
                and equipmentTemplateName like concat('%',#{equipmentTemplateName},'%')
            </if>
        </where>
    </select>
    <select id="findReferenceEquipmentNameByProtocolCodes" resultType="java.lang.String">
        SELECT c.equipmentName FROM tbl_equipmenttemplate a inner JOIN tsl_sampler b on a.protocolcode = b.ProtocolCode
        INNER JOIN tbl_equipment c ON c.equipmentTemplateId = a.EquipmentTemplateId
        WHERE b.ProtocolCode IN
        <foreach collection="protocolCodeList" item="protocolCode" open="(" close=")" separator=",">
            #{protocolCode}
        </foreach>
    </select>
    <select id="findNameByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT EquipmentTemplateId as id,EquipmentTemplateName as value FROM tbl_equipmenttemplate WHERE EquipmentTemplateId IN
        <foreach collection="equipmentTemplateIds" item="equipmentTemplateId" open="(" close=")" separator=",">
            #{equipmentTemplateId}
        </foreach>
    </select>
    <select id="findBaseClassAll" resultType="org.siteweb.config.common.vo.EquipmentTemplateBaseClassVO">
        select t.EquipmentTemplateId,
        t.EquipmentTemplateName,
        t.ParentTemplateId,
        (select EquipmentTemplateName from TBL_EquipmentTemplate where EquipmentTemplateId = t.ParentTemplateId) parentTemplateName,
        t.ProtocolCode protocolCode,
        t.EquipmentCategory,
        (select concat(ItemValue::varchar,'[',t.EquipmentCategory::varchar,']') from TBL_DataItem where EntryId = 7 and ItemId = t.EquipmentCategory limit 1) equipmentCategoryName,
        t.EquipmentBaseType,
        (select concat(BaseEquipmentName,'[',t.EquipmentBaseType::varchar,']') from TBL_EquipmentBaseType where BaseEquipmentId = t.EquipmentBaseType limit 1) equipmentBaseTypeName
        from TBL_EquipmentTemplate t order by t.EquipmentCategory ASC,ProtocolCode ASC
    </select>
    <select id="findDynamicConfigTemplate" resultType="org.siteweb.config.common.entity.TblEquipmentTemplate">
        SELECT EquipmentTemplateId,ParentTemplateId,EquipmentTemplateName,EquipmentCategory FROM tbl_equipmenttemplate
        <where>
            <if test="hideDynamicConfigTemplate != null and hideDynamicConfigTemplate == true">
                AND memo != '动态配置'
            </if>
        </where>
    </select>
</mapper>