<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEventMapper">
    <resultMap id="eventConfigItemMap" type="org.siteweb.config.common.dto.EventConfigItem">
        <id column="id" property="id"/>
        <result column="equipmenttemplateid" property="equipmentTemplateId"/>
        <result column="eventid" property="eventId"/>
        <result column="eventname" property="eventName"/>
        <result column="starttype" property="startType"/>
        <result column="endtype" property="endType"/>
        <result column="startexpression" property="startExpression"/>
        <result column="suppressexpression" property="suppressExpression"/>
        <result column="eventcategory" property="eventCategory"/>
        <result column="signalid" property="signalId"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="description" property="description"/>
        <result column="displayindex" property="displayIndex"/>
        <result column="moduleno" property="moduleNo"/>
        <result column="turnover" property="turnover"/>
        <collection property="eventConditionList" ofType="org.siteweb.config.common.dto.EventConditionDTO" column="equipmenttemplateid,eventid" notNullColumn="conditionId">
            <id column="conditionId" property="id"/>
            <result column="equipmenttemplateid" property="equipmentTemplateId"/>
            <result column="eventid" property="eventId"/>
            <result column="eventconditionid" property="eventConditionId"/>
            <result column="startoperation" property="startOperation"/>
            <result column="startcomparevalue" property="startCompareValue"/>
            <result column="startdelay" property="startDelay"/>
            <result column="endoperation" property="endOperation"/>
            <result column="endcomparevalue" property="endCompareValue"/>
            <result column="enddelay" property="endDelay"/>
            <result column="frequency" property="frequency"/>
            <result column="frequencythreshold" property="frequencyThreshold"/>
            <result column="meanings" property="meanings"/>
            <result column="equipmentstate" property="equipmentState"/>
            <result column="basetypeid" property="baseTypeId"/>
            <result column="eventseverity" property="eventSeverity"/>
            <result column="standardname" property="standardName"/>
            <result column="baseTypeName" property="baseTypeName"/>
            <result column="baseNameExt" property="baseNameExt"/>
        </collection>
    </resultMap>
    <sql id="findEventItemConfigSql">
        SELECT a.id,
               a.equipmenttemplateid,
               a.eventid,
               a.eventname,
               a.starttype,
               a.endtype,
               a.startexpression,
               a.suppressexpression,
               a.eventcategory,
               a.signalid,
               a.enable,
               a.visible,
               a.description,
               a.displayindex,
               a.moduleno,
               b.id AS conditionId,
               b.eventconditionid,
               b.startoperation,
               b.startcomparevalue,
               b.startdelay,
               b.endoperation,
               b.endcomparevalue,
               b.enddelay,
               b.frequency,
               b.frequencythreshold,
               b.meanings,
               b.equipmentstate,
               b.basetypeid,
               b.eventseverity,
               b.standardname,
               d.BaseTypeName,
               d.baseNameExt,
               ex.turnover
        FROM tbl_event a
                 LEFT JOIN tbl_eventcondition b
                           ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.EventId = b.EventId
        LEFT JOIN tbl_eventex ex ON a.EquipmentTemplateId = ex.EquipmentTemplateId and a.EventId = ex.EventId
        LEFT JOIN tbl_eventbasedic d ON d.BaseTypeId = b.BaseTypeId
    </sql>
    <delete id="deleteEvent">
        DELETE FROM TBL_Event WHERE EquipmentTemplateId=#{equipmentTemplateId} AND EventId=#{eventId}
    </delete>
    <select id="findByEquipmentTemplateId" resultType="org.siteweb.config.common.entity.TblEvent">
        SELECT Id, EquipmentTemplateId, EventId, EventName, StartType, EndType,
        StartExpression, SuppressExpression, EventCategory, SignalId, Enable,
        Visible, Description, DisplayIndex, ModuleNo FROM TBL_Event
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findMaxEventIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(EventId)
        FROM TBL_Event
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT t.BaseTypeId
        FROM TBL_EventCondition t
        WHERE t.EquipmentTemplateId = #{equipmentTemplateId}
        AND t.BaseTypeId IS NOT NULL
        AND t.BaseTypeId != 0
        AND t.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_EventBaseDic);
    </select>
    <select id="findEventExpression" resultType="org.siteweb.config.common.dto.EventExpressionDTO">
        SELECT a.EventId, a.EventName, b.EventConditionId
        FROM TBL_Event a
        LEFT JOIN TBL_EventCondition b ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.EventId = b.EventId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findEventItemByEquipmentTemplateId" resultMap="eventConfigItemMap">
        <include refid="findEventItemConfigSql"/>
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findEventItemByEquipmentTemplateIds" resultMap="eventConfigItemMap">
        <include refid="findEventItemConfigSql"/>
        WHERE a.EquipmentTemplateId in
        <foreach collection="equipmentTemplateIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findAllEventItem" resultMap="eventConfigItemMap">
        <include refid="findEventItemConfigSql"/>
    </select>

    <select id="findMaxEventByEquipmentTemplateId" resultMap="eventConfigItemMap">
        <include refid="findEventItemConfigSql"/>
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY a.EventId DESC
        LIMIT 1
    </select>
    <select id="diffEvent" resultType="org.siteweb.config.common.entity.TblEvent">
        SELECT EventId, EventName
        FROM TBL_Event
        WHERE EquipmentTemplateId = #{oldEquipmentTemplateId}
          AND EventId NOT IN (SELECT EventId FROM TBL_Event WHERE EquipmentTemplateId = #{newEquipmentTemplateId})
    </select>
    <update id="updateWorkStationEventName">
        UPDATE tbl_event
        SET EventName = CONCAT(#{prefix}, EventName)
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <update id="updateEventIdAndStartExpressionAndSignalId">
        UPDATE tbl_event
        SET eventId = #{centerId} * 1000000 + eventId,
        StartExpression = CONCAT('[', '-1', ',', #{centerId} * 1000000 + eventId, ']'),
        SignalId = #{centerId} * 1000000 + SignalId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
        UPDATE tbl_eventcondition
        SET EventId = #{centerId} * 1000000 + EventId
        WHERE EquipmentTemplateId = #{equipmentTemplateId};
    </update>
    <update id="batchUpdate">
        <foreach collection="eventList" item="event" separator=";">
            update tbl_event
            <set>
                <if test="event.eventName != null">
                    EventName = #{event.eventName},
                </if>
                <if test="event.startType != null">
                    StartType = #{event.startType},
                </if>
                <if test="event.endType != null">
                    EndType = #{event.endType},
                </if>
                <if test="event.startExpression != null">
                    StartExpression = #{event.startExpression},
                </if>
                <if test="event.suppressExpression != null">
                    SuppressExpression = #{event.suppressExpression},
                </if>
                <if test="event.eventCategory != null">
                    EventCategory = #{event.eventCategory},
                </if>
                <if test="event.signalId != null">
                    SignalId = #{event.signalId},
                </if>
                <if test="event.enable != null">
                    Enable = #{event.enable},
                </if>
                <if test="event.visible != null">
                    Visible = #{event.visible},
                </if>
                <if test="event.description != null">
                    Description = #{event.description},
                </if>
                <if test="event.displayIndex != null">
                    DisplayIndex = #{event.displayIndex}
                </if>
            </set>
            where EquipmentTemplateId = #{event.equipmentTemplateId}
            and EventId = #{event.eventId}
        </foreach>
    </update>

    <insert id="batchInsertLianTongEvents">
        <!-- adaptive-multi-dm 去除反引号  -->
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010162,'蓄电池组总电压告警',1,3,'','',33,'10001013',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010178,'蓄电池组放电告警',1,3,'','',43,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010166,'蓄电池单体电压告警',1,3,'','',2,'10001014',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010170,'蓄电池组单体温度高告警',1,3,'','',2,'10001015',1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010174,'通信故障告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000104,100010175,'其它故障告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010125,'合母电压告警',1,3,'','',2,'10001012',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010126,'控母电压告警',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010128,'电池故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010129,'蓄电池单体温度高告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010130,'输入电源中断告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010131,'模块故障告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010132,'通信故障告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000069,100010133,'其它故障告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000070,100010133,'设备温度高告警',1,3,'','',2,'10001013',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000070,100010134,'超温跳闸告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000070,100010135,'通信故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000070,100010136,'其它故障告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010129,'输出相电压告警Ua',1,3,'','',2,'10001011',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010130,'输出相电压告警Ub',1,3,'','',2,'10001011',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010131,'输出相电压告警Uc',1,3,'','',2,'10001011',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010135,'输出线电压告警Uab',1,3,'','',2,'10001011',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010136,'输出线电压告警Ubc',1,3,'','',2,'10001011',1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010137,'输出线电压告警Uca',1,3,'','',2,'10001011',1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010141,'交流输出电流高告警Ia',1,3,'','',2,'10001011',1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010142,'交流输出电流高告警Ib',1,3,'','',2,'10001011',1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010143,'交流输出电流高告警Ic',1,3,'','',2,'10001012',1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010144,'零线电流高告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010145,'输出频率告警',1,3,'','',2,'10001012',1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010147,'输出功率因数低告警',1,3,'','',2,'10001012',1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010148,'失压跳闸告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010149,'接地跳闸告警',1,3,'','',2,null,1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010150,'过流跳闸告警',1,3,'','',2,null,1,1,'',22,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010152,'系统绝缘故障告警',1,3,'','',2,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010153,'防雷器故障告警',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010154,'通信故障告警',1,3,'','',2,null,1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000071,100010155,'其它故障告警',1,3,'','',2,null,1,1,'',27,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010132,'输出相电压告警Ua',1,3,'','',2,'10001011',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010133,'输出相电压告警Ub',1,3,'','',2,'10001011',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010134,'输出相电压告警Uc',1,3,'','',2,'10001011',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010138,'输出线电压告警Uab',1,3,'','',2,'10001011',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010139,'输出线电压告警Ubc',1,3,'','',2,'10001011',1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010140,'输出线电压告警Uca',1,3,'','',2,'10001012',1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010144,'交流输出电流告警Ia',1,3,'','',2,'10001012',1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010145,'交流输出电流告警Ib',1,3,'','',2,'10001012',1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010146,'交流输出电流告警Ic',1,3,'','',2,'10001012',1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010147,'输入电源中断告警',1,3,'','',10,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010148,'输出零线电流高告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010149,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010150,'输出开关跳闸告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010151,'防雷器故障',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010152,'输出电源缺相告警',1,3,'','',2,null,1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010153,'通信故障告警',1,3,'','',2,null,1,1,'',22,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000072,100010154,'其它故障告警',1,3,'','',2,null,1,1,'',23,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000073,100010117,'输入功率因数低告警',1,3,'','',2,'10001011',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000073,100010118,'设备温度高告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000073,100010119,'风机故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000073,100010120,'通信故障告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000073,100010121,'其它故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010119,'输出总谐波电压高告警',1,3,'','',2,'10001011',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010120,'输出总谐波电流高告警',1,3,'','',2,'10001011',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010121,'IGBT温度高告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010122,'控制器温度高告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010123,'通信故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000074,100010124,'其它故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000075,100010125,'输入电源中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000075,100010126,'输入电源缺相告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000075,100010127,'主用电源频率告警',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000075,100010129,'通信故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000075,100010130,'其它故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010142,'超速停机告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010143,'丢速停机告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010147,'高水温停机告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010148,'过载停机告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010149,'紧急停机告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010150,'冷却液泄漏停机告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010151,'润滑油油温告警',1,3,'','',2,'10001013',1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010152,'启动告警',1,3,'','',42,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010153,'润滑油油压低告警',1,3,'','',2,'10001013',1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010155,'冷却液温度告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010157,'冷却液液位低告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010158,'启动失败告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010159,'控制器故障告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010160,'进排风风门告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010161,'启动电池电压告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010163,'充电器故障告警',1,3,'','',2,null,1,1,'',22,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010164,'燃油液位低告警',1,3,'','',2,'10001013',1,1,'',23,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010165,'非自动状态告警',1,3,'','',2,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010166,'输出相电压告警Ua',1,3,'','',2,'10001011',1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010167,'输出相电压告警Ub',1,3,'','',2,'10001012',1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010168,'输出相电压告警Uc',1,3,'','',2,'10001012',1,1,'',27,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010169,'输出线电压告警Uab',1,3,'','',2,'10001012',1,1,'',28,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010170,'输出线电压告警Ubc',1,3,'','',2,'10001012',1,1,'',29,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010171,'输出线电压告警Uca',1,3,'','',2,'10001012',1,1,'',30,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010178,'输出频率告警',1,3,'','',2,'10001012',1,1,'',37,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010180,'低油压停机告警',1,3,'','',2,null,1,1,'',39,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010181,'市电故障告警',1,3,'','',2,null,1,1,'',40,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010182,'主用电源频率告警',1,3,'','',2,null,1,1,'',41,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010184,'备用电源供电告警',1,3,'','',42,null,1,1,'',43,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010185,'通信故障告警',1,3,'','',2,null,1,1,'',44,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010186,'其它故障告警',1,3,'','',2,null,1,1,'',45,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010187,'输出相电压低停机告警',1,3,'','',2,null,1,1,'',46,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000076,100010190,'输出总视在功率告警',1,3,'','',2,'10001014',1,1,'',49,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010144,'超速停机告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010145,'丢速停机告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010146,'输出电压低停机告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010149,'高水温停机告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010150,'过载停机告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010151,'紧急停机告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010152,'冷却液泄漏停机告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010153,'润滑油油温告警',1,3,'','',2,'10001013',1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010154,'启动告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010155,'润滑油油压低告警',1,3,'','',2,'10001013',1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010157,'冷却液温度告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010159,'冷却液液位低告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010160,'启动失败告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010161,'控制器故障告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010162,'进排风风门告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010163,'启动电池电压告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010165,'控制电池电压告警',1,3,'','',2,null,1,1,'',22,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010167,'充电器故障告警',1,3,'','',2,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010168,'非自动状态告警',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010169,'输出相电压Ua告警',1,3,'','',2,'10001012',1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010170,'输出相电压Ub告警',1,3,'','',2,'10001012',1,1,'',27,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010171,'输出相电压Uc告警',1,3,'','',2,'10001012',1,1,'',28,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010172,'输出线电压Uab告警',1,3,'','',2,'10001012',1,1,'',29,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010173,'输出线电压Ubc告警',1,3,'','',2,'10001012',1,1,'',30,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010174,'输出线电压Uca告警',1,3,'','',2,'10001012',1,1,'',31,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010181,'输出总视在功率高告警',1,3,'','',2,null,1,1,'',38,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010182,'输出频率告警',1,3,'','',2,'10001012',1,1,'',39,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010184,'低油压停机告警',1,3,'','',2,null,1,1,'',41,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010185,'市电故障告警',1,3,'','',2,null,1,1,'',42,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010186,'主用电源频率告警',1,3,'','',2,null,1,1,'',43,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010188,'通信故障告警',1,3,'','',2,null,1,1,'',45,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000077,100010189,'其它故障告警',1,3,'','',2,null,1,1,'',46,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010140,'输入电源中断告警',1,3,'','',10,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010141,'主用相电压Ua告警',1,3,'','',2,'10001012',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010142,'主用相电压Ub告警',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010143,'主用相电压Uc告警',1,3,'','',2,'10001012',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010147,'主用线电压Uab告警',1,3,'','',2,'10001012',1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010148,'主用线电压Ubc告警',1,3,'','',2,'10001012',1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010149,'主用线电压Uca告警',1,3,'','',2,'10001012',1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010153,'输入电源缺相告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010154,'主用电源频率告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010156,'旁路电源中断告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010157,'旁路电源相电压Ua告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010158,'旁路电源相电压Ub告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010159,'旁路电源相电压Uc告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010163,'旁路电源线电压Uab告警',1,3,'','',2,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010164,'旁路电源线电压Ubc告警',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010165,'旁路电源线电压Uca告警',1,3,'','',2,null,1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010169,'旁路电源缺相告警',1,3,'','',2,null,1,1,'',30,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010170,'旁路电源频率告警',1,3,'','',2,null,1,1,'',31,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010172,'输出电源故障告警',1,3,'','',2,null,1,1,'',33,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010173,'输出相电压Ua告警',1,3,'','',2,'10001012',1,1,'',34,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010174,'输出相电压Ub告警',1,3,'','',2,'10001012',1,1,'',35,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010175,'输出相电压Uc告警',1,3,'','',2,'10001012',1,1,'',36,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010179,'输出线电压Uab告警',1,3,'','',2,'10001013',1,1,'',40,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010180,'输出线电压Ubc告警',1,3,'','',2,'10001013',1,1,'',41,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010181,'输出线电压Uca告警',1,3,'','',2,'10001013',1,1,'',42,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010185,'输出电源中断告警',1,3,'','',2,null,1,1,'',46,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010186,'输出频率告警',1,3,'','',2,'10001013',1,1,'',47,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010188,'交流输出电流高告警',1,3,'','',2,null,1,1,'',49,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010189,'旁路电源供电告警',1,3,'','',2,null,1,1,'',50,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010190,'旁路不可用告警',1,3,'','',2,null,1,1,'',51,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010191,'手动旁路告警',1,3,'','',2,null,1,1,'',52,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010192,'UPS整流器故障告警',1,3,'','',2,null,1,1,'',53,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010193,'不同步故障告警',1,3,'','',2,null,1,1,'',54,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010194,'UPS逆变器输入电压告警',1,3,'','',2,null,1,1,'',55,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010195,'UPS逆变器故障告警',1,3,'','',2,null,1,1,'',56,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010196,'风机故障告警',1,3,'','',2,null,1,1,'',57,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010197,'熔丝故障告警',1,3,'','',2,null,1,1,'',58,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010198,'蓄电池放电告警',1,3,'','',2,null,1,1,'',59,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010200,'蓄电池组总电压告警',1,3,'','',33,null,1,1,'',61,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010201,'设备温度高告警',1,3,'','',2,null,1,1,'',62,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010203,'温度传感器故障告警',1,3,'','',2,null,1,1,'',64,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010204,'传感器故障告警',1,3,'','',2,null,1,1,'',65,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010205,'并机系统故障告警',1,3,'','',2,null,1,1,'',66,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010206,'电池组接触器故障告警',1,3,'','',2,null,1,1,'',67,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010207,'充电器故障告警',1,3,'','',2,null,1,1,'',68,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010208,'电池组开关断开告警',1,3,'','',2,null,1,1,'',69,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010209,'通信故障告警',1,3,'','',2,null,1,1,'',70,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010210,'其它故障告警',1,3,'','',2,null,1,1,'',71,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000078,100010211,'旁路电源故障告警',1,3,'','',2,null,1,1,'',72,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010141,'输入电源中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010142,'主用相电压告警Ua',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010143,'主用相电压告警Ub',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010144,'主用相电压告警Uc',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010148,'主用线电压告警Uab',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010149,'主用线电压告警Ubc',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010150,'主用线电压告警Uca',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010154,'输入电源缺相告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010155,'主用电源频率告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010157,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010158,'旁路电源中断告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010159,'旁路电源相电压告警Ua',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010160,'旁路电源相电压告警Ub',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010161,'旁路电源相电压告警Uc',1,3,'','',2,null,1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010165,'旁路电源线电压告警Uab',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010166,'旁路电源线电压告警Ubc',1,3,'','',2,null,1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010167,'旁路电源线电压告警Uca',1,3,'','',2,null,1,1,'',27,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010171,'旁路电源缺相告警',1,3,'','',2,null,1,1,'',31,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010172,'旁路电源频率告警',1,3,'','',2,null,1,1,'',32,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010174,'输出电源故障告警',1,3,'','',2,null,1,1,'',34,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010175,'输出相电压告警Ua',1,3,'','',2,'10001012',1,1,'',35,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010176,'输出相电压告警Ub',1,3,'','',2,'10001012',1,1,'',36,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010177,'输出相电压告警Uc',1,3,'','',2,'10001013',1,1,'',37,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010181,'输出线电压告警Uab',1,3,'','',2,'10001013',1,1,'',41,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010182,'输出线电压告警Ubc',1,3,'','',2,'10001013',1,1,'',42,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010183,'输出线电压告警Uca',1,3,'','',2,'10001013',1,1,'',43,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010187,'输出电源中断告警',1,3,'','',2,null,1,1,'',47,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010188,'输出频率告警',1,3,'','',2,'10001013',1,1,'',48,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010190,'输出电流高告警',1,3,'','',2,null,1,1,'',50,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010191,'紧急停机告警',1,3,'','',2,null,1,1,'',51,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010192,'旁路电源供电告警',1,3,'','',2,null,1,1,'',52,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010193,'旁路电源故障告警',1,3,'','',2,null,1,1,'',53,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010194,'旁路不可用告警',1,3,'','',2,null,1,1,'',54,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010195,'手动旁路告警',1,3,'','',2,null,1,1,'',55,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010196,'模块故障告警',1,3,'','',2,null,1,1,'',56,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010197,'模块温度高告警',1,3,'','',2,null,1,1,'',57,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010198,'风机故障告警',1,3,'','',2,null,1,1,'',58,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010199,'模块故障关机告警',1,3,'','',2,null,1,1,'',59,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010200,'UPS整流器故障告警',1,3,'','',2,null,1,1,'',60,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010201,'不同步故障告警',1,3,'','',2,null,1,1,'',61,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010202,'UPS逆变器输入电压告警',1,3,'','',2,null,1,1,'',62,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010203,'UPS逆变器故障告警',1,3,'','',2,null,1,1,'',63,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010204,'熔丝故障告警',1,3,'','',2,null,1,1,'',64,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010205,'蓄电池放电告警',1,3,'','',2,null,1,1,'',65,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010207,'蓄电池组总电压告警',1,3,'','',2,null,1,1,'',67,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010208,'设备温度高告警',1,3,'','',2,null,1,1,'',68,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010210,'温度传感器故障告警',1,3,'','',2,null,1,1,'',70,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010211,'传感器故障告警',1,3,'','',2,null,1,1,'',71,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010212,'并机系统故障告警',1,3,'','',2,null,1,1,'',72,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010213,'电池组接触器故障告警',1,3,'','',2,null,1,1,'',73,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010214,'充电器故障告警',1,3,'','',2,null,1,1,'',74,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010215,'电池组开关断开告警',1,3,'','',2,null,1,1,'',75,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010216,'通信故障告警',1,3,'','',2,null,1,1,'',76,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000079,100010217,'其它故障告警',1,3,'','',2,null,1,1,'',77,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010134,'交流输出电流高告警Ia',1,3,'','',2,'10001012',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010135,'交流输出电流高告警Ib',1,3,'','',2,'10001013',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010136,'交流输出电流高告警Ic',1,3,'','',2,'10001013',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010137,'输入电源中断告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010138,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010139,'输出开关跳闸告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010140,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010141,'通信故障告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010142,'输入相电压告警Ua',1,3,'','',2,'10001012',1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010143,'输入相电压告警Ub',1,3,'','',2,'10001012',1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000080,100010144,'输入相电压告警Uc',1,3,'','',2,'10001012',1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010132,'输入电源中断告警',1,3,'','',10,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010133,'输入相电压告警Ua',1,3,'','',2,'10001012',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010134,'输入相电压告警Ub',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010135,'输入相电压告警Uc',1,3,'','',2,'10001012',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010136,'输入线电压告警Uab',1,3,'','',2,'10001012',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010137,'输入线电压告警Ubc',1,3,'','',2,'10001012',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010138,'输入线电压告警Uca',1,3,'','',2,'10001012',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010145,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010146,'输出开关跳闸告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010147,'通信故障告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000081,100010148,'其它故障告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010145,'水流开关故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010146,'高压开关故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010147,'油位开关故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010148,'压缩机排气压力高告警',1,3,'','',2,'10001013',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010149,'压缩机吸气压力低告警',1,3,'','',2,'10001013',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010150,'排气温度高告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010151,'冷冻水出水温度告警',1,3,'','',2,'10001012',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010153,'油压差低告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010154,'冷却塔水位告警',1,3,'','',2,'10001013',1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010156,'油过滤器阻塞告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010157,'传感器故障告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010158,'联锁故障告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010159,'压缩机启动失败告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010160,'压缩机内置保护告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010161,'压缩机电流过高',1,3,'','',2,'10001013',1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010163,'通信故障告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000082,100010164,'其它故障告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000083,100010154,'通信故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000083,100010155,'其它故障告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010154,'水浸告警',1,3,'','',37,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010155,'烟感告警',1,3,'','',5,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010156,'红外告警',1,3,'','',36,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010157,'室内环境温度告警',1,3,'','',34,'10001015',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010159,'室内环境湿度告警',1,3,'','',44,'10001015',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010161,'蓄电池组防盗告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010162,'接地排防盗告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010163,'空调室外机防盗告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010164,'防盗告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000084,100010165,'震动告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010139,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010140,'输入相电压告警Ua',1,3,'','',2,'10001012',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010141,'输入相电压告警Ub',1,3,'','',2,'10001013',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010142,'输入相电压告警Uc',1,3,'','',2,'10001013',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010143,'输入线电压告警Uab',1,3,'','',2,'10001013',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010144,'输入线电压告警Ubc',1,3,'','',2,'10001013',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010145,'输入线电压告警Uca',1,3,'','',2,'10001013',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010152,'输入电源中断告警',1,3,'','',10,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010153,'输入电源缺相告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010154,'输入频率告警',1,3,'','',2,'10001013',1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010156,'防雷器空开跳闸告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010157,'防雷器故障告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010158,'通信故障告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000085,100010159,'其它故障告警',1,3,'','',2,null,1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010150,'风机过载告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010151,'气流丢失告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010152,'过滤网堵塞告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010153,'室内环境湿度高告警',1,3,'','',2,'10001014',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010155,'室内环境温度高告警',1,3,'','',2,'10001014',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010157,'冷冻水进水温度高告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010158,'冷冻水进水压力低告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010159,'加湿罐脏告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010160,'加热器过热告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010161,'溢水告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010162,'水浸告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010163,'通信故障告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000086,100010164,'其它故障告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000087,100010154,'非法出入告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000087,100010156,'长时间门开告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000087,100010157,'通信故障告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000087,100010158,'其它故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010139,'直流输入电压告警',1,3,'','',2,'10001012',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010141,'输出相电压告警Ua',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010142,'输出相电压告警Ub',1,3,'','',2,'10001013',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010143,'输出相电压告警Uc',1,3,'','',2,'10001013',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010149,'输出线电压告警Uab',1,3,'','',2,'10001013',1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010150,'输出线电压告警Ubc',1,3,'','',2,'10001013',1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010151,'输出线电压告警Uca',1,3,'','',2,'10001013',1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010157,'交流输出电流过高Ia',1,3,'','',2,'10001013',1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010158,'交流输出电流过高Ib',1,3,'','',2,'10001013',1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010159,'交流输出电流过高Ic',1,3,'','',2,'10001013',1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010160,'输出频率告警',1,3,'','',2,'10001013',1,1,'',22,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010162,'通信故障告警',1,3,'','',2,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000088,100010163,'其它故障告警',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010151,'输入电源故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010152,'风机故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010153,'控制器故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010154,'温度传感器故障告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010155,'空调故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010156,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000089,100010157,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010152,'输入电源故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010153,'风机故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010154,'控制器故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010155,'热交换器脏告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010156,'温度传感器故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010157,'空调故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010158,'通信故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000090,100010159,'其它故障告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010158,'风机故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010159,'控制器故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010160,'过滤网堵塞告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010161,'溢水告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010162,'空调故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010163,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000091,100010164,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010154,'温度传感器故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010155,'传感器故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010156,'压缩机排气压力高告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010157,'压缩机吸气压力低告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010158,'风机故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010159,'压缩机过载告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010160,'输入电源故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010161,'通信故障告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000092,100010162,'其它故障告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000093,100010140,'直流输出电压高告警',1,3,'','',2,'10001013',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000093,100010141,'通信故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000093,100010142,'其它故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000093,100010143,'直流输出电流高告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000093,100010144,'传感器故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010158,'风机故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010160,'控制器故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010161,'过滤网堵塞告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010162,'空调故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010163,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000094,100010164,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010153,'空调故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010154,'风机故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010155,'控制器故障告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010156,'蓄电池区域温度高告警',1,3,'','',2,'10001014',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010157,'温度传感器故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010158,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000095,100010159,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010132,'模块故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010133,'风机故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010134,'模块过压关机告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010135,'模块温度高告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010136,'通信故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000096,100010137,'其它故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010135,'蓄电池熔丝故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010136,'蓄电池充电电流高告警',1,3,'','',2,'10001013',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010137,'蓄电池单体温度高告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010138,'蓄电池放电不平衡告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010139,'蓄电池放电告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010140,'直流输出电流高告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010141,'熔丝故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010142,'直流输出电压告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010144,'输出电源中断告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010145,'一级负载下电告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010146,'二级负载下电告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010147,'通信故障告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000097,100010148,'其它故障告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010145,'输入电源中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010146,'直流输入电压告警',1,3,'','',2,'10001014',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010148,'直流输出电压告警',1,3,'','',2,'10001014',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010150,'直流输出电流高告警',1,3,'','',2,'10001014',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010151,'设备温度高告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010152,'电力线搭接告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010153,'供电电缆断告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010154,'供电线路漏电告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010155,'通信故障告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000098,100010156,'其它故障告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000099,100010141,'直流输入电压告警',1,3,'','',2,'10001013',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000099,100010143,'直流输出电压告警',1,3,'','',2,'10001013',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000099,100010145,'直流输出电流高告警',1,3,'','',2,'10001014',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000099,100010146,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000099,100010147,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010148,'风机过载告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010149,'气流丢失告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010150,'过滤网堵塞告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010151,'室内环境湿度告警',1,3,'','',2,'10001014',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010153,'室内环境温度告警',1,3,'','',2,'10001014',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010155,'压缩机排气压力高告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010156,'压缩机吸气压力低告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010157,'压缩机过热告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010158,'压缩机过载告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010159,'加湿器故障告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010160,'溢水告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010161,'水浸告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010162,'输入电源故障告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010163,'通信故障告警',1,3,'','',2,null,1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000100,100010164,'其它故障告警',1,3,'','',2,null,1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010147,'蓄电池熔丝故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010148,'蓄电池充电电流高告警',1,3,'','',2,'10001014',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010149,'蓄电池单体温度高告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010150,'蓄电池放电不平衡告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010151,'蓄电池放电告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010152,'直流输出电流高告警',1,3,'','',2,'10001014',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010153,'熔丝故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010154,'直流输出电压告警',1,3,'','',2,'10001014',1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010156,'输出电源中断告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010157,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010158,'输入相电压告警Ua',1,3,'','',2,'10001013',1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010159,'输入相电压告警Ub',1,3,'','',2,'10001013',1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010160,'输入相电压告警Uc',1,3,'','',2,'10001013',1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010161,'输入线电压告警Uab',1,3,'','',2,'10001013',1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010162,'输入线电压告警Ubc',1,3,'','',2,'10001013',1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010163,'输入线电压告警Uca',1,3,'','',2,'10001013',1,1,'',17,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010170,'输入电源中断告警',1,3,'','',10,null,1,1,'',24,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010171,'输入电源缺相告警',1,3,'','',2,null,1,1,'',25,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010172,'输入频率告警',1,3,'','',2,'10001014',1,1,'',26,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010174,'防雷器空开跳闸告警',1,3,'','',2,null,1,1,'',28,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010175,'防雷器故障告警',1,3,'','',2,null,1,1,'',29,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010176,'模块故障告警',1,3,'','',2,null,1,1,'',30,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010177,'风机故障告警',1,3,'','',2,null,1,1,'',31,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010178,'模块过压关机告警',1,3,'','',2,null,1,1,'',32,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010179,'模块温度高告警',1,3,'','',2,null,1,1,'',33,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010180,'一级负载下电告警',1,3,'','',2,null,1,1,'',34,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010181,'二级负载下电告警',1,3,'','',2,null,1,1,'',35,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010182,'通信故障告警',1,3,'','',2,null,1,1,'',36,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000101,100010183,'其它故障告警',1,3,'','',2,null,1,1,'',37,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010132,'A路输入电源中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010134,'A路直流输入电压告警',1,3,'','',2,'10001012',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010138,'A路直流输入电流高告警',1,3,'','',2,'10001012',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010140,'A路输入开关跳闸告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010142,'A路输出开关跳闸告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010144,'通信故障告警',1,3,'','',2,null,1,1,'',13,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000102,100010145,'其它故障告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010129,'输入电源中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010130,'直流输入电压告警',1,3,'','',2,'10001012',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010132,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010133,'输出开关跳闸告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010134,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000103,100010135,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000105,100010144,'蓄电池组总电压告警',1,3,'','',2,'10001013',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000105,100010146,'蓄电池单体电压告警',1,3,'','',2,'10001014',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000105,100010148,'蓄电池单体温度高告警',1,3,'','',2,'10001014',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000105,100010149,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000105,100010150,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010153,'采集单元通信中断告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010161,'SS通信中断告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010162,'SC通信中断告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010163,'通信前置机故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010164,'应用服务器故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010165,'数据库空间不足告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010166,'数据库连接异常告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010167,'C接口通信故障告警',1,3,'','',2,null,1,1,'',9,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010168,'D接口通信故障告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010169,'综合网管接口通信故障告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010170,'数据服务控制程序异常告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010171,'服务器内存占有率高告警',1,3,'','',2,'10001015',1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000106,100010172,'服务器CPU占有率高告警',1,3,'','',2,'10001015',1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000107,100010146,'蓄电池组总电压高告警',1,3,'','',2,'10001014',1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000107,100010148,'蓄电池单体电压高告警',1,3,'','',2,'10001014',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000107,100010150,'蓄电池单体温度高告警',1,3,'','',2,'10001014',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000107,100010151,'通信故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000107,100010152,'其它故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010140,'输入开关跳闸告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010141,'输入相电压告警Ua',1,3,'','',2,'10001013',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010142,'输入相电压告警Ub',1,3,'','',2,'10001013',1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010143,'输入相电压告警Uc',1,3,'','',2,'10001013',1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010144,'输入线电压告警Uab',1,3,'','',2,'10001013',1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010145,'输入线电压告警Ubc',1,3,'','',2,'10001013',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010146,'输入线电压告警Uca',1,3,'','',2,'10001013',1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010153,'输入电源中断告警',1,3,'','',2,null,1,1,'',14,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010154,'输入电源缺相告警',1,3,'','',2,null,1,1,'',15,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010155,'输入频率告警',1,3,'','',2,'10001013',1,1,'',16,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010157,'防雷器空开跳闸告警',1,3,'','',2,null,1,1,'',18,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010158,'防雷器故障告警',1,3,'','',2,null,1,1,'',19,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010159,'通信故障告警',1,3,'','',2,null,1,1,'',20,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000108,100010160,'其它故障告警',1,3,'','',2,null,1,1,'',21,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010136,'模块故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010137,'风机故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010138,'模块过压关机告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010139,'模块温度高告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010140,'系统绝缘故障告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010141,'分路绝缘故障告警',1,3,'','',2,null,1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010142,'通信故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000109,100010143,'其它故障告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010137,'蓄电池熔丝故障告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010138,'蓄电池充电电流高告警',1,3,'','',2,'10001013',1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010139,'蓄电池单体温度高告警',1,3,'','',2,null,1,1,'',3,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010140,'蓄电池放电不平衡告警',1,3,'','',2,null,1,1,'',4,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010141,'蓄电池放电告警',1,3,'','',2,null,1,1,'',5,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010142,'直流输入电流高告警',1,3,'','',2,'10001013',1,1,'',6,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010143,'熔丝故障告警',1,3,'','',2,null,1,1,'',7,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010144,'直流输出电压告警',1,3,'','',2,null,1,1,'',8,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010146,'输出电源中断告警',1,3,'','',2,null,1,1,'',10,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010147,'通信故障告警',1,3,'','',2,null,1,1,'',11,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000110,100010148,'其它故障告警',1,3,'','',2,null,1,1,'',12,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000111,100010155,'开关跳闸告警',1,3,'','',2,null,1,1,'',1,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000111,100010156,'通信故障告警',1,3,'','',2,null,1,1,'',2,0);
        INSERT INTO TBL_Event (EquipmentTemplateId, EventId, EventName, StartType, EndType, StartExpression, SuppressExpression, EventCategory, SignalId, Enable, Visible, Description, DisplayIndex, ModuleNo)	VALUES (123000111,100010157,'其它故障告警',1,3,'','',2,null,1,1,'',3,0);
    </insert>
    <select id="findEventsByEquipmentIdAndEventIds" resultType="org.siteweb.config.common.dto.batchtool.EquipmentEventDTO">
        SELECT a.*,b.EquipmentId,b.EquipmentName FROM tbl_event a
        INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateid
        WHERE b.EquipmentId = #{equipmentId} AND eventId IN
        <foreach collection="eventIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findEventsByEquipmentIdAndSignalIds" resultType="org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO">
        SELECT a.signalId,a.EventId,a.EventName,b.EquipmentId FROM TBL_Event a, TBL_Equipment b
        WHERE b.EquipmentTemplateId = a.EquipmentTemplateId
        AND b.EquipmentId = #{eventRequestBySignalId.equipmentId}
        AND a.signalId IN
        <foreach collection="eventRequestBySignalId.signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findByEquipmentTemplateIdAndEventId"  resultMap="eventConfigItemMap">
        <include refid="findEventItemConfigSql"/>
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId} and a.eventId = #{eventId}
    </select>
    <sql id="findEventBaseClassSql">
        SELECT
        s.EquipmentTemplateId,
        e.EquipmentTemplateName,
        e.ParentTemplateId,
        (
        SELECT
        EquipmentTemplateName
        FROM
        TBL_EquipmentTemplate
        WHERE
        EquipmentTemplateId = e.ParentTemplateId
        LIMIT 1) AS ParentTemplateName,
        e.ProtocolCode,
        s.EventId,
        s.EventName,
        s.EventCategory,
        c.EventConditionId,
        c.Meanings,
        c.EventSeverity,
        c.BaseTypeId,
        (
        SELECT
        BaseTypeName
        FROM
        TBL_EventBaseDic
        WHERE
        BaseTypeId = c.BaseTypeId
        LIMIT 1) AS BaseTypeName,
        e.EquipmentBaseType AS CategoryOrBaseType,
        e.EquipmentBaseType,
        (
        SELECT
        BaseEquipmentName
        FROM
        TBL_EquipmentBaseType
        WHERE
        BaseEquipmentId = e.EquipmentBaseType
        LIMIT 1) AS EquipmentBaseTypeName,
        s.ModuleNo,
        d.baseNameExt
        FROM
        TBL_EquipmentTemplate e
        INNER JOIN TBL_Event s ON e.EquipmentTemplateId = s.EquipmentTemplateId
        INNER JOIN TBL_EventCondition c ON s.EquipmentTemplateId = c.EquipmentTemplateId AND s.EventId = c.EventId
        LEFT JOIN tbl_eventbasedic d ON d.BaseTypeId = c.BaseTypeId
    </sql>
    <select id="findEventBaseClassList" resultType="org.siteweb.config.common.dto.EventBaseClassDetailDTO">
        <include refid="findEventBaseClassSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
    </select>
    <select id="findEventBaseClassDetails" resultType="org.siteweb.config.common.dto.EventBaseClassDetailDTO">
        <include refid="findEventBaseClassSql"/>
        WHERE
        e.EquipmentBaseType IS NOT NULL
        <if test="equipmentBaseType != null">
            AND e.EquipmentBaseType = #{equipmentBaseType}
        </if>
        <if test="eventName != null and eventName != ''">
            AND s.eventName = #{eventName}
        </if>
        <choose>
            <when test="meanings != null and meanings != ''">
                AND c.Meanings = #{meanings}
            </when>
            <otherwise>
                AND c.Meanings IS NULL
            </otherwise>
        </choose>
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT concat(a.EquipmentTemplateId,'.',a.eventId) as id,concat( b.EquipmentTemplateName,'.',a.eventName) as value
        FROM tbl_event a inner join tbl_equipmenttemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE
        (a.EquipmentTemplateId, a.eventId) IN
        <foreach item="item" collection="eventUniqueIds" separator="," open="(" close=")">
            (#{item.equipmentTemplateId}, #{item.eventId})
        </foreach>
    </select>
    <select id="findEventProgressList" resultType="org.siteweb.config.common.dto.EventProgressDTO">
        SELECT
        s.EquipmentTemplateId,
        c.BaseTypeId,
        s.EventId,
        c.EventConditionId
        FROM
        TBL_Event s
        INNER JOIN TBL_EventCondition c ON
        s.EquipmentTemplateId = c.EquipmentTemplateId
        AND s.EventId = c.EventId
    </select>
    <select id="getApplyStandards" resultType="org.siteweb.config.common.dto.EventApplyStandardDTO">
        SELECT cond.id,
        dic.StandardDicId AS standardName,
        IFNULL(CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 6) = 1 THEN dic.EventSeverity ELSE NULL END, cond.EventSeverity) AS EventSeverity,
        IFNULL((CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 8) = 1 THEN dic.StartDelay ELSE NULL END)::INT, cond.StartDelay) AS StartDelay,
        IFNULL((CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 7) = 1 THEN (CASE WHEN mysignal.SignalCategory = 1 THEN dic.CompareValue ELSE NULL END) ELSE NULL END)::DOUBLE, cond.StartCompareValue) AS StartCompareValue,
        IFNULL(CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 9) = 1 THEN dic.Meanings ELSE NULL END, cond.Meanings) AS Meanings
        FROM
        TBL_EventCondition cond
        INNER JOIN TBL_EquipmentTemplate template ON template.EquipmentTemplateId = cond.EquipmentTemplateId
        INNER JOIN TBL_EventBaseMap map ON FLOOR(map.BaseTypeId / 1000) = FLOOR(cond.BaseTypeId / 1000)
        INNER JOIN TBL_StandardDicEvent dic ON dic.StandardDicId = map.StandardDicId AND dic.StationCategory = map.StationBaseType AND dic.StandardType = map.StandardType
        LEFT JOIN TBL_Event evt ON cond.EventId = evt.EventId AND template.EquipmentTemplateId = evt.EquipmentTemplateId
        LEFT JOIN TBL_Signal mysignal ON mysignal.SignalId = evt.SignalId AND mysignal.EquipmentTemplateId = template.EquipmentTemplateId
        WHERE dic.StationCategory = template.StationCategory
        AND dic.StandardType = #{standardId}
        <if test="equipmentTemplateIds != null and equipmentTemplateIds.size > 0">
            AND cond.EquipmentTemplateId in
            <foreach collection="equipmentTemplateIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="restoreStandard">
        UPDATE TBL_EventCondition myevent
        INNER JOIN TBL_StandardBack bak ON bak.EquipmentTemplateId = myevent.EquipmentTemplateId AND bak.EntryId= myevent.EventId AND myevent.EventConditionId = bak.EventConditionId
        SET myevent.StandardName = bak.StandardName,
        myevent.EventSeverity = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 6) = 1 THEN bak.EventSeverity ELSE myevent.EventSeverity END,
        myevent.StartCompareValue = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 7) = 1 THEN bak.StartCompareValue ELSE myevent.StartCompareValue END,
        myevent.StartDelay = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 8) = 1 THEN bak.StartDelay ELSE myevent.StartDelay END,
        myevent.Meanings = CASE WHEN (SELECT wbe.Enable FROM TBL_WriteBackEntry wbe WHERE wbe.EntryId = 9) = 1 THEN bak.Meanings ELSE myevent.Meanings END
        WHERE bak.EntryCategory = 2
    </update>
    <update id="batchUpdateField">
        <foreach collection="eventList" item="event" separator=";">
            UPDATE tbl_event
            <set>
                <if test="event.equipmentTemplateId != null">
                    EquipmentTemplateId = #{event.equipmentTemplateId},
                </if>
                <if test="event.eventId != null">
                    EventId = #{event.eventId},
                </if>
                <if test="event.eventName != null">
                    EventName = #{event.eventName},
                </if>
                <if test="event.startType != null">
                    StartType = #{event.startType},
                </if>
                <if test="event.endType != null">
                    EndType = #{event.endType},
                </if>
                <if test="event.startExpression != null">
                    StartExpression = #{event.startExpression},
                </if>
                <if test="event.suppressExpression != null">
                    SuppressExpression = #{event.suppressExpression},
                </if>
                <if test="event.eventCategory != null">
                    EventCategory = #{event.eventCategory},
                </if>
                <if test="event.signalId != null">
                    SignalId = #{event.signalId},
                </if>
                <if test="event.enable != null">
                    Enable = #{event.enable},
                </if>
                <if test="event.visible != null">
                    Visible = #{event.visible},
                </if>
                <if test="event.description != null">
                    Description = #{event.description},
                </if>
                <if test="event.displayIndex != null">
                    DisplayIndex = #{event.displayIndex},
                </if>
                <if test="event.moduleNo != null">
                    ModuleNo = #{event.moduleNo},
                </if>
            </set>
            WHERE equipmentTemplateId = #{event.equipmentTemplateId} AND eventId = #{event.eventId}
        </foreach>
    </update>

    <select id="getStandardCompareData" resultType="org.siteweb.config.common.dto.StandardEventCompareDTO">
        SELECT
        bak.EquipmentTemplateId AS equipmentTemplateIdBefore,
        template.EquipmentTemplateName AS equipmentTemplateNameBefore,
        bak.EntryId AS EventIdBefore,
        evt.EventName AS EventNameBefore,
        bak.EventSeverity AS EventSeverityBefore,
        (SELECT	ItemValue FROM TBL_DataItem WHERE EntryId = 23 AND ItemId = bak.EventSeverity LIMIT 1) AS eventSeverityNameBefore,
        bak.StartCompareValue AS StartCompareValueBefore,
        bak.StartDelay AS StartDelayBefore,
        bak.StandardName AS StandardNameBefore,
        bak.Meanings AS MeaningsBefore,
        cond.EquipmentTemplateId AS EquipmentTemplateIdAfter,
        template.EquipmentTemplateName AS EquipmentTemplateNameAfter,
        cond.EventId AS EventIdAfter,
        evt.EventName AS EventNameAfter,
        cond.EventSeverity AS EventSeverityAfter,
        (SELECT	ItemValue FROM TBL_DataItem WHERE EntryId = 23 AND ItemId = cond.EventSeverity LIMIT 1) AS eventSeverityNameAfter,
        cond.StartCompareValue AS StartCompareValueAfter,
        cond.StartDelay AS StartDelayAfter,
        cond.StandardName AS StandardNameAfter,
        cond.Meanings AS MeaningsAfter
        FROM
        TBL_StandardBack bak
        LEFT JOIN TBL_EquipmentTemplate template ON
        template.EquipmentTemplateId = bak.EquipmentTemplateId
        LEFT JOIN TBL_Event evt ON
        bak.EquipmentTemplateId = evt.EquipmentTemplateId
        AND bak.EntryId = evt.EventId
        LEFT JOIN TBL_EventCondition cond ON
        bak.EquipmentTemplateId = cond.EquipmentTemplateId
        AND bak.EntryId = cond.EventId
        AND bak.EventConditionId = cond.EventConditionId
        WHERE
        bak.EntryCategory = 2
    </select>

    <select id="getEventStandardApplyCheckData" resultType="org.siteweb.config.common.dto.StandardApplyEventCheckDTO">
        SELECT
        (CASE
        WHEN (
        SELECT
        COUNT(*)
        FROM
        TBL_StandardBack sb
        WHERE
        sb.EntryCategory = 2
        AND sb.EquipmentTemplateId = event.EquipmentTemplateId
        AND sb.EntryId = event.EventId
        AND sb.EventConditionId = event.EventConditionId) >= 1 THEN 1
        ELSE 0
        END) AS Standarded,
        event.EquipmentTemplateId,
        et.EquipmentTemplateName,
        event.EventId,
        ev.EventName,
        event.EventSeverity,
        (SELECT	ItemValue FROM TBL_DataItem WHERE EntryId = 23 AND ItemId = event.EventSeverity LIMIT 1) AS eventSeverityName,
        event.StartCompareValue,
        event.StartDelay,
        event.Meanings,
        event.BaseTypeId,
        ebd.BaseTypeName,
        sde.StandardDicId,
        sde.EventStandardName,
        sde.EquipmentLogicClassId,
        sde.EquipmentLogicClass,
        sde.EventLogicClassId,
        sde.EventLogicClass,
        ebt.BaseEquipmentId,
        ebt.BaseEquipmentName
        FROM
        TBL_EventCondition event
        LEFT JOIN TBL_Event ev ON
        ev.EquipmentTemplateId = event.EquipmentTemplateId
        AND ev.EventId = event.EventId
        LEFT JOIN TBL_EquipmentTemplate et ON
        et.EquipmentTemplateId = event.EquipmentTemplateId
        LEFT JOIN TBL_EventBaseDic ebd ON
        ebd.BaseTypeId = event.BaseTypeId
        LEFT JOIN TBL_EventBaseMap ebm ON
        ebm.BaseTypeId = event.BaseTypeId
        AND ebm.StationBaseType = et.StationCategory
        AND ebm.StandardType = #{standardId}
        LEFT JOIN TBL_StandardDicEvent sde ON
        sde.StandardDicId = ebm.StandardDicId
        AND sde.StandardType = ebm.StandardType
        AND sde.StationCategory = ebm.StationBaseType
        AND sde.StandardType = #{standardId}
        LEFT JOIN TBL_EquipmentBaseType ebt ON
        ebt.BaseEquipmentId = ebd.BaseEquipmentId
    </select>

    <select id="getEventStandardMappingCheck"  resultType="org.siteweb.config.common.dto.StandardMappingEventCheckDTO">
        SELECT
        DISTINCT event.EventId,
        event.EventName,
        (CASE
        WHEN dicEvent.StandardDicId IS NULL THEN 0
        ELSE 1
        END) AS Standarded,
        station.StationId,
        station.StationName,
        station.StationCategory,
        equip.EquipmentId,
        equip.EquipmentName,
        equip.EquipmentCategory,
        eq.EquipmentTemplateId,
        eq.EquipmentTemplateName,
        cond.Meanings,
        dic.BaseTypeId,
        dic.BaseTypeName,
        dicEvent.StandardDicId,
        REPLACE(dicEvent.EventStandardName, 'XX', RIGHT(concat('0', (cond.BaseTypeId - floor(cond.BaseTypeId / 1000)* 1000)::VARCHAR), 2)) AS EventStandardName
        FROM
        TBL_Station station
        LEFT JOIN TBL_Equipment equip ON  station.StationId = equip.StationId
        LEFT JOIN TBL_EquipmentTemplate eq ON  eq.EquipmentTemplateId = equip.EquipmentTemplateId
        LEFT JOIN TBL_Event event ON
        eq.EquipmentTemplateId = event.EquipmentTemplateId
        LEFT JOIN TBL_EventCondition cond ON
        cond.EventId = event.EventId
        AND cond.EquipmentTemplateId = event.EquipmentTemplateId
        LEFT JOIN TBL_EventBaseDic dic ON
        dic.BaseTypeId = cond.BaseTypeId
        LEFT JOIN TBL_StationBaseMap stationMap ON
        stationMap.StationCategory = station.StationCategory
        AND stationMap.StandardType = #{standardId}
        LEFT JOIN TBL_EventBaseMap MAP ON
        FLOOR(map.BaseTypeId / 1000)= FLOOR(dic.BaseTypeId / 1000)
        AND map.StationBaseType = stationMap.StationBaseType
        AND map.StandardType = #{standardId}
        LEFT JOIN TBL_StandardDicEvent dicEvent ON
        dicEvent.StandardDicId = map.StandardDicId
        AND (dicEvent.StationCategory = map.StationBaseType
        OR dicEvent.StationCategory = 0)
        AND dicEvent.StandardType = #{standardId}
        WHERE
        event.EventId IS NOT NULL
        <if test="equipmentCategory != null">
            AND equip.EquipmentCategory = #{equipmentCategory}
        </if>
        ORDER BY StationId
    </select>
    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT max(DisplayIndex) FROM tbl_event WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findByEquipmentCategory" resultType="org.siteweb.config.common.entity.TblEvent">
        SELECT
        s.EquipmentTemplateId,
        s.EventId,
        s.EventName ,
        s.Description
        FROM
        tbl_event s
        INNER JOIN tbl_equipmenttemplate eq ON s.EquipmentTemplateId = eq.EquipmentTemplateId
        WHERE
        eq.EquipmentCategory = #{equipmentCategory}
    </select>
    <select id="findExcelDtoByEquipmentTemplateId" resultType="org.siteweb.config.common.dto.excel.EventExcel">
        SELECT b.EquipmentId,
               b.EquipmentName,
               a.EventId,
               a.EventName,
               c.Id,
               c.eventconditionId,
               c.EquipmentTemplateId,
               c.StartOperation,
               c.StartCompareValue,
               c.StartDelay,
               c.EndOperation,
               c.EndCompareValue,
               c.EndDelay,
               c.Frequency,
               c.FrequencyThreshold,
               c.Meanings,
               c.EquipmentState,
               c.BaseTypeId,
               c.EventSeverity
        FROM tbl_event a
                 LEFT JOIN
             tbl_equipment b ON b.EquipmentTemplateid = a.EquipmentTemplateId
                 LEFT JOIN
             tbl_eventcondition c ON c.EquipmentTemplateId = a.EquipmentTemplateId AND c.EventId = a.EventId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>