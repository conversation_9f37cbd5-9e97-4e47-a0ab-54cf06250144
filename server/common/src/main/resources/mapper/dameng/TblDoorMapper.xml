<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblDoorMapper">
    <select id="findDoorControlId" resultType="java.lang.Integer">
        <!-- adaptive-multi-dm 转换数字，英文字符串转数字0-->
        SELECT CASE WHEN LENGTH(TRIM(a.Expression)) = 0 THEN 0  -- 空字符串返回0
        WHEN REGEXP_LIKE(TRIM(a.Expression), '^\d+$') THEN CAST(a.Expression AS varchar)::NUMERIC  -- 纯数字字符串转为整数
        ELSE 0  -- 其他情况返回0
        END AS ConvertedExpression
        FROM TBL_Signal a,
             TBL_Equipment b
        WHERE a.EquipmentTemplateId = b.EquipmentTemplateId
          AND b.EquipmentId = #{equipmentId}
          AND a.ChannelNo = 39
    </select>
    <select id="findDoorNoByEquipmentIdAndFour" resultType="java.lang.Integer">
        <!-- adaptive-multi-dm8 DIV修改为FLOOR，保证结果一定是整数 -->
        SELECT DISTINCT FLOOR(a.ChannelNo / 20) AS DoorNo
        FROM TBL_Signal a,
             TBL_Equipment b
        WHERE a.EquipmentTemplateId = b.EquipmentTemplateId
          AND b.EquipmentId = #{equipmentId}
          AND b.EquipmentCategory = 82
          AND a.ChannelNo = 20
          AND a.ChannelNo &lt; 100
          AND a.ChannelNo &gt; 0
    </select>
    <select id="findDoorNoByEquipmentIdAndTwelve" resultType="java.lang.Integer">
        SELECT b.Address % 10
        FROM TBL_Equipment a
                 INNER JOIN TSL_SamplerUnit b ON a.SamplerUnitId = b.SamplerUnitId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findDoorNoByEquipmentIdAndTwenty" resultType="java.lang.Integer">
        <!-- adaptive-multi-dm 截取数字-->
        SELECT CASE WHEN LENGTH(TRIM(SUBSTR(b.Address, 2, 2))) = 0 THEN 0
        ELSE CAST(SUBSTR(b.Address, 2, 2) AS INT)
        FROM TBL_Equipment a
                 INNER JOIN TSL_SamplerUnit b ON a.SamplerUnitId = b.SamplerUnitId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findDoorNoByEquipmentIdAndOther" resultType="java.lang.Integer">
        <!-- adaptive-multi-dm DIV修改为FLOOR，保证结果一定是整数 -->
        SELECT DISTINCT  FLOOR(a.ChannelNo / 20) AS DoorNo
        FROM TBL_Signal a,
             TBL_Equipment b
        WHERE a.EquipmentTemplateId = b.EquipmentTemplateId
          AND b.EquipmentId = #{equipmentId}
          AND b.EquipmentCategory = 82
          AND a.ChannelNo = 20
          AND a.ChannelNo &lt; 100
          AND a.ChannelNo &gt; 0
    </select>
</mapper>