<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblCommandBaseDicMapper">
    <insert id="generateSignalBaseDic">
        INSERT INTO TBL_CommandBaseDic
        (BaseTypeId,
         BaseTypeName,
         BaseEquipmentId,
         EnglishName,
         BaseLogicCategoryId,
         CommandType,
         BaseStatusId,
         ExtendField1,
         ExtendField2,
         ExtendField3,
         Description,
         BaseNameExt,
         IsSystem)
        SELECT #{baseTypeId},
               T.BaseTypeName,
               T.BaseEquipmentId,
               T.EnglishName,
               T.BaseLogicCategoryId,
               T.CommandType,
               T.BaseStatusId,
               T.ExtendField1,
               T.ExtendField2,
               T.ExtendField3,
               T.Description,
               T.BaseNameExt,
               0
        FROM TBL_CommandBaseDic T
        WHERE T.BaseTypeId = #{sourceId}
    </insert>
    <select id="findCommandBaseDic" resultType="org.siteweb.config.common.entity.TblCommandBaseDic">
        <!-- adaptive-multi-dm BaseTypeId数字类型pg不支持模糊查询   -->
        SELECT BaseTypeId,BaseTypeName,BaseNameExt FROM TBL_CommandBaseDic
        WHERE BaseEquipmentId = (#{eqTypeId} * 100 + #{eqSubTypeId})
        <if test="baseTypeId != null and baseTypeId != ''">
            AND BaseTypeId LIKE CONCAT('%', #{baseTypeId},'%')
        </if>
        <if test="baseTypeName != null and baseTypeName != ''">
            AND BaseTypeName LIKE CONCAT('%', #{baseTypeName},'%')
        </if>
        <if test="baseNameExt != null and baseNameExt != ''">
            AND BaseNameExt LIKE CONCAT('%', #{baseNameExt},'%')
        </if>
        order by BaseTypeId ASC
    </select>
    <sql id="commandBaseDicSql">
        SELECT
        tcb.BaseTypeId,
        tcb.BaseTypeName,
        tcb.BaseEquipmentId,
        tcb.EnglishName,
        tcb.BaseLogicCategoryId,
        tcb.CommandType,
        case when tcb.CommandType =2 then '遥控' else '遥调' end as commandTypeName,
        tcb.BaseStatusId,
        tcb.ExtendField1,
        tcb.ExtendField2,
        tcb.ExtendField3,
        tcb.BaseNameExt,
        tcb.Description,
        tcb.IsSystem,
        teb.BaseEquipmentName,
        tbc.BaseClassName,
        tlb.BaseLogicCategoryName
        FROM
        tbl_commandbasedic tcb
        LEFT JOIN tbl_equipmentbasetype teb ON tcb.BaseEquipmentId = teb.BaseEquipmentId
        LEFT JOIN tbl_baseclassdic tbc ON teb.EquipmentTypeId = tbc.BaseClassId
        LEFT JOIN tbl_logiccategorybasedic tlb ON tcb.BaseLogicCategoryId = tlb.BaseLogicCategoryId
    </sql>
    <select id="findCommandBaseDicList" resultType="org.siteweb.config.common.dto.CommandBaseDicDTO">
        <include refid="commandBaseDicSql"/>
    </select>
    <select id="findCommandBaseDicDetail" resultType="org.siteweb.config.common.dto.CommandBaseDicDTO">
        <include refid="commandBaseDicSql"/>
        WHERE tcb.BaseTypeId = #{baseTypeId}
    </select>
    <select id="findCommandBaseStatusList" resultType="org.siteweb.config.common.dto.CommandlBaseStatusDTO">
        SELECT
        x.BaseTypeId,
        y.BaseCondId,
        y.Meaning AS baseMeaning
        FROM
        TBL_CommandBaseDic x
        RIGHT OUTER JOIN TBL_StatusBaseDic y ON
        x.BaseStatusId = y.BaseStatusId
    </select>
</mapper>