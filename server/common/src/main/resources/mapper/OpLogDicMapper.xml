<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.OpLogDicMapper">
    <!--    二级缓存
    eviction 属性定义了缓存的淘汰策略，常见的有 LRU（最近最少使用）、FIFO（先进先出）等。
    flushInterval 属性定义了刷新缓存的时间间隔，单位为毫秒。
    size 属性定义了缓存的大小，表示缓存项的个数。
    readOnly 属性表示是否只读缓存，如果为 true，缓存数据不会被修改。-->
    <cache eviction="LRU" flushInterval="60000" size="512" readOnly="true"/>

    <select id="executeSql" resultType="org.siteweb.config.common.dto.LowerCaseKeyMap">
        ${sql}
    </select>
    <select id="executeSqls" resultType="org.siteweb.config.common.dto.LowerCaseKeyMap">
        ${sql}
    </select>
</mapper>