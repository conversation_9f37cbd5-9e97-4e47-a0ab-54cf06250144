<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStationCMCCMapper">
    <select id="findStationsByCategory" resultType="org.siteweb.config.common.entity.TblStationCMCC">
        SELECT station.StationName AS SiteName,
               station.StationId   AS stationId,
               station.Description AS Description,
               ''                  AS siteID
        FROM TBL_Station station
                 INNER JOIN TSL_MonitorUnit mu ON mu.StationId = station.StationId
        WHERE NOT EXISTS
            (SELECT 1
             FROM TBL_StationCMCC stationCMCC
             WHERE stationCMCC.StationId = station.StationId)
          AND mu.MonitorUnitCategory = #{categoryId}
    </select>
    <select id="findSiteIdByStationId" resultType="java.lang.String">
        SELECT SiteID FROM TBL_StationCMCC WHERE StationId = #{stationId}
    </select>
</mapper>