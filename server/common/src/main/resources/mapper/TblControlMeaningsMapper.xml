<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblControlMeaningsMapper">
    <insert id="batchInsertControlMeanings" parameterType="java.util.List">
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000072,100010132,0,'开机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000072,100010132,1,'关机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010142,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010142,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010143,0,'待命',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010145,0,'自动',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010145,1,'手动',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010147,0,'主机组工作',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000076,100010147,1,'备机组工作',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010144,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010144,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010145,0,'待命',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010147,0,'自动',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010147,1,'手动',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010149,0,'主机组工作',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000077,100010149,1,'备机组工作',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000086,100010152,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000086,100010152,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000087,100010154,1,'门关',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000089,100010156,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000089,100010157,1,'关机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000090,100010151,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000091,100010158,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000091,100010158,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000092,100010154,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000092,100010154,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000093,100010140,0,'撤出',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000093,100010140,1,'投入',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000094,100010158,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000094,100010158,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000096,100010132,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000096,100010132,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000096,100010134,0,'均充',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000096,100010134,1,'浮充',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000096,100010135,0,'待命',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000100,100010148,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000100,100010148,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000101,100010147,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000101,100010147,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000101,100010149,0,'均充',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000101,100010149,1,'浮充',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000101,100010150,0,'放电',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000109,100010136,0,'关机',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000109,100010136,1,'开机',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000109,100010138,0,'均充',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000109,100010138,1,'浮充',0);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000109,100010139,0,'待命',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000104,100010177,0,'结束',1);
        INSERT INTO TBL_ControlMeanings (EquipmentTemplateId, ControlId, ParameterValue, Meanings, BaseCondId)	VALUES  (123000104,100010177,1,'开始',0);
    </insert>


    <insert id="batchInsertControlMeaningsByControl">
        INSERT INTO TBL_ControlMeanings(EquipmentTemplateId,ControlId,ParameterValue,Meanings,BaseCondId)
        SELECT #{equipmentTemplateId} AS EquipmentTemplateId, #{controlId} AS ControlId, a.ParameterValue, a.Meanings, a.BaseCondId FROM TBL_ControlMeanings a
        WHERE a.EquipmentTemplateId= #{originEquipmentTemplateId} AND a.ControlId= #{originControlId}
    </insert>

    <select id="selectMaxParameterValueByControlId" resultType="java.lang.Short">
        SELECT MAX(ParameterValue) FROM TBL_ControlMeanings WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT concat(a.EquipmentTemplateId,'.',a.ControlId,'.',a.ParameterValue) as id,a.Meanings as value
        FROM tbl_controlmeanings a
        WHERE
        (a.EquipmentTemplateId, a.ControlId,a.ParameterValue) IN
        <foreach item="item" collection="controlUniqueIds" separator="," open="(" close=")">
            (#{item.equipmentTemplateId}, #{item.controlId},#{parameterValue})
        </foreach>
    </select>
</mapper>