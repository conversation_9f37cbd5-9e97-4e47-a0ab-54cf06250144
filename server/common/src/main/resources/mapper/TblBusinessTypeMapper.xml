<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblBusinessTypeMapper">


    <select id="findTblBusinessTypeByMonitorUnitId" resultType="org.siteweb.config.common.entity.TblBusinessType">
        SELECT distinct a.BusinessTypeId, b.BusinessTypeName,b.MiddleTableName,b.Note
        FROM TBL_BizExpStationsMap a
        INNER JOIN TBL_BusinessType b ON a.BusinessTypeId = b.BusinessTypeId WHERE a.MonitorUnitId=#{monitorId} and (b.BusinessTypeId &lt; 6000)
    </select>

    <select id="findDistinctBusinessTypesAndExpressionsForMonitorUnit" resultType="org.siteweb.config.common.vo.BusinessTypeExpressionVO">
        SELECT distinct a.BusinessTypeId,d.BusinessTypeName,a.ExpressionId, e.ExpressionName,a.StationId,b.StationName, a.MonitorUnitId,
        c.MonitorUnitName, a.Expression, a.SuppressExpression, a.StateTriggerValue,a.BeforeChgStoreInterval,
        a.AfterChgStoreInterval,a.ErrorFlag, a.Note ,a.SerialId,
        CASE WHEN EXISTS(SELECT 'x' FROM TBL_BizExpEquSignalsMap WHERE ExpressionId =a.ExpressionId and MonitorUnitId=a.MonitorUnitId and SerialId=a.SerialId)
        THEN 1 ELSE 0 END AS EnableExpSignals
        FROM TBL_BizExpStationsMap a
        INNER JOIN TBL_Station b ON a.StationId=b.StationId
        INNER JOIN TSL_MonitorUnit c ON a.MonitorUnitId=c.MonitorUnitId
        INNER JOIN TBL_BusinessType d ON a.BusinessTypeId=d.BusinessTypeId and (d.BusinessTypeId <![CDATA[ < ]]> 6000)
        INNER JOIN TBL_BusinessExpressionCfg e ON a.ExpressionId =e.ExpressionId
        WHERE  (a.ErrorFlag /100) != 1 and a.ErrorFlag % 10 != 1 and ((a.ErrorFlag % 100)/10) != 1 AND a.MonitorUnitId=#{monitorUnitId}
    </select>

    <select id="findDistinctBusinessExpressionsForMonitorUnit" resultType="org.siteweb.config.common.vo.BusinessExpressionVO">
        SELECT distinct a.BusinessTypeId,d.BusinessTypeName,
        a.ExpressionId,g.ExpressionName, g.Expression, g.SuppressExpression, a.AssociationId,
        g.StateTriggerValue,g.BeforeChgStoreInterval,g.AfterChgStoreInterval,
        a.StoreInterval, a.AbsValueThreshold,
        g.StationId,b.StationName,
        a.EquipmentId,e.EquipmentName,
        a.SignalId,f.SignalName,
        a.MonitorUnitId,c.MonitorUnitName,a.SerialId
        FROM TBL_BizExpEquSignalsMap a
        inner join TSL_MonitorUnit c ON a.MonitorUnitId=c.MonitorUnitId
        inner join TBL_BusinessType d ON a.BusinessTypeId=d.BusinessTypeId and (d.BusinessTypeId <![CDATA[ < ]]> 6000)
        inner join TBL_Equipment e ON a.EquipmentId=e.EquipmentId
        inner join TBL_Signal f ON a.SignalId=f.SignalId and a.EquipmentId =e.EquipmentId AND e.EquipmentTemplateId=f.EquipmentTemplateId
        inner join TBL_BusinessExpressionCfg g ON a.ExpressionId=g.ExpressionId
        inner join TBL_Station b ON a.StationId=b.StationId
        WHERE a.MonitorUnitId=#{monitorUnitId}
    </select>

    <select id="findBusinessExpressionVOByStationId" resultType="org.siteweb.config.common.vo.BusinessExpressionVO">
        SELECT * FROM TBL_BizExpEquSignalsMap WHERE StationId=#{stationId}
    </select>
</mapper>