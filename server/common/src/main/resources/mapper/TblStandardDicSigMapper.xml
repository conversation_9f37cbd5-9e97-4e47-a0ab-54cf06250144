<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStandardDicSigMapper">
    <resultMap id="StandardDicSigIniMap" type="org.siteweb.config.common.dto.StandardDicSigIniDTO">
        <result property="standardDicId" column="StandardDicId"/>
        <result property="stationCategory" column="StationCategory"/>
        <result property="signalLogicClass" column="SignalLogicClass"/>
        <result property="signalStandardName" column="SignalStandardName"/>
        <result property="netManageId" column="NetManageId"/>
        <collection property="baseTypeList" ofType="org.siteweb.config.common.dto.BaseTypeInnerDTO" column="NetManageId" notNullColumn="NetManageId">
            <result property="baseTypeId" column="BaseTypeId"/>
            <result property="baseTypeName" column="BaseTypeName"/>
        </collection>
    </resultMap>
    <update id="updateEntity">
        UPDATE TBL_StandardDicSig
        SET equipmentLogicClassId = #{standardDicSig.equipmentLogicClassId},
            equipmentLogicClass   = #{standardDicSig.equipmentLogicClass},
            signalLogicClassId    = #{standardDicSig.signalLogicClassId},
            signalLogicClass      = #{standardDicSig.signalLogicClass},
            signalStandardName    = #{standardDicSig.signalStandardName},
            stationCategory       = #{standardDicSig.stationCategory},
            netManageId           = #{standardDicSig.netManageId},
            storeInterval         = #{standardDicSig.storeInterval},
            absValueThreshold     = #{standardDicSig.absValueThreshold},
            statisticsPeriod      = #{standardDicSig.statisticsPeriod},
            percentThreshold      = #{standardDicSig.percentThreshold},
            modifyType            = #{standardDicSig.modifyType},
            description           = #{standardDicSig.description},
            extendFiled1          = #{standardDicSig.extendFiled1},
            extendFiled2          = #{standardDicSig.extendFiled2}
        WHERE standardDicId = #{standardDicSig.standardDicId}
          AND standardType = #{standardDicSig.standardType}
    </update>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT StandardDicId as id,SignalStandardName as value FROM tbl_standarddicsig WHERE StandardDicId IN
        <foreach collection="standardDicIds" item="standardDicId" open="(" close=")" separator=",">
            #{standardDicId}
        </foreach>
    </select>
    <select id="getStandardDicSigInis" resultMap="StandardDicSigIniMap">
        SELECT
        sig.StandardDicId,
        sig.StationCategory,
        sig.SignalLogicClass,
        sig.SignalStandardName,
        sig.NetManageId,
        base.BaseTypeId,
        base.BaseTypeName
        FROM
        TBL_StandardDicSig sig
        LEFT JOIN (
        SELECT
        a.StandardDicId,
        a.StationBaseType,
        a.StandardType,
        a.BaseTypeId,
        b.BaseTypeName
        FROM
        TBL_SignalBaseMap a
        LEFT JOIN TBL_SignalBaseDic B ON
        a.BaseTypeId = B.BaseTypeId
        ) base
        ON
        base.StandardDicId = sig.StandardDicId
        AND base.StationBaseType = sig.StationCategory
        AND base.StandardType = sig.StandardType
        WHERE
        sig.StandardType = #{standardId}
    </select>
    <select id="maxStandardDicId" resultType="java.lang.Integer">
        SELECT MAX(MaxStandardDicId)
        FROM (SELECT MAX(aa.StandardDicId) AS MaxStandardDicId
              FROM TBL_StandardDicSig aa
              WHERE aa.StandardType = 3
              UNION
              SELECT MAX(bb.StandardDicId) AS MaxStandardDicId
              FROM TBL_StandardDicEvent bb
              WHERE bb.StandardType = 3
              UNION
              SELECT MAX(cc.StandardDicId) AS MaxStandardDicId
              FROM TBL_StandardDicControl cc
              WHERE cc.StandardType = 3) TBL_StandardDic;
    </select>
</mapper>