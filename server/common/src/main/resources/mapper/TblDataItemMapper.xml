<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblDataItemMapper">
    <select id="findEquipmentCategory" resultType="org.siteweb.config.common.entity.TblDataItem">
        SELECT DISTINCT b.ItemId,
                        b.ItemValue
        FROM TBL_EquipmentTemplate a,
             TBL_DataItem b
        WHERE a.EquipmentCategory = b.ItemId
          AND b.EntryId = 7
    </select>
    <select id="selectMaxItemId" resultType="java.lang.Integer">
        SELECT MAX(EntryItemId) FROM TBL_DataItem
    </select>

    <select id="selectMaxItemIdByEntryId" resultType="java.lang.Integer">
        SELECT MAX(itemId) FROM TBL_DataItem WHERE EntryId = #{entryId}
    </select>

    <update id="updatePortExtendField2">
        UPDATE tbl_dataitem SET ExtendField2  = '2,4,5,6,7,8,9,10,11,12,14,16,17,18,19,20,21,22,23,25' WHERE EntryId = 39 AND ItemId IN (3,34,35);

        UPDATE tbl_dataitem SET ExtendField2 = '1,24' where EntryId = 39 and ItemId in (33,31,30,32);

        UPDATE tbl_dataitem SET ExtendField2 = '2,4,5,6,7,8,9,10,11,12,14,16,17,18,19,20,21,22,23,1,24,25' where EntryId = 39 and ItemId in (1,6,5,19);
    </update>
</mapper>

