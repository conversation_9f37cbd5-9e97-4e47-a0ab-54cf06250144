<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TslMonitorUnitCUCCMapper">
    <update id="updateEntity">
        UPDATE tsl_monitorunitcucc
        SET SUID        = #{tslMonitorUnitCUCC.suId},
            SUName      = #{tslMonitorUnitCUCC.suName},
            SURID       = #{tslMonitorUnitCUCC.surId},
            UserName    = #{tslMonitorUnitCUCC.userName},
            PassWord    = #{tslMonitorUnitCUCC.passWord},
            SUIP        = #{tslMonitorUnitCUCC.suIp},
            FTPUserName = #{tslMonitorUnitCUCC.ftpUserName},
            FTPPassWord = #{tslMonitorUnitCUCC.ftpPassWord}
        WHERE StationId = #{tslMonitorUnitCUCC.stationId}
          AND MonitorUnitId = #{tslMonitorUnitCUCC.monitorUnitId}
    </update>
    <select id="findAllVO" resultType="org.siteweb.config.common.vo.binterface.MonitorUnitCUCCVO">
        SELECT moniturUnit.StationId,
               station.StationName,
               moniturUnit.MonitorUnitId,
               moniturUnit.SUID,
               moniturUnit.SUName,
               moniturUnit.SURID,
               moniturUnit.UserName,
               moniturUnit.PassWord,
               moniturUnit.SUIP,
               moniturUnit.FTPUserName,
               moniturUnit.FTPPassWord
        FROM TSL_MonitorUnitCUCC moniturUnit
                 INNER JOIN TBL_Station station ON moniturUnit.StationId = station.StationId
    </select>
</mapper>