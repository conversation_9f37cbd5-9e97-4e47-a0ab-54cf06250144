<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblWorkStationMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tbl_workstation
        (WorkStationId, WorkStationName, WorkStationType, IPAddress, ParentId, ConnectState, UpdateTime, IsUsed, CPU, Memory, ThreadCount, DiskFreeSpace, DBFreeSpace, LastCommTime)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.workStationId}, #{item.workStationName}, #{item.workStationType}, #{item.ipAddress}, #{item.parentId}, #{item.connectState}, #{item.updateTime}, #{item.isUsed}, #{item.cpu}, #{item.memory}, #{item.threadCount}, #{item.diskFreeSpace}, #{item.dbFreeSpace}, #{item.lastCommTime})
        </foreach>
    </insert>


    <select id="findByWorkstationTypes" resultType="org.siteweb.config.common.entity.TblWorkStation">
        SELECT * FROM TBL_Workstation
        WHERE WorkStationType IN
        <foreach collection="workstationTypes" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT workStationId as id,WorkStationName as value FROM tbl_workstation WHERE WorkStationId IN
        <foreach collection="workStationIds" item="workStationId" open="(" close=")" separator=",">
            #{workStationId}
        </foreach>
    </select>
</mapper>