<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEquipmentBaseTypeMapper">
    <select id="findEquipmentSubTypeById" resultType="org.siteweb.config.common.dto.EquipmentSubTypeDTO">
        SELECT EquipmentSubTypeId as eqSubTypeId,BaseEquipmentName as eqSubTypeName FROM TBL_EquipmentBaseType WHERE EquipmentTypeId=#{equipmentTypeId}
    </select>
    <select id="findEquipmentSubTypeTreeVOs" resultType="org.siteweb.config.common.vo.EquipmentSubTypeTreeVO">
        SELECT t.EquipmentTypeId as eqTypeId, d.BaseClassName as eqTypeName, t.EquipmentSubTypeId as eqSubTypeId, t.BaseEquipmentName as eqSubTypeName
        FROM TBL_EquipmentBaseType t
        left join tbl_baseclassdic d on d.BaseClassId = t.EquipmentTypeId
    </select>
    <select id="findEquipmentBaseTypeAndClass" resultType="org.siteweb.config.common.dto.EquipmentBaseTypeClassDTO">
        SELECT
        teb.BaseEquipmentId,
        teb.BaseEquipmentName,
        teb.EquipmentTypeId,
        teb.EquipmentSubTypeId,
        teb.Description,
        tbc.BaseClassName
        FROM
        tbl_equipmentbasetype teb
        LEFT JOIN tbl_baseclassdic tbc ON
        teb.EquipmentTypeId = tbc.BaseClassId
    </select>
</mapper>