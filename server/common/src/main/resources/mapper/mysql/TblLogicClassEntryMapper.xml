<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblLogicClassEntryMapper">

    <update id="updateLogicClassByDataItem" parameterType="java.util.Map">
        UPDATE TBL_LogicClassEntry, TBL_DataItem
        SET TBL_LogicClassEntry.LogicClass = TBL_DataItem.ItemValue
        WHERE TBL_LogicClassEntry.StandardType = #{standardType}
          AND TBL_DataItem.EntryId = #{entryId}
          AND TBL_LogicClassEntry.Description = TBL_DataItem.ExtendField2;
    </update>
    <select id="maxEntryId" resultType="java.lang.Integer">
        SELECT MAX(EntryId)
        FROM TBL_LogicClassEntry;
    </select>
    <select id="maxLogicClassId" resultType="java.lang.Integer">
        SELECT max(LogicClassId) FROM TBL_LogicClassEntry WHERE StandardType = #{standardType} AND EntryCategory = #{entryCategory}
    </select>

</mapper>

