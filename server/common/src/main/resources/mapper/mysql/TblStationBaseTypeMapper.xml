<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStationBaseTypeMapper">

    <select id="getBaseStationMapType" resultType="org.siteweb.config.common.dto.BaseStationMapTypeDTO">
        SELECT cast_to_bit(1) AS Checked, ItemId, ItemValue, (SELECT COUNT(*) FROM TBL_Station WHERE StationCategory = di.ItemId) AS Counts,  sbt.Type, sbt.Id
        FROM TBL_StationBaseMap map
        INNER JOIN TBL_DataItem di ON map.StationCategory = di.ItemId AND di.EntryId = 71 AND map.StationBaseType = #{stationBaseTypeId}
        INNER JOIN TBL_StationBaseType sbt ON sbt.Id = map.StationBaseType AND sbt.StandardId = #{standardId}
        WHERE map.StandardType = #{standardId}
        UNION
        SELECT cast_to_bit(0) AS Checked, ItemId, ItemValue, (SELECT COUNT(*) FROM TBL_Station WHERE StationCategory = dc.ItemId) AS Counts, sbt.Type, sbt.Id
        FROM TBL_DataItem dc
        LEFT JOIN TBL_StationBaseMap sbm ON dc.ItemId = sbm.StationCategory AND sbm.StandardType = #{standardId}
        LEFT JOIN TBL_StationBaseType sbt ON sbt.Id = sbm.StationBaseType AND sbt.StandardId = #{standardId}
        WHERE EntryId = 71 AND ItemId NOT IN
        (SELECT StationCategory   FROM TBL_StationBaseMap WHERE StationBaseType = #{stationBaseTypeId} AND StandardType = #{standardId})
        ORDER BY Id ASC, ItemId ASC
    </select>

    <select id="getEquipmentTemplateStationBaseType"  resultType="org.siteweb.config.common.dto.EqStationBaseTypeDTO">
        SELECT eqt.EquipmentTemplateId, eqt.EquipmentTemplateName, eqt.StationCategory AS Id, sbt.Type,eqt.EquipmentCategory,eqt.ProtocolCode, 0 AS IsDirty
        FROM TBL_EquipmentTemplate eqt
        LEFT JOIN TBL_StationBaseType sbt ON sbt.Id = eqt.StationCategory AND sbt.StandardId = #{standardId}
        WHERE eqt.EquipmentTemplateId > 0
        ORDER BY eqt.EquipmentCategory,eqt.ProtocolCode,eqt.EquipmentTemplateName
    </select>
    <select id="getSignalStationBaseType" resultType="org.siteweb.config.common.dto.SignalStationBaseTypeDTO">
        <choose>
            <when test="standardId != null and standardId == 1">
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.SignalLogicClass, dic.SignalStandardName,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicSig dic
                LEFT JOIN TBL_StationBaseType sbt ON sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </when>
            <otherwise>
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.SignalLogicClass, dic.SignalStandardName,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicSig dic
                LEFT JOIN TBL_StationBaseType sbt ON sbt.Id = dic.StationCategory AND sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </otherwise>
        </choose>
    </select>

    <select id="getEventStationBaseType" resultType="org.siteweb.config.common.dto.EventStationBaseTypeDTO">
        <choose>
            <when test="standardId != null and standardId == 1">
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.EventLogicClass, dic.EventStandardName, dic.Meanings, dic.CompareValue,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicEvent dic
                LEFT JOIN TBL_StationBaseType sbt ON  sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </when>
            <otherwise>
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.EventLogicClass, dic.EventStandardName, dic.Meanings, dic.CompareValue,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicEvent dic
                LEFT JOIN TBL_StationBaseType sbt ON sbt.Id = dic.StationCategory AND sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </otherwise>
        </choose>
    </select>

    <select id="getControlStationBaseType" resultType="org.siteweb.config.common.dto.ControlStationBaseTypeDTO">
        <choose>
            <when test="standardId != null and standardId == 1">
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.ControlLogicClass, dic.ControlStandardName,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicControl dic
                LEFT JOIN TBL_StationBaseType sbt ON  sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </when>
            <otherwise>
                SELECT DISTINCT dic.StandardDicId, dic.EquipmentLogicClass, dic.ControlLogicClass, dic.ControlStandardName,
                sbt.Id,
                sbt.Type,
                null AS BaseTypeId,
                null AS BaseTypeName,
                0 AS IsDirty
                FROM TBL_StandardDicControl dic
                LEFT JOIN TBL_StationBaseType sbt ON sbt.Id = dic.StationCategory AND sbt.StandardId = #{standardId}
                WHERE dic.StandardType = #{standardId}
                ORDER BY dic.StandardDicId
            </otherwise>
        </choose>
    </select>
</mapper>