<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblEquipmentMapper">


    <sql id="findEquipmentsQuery">
        SELECT
        eq.StationId,
        eq.EquipmentId,
        eq.EquipmentName,
        eq.EquipmentNo,
        eq.EquipmentModule,
        eq.EquipmentStyle,
        eq.AssetState,
        eq.Price,
        eq.UsedLimit,
        eq.UsedDate,
        eq.BuyDate,
        eq.Vendor,
        eq.Unit,
        eq.EquipmentCategory,
        eq.EquipmentType,
        eq.EquipmentClass,
        eq.EquipmentState,
        eq.EventExpression,
        eq.StartDelay,
        eq.EndDelay,
        eq.Property,
        eq.Description,
        eq.EquipmentTemplateId,
        eq.HouseId,
        eq.MonitorUnitId,
        eq.WorkStationId,
        eq.SamplerUnitId,
        eq.DisplayIndex,
        eq.ConnectState,
        eq.UpdateTime,
        eq.ParentEquipmentId,
        eq.RatedCapacity,
        eq.InstalledModule,
        eq.ExtValue,
        info.ProjectName,
        info.ContractNo,
        info.InstallTime,
        info.EquipmentSN,
        info.SO,
        eq.ResourceStructureId,
        ts.PortId,
        tp.PortName
        FROM TBL_Equipment eq LEFT JOIN TBL_EquipmentProjectInfo info
        ON eq.EquipmentId = info.EquipmentId
        LEFT JOIN tsl_samplerunit ts ON eq.SamplerUnitId = ts.SamplerUnitId
        LEFT JOIN tsl_port tp ON tp.PortId = ts.PortId
    </sql>


    <select id="findReferenceVoByEquipmentTemplateId" resultType="org.siteweb.config.common.vo.EquipmentReferenceVO">
        SELECT a.EquipmentId,
               a.EquipmentName,
               b.StationId,
               b.MonitorUnitId,
               b.MonitorUnitName,
               b.IpAddress,
               c.StationName
        FROM TBL_Equipment a
                 LEFT JOIN TSL_MonitorUnit b ON a.StationId = b.StationId AND a.MonitorUnitId = b.MonitorUnitId
                 LEFT JOIN TBL_Station c ON a.StationId = c.StationId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <resultMap id="equipmentDetailDTOMap" type="org.siteweb.config.common.dto.EquipmentDetailDTO" autoMapping="true">
        <association property="equipmentProjectInfo" javaType="org.siteweb.config.common.dto.EquipmentDetailDTO$EquipmentProjectInfo" >
            <result property="projectName" column="projectName"/>
            <result property="contractNo" column="contractNo"/>
        </association>
    </resultMap>
    <select id="findEquipmentDetail" resultMap="equipmentDetailDTOMap">
        SELECT
        te.StationId,
        te.EquipmentId,
        te.EquipmentName,
        te.EquipmentTemplateid AS equipmentTemplateId,
        tet.EquipmentTemplateName,
        te.ParentEquipmentId,
        te.WorkstationId,
        tw.WorkStationName,
        sta.StationName,
        te.MonitorUnitId,
        mon.MonitorUnitName,
        te.EquipmentCategory,
        te.EquipmentType,
        te.EquipmentClass,
        te.EquipmentState,
        te.Vendor,
        te.Unit,
        te.EquipmentStyle,
        te.EquipmentModule,
        te.StartDelay,
        te.EndDelay,
        te.Property,
        tet.EquipmentBaseType,
        te.EventExpression,
        te.EquipmentNo,
        te.AssetState,
        te.BuyDate,
        te.UsedDate,
        te.UsedLimit,
        te.Price,
        te.RatedCapacity,
        te.installedmodule,
        te.Description,
        tep.ProjectName,
        tep.ContractNo,
        te.SamplerUnitId
        FROM
        tbl_equipment te
        LEFT JOIN tbl_equipmenttemplate tet ON
        te.EquipmentTemplateid = tet.EquipmentTemplateId
        LEFT JOIN tbl_equipmentprojectinfo tep ON
        te.StationId = tep.StationId
        AND te.EquipmentId = tep.EquipmentId
        AND te.MonitorUnitId = tep.MonitorUnitId
        LEFT JOIN tbl_workstation tw ON te.WorkStationId = tw.WorkStationId
        LEFT JOIN tbl_station sta ON te.StationId = sta.StationId
        LEFT JOIN tsl_monitorunit mon ON te.MonitorUnitId = mon.MonitorUnitId
        WHERE
        te.EquipmentId = #{equipmentId}
    </select>
    <select id="selectAll" resultType="org.siteweb.config.common.entity.TblEquipment">
        <include refid="findEquipmentsQuery"/>
    </select>
    <select id="selectByEquipmentId" resultType="org.siteweb.config.common.entity.TblEquipment">
        <include refid="findEquipmentsQuery"/>
        WHERE eq.EquipmentId = #{equipmentId}
    </select>
    <select id="selectByMonitorUnitId" resultType="org.siteweb.config.common.entity.TblEquipment">
        <include refid="findEquipmentsQuery"/>
        WHERE eq.MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="findVosByStructureId" resultType="org.siteweb.config.common.vo.EquipmentVO">
        SELECT
        eq.StationId,
        ts.StationName,
        eq.EquipmentId,
        eq.EquipmentName,
        eq.EquipmentNo,
        eq.EquipmentModule,
        eq.EquipmentStyle,
        eq.AssetState,
        eq.Price,
        eq.UsedLimit,
        eq.UsedDate,
        eq.BuyDate,
        eq.Vendor,
        eq.Unit,
        eq.EquipmentCategory,
        eq.EquipmentType,
        eq.EquipmentClass,
        eq.EquipmentState,
        eq.EventExpression,
        eq.StartDelay,
        eq.EndDelay,
        eq.Property,
        eq.Description,
        eq.EquipmentTemplateId,
        eq.HouseId,
        eq.MonitorUnitId,
        mon.MonitorUnitName,
        eq.WorkStationId,
        eq.SamplerUnitId,
        eq.DisplayIndex,
        eq.ConnectState,
        eq.UpdateTime,
        eq.ParentEquipmentId,
        eq.RatedCapacity,
        eq.InstalledModule,
        info.ProjectName,
        info.ContractNo,
        info.InstallTime,
        info.EquipmentSN,
        info.SO,
        eq.ResourceStructureId
        FROM TBL_Equipment eq LEFT JOIN TBL_EquipmentProjectInfo info
        ON eq.EquipmentId = info.EquipmentId
        LEFT JOIN tsl_monitorunit mon ON eq.MonitorUnitId = mon.MonitorUnitId
        LEFT JOIN tbl_station ts ON ts.StationId = eq.StationId
        WHERE eq.ResourceStructureId = #{resourceStructureId}
    </select>

    <select id="findVosByStationId" resultType="org.siteweb.config.common.vo.EquipmentVO">
        SELECT
        eq.StationId,
        eq.EquipmentId,
        eq.EquipmentName,
        eq.EquipmentNo,
        eq.EquipmentModule,
        eq.EquipmentStyle,
        eq.AssetState,
        eq.Price,
        eq.UsedLimit,
        eq.UsedDate,
        eq.BuyDate,
        eq.Vendor,
        eq.Unit,
        eq.EquipmentCategory,
        eq.EquipmentType,
        eq.EquipmentClass,
        eq.EquipmentState,
        eq.EventExpression,
        eq.StartDelay,
        eq.EndDelay,
        eq.Property,
        eq.Description,
        eq.EquipmentTemplateId,
        eq.HouseId,
        eq.MonitorUnitId,
        mon.MonitorUnitName,
        eq.WorkStationId,
        eq.SamplerUnitId,
        eq.DisplayIndex,
        eq.ConnectState,
        eq.UpdateTime,
        eq.ParentEquipmentId,
        eq.RatedCapacity,
        eq.InstalledModule,
        info.ProjectName,
        info.ContractNo,
        info.InstallTime,
        info.EquipmentSN,
        info.SO,
        eq.ResourceStructureId
        FROM TBL_Equipment eq LEFT JOIN TBL_EquipmentProjectInfo info
        ON eq.EquipmentId = info.EquipmentId
        LEFT JOIN tsl_monitorunit mon ON eq.MonitorUnitId = mon.MonitorUnitId
        WHERE eq.StationId = #{stationId}
    </select>

    <select id="findVosByHouseId" resultType="org.siteweb.config.common.vo.EquipmentVO">
        SELECT
        eq.StationId,
        eq.EquipmentId,
        eq.EquipmentName,
        eq.EquipmentNo,
        eq.EquipmentModule,
        eq.EquipmentStyle,
        eq.AssetState,
        eq.Price,
        eq.UsedLimit,
        eq.UsedDate,
        eq.BuyDate,
        eq.Vendor,
        eq.Unit,
        eq.EquipmentCategory,
        eq.EquipmentType,
        eq.EquipmentClass,
        eq.EquipmentState,
        eq.EventExpression,
        eq.StartDelay,
        eq.EndDelay,
        eq.Property,
        eq.Description,
        eq.EquipmentTemplateId,
        eq.HouseId,
        eq.MonitorUnitId,
        mon.MonitorUnitName,
        eq.WorkStationId,
        eq.SamplerUnitId,
        eq.DisplayIndex,
        eq.ConnectState,
        eq.UpdateTime,
        eq.ParentEquipmentId,
        eq.RatedCapacity,
        eq.InstalledModule,
        info.ProjectName,
        info.ContractNo,
        info.InstallTime,
        info.EquipmentSN,
        info.SO,
        eq.ResourceStructureId
        FROM TBL_Equipment eq LEFT JOIN TBL_EquipmentProjectInfo info
        ON eq.EquipmentId = info.EquipmentId
        LEFT JOIN tsl_monitorunit mon ON eq.MonitorUnitId = mon.MonitorUnitId
        WHERE eq.HouseId = #{houseId} and eq.StationId = #{stationId}
    </select>



    <update id="updateEquipmentCategoryByCategoryIdMap" parameterType="java.util.Map">
        UPDATE TBL_Equipment, TBL_CategoryIdMap
        SET TBL_Equipment.EquipmentCategory = TBL_CategoryIdMap.BusinessCategoryId
        WHERE TBL_CategoryIdMap.BusinessId = #{businessId} AND TBL_CategoryIdMap.CategoryTypeId = #{categoryTypeId}
        AND TBL_Equipment.EquipmentCategory = TBL_CategoryIdMap.OriginalCategoryId;
    </update>


    <select id="findStationEquipmentList" resultType="org.siteweb.config.common.vo.StationEquipment">
        SELECT A.StationId,B.EquipmentId,A.StationName,B.EquipmentName,B.EquipmentTemplateId
        FROM TBL_Station A,TBL_Equipment B
        WHERE A.StationId = B.StationId
    </select>
    <select id="findNameByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT EquipmentId as id,EquipmentName as value FROM tbl_equipment WHERE EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="findEquipmentIdsByProperty" resultType="java.lang.Integer">
        select EquipmentId FROM tbl_equipment WHERE Property LIKE concat('%',#{property},'%')
    </select>
    <select id="findStationEquipmentListByEquipmentIds" resultType="org.siteweb.config.common.vo.StationEquipment">
        SELECT b.StationId,b.StationName,a.EquipmentId,a.EquipmentName
        FROM tbl_equipment a inner join tbl_station b on a.StationId = b.StationId
        WHERE a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="findByStationIdAndMonitorUnitId" resultType="org.siteweb.config.common.dto.SimplifyEquipmentDTO">
        SELECT a.EquipmentId, a.EquipmentName, a.EquipmentTemplateid, b.StationName
        FROM tbl_equipment a
                 INNER JOIN tbl_station b ON a.StationId = b.StationId
        WHERE a.StationId = #{stationId}
          AND a.MonitorUnitId = #{monitorUnitId}
    </select>

    <select id="findCountByEquipmentBaseType" resultType="org.siteweb.config.common.dto.EquipmentCountDTO">
        SELECT b.equipmentBaseType, COUNT(*) AS count
        FROM tbl_equipment a
        INNER JOIN tbl_equipmenttemplate b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        GROUP BY b.EquipmentBaseType
    </select>

    <select id="fetchEquipmentWithMissingOrInvalidGuid" resultType="org.siteweb.config.common.entity.TblEquipment">
        SELECT *
        FROM tbl_equipment
        WHERE ExtValue IS NULL
        OR JSON_CONTAINS_PATH(ExtValue, 'one', '$[*].guid') = 0;
    </select>


    <update id="updateBatchById">
        <foreach collection="equipments" item="equipment" separator=";">
            UPDATE tbl_equipment
            SET
            ExtValue = #{equipment.extValue}
            WHERE
            EquipmentId = #{equipment.equipmentId}
        </foreach>
    </update>

    <update id="updateBatchByIdsSyncTemplate">
        UPDATE tbl_equipment
        SET
        EquipmentCategory = #{equipmentCategory},
        Vendor = #{vendor},
        Unit = #{unit},
        EquipmentStyle = #{equipmentStyle}
        WHERE
        EquipmentTemplateId = #{equipmentTemplateId}
    </update>

    <!-- 根据局站ID和局房ID查询设备信息，并关联采集单元和端口信息 -->
    <select id="findByHouseIdAndStationIdWithPortInfo" resultType="org.siteweb.config.common.entity.TblEquipment">
        SELECT te.*, ts.PortId, tp.PortName
        FROM tbl_equipment te
        LEFT JOIN tsl_samplerunit ts ON te.SamplerUnitId = ts.SamplerUnitId
        LEFT JOIN tsl_port tp ON tp.PortId = ts.PortId
        <where>
            <!-- stationId 不能为空 -->
            te.StationId = #{stationId}
            <!-- 如果houseId不为空，则添加houseId条件 -->
            <if test="houseId != null">
                AND te.HouseId = #{houseId}
            </if>
        </where>
    </select>

</mapper>