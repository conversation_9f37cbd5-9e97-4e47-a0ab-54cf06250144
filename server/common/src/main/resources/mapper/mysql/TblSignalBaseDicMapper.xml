<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblSignalBaseDicMapper">
    <insert id="generateSignalBaseDic">
        INSERT INTO TBL_SignalBaseDic
        (BaseTypeId,
         BaseTypeName,
         BaseEquipmentId,
         EnglishName,
         BaseLogicCategoryId,
         StoreInterval,
         AbsValueThreshold,
         PercentThreshold,
         StoreInterval2,
         AbsValueThreshold2,
         PercentThreshold2,
         ExtendField1,
         ExtendField2,
         ExtendField3,
         UnitId,
         BaseStatusId,
         BaseHysteresis,
         BaseFreqPeriod,
         BaseFreqCount,
         BaseShowPrecision,
         BaseStatPeriod,
         CGElement,
         Description,
         BaseNameExt,
         IsSystem)
        SELECT #{baseTypeId},
               signalBaseDic.BaseTypeName,
               signalBaseDic.BaseEquipmentId,
               signalBaseDic.EnglishName,
               signalBaseDic.BaseLogicCategoryId,
               signalBaseDic.StoreInterval,
               signalBaseDic.AbsValueThreshold,
               signalBaseDic.PercentThreshold,
               signalBaseDic.StoreInterval2,
               signalBaseDic.AbsValueThreshold2,
               signalBaseDic.PercentThreshold2,
               signalBaseDic.ExtendField1,
               signalBaseDic.ExtendField2,
               signalBaseDic.ExtendField3,
               signalBaseDic.UnitId,
               signalBaseDic.BaseStatusId,
               signalBaseDic.BaseHysteresis,
               signalBaseDic.BaseFreqPeriod,
               signalBaseDic.BaseFreqCount,
               signalBaseDic.BaseShowPrecision,
               signalBaseDic.BaseStatPeriod,
               signalBaseDic.CGElement,
               signalBaseDic.Description,
               signalBaseDic.BaseNameExt,
               0
        FROM TBL_SignalBaseDic signalBaseDic
        WHERE signalBaseDic.BaseTypeId = #{sourceId}
    </insert>
    <select id="findSignalBaseDic" resultType="org.siteweb.config.common.entity.TblSignalBaseDic">
        SELECT BaseTypeId,BaseTypeName,BaseNameExt FROM TBL_SignalBaseDic
        WHERE BaseEquipmentId DIV 100=#{eqTypeId} AND BaseEquipmentId mod 100=#{eqSubTypeId}
        <if test="baseTypeId != null and baseTypeId != ''">
            AND BaseTypeId LIKE CONCAT('%', #{baseTypeId},'%')
        </if>
        <if test="baseTypeName != null and baseTypeName != ''">
            AND BaseTypeName LIKE CONCAT('%', #{baseTypeName},'%')
        </if>
        <if test="baseNameExt != null and baseNameExt != ''">
            AND BaseNameExt LIKE CONCAT('%', #{baseNameExt},'%')
        </if>
        order by BaseTypeId ASC
    </select>
    <sql id="findSignalBaseDicSql">
        SELECT
        tsb.BaseTypeId,
        tsb.BaseTypeName,
        tsb.BaseEquipmentId,
        tsb.EnglishName,
        tsb.BaseLogicCategoryId,
        tlb.BaseLogicCategoryName,
        tsb.StoreInterval,
        tsb.AbsValueThreshold,
        tsb.PercentThreshold,
        tsb.StoreInterval2,
        tsb.AbsValueThreshold2,
        tsb.PercentThreshold2,
        tsb.ExtendField1,
        tsb.ExtendField2,
        tsb.ExtendField3,
        tsb.UnitId,
        tbu.BaseUnitSymbol ,
        tsb.BaseStatusId,
        tsb.BaseHysteresis,
        tsb.BaseFreqPeriod,
        tsb.BaseFreqCount,
        tsb.BaseShowPrecision,
        tsb.BaseStatPeriod,
        tsb.CGElement,
        tsb.BaseNameExt,
        tsb.Description,
        tsb.IsSystem,
        tbc.BaseClassName,
        teb.BaseEquipmentName
        FROM
        tbl_signalbasedic tsb
        LEFT JOIN tbl_equipmentbasetype teb ON tsb.BaseEquipmentId = teb.BaseEquipmentId
        LEFT JOIN tbl_baseclassdic tbc ON teb.EquipmentTypeId = tbc.BaseClassId
        LEFT JOIN tbl_logiccategorybasedic tlb ON tsb.BaseLogicCategoryId = tlb.BaseLogicCategoryId
        LEFT JOIN TBL_BaseUnitDic tbu ON tsb.UnitId = tbu.BaseUnitId
    </sql>
    <select id="findSignalBaseDicList" resultType="org.siteweb.config.common.dto.SignalBaseDicsDTO">
        <include refid="findSignalBaseDicSql"/>
    </select>
    <select id="findSignalBaseDicByBaseTypeId" resultType="org.siteweb.config.common.dto.SignalBaseDicsDTO">
        <include refid="findSignalBaseDicSql"/>
        WHERE tsb.BaseTypeId = #{baseTypeId}
    </select>
    <select id="findSignalBaseStatusList" resultType="org.siteweb.config.common.dto.SignalBaseStatusDTO">
        SELECT
        x.BaseTypeId,
        y.BaseCondId,
        y.Meaning AS baseMeaning
        FROM
        TBL_SignalBaseDic x
        RIGHT OUTER JOIN TBL_StatusBaseDic y ON
        x.BaseStatusId = y.BaseStatusId
    </select>
</mapper>