<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TslSamplerUnitMapper">
    <select id="findReferenceSamplerNameBySamplerIdList" resultType="java.lang.String">
        SELECT b.SamplerName FROM tsl_samplerunit a inner JOIN tsl_sampler b on a.SamplerId = b.SamplerId
        WHERE b.SamplerId IN
        <foreach collection="samplerIdList" item="samplerId" open="(" close=")" separator=",">
            #{samplerId}
        </foreach>
    </select>
    <select id="selectSamplerUnitWithPort" resultType="org.siteweb.config.common.entity.TslSamplerUnit">
        SELECT a.* FROM TSL_SamplerUnit a, TSL_Port b WHERE
        a.MonitorUnitId = #{monitorUnitId} and  a.PortId = b.PortId  and a. MonitorUnitId = b. MonitorUnitId
        ORDER BY b.PortNo, a.Address
    </select>
    <select id="findSamplerUnit" resultType="org.siteweb.config.common.entity.TslSamplerUnit">
        SELECT samplerUnit.SamplerUnitId, samplerUnit.SamplerUnitName
        FROM tsl_monitorunit monitorUnit
        INNER JOIN tsl_samplerunit samplerUnit ON monitorUnit.MonitorUnitId = samplerUnit.MonitorUnitId
        INNER JOIN tsl_port port ON port.PortId = samplerUnit.PortId
        WHERE monitorUnit.MonitorUnitId = #{monitorUnitId}
        AND samplerUnit.samplerUnitName = #{samplerUnitName}
        AND port.PortName = #{portName}
    </select>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT samplerUnitId as id,SamplerUnitName as value FROM tsl_samplerunit WHERE SamplerUnitId IN
        <foreach collection="samplerUnitIds" item="samplerUnitId" open="(" close=")" separator=",">
            #{samplerUnitId}
        </foreach>
    </select>

</mapper>