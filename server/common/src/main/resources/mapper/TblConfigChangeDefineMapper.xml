<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblConfigChangeDefineMapper">


    <select id="selectListIgnoreCase" resultType="org.siteweb.config.common.entity.TblConfigChangeDefine">
        SELECT * FROM tbl_configchangedefine
        WHERE LOWER(TableName) = LOWER(#{tableName})
    </select>

</mapper>