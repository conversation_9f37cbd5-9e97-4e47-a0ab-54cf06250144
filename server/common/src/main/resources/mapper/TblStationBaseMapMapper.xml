<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStationBaseMapMapper">

    <select id="getStationBaseMapExport" resultType="org.siteweb.config.common.dto.StationBaseMapExportDTO">
        SELECT map.StandardType, st.StandardName, map.StationBaseType, sbt.Type, map.StationCategory, di.ItemValue
        FROM TBL_StationBaseMap map
        INNER JOIN TBL_StationBaseType sbt ON sbt.Id = map.StationBaseType AND sbt.StandardId = #{standardId}
        INNER JOIN TBL_DataItem di ON map.StationCategory = di.ItemId AND di.EntryId = 71
        INNER JOIN TBL_StandardType st ON st.StandardId = map.StandardType
        WHERE map.StandardType = #{standardId}
        ORDER BY map.StationBaseType ASC, map.StationCategory ASC
    </select>

</mapper>