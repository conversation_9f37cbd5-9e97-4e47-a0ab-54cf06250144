<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStandardDicControlMapper">
    <resultMap id="StandardDicControlIniMap" type="org.siteweb.config.common.dto.StandardDicControlIniDTO">
        <result property="standardDicId" column="StandardDicId"/>
        <result property="stationCategory" column="StationCategory"/>
        <result property="controlLogicClass" column="ControlLogicClass"/>
        <result property="controlStandardName" column="ControlStandardName"/>
        <result property="netManageId" column="NetManageId"/>
        <collection property="baseTypeList" ofType="org.siteweb.config.common.dto.BaseTypeInnerDTO" column="NetManageId" notNullColumn="NetManageId">
            <result property="baseTypeId" column="BaseTypeId"/>
            <result property="baseTypeName" column="BaseTypeName"/>
        </collection>
    </resultMap>
    <update id="updateEntity">
        UPDATE tbl_standarddiccontrol
        SET EquipmentLogicClassId = #{standardDicControl.equipmentLogicClassId},
            EquipmentLogicClass   = #{standardDicControl.equipmentLogicClass},
            ControlLogicClassId   = #{standardDicControl.controlLogicClassId},
            ControlLogicClass     = #{standardDicControl.controlLogicClass},
            ControlStandardName   = #{standardDicControl.controlStandardName},
            NetManageId           = #{standardDicControl.netManageId},
            StationCategory       = #{standardDicControl.stationCategory},
            ModifyType            = #{standardDicControl.modifyType},
            Description           = #{standardDicControl.description},
            ExtendFiled1          = #{standardDicControl.extendFiled1},
            ExtendFiled2          = #{standardDicControl.extendFiled2}
        WHERE StandardDicId = #{standardDicControl.standardDicId}
          AND StandardType = #{standardDicControl.standardType}
    </update>
    <select id="findNamesByIds" resultType="org.siteweb.config.common.dto.IdValueDTO">
        SELECT StandardDicId as id,ControlStandardName as value FROM tbl_standarddiccontrol WHERE StandardDicId IN
        <foreach collection="standardDicIds" item="standardDicId" open="(" close=")" separator=",">
            #{standardDicId}
        </foreach>
    </select>
    <select id="getStandardDicControlInis" resultMap="StandardDicControlIniMap">
        SELECT
        con.StandardDicId,
        con.StationCategory,
        con.ControlLogicClass,
        con.ControlStandardName,
        con.NetManageId,
        base.BaseTypeId,
        base.BaseTypeName
        FROM
        TBL_StandardDicControl con
        LEFT JOIN(
        SELECT
        a.StandardDicId,
        a.StationBaseType,
        a.StandardType,
        a.BaseTypeId,
        b.BaseTypeName
        FROM
        TBL_CommandBaseMap a
        LEFT JOIN TBL_CommandBaseDic B ON
        a.BaseTypeId = B.BaseTypeId
        ) base
        ON
        base.StandardDicId = con.StandardDicId
        AND base.StationBaseType = con.StationCategory
        AND base.StandardType = con.StandardType
        WHERE
        con.StandardType = #{standardId}
    </select>
</mapper>