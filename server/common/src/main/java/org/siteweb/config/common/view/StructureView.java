package org.siteweb.config.common.view;

import org.siteweb.config.common.dto.StructureTreeNodeDTO;
import org.siteweb.config.common.entity.ResourceStructure;

/**
 * 层级结构Jackson序列化控制视图
 * 视图无法在外面组合，只能在此类中用interface进行组合
 *
 * <AUTHOR> (2024-03-07)
 * @see ResourceStructure
 * @see StructureTreeNodeDTO
 **/
public class StructureView {


    /**
     * 设备视图，表示序列化时包含层级对象的设备信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface Equipment {
    }

    /**
     * 数结构视图，表示序列化时包含层级对象的Children信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface Tree {
    }


    /**
     * 简单的视图，表示序列化时仅包含一些简单的概要信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface Simple extends RestfulView.Default {
    }


    /**
     * 简单的树视图，表示序列化时仅包含一些简单的概要树信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface SimpleTree extends RestfulView.Default, Tree {
    }


    /**
     * 完整的树视图，表示序列化时包含所有信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface WholeTree extends RestfulView.Default, Tree {
    }


    /**
     * 简单的设备树视图，表示序列化时包含一些简单的设备信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface AndEquipmentSimple extends Simple, Equipment, EquipmentView.Simple {
    }

    /**
     * 简单的设备树视图，表示序列化时仅包含一些简单的概要信息及设备信息
     *
     * <AUTHOR> (2024/3/7)
     */
    public interface AndEquipmentSimpleTree extends Simple, Equipment, EquipmentView.Simple, Tree {
    }


}
