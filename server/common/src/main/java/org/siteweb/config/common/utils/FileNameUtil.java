package org.siteweb.config.common.utils;

import cn.hutool.core.io.FileUtil;
import lombok.experimental.UtilityClass;

@UtilityClass
public class FileNameUtil {

    /**
     * 将文件名的后缀改为小写
     *
     * @param fileName 原始文件名
     * @return 后缀改为小写的文件名
     */
    public String toLowerCaseExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        String extension = FileUtil.extName(fileName);
        if (extension.isEmpty()) {
            return fileName;
        }

        String nameWithoutExtension = FileUtil.mainName(fileName);
        return nameWithoutExtension + "." + extension.toLowerCase();
    }
}
