package org.siteweb.config.common.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.siteweb.config.common.annotation.ChangeSource;
import org.siteweb.config.common.change.ChangeMessage;
import org.siteweb.config.common.change.ChangeOperatorEnum;
import org.siteweb.config.common.change.ChangeQueueItem;
import org.siteweb.config.common.change.LocalCacheEvent;
import org.siteweb.config.common.service.ChangeEventService;
import org.siteweb.config.common.utils.AnnotationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR> (2024-01-08)
 **/
@Service()
@Configuration
@Slf4j
public class ChangeEventServiceImpl extends Thread implements ChangeEventService {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    private final Map<Class<?>, Field> primaryKeyMap = new ConcurrentHashMap<>();
    private final Map<Class<?>, ChangeSource> changeSourceCache = new ConcurrentHashMap<>();
    private final LinkedBlockingQueue<ChangeQueueItem> messageQueue = new LinkedBlockingQueue<>(100000);

    @Autowired
    private MqttAsyncClient mqttClient;


    @Override
    public <T> void sendCreate(T object) {
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (isTransactionActive) {
            // 在事务中执行的逻辑
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    emit(ChangeOperatorEnum.CREATE.getOperator(), object);
                }
            });
        } else {
            // 没有事务时的逻辑
            emit(ChangeOperatorEnum.CREATE.getOperator(), object);
        }
    }

    @Override
    public <T> void sendDelete(T object) {
        emit(ChangeOperatorEnum.DELETE.getOperator(), object);
    }

    @Override
    public <T> void sendUpdate(T object) {
        emit(ChangeOperatorEnum.UPDATE.getOperator(), object);
    }

    @Override
    public <T> void sendBatchUpdate(List<T> list) {
        list.parallelStream().forEach(object -> emit(ChangeOperatorEnum.UPDATE.getOperator(), object));
    }

    public <T> void emit(String operator, T object) {
        if (object == null) {
            log.warn("输入的对象不能为null");
            return;
        }
        Class<?> _class = object.getClass();
        ChangeSource changeSource = getChangeSourceAnnotation(_class);
        if (changeSource == null) {
            log.warn("对象 {} 未实现@ChangeSource的注解", object);
            return;
        }
        Object pk = this.getPrimaryKeyValue(object, _class);
        if (pk == null) {
            log.warn("对象 {} 内属性未实现@TableId的注解", object);
            return;
        }
        String topic = "gateway/" + changeSource.channel() + "/" + changeSource.product() + "/" + changeSource.source() + "/" + operator + "/" + pk;
        try {
            ChangeMessage message = new ChangeMessage(object);
            String jsonMessage = objectMapper.writeValueAsString(message);
            MqttMessage msg = new MqttMessage();
            msg.setQos(2);
            msg.setRetained(false);
            msg.setPayload(jsonMessage.getBytes());
            messageQueue.put(new ChangeQueueItem(topic, msg)); // 将消息放入队列
            log.debug("LocalCacheEvent =>: {}", "发布缓存更新事件");
            eventPublisher.publishEvent(new LocalCacheEvent(this, topic, msg));
        } catch (JsonProcessingException | InterruptedException e) {
            throw new RuntimeException(e);
        }
        if (!isAlive()) {
            start();
        }
    }


    public synchronized void run() {
        ChangeQueueItem item = null;
        while (true) {
            try {
                if (item == null) item = messageQueue.take();
                IMqttToken token = mqttClient.publish(item.getTopic(), item.getMessage());
                token.waitForCompletion();
                item = null;
            } catch (MqttException e) {
                if (e.getReasonCode() == 32001) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ex) {
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }


    private ChangeSource getChangeSourceAnnotation(Class<?> _class) {
        ChangeSource changeSource = changeSourceCache.get(_class);
        if (changeSource == null) {
            changeSource = AnnotationUtils.getAnnotationInHierarchy(_class, ChangeSource.class);
            if (changeSource != null) {
                changeSourceCache.put(_class, changeSource);
            }
        }
        return changeSource;
    }


    private Object getPrimaryKeyValue(Object object, Class<?> _class) {
        Field pkField = primaryKeyMap.get(_class);
        if (Objects.nonNull(pkField)) {
            return getFieldValueByObject(object, pkField);
        }
        Field field = getTableIdField(_class);
        if (field != null) {
            primaryKeyMap.put(_class, field);
            return getFieldValueByObject(object, field);
        }
        return null;
    }

    /**
     * 递归查询父类中的注解
     *
     * @param _class
     * <AUTHOR> (2024/4/9)
     */
    private Field getTableIdField(Class<?> _class) {
        for (var field : _class.getDeclaredFields()) {
            if (field.isAnnotationPresent(TableId.class)) {
                return field;
            }
        }
        Class<?> superClass = _class.getSuperclass();
        if (superClass != null) {
            return getTableIdField(superClass);
        }
        return null;
    }


    private Object getFieldValueByObject(Object object, Field field) {
        field.setAccessible(true);
        try {
            return field.get(object);
        } catch (IllegalAccessException e) {
            log.error("获取对象值失败,{}", ExceptionUtil.stacktraceToString(e));
        }
        return null;
    }
}
