package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024-03-01)
 **/
@Data
@ConfigId(23)
@TableName(value = "TBL_DataEntry", autoResultMap = true)
@NoArgsConstructor
public class TblDataEntry {

    @TableId(value = "EntryId", type = IdType.AUTO)
    private Integer entryId;

    private Integer entryCategory;

    private String entryName;

    private String entryTitle;

    private String entryAlias;

    private Boolean enable;

    private String description;

}
