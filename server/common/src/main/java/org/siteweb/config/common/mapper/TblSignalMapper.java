package org.siteweb.config.common.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EquipmentSignalDTO;
import org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO;
import org.siteweb.config.common.dto.excel.SignalExcel;
import org.siteweb.config.common.entity.TblSignal;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (2024-03-09)
 */

@Mapper
public interface TblSignalMapper extends BatchBaseMapper<TblSignal> {

    List<TblSignal> findByEquipmentTemplateId(int equipmentTemplateId);

    Integer findMaxSignalIdByEquipmentTemplateId(Integer equipmentTemplateId);

    Integer findMaxDisplayIndexByEquipmentTemplateId(Integer equipmentTemplateId);

    List<Long> findBaseTypeIdsNotInSignalBaseDicForEquipmentTemplate(Integer equipmentTemplateId);

    /**
     * 删除信号
     * 会删除信号、信号含义、属性、跨站表达式
     * @param equipmentTemplateId 设备模板id
     * @param signalId            信号id
     * @return int
     */
    int deleteSignal(int equipmentTemplateId, int signalId);

    void updateWorkStationSignalName(@Param("prefix") String prefix, @Param("equipmentTemplateId") int equipmentTemplateId);

    List<SignalConfigItem> findAllSignalItem();

    List<SignalConfigItem> findSignalItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
    SignalConfigItem findByEquipmentTemplateIdAndSignalId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("signalId") Integer signalId);

    void updateSelfDiagnosisSignal(@Param("equipmentTemplateId") int equipmentTemplateId, @Param("centerId") int centerId);

    void batchInsertLianTongSignal();

    List<TblSignal> diffSignal(@Param("originTemplateId") Integer originTemplateId, @Param("destTemplateId") Integer destTemplateId);

    List<TblSignal> findSameVirtualSignals(@Param("oldEquipmentTemplateId") Integer oldEquipmentTemplateId, @Param("newEquipmentTemplateId") Integer newEquipmentTemplateId);

    SignalConfigItem findMaxSignalByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<EquipmentSignalDTO> findSignalsByEquipmentIdAndSignalIds(@Param("equipmentId") Integer equipmentId, @Param("signalIds") List<Integer> signalIds);

    List<SimpleEventSignalDTO> findSignalEvent(@Param("equipmentId") Integer equipmentId);

    List<IdValueDTO<String, String>> findNamesByIds(@Param("signalUniqueIds") List<Map<String, String>> signalUniqueIds);

    List<SignalProgressDTO> findSignalProgressList();

    /**
     * 获取基类信号
     */
    List<SignalBaseClassDetailDTO> findSignalBaseClass(@Param("equipmentBaseType") Integer equipmentBaseType);

    /**
     * 获取基类信号明细
     */
    List<SignalBaseClassDetailDTO> findSignalBaseClassDetails(@Param("equipmentBaseType") Integer equipmentBaseType, @Param("signalName") String signalName, @Param("meanings") String meanings);

    /**
     * 获取应用标准化数据
     */
    List<SignalApplyStandardDTO> getApplyStandards(@Param("standardId") Integer standardId, @Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    /**
     * 还原标准化
     */
    long restoreStandard();

    /**
     * 获取标准化信号比较数据
     */
    List<StandardSignalCompareDTO> getStandardCompareData();
    /**
     * 获取信号应用标准化检查
     */
    List<StandardApplySignalCheckDTO> getSignalStandardApplyCheckData(@Param("standardId") Integer standardId);

    /**
     * 获取信号映射标准化检查
     */
    List<StandardMappingSignalCheckDTO> getSignalStandardMappingCheck(@Param("standardId") Integer standardId, @Param("equipmentCategory") Integer equipmentCategory);

    /**
     * 根据设备类型获取信号
     */
    List<TblSignal> findByEquipmentCategory(Integer equipmentCategory);

    int batchUpdateField(@Param("signalList") List<TblSignal> signalList);

    List<SignalExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
}
