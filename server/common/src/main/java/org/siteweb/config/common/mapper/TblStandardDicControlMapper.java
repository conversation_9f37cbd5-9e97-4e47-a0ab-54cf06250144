package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.dto.StandardDicControlIniDTO;
import org.siteweb.config.common.entity.TblStandardDicControl;

import java.util.List;

@Mapper
public interface TblStandardDicControlMapper extends BatchBaseMapper<TblStandardDicControl> {
    void updateEntity(@Param("standardDicControl") TblStandardDicControl standardDicControl);

    List<IdValueDTO<String, String>> findNamesByIds(List<Integer> standardDicIds);

    List<StandardDicControlIniDTO> getStandardDicControlInis(@Param("standardId") Integer standardId);
}
