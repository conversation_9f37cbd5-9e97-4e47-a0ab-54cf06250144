package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.siteweb.config.common.entity.ResourceStructure;

@Mapper
public interface SyncSiteWeb2Mapper extends BaseMapper<ResourceStructure> {

    Integer getIsScValue();

    void updateResourceStructureForSc();

    void revertResourceStructureForSc();

    void updateInitialStructureType();

    void insertCenterStructure();

    void updateCenterStructureName();

    void deleteCenterStructure();

    void updateCenterStructurePath();

    void insertSubStructure();

    void updateSubStructureName();

    void deleteSubStructure();

    void updateSubStructureParentPath();

    void updateSubStructureSecondLevel();

    void insertStationStructure();

    void updateStationStructure();

    void deleteStationStructure();

    void updateStationStructurePath();

    void insertHouseStructure();

    void updateHouseStructure();

    void deleteHouseStructure();

    void updateHouseStructurePath();

    void updateEquipmentResourceStructure();

    void insertConfigChangeLog();
}
