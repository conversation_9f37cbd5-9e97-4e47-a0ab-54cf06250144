package org.siteweb.config.common.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.EquipmentCountDTO;
import org.siteweb.config.common.dto.EquipmentDetailDTO;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.dto.SimplifyEquipmentDTO;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.vo.EquipmentReferenceVO;
import org.siteweb.config.common.vo.EquipmentVO;
import org.siteweb.config.common.vo.StationEquipment;

import java.util.List;

/**
 * <AUTHOR> (2024/3/9)
 */
@Mapper
public interface TblEquipmentMapper extends BatchBaseMapper<TblEquipment> {
    List<EquipmentReferenceVO> findReferenceVoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    void updateEquipmentCategoryByCategoryIdMap(int businessId, int categoryTypeId);

    /***
     * 获取设备信息详情
     *
     * @param equipmentId 设备ID
     * @return {@link EquipmentDetailDTO}
     * <AUTHOR> (2024/4/11)
     */
    EquipmentDetailDTO findEquipmentDetail(Integer equipmentId);


    List<TblEquipment> selectAll();

    TblEquipment selectByEquipmentId(Integer equipmentId);

    List<TblEquipment> selectByMonitorUnitId(Integer monitorUnitId);

    List<EquipmentVO> findVosByStructureId(Integer resourceStructureId);

    List<EquipmentVO> findVosByStationId(Integer stationId);

    List<EquipmentVO> findVosByHouseId(Integer houseId, Integer stationId);

    List<StationEquipment> findStationEquipmentList();


    List<IdValueDTO<String,String>> findNameByIds(@Param("equipmentIds") List<Integer> equipmentIds);

    /**
     * 获取所有的虚拟设备id
     * @return {@link List }<{@link Integer }>
     */
    List<Integer> findEquipmentIdsByProperty(String property);

    List<StationEquipment> findStationEquipmentListByEquipmentIds(@Param("equipmentIds") List<Integer> equipmentIds);

    List<SimplifyEquipmentDTO> findByStationIdAndMonitorUnitId(@Param("stationId") Integer stationId, @Param("monitorUnitId") Integer monitorUnitId);

   List<EquipmentCountDTO> findCountByEquipmentBaseType();

    List<TblEquipment> fetchEquipmentWithMissingOrInvalidGuid();

    int updateBatchById(List<TblEquipment> equipments);

    //同步模板更新的批量update设备的厂商、型号、单位、类型
    int updateBatchByIdsSyncTemplate(@Param("equipmentCategory")Integer equipmentCategory, @Param("vendor") String vendor, @Param("unit")String unit, @Param("equipmentStyle")String equipmentStyle, @Param("equipmentTemplateId")Integer equipmentTemplateId);

    /**
     * 根据局站ID和局房ID查询设备信息，并关联采集单元和端口信息
     *
     * @param stationId 局站ID
     * @param houseId 局房ID
     * @return 设备列表，包含采集单元和端口信息
     */
    List<TblEquipment> findByHouseIdAndStationIdWithPortInfo(@Param("stationId") Integer stationId, @Param("houseId") Integer houseId);

}

