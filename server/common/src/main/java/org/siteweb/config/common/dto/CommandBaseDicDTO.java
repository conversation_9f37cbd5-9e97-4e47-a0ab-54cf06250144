package org.siteweb.config.common.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldNameConstants
@ExcelIgnoreUnannotated
public class CommandBaseDicDTO {
    /**
     * 基类id
     */
    @ExcelProperty(value = "控制基础类别ID")
    private Long baseTypeId;
    /**
     * 基类名
     */
    @ExcelProperty(value = "控制基础类别名称")
    private String baseTypeName;
    /**
     * 设备基类id
     */
    private Integer baseEquipmentId;
    /**
     * 基础类别名称(大类名称)
     */
    @ExcelProperty(value = "基础设备大类")
    private String baseClassName;
    /**
     * 基础设备子类名称(设备类型)
     */
    @ExcelProperty(value = "基础设备子类")
    private String baseEquipmentName;
    /**
     * 英文名
     */
    private String englishName;
    private Integer baseLogicCategoryId;
    /**
     * 控制逻辑分类名称
     */
    @ExcelProperty(value = "控制逻辑分类")
    private String baseLogicCategoryName;
    /**
     * 控制类型
     */
    private Integer commandType;
    /**
     * 控制类型名称
     */
    @ExcelProperty(value = "控制类型")
    private String commandTypeName;
    /**
     * 基类状态
     */
    private Integer baseStatusId;
    /**
     * 状态名称
     */
    private String baseStatusName;
    /**
     * 扩展字段3
     */
    private String  extendField1;
    /**
     * 扩展字段2
     */
    private String extendField2;
    /**
     * 扩展字段3
     */
    private String extendField3;
    /**
     * 基类扩展名
     */
    @ExcelProperty(value = "名称扩展表达式")
    private String baseNameExt;
    /**
     * 是否为系统
     */
    @ExcelProperty(value = "是否系统内置")
    private Boolean isSystem;
    /**
     * 描述
     */
    @ExcelProperty(value = "备注")
    private String description;

}
