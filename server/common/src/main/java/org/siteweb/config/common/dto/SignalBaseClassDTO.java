package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基类信号
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignalBaseClassDTO {

    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;
    /**
     * 信号名
     */
    private String signalName;
    /**
     * 含义
     */
    private String meanings;
    /**
     * 数量
     */
    private Integer signalNumber;


    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 信号种类 2 开关量 1模拟量
     */
    private Integer signalCategory;
    /**
     * 信号基础类别id
     */
    private Long baseTypeId;
    /**
     * 基类信号名
     */
    private String baseTypeName;
    /**
     * 基类扩展表达式
     */
    private String baseNameExt;
    /**
     * 基类含义id
     */
    private Integer baseCondId;
    /**
     * 基类含义
     */
    private String baseMeaning;
    private Integer stateValue;
    /**
     * 基类情况 已检查
     */
    private String subState;
    /**
     * 子项情况
     */
    private String childState;

}
