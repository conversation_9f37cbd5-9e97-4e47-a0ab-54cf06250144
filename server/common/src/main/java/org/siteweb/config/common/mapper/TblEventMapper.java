package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.batchtool.EquipmentEventDTO;
import org.siteweb.config.common.dto.batchtool.EventRequestBySignalId;
import org.siteweb.config.common.dto.batchtool.SimpleEventSignalDTO;
import org.siteweb.config.common.dto.excel.EventExcel;
import org.siteweb.config.common.entity.TblEvent;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (2024/3/9)
 */
@Mapper
public interface TblEventMapper extends BatchBaseMapper<TblEvent> {

    List<TblEvent> findByEquipmentTemplateId(int equipmentTemplateId);

    Integer findMaxEventIdByEquipmentTemplateId(Integer equipmentTemplateId);

    List<Long> findBaseTypeIdsNotInEventBaseDicForEquipmentTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<EventExpressionDTO> findEventExpression(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    int deleteEvent(int equipmentTemplateId, int eventId);

    void updateWorkStationEventName(@Param("prefix") String prefix, @Param("equipmentTemplateId") int equipmentTemplateId);


    void updateEventIdAndStartExpressionAndSignalId(@Param("centerId") Integer centerId, @Param("equipmentTemplateId") int equipmentTemplateId);

    List<EventConfigItem> findEventItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
    List<EventConfigItem> findEventItemByEquipmentTemplateIds(@Param("equipmentTemplateIds") Collection<Integer> equipmentTemplateIds);
    List<EventConfigItem> findAllEventItem();
    EventConfigItem findByEquipmentTemplateIdAndEventId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);
    void batchInsertLianTongEvents();

    List<TblEvent> diffEvent(@Param("oldEquipmentTemplateId") Integer oldEquipmentTemplateId, @Param("newEquipmentTemplateId") Integer newEquipmentTemplateId);

    void batchUpdate(@Param("eventList") List<TblEvent> eventList);

    EventConfigItem findMaxEventByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<EquipmentEventDTO> findEventsByEquipmentIdAndEventIds(@Param("equipmentId") Integer equipmentId, @Param("eventIds") List<Integer> eventIds);

    List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(@Param("eventRequestBySignalId") EventRequestBySignalId eventRequestBySignalId);

    /**
     * 获取基类事件
     */
    List<EventBaseClassDetailDTO> findEventBaseClassList(@Param("equipmentBaseType") Integer equipmentBaseType);
    /**
     * 获取基类事件详情
     */
    List<EventBaseClassDetailDTO> findEventBaseClassDetails(@Param("equipmentBaseType") Integer equipmentBaseType, @Param("eventName") String eventName, @Param("meanings") String meanings);

    List<IdValueDTO<String, String>> findNamesByIds(@Param("eventUniqueIds") List<Map<String, String>> eventUniqueIds);

    /**
     * 获取基类事件进度列表
     */
    List<EventProgressDTO> findEventProgressList();

    /**
     * 获取应用标准化
     */
    List<EventApplyStandardDTO> getApplyStandards(@Param("standardId") Integer standardId, @Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    /**
     * 还原标准化
     */
    long restoreStandard();

    /**
     * 获取标准化告警比较
     */
    List<StandardEventCompareDTO> getStandardCompareData();

    /**
     * 获取告警应用标准化检查
     */
    List<StandardApplyEventCheckDTO> getEventStandardApplyCheckData(@Param("standardId") Integer standardId);

    /**
     * 获取告警映射标准化检查
     */
    List<StandardMappingEventCheckDTO> getEventStandardMappingCheck(@Param("standardId") Integer standardId, @Param("equipmentCategory") Integer equipmentCategory);

    Integer findMaxDisplayIndexByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<TblEvent> findByEquipmentCategory(Integer equipmentCategory);

    int batchUpdateField(@Param("eventList") List<TblEvent> eventList);

    List<EventExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
}
