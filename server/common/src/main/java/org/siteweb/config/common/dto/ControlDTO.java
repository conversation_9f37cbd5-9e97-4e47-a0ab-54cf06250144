package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/10
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ControlDTO {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    private String controlName;
    private Integer controlCategory;
    private String cmdToken;
    private Double baseTypeId;
    private Integer controlSeverity;
    private Integer signalId;
    private Double timeOut;
    private Integer retry;
    private String description;
    private Boolean enable;
    private Boolean visible;
    private Integer displayIndex;
    private Integer commandType;
    private Short controlType;
    private Short dataType;
    private Double maxValue;
    private Double minValue;
    private Double defaultValue;
    private Integer moduleNo;
    private List<ControlMeaningsDTO> controlMeanings;
}
