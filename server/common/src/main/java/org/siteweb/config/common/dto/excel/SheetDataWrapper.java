package org.siteweb.config.common.dto.excel;

import lombok.Getter;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Collection;
import java.util.Collections;
import java.util.Objects;


/**
 * 定义一个简单的包装类来同时持有数据和对应的 Class
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@Getter
public class SheetDataWrapper {
    private final Class<?> headClass;
    private final Collection<?> data;

    public SheetDataWrapper(Class<?> headClass, Collection<?> data) {
        Objects.requireNonNull(headClass, "Header class cannot be null");
        this.headClass = headClass;
        // 如果 data 为 null，视作空集合处理
        this.data = (data == null) ? Collections.emptyList() : data;
    }

    public static SheetDataWrapper of(Class<?> headClass, Collection<?> data){
        return new SheetDataWrapper(headClass, data);
    }
}
