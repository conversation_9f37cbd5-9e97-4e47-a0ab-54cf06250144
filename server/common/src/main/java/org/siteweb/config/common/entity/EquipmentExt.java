package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 设备扩展表
 *
 * <AUTHOR> (2024-05-17 14:16)
 */
@TableName("tbl_equipmentExt")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentExt {
    public EquipmentExt(Integer equipmentId, Integer isReset, Integer isUpload, Integer driveTemplateId) {
        this.equipmentId = equipmentId;
        this.isReset = isReset;
        this.isUpload = isUpload;
        this.driveTemplateId = driveTemplateId;
    }

    /**
     * 设备id(扩展表主键)
     */
    @TableId
    private Integer equipmentId;
    /**
     * 引用模板id
     */
    private Integer driveTemplateId;
    /**
     * 是否重新生成 0否 1是
     */
    private Integer isReset;

    /**
     * 是否已经上传 0否 1是
     */
    private Integer isUpload;

    /**
     * 文件id
     */
    private Integer fileId;

    /**
     * 设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(用于判断客户端工具是否修改了)
     */
    private Integer fieldHash;
}
