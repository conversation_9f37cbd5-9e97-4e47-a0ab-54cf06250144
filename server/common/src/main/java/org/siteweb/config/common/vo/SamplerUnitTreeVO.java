package org.siteweb.config.common.vo;

import lombok.Data;
import org.siteweb.config.common.entity.TslSamplerUnit;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2024-04-12)
 **/
@Data
public class SamplerUnitTreeVO {
    private Integer samplerUnitId;
    private String samplerUnitName;
    private Integer samplerId;
    private Short samplerType;
    private List<EquipmentTreeVO> equipments = new ArrayList<>();

    public SamplerUnitTreeVO copy(TslSamplerUnit port) {
        this.setSamplerUnitId(port.getSamplerUnitId());
        this.setSamplerUnitName(port.getSamplerUnitName());
        this.setSamplerId(port.getSamplerId());
        this.setSamplerType(port.getSamplerType());
        return this;

    }

}
