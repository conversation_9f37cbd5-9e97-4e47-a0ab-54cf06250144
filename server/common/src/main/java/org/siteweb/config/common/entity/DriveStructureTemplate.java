package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Objects;

/**
 * 驱动结构模板表
 *
 * <AUTHOR> (2024-05-15 10:05)
 */
@Data
@TableName("tbl_drivestructuretemplate")
public class DriveStructureTemplate {

    @TableId
    private Integer id;

    /**
     * 模板路径
     */
    private String filePath;

    /**
     * 上级驱动结构id
     */
    private Integer pid;

    /**
     * 是否是叶子节点
     */
    private Integer isLeaf;

    /**
     * 是否是目录
     */
    private Integer isDisk;

    /**
     * 驱动结构文件id
     */
    private Integer fileId;

    /**
     * 是否需要上传
     */
    private Integer isUpload;

    /**
     * 是否需要填充
     */
    private Integer isFill;

    /**
     * 所属驱动模板id
     */
    private Integer driveTemplateId;

    /**
     * 上传时机 0-模板创建时 1-生成配置时
     */
    private Integer uploadTiming;

    /**
     * 计算hash
     */
    public Integer calcHashCode() {
        return Objects.hash(id, filePath, pid, isLeaf, isDisk, fileId, isUpload, isFill, driveTemplateId, uploadTiming);
    }
}
