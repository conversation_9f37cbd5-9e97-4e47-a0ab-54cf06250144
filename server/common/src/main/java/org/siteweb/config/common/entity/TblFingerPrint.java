package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2024-09-11)
 **/
@Data
//@ConfigId(5)
@TableName("TBL_FingerPrint")
//@ChangeSource(channel = "config", product = "siteweb", source = "Tbl_FaceData")
@NoArgsConstructor
public class TblFingerPrint {
    @TableId(value = "FingerPrintId", type = IdType.INPUT)
    private Integer fingerPrintId;
    private Integer fingerPrintNO;
    private byte[] fingerPrintData;
}
