package org.siteweb.config.common.core;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2024-06-12)
 **/
@Data
public class ApplicationProperties {

    /***
     * 无效的配置，从服务器获取配置失败。。
     */
    public static final ApplicationProperties Invalid;
    static {
        Invalid = new ApplicationProperties();
        Invalid.mqtt = new MqttConfigure();
        Invalid.mqtt.setBroker("tcp://examples.mqtt:1883");
    }

    private TokenInfo token;
    private MqttConfigure mqtt;

    @JsonIgnore
    public boolean isInvalid(){
        return this == Invalid;
    }

    @Data
    public static class MqttConfigure {
        private String broker;
        private String user;
        private String password;
        private Long sessionExpiry;
    }


    @Data
    @NoArgsConstructor
    public static class TokenInfo {
        public TokenInfo(String header, String value) {
            this.header = header;
            this.value = value;
        }

        private String header;
        private String value;
    }


}
