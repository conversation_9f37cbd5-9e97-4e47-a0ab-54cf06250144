package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-09-26)
 **/

@TableName("tbl_card")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TblCard {
    @TableId(value = "cardId", type = IdType.AUTO   )
    private Integer cardId;
    private String cardCode;
    private String cardName;

    private Integer cardCategory;
    private Integer cardGroup;
    private Integer userId;
    private Integer stationId;
    private Integer cardStatus;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private LocalDateTime registerTime;
    private LocalDateTime unRegisterTime;
    private LocalDateTime lostTime;
    private String description;

}
