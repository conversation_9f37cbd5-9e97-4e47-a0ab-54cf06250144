package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardMappingControlCheckDTO {
    /**
     * 是否标准化
     */
    private Boolean standarded;

    private Integer equipmentTemplateId;

    private String equipmentTemplateName;

    private Integer controlId;

    private String controlName;

    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 局站类型
     */
    private Integer stationCategory;
    /**
     * 局站类型名称
     */
    private String stationCategoryName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备类型 字典 7
     */
    private Integer equipmentCategory;
    /**
     * 设备类型
     */
    private String equipmentCategoryName;
    /**
     * 基类控制ID
     */
    private Long baseTypeId;
    /**
     * 基类控制名称
     */
    private String baseTypeName;
    /**
     * 标准化控制ID
     */
    private Integer standardDicId;
    /**
     * 标准化控制名称
     */
    private String controlStandardName;

}
