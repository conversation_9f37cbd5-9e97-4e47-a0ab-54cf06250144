package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblDataItem;

/**
 * <AUTHOR> (2024-04-17)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TypeItemDTO {
    public TypeItemDTO(TblDataItem dataItem) {
        this.typeId = dataItem.getItemId();
        this.typeName = dataItem.getItemValue();
        if (dataItem.getEntryId() == 34) {
            this.order = dataItem.getExtendField2() != null ? Integer.parseInt(dataItem.getExtendField2()) : 99;
        } else if (dataItem.getEntryId() == 39) {
            this.order = dataItem.getExtendField1() != null ? Integer.parseInt(dataItem.getExtendField1()) : 99;
        }
    }

    private Integer typeId;
    private String typeName;
    private Integer order;
}
