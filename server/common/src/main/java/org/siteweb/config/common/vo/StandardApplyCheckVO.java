package org.siteweb.config.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.dto.StandardApplyControlCheckDTO;
import org.siteweb.config.common.dto.StandardApplyEventCheckDTO;
import org.siteweb.config.common.dto.StandardApplySignalCheckDTO;

import java.util.List;

/**
 * 应用标准化检查
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardApplyCheckVO {

    private List<StandardApplySignalCheckDTO> signalChecks;

    private List<StandardApplyEventCheckDTO> eventChecks;

    private List<StandardApplyControlCheckDTO> controlChecks;

}
