package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024/3/9)
 */
@TableName(value = "TBL_EventCondition", autoResultMap = true)
@ConfigId(11)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TblEventCondition {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer eventConditionId;
    private Integer equipmentTemplateId;
    private Integer eventId;
    private String startOperation;
    private Double startCompareValue;
    private Integer startDelay;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String endOperation;
    private Double endCompareValue;
    private Integer endDelay;
    private Integer frequency;
    private Integer frequencyThreshold;
    private String meanings;
    private Integer equipmentState;
    private Long baseTypeId;
    private Integer eventSeverity;
    private Integer standardName;


}
