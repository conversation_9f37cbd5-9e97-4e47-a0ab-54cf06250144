package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblStation;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> (2024-04-15)
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StationDTO {
    private Integer stationId;

    @Size(max = 255, message = "局站名称长度不能超过255")
    private String stationName;
    private Double latitude;
    private Double longitude;
    private Integer stationCategory;
    private Integer stationGrade;

    private Integer companyId;
    private Integer contactId;

    private String description;
    private String projectName;
    private String contractNo;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer stationState;
    private LocalDateTime maskStartTime;
    private LocalDateTime maskEndTime;

    private Integer stationStructureId;

    // 局站模板
    private Integer stationTemplateId;

    private List<Integer> stationIds;

    private Integer bordNumber;


    public void copyTo(TblStation station) {
        station.setStationId(this.stationId);
        station.setStationName(this.stationName);
        station.setLatitude(this.latitude);
        station.setLongitude(this.longitude);
        station.setStationCategory(this.stationCategory);
        station.setStationGrade(this.stationGrade);

        station.setCompanyId(this.companyId);
        station.setContactId(this.contactId);
        station.setStationState(this.stationState);

        station.setDescription(this.description);
        // 更新tbl_stationprojectinfo表
//        station.setProjectName(this.projectName);
//        station.setContractNo(this.contractNo);
        station.setStartTime(this.startTime);
        station.setEndTime(this.endTime);
        station.setBordNumber(this.bordNumber);
    }


    public static StationDTO from(TblStation station) {
        StationDTO dto = new StationDTO();
        dto.setStationId(station.getStationId());
        dto.setStationName(station.getStationName());
        dto.setLatitude(station.getLatitude());
        dto.setLongitude(station.getLongitude());
        dto.setStationCategory(station.getStationCategory());
        dto.setStationGrade(station.getStationGrade());

        dto.setCompanyId(station.getCompanyId());
        dto.setContactId(station.getContactId());

        dto.setDescription(station.getDescription());
        dto.setProjectName(station.getProjectName());
        dto.setContractNo(station.getContractNo());
        dto.setStartTime(station.getStartTime());
        dto.setEndTime(station.getEndTime());
        dto.setMaskEndTime(station.getMaskEndTime());
        dto.setMaskStartTime(station.getMaskStartTime());
        dto.setStationState(station.getStationState());
        dto.setBordNumber(station.getBordNumber());
        return dto;
    }


}
