package org.siteweb.config.common.dto.dataitem;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class DataItemUpdateDTO {
    /**
     * 主键id
     */
    private Integer entryItemId;
    /**
     * 条目ID
     */
    private Integer itemId;
    /**
     * 字典值
     */
    private String itemValue;
    /**
     * 字典项目
     */
    private String itemAlias;
    /**
     * 描述
     */
    private String description;
}
