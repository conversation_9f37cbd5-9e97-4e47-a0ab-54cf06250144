package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblDataItem;

import java.util.List;


/**
 * <AUTHOR> (2024-03-09)
 **/
@Mapper
public interface TblDataItemMapper extends BatchBaseMapper<TblDataItem> {

    /**
     * 查找设备类别
     * 仅获取系统中存在的设备种类
     *
     * @return {@link List}<{@link TblDataItem}>
     */
    List<TblDataItem> findEquipmentCategory();
    Integer selectMaxItemId();

    Integer selectMaxItemIdByEntryId(Integer entryId);

    Integer updatePortExtendField2();
}