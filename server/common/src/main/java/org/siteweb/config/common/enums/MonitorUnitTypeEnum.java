package org.siteweb.config.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MonitorUnitTypeEnum {
    /**
     * VMU
     */
    VMU(0, "VMU"),
    /**
     * RMU下MU
     */
    RMU(1, "RMU下MU"),
    /**
     * IDU
     */
    IDU(2, "IDU"),
    /**
     * 后台服务VMU
     */
    VMU_BackGround(3, "后台服务VMU"),
    /**
     * IPLU
     */
    IPLU(4, "IPLU"),
    /**
     * IMU
     */
    IMU(5, "IMU"),
    /**
     * eStone
     */
    eStone(6, "eStone"),
    /**
     * ISU
     */
    ISU(8, "ISU"),
    /**
     * FSU
     */
    FSU(9, "FSU"),
    /**
     * BInterface
     */
    BInterface(10, "BInterface"),
    /**
     * Catcher
     */
    Catcher(11, "Catcher"),
    /**
     * GFSU
     */
    GFSU(12, "GFSU"),
    /**
     * ISUV2
     */
    ISUV2(14, "ISUV2"),
    RFSU(15, "RFSU"),
    WorkStation(16, "WorkStation"),
    /**
     * ECG
     */
    ECG(17, "ECG");

    private final int value;
    private final String describe;

    public static String getDescribeByValue(int value) {
        for (MonitorUnitTypeEnum type : MonitorUnitTypeEnum.values()) {
            if (type.getValue() == value) {
                return type.getDescribe();
            }
        }
        return null;
    }
}