package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.entity.TslSamplerUnit;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> (2024-03-09)
 **/
@Mapper
public interface TslSamplerUnitMapper extends BatchBaseMapper<TslSamplerUnit> {
    Set<String> findReferenceSamplerNameBySamplerIdList(@Param("samplerIdList") List<Integer> samplerIdList);

    List<TslSamplerUnit> selectSamplerUnitWithPort(@Param("monitorUnitId") Integer monitorUnitId);

    TslSamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName, String portName);

    List<IdValueDTO<String, String>> findNamesByIds(@Param("samplerUnitIds") List<Integer> samplerUnitIds);
}