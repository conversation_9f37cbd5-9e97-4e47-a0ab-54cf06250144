package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> (2024-03-23)
 **/
@Data
@TableName("ResourceStructureType")
@NoArgsConstructor
public class ResourceStructureType {

    @TableId(value = "ResourceStructureTypeId", type = IdType.AUTO)
    @JsonProperty("typeId")
    private Integer ResourceStructureTypeId;

    @JsonProperty("sceneId")
    private Integer SceneId;

    @JsonProperty("typeName")
    private String ResourceStructureTypeName;

    @JsonProperty("desc")
    private String Description;

}
