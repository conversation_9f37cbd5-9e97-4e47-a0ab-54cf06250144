package org.siteweb.config.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.dto.StandardMappingControlCheckDTO;
import org.siteweb.config.common.dto.StandardMappingEventCheckDTO;
import org.siteweb.config.common.dto.StandardMappingSignalCheckDTO;

import java.util.List;

/**
 * 映射标准化检查
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardMappingCheckVO {

    private List<StandardMappingSignalCheckDTO> signalChecks;

    private List<StandardMappingEventCheckDTO> eventChecks;

    private List<StandardMappingControlCheckDTO> controlChecks;

}
