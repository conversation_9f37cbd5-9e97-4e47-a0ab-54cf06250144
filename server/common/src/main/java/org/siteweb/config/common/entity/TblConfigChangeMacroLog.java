package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("TBL_ConfigChangeMacroLog")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TblConfigChangeMacroLog {
    private String objectId;
    private Integer configId;
    private Integer editType;
    private LocalDateTime updateTime;
}
