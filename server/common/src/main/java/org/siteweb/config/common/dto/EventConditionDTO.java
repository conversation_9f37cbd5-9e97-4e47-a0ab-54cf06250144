package org.siteweb.config.common.dto;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * Creation Date: 2024/4/15
 */
@Data
public class EventConditionDTO {

    private Integer id;
    private Integer eventConditionId;
    private Integer equipmentTemplateId;
    private Integer eventId;
    private String startOperation;
    private Double startCompareValue;
    private Integer startDelay;
    private String endOperation;
    private Double endCompareValue;
    private Integer endDelay;
    private Integer frequency;
    private Integer frequencyThreshold;
    private String meanings;
    private Integer equipmentState;
    private Long baseTypeId;
    private Integer eventSeverity;
    private Integer standardName;
    /**
     * 基类事件名称
     */
    private String baseTypeName;
    /**
     * 扩展名
     */
    private String baseNameExt;
}
