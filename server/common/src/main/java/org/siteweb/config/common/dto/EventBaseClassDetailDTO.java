package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 基类事件详情
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventBaseClassDetailDTO {

    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    /**
     * 协议代码【与采集器关联】
     */
    private String protocolCode;
    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;

    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件种类 字典24
     */
    private Integer eventCategory;
    /**
     * 事件种类
     */
    private String eventCategoryName;
    /**
     * 所属模块
     */
    private Integer moduleNo;

    /**
     * 事件等级 字典23
     */
    private Integer eventSeverity;
    /**
     * 事件等级
     */
    private String eventSeverityName;
    /**
     * 事件条件id
     */
    private Integer eventConditionId;
    /**
     * 事件条件
     */
    private String meanings;
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 基类扩展表达式
     */
    private String baseNameExt;
    /**
     * 基类情况 已检查
     */
    private String subState;

}
