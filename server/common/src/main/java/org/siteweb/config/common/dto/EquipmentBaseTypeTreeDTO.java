package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 基类设备类型树
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EquipmentBaseTypeTreeDTO {
    private Integer value;
    private String label;
    /**
     * 设备标识 true则此基类设备类型下存在设备
     */
    private Boolean equipmentFlag;
    private List<EquipmentBaseTypeTreeDTO> children;
}
