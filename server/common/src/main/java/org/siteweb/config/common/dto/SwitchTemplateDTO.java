package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 切换模板dto
 *
 * <AUTHOR>
 * @date 2024/04/11
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SwitchTemplateDTO {
    /**
     * 目标模板id
     */
    private Integer destTemplateId;
    /**
     * 当前模板id
     */
    private Integer originTemplateId;
    /**
     * 需要切换的设备ids
     */
    private List<Integer> equipmentIds;
}
