package org.siteweb.config.common.dto;

import java.util.HashMap;

/**
 * pg数据库默认存的是小写，带有驼峰的key会取不到值
 * 为了兼容pg，用此map来代替传统map当返回值
 *
 * <AUTHOR>
 * Creation Date: 2024/7/15
 */
public class LowerCaseKeyMap<V> extends HashMap<String, V> {

    @Override
    public V put(String key, V value) {
        key = key == null ? null : key.toLowerCase();
        return super.put(key, value);
    }

    @Override
    public V get(Object key) {
        key = key == null ? null : key.toString().toLowerCase();
        return super.get(key);
    }
}
