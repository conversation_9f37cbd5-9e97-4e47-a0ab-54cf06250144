package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Description: 基类单位表
 * Author: <EMAIL>
 * Creation Date: 2024/5/16
 */
@Data
@TableName("TBL_BaseUnitDic")
public class TblBaseUnitDic {

    @TableId
    private Integer baseUnitID;
    /**
     * 单位名称
     */
    private String baseUnitName;
    /**
     * 单位符号
     */
    private String baseUnitSymbol;
    /**
     * 描述
     */
    private String baseUnitDescription;

}
