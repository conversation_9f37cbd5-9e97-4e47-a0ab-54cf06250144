package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_EquipmentCMCC")
public class TblEquipmentCMCC {
    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备ID（字符串）
     */
    private String deviceID;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * FSUID
     */
    private String fsuId;

    /**
     * 站点ID（字符串）
     */
    private String siteID;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 房间ID
     */
    private String roomID;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备子类型
     */
    private Integer deviceSubType;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 额定容量
     */
    private Double ratedCapacity;

    /**
     * 版本
     */
    private String version;

    /**
     * 开始运行时间
     */
    private LocalDateTime beginRunTime;

    /**
     * 设备描述
     */
    private String devDescribe;

    /**
     * 扩展字段1
     */
    private String extendField1;

    /**
     * 扩展字段2
     */
    private String extendField2;
}
