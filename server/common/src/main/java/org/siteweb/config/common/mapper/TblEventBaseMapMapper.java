package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.EventBaseMapDTO;
import org.siteweb.config.common.dto.EventBaseMapExportDTO;
import org.siteweb.config.common.entity.TblEventBaseMap;

import java.util.List;

@Mapper
public interface TblEventBaseMapMapper extends BatchBaseMapper<TblEventBaseMap> {
    List<EventBaseMapDTO> getEventBaseMap(@Param("standardId") Integer standardId);

    List<EventBaseMapExportDTO> getEventBaseMapExport(@Param("standardId") Integer standardId);
}
