package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/28
 */
@Data
@TableName("tbl_baseequipmentcategorymap")
@NoArgsConstructor
@AllArgsConstructor
public class TblBaseEquipmentCategoryMap {

    @TableId(type = IdType.INPUT)
    private Integer baseEquipmentId;

    private Integer equipmentCategory;

    @TableField(exist = false)
    private String equipmentCategoryName;


    public TblBaseEquipmentCategoryMap(Integer baseEquipmentId, Integer equipmentCategory) {
        this.baseEquipmentId = baseEquipmentId;
        this.equipmentCategory = equipmentCategory;
    }


}
