package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("tbl_employee")
public class Employee {

    @TableId(value = "EmployeeId", type = IdType.INPUT)
    private Integer employeeId;

    private Integer departmentId;

    private String employeeName;

    private Integer employeeType;

    private Integer employeeTitle;

    private String jobNumber;

    private Integer gender;

    private String mobile;

    private String phone;

    private String email;

    private String address;

    private String postAddress;

    private boolean enable;

    private String description;

    private boolean isAddTempUser;

    private Integer userValidTime;
}