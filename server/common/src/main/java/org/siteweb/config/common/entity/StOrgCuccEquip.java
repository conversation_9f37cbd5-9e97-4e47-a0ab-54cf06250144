package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("st_org_cucc_equip")
public class StOrgCuccEquip {


    private Integer rowId;

    /**
     * 类别
     * class是关键字，不能用，所以使用注解
     */
    @TableField("class")
    private String clazz;

    /**
     * 类型
     */
    private String type;

    /**
     * C1
     */
    private String c1;

    /**
     * C2
     */
    private String c2;

    /**
     * 类型ID
     */
    private String typeId;

    /**
     * 备注
     */
    private String note;
}

