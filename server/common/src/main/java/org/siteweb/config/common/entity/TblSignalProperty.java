package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024/3/9)
 */
@Data
@ConfigId(9)
@NoArgsConstructor
@AllArgsConstructor
@TableName("TBL_SignalProperty")
public class TblSignalProperty {
    public TblSignalProperty(Integer equipmentTemplateId, Integer signalId, Integer signalPropertyId) {
        this.equipmentTemplateId = equipmentTemplateId;
        this.signalId = signalId;
        this.signalPropertyId = signalPropertyId;
    }

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer signalId;
    private Integer signalPropertyId;
}
