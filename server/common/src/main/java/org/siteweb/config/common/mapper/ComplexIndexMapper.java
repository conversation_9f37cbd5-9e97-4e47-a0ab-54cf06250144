package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.dto.ComplexIndexDTO;
import org.siteweb.config.common.entity.ComplexIndex;

import java.util.List;

/**
 * @Description:
 */
public interface ComplexIndexMapper extends BaseMapper<ComplexIndex> {
    List<ComplexIndexDTO> findByObjectIdAndObjectTypeId(@Param("objectId") List<Integer> objectId, @Param("objectTypeId") Integer objectTypeId);

}
