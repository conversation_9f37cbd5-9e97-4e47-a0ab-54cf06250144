package org.siteweb.config.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.dto.StandardControlCompareDTO;
import org.siteweb.config.common.dto.StandardEventCompareDTO;
import org.siteweb.config.common.dto.StandardSignalCompareDTO;

import java.util.List;

/**
 * 应用标准化比较内容
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardCompareVO {

    private List<StandardSignalCompareDTO> signalCompares;
    private List<StandardEventCompareDTO> eventCompares;
    private List<StandardControlCompareDTO> controlCompares;

}
