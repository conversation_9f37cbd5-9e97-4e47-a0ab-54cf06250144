package org.siteweb.config.common.dto.batchtool;

import lombok.Data;


/**
 * 配置管理查询过滤列表Dto
 *
 * <AUTHOR>
 * @date 2022/03/29
 */
@Data
public class EquipmentSettingDTO {
    /**
     * 局站名
     */
    private String stationName;
    /**
     * 局房名
     */
    private String houseName;
    /**
     * 监控单元名
     */
    private String monitorUnitName;
    /**
     * 端口名
     */
    private String portName;
    /**
     * 端口设置
     */
    private String setting;
    /**
     * 电话号码
     */
    private String phoneNumber;
    /**
     * 采集单元名称
     */
    private String samplerUnitName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 采集单元地址
     */
    private Integer address;
    /**
     * 采集动态库地址
     */
    private String dllPath;
    /**
     * 采集周期
     */
    private Double spUnitInterval;
    /**
     * 引用设备模板
     */
    private String equipmentTemplateName;
    /**
     * 引用设备模板
     */
    private String driverTemplateName;
    /**
     * 模板id
     */
    private Integer templateId;
    /**
     * 设备类型 5.虚拟设备 32BACNet 33 SNMP
     */
    private Integer portType;
    /**
     * 是否上传 1是 0否
     */
    private Integer isUpload;
    /**
     * 是否需要重新生成 0否 1是
     */
    private Integer isReset;
}
