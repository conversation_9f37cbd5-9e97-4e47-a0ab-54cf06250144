package org.siteweb.config.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

@Component
public class DefaultMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        //当部分字段为null时，设置默认值
        defaultFillMethod(metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        //当部分字段为null时，设置默认值
        defaultFillMethod(metaObject);
    }

    /**
     * 默认的填充方法
     *
     * @param metaObject 原始对象
     */
    private void defaultFillMethod(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "protocolCode", String.class, "");
        this.strictInsertFill(metaObject, "dllCode", String.class, "");
        this.strictInsertFill(metaObject, "dllVersion", String.class, "");
        this.strictInsertFill(metaObject, "protocolFilePath", String.class, "");
        this.strictInsertFill(metaObject, "dllFilePath", String.class, "");
        this.strictInsertFill(metaObject, "description", String.class, "");
        this.strictInsertFill(metaObject, "soCode", String.class, "");
        this.strictInsertFill(metaObject, "soPath", String.class, "");
        this.strictInsertFill(metaObject, "unit", String.class, "");
        this.strictInsertFill(metaObject, "suppressExpression", String.class, "");
        this.strictInsertFill(metaObject, "endOperation", String.class, "");
    }
}
