package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblEquipmentCUCC;
import org.siteweb.config.common.vo.binterface.EquipmentCUCCVO;

import java.util.List;


@Mapper
public interface TblEquipmentCUCCMapper extends BatchBaseMapper<TblEquipmentCUCC> {
    List<EquipmentCUCCVO> findVO();

    int updateEntity(@Param("tblEquipmentCUCC") TblEquipmentCUCC tblEquipmentCUCC);

    void updateSUID(@Param("suId") String suId, @Param("stationId") Integer stationId, @Param("monitorUnitId") Integer monitorUnitId);
}
