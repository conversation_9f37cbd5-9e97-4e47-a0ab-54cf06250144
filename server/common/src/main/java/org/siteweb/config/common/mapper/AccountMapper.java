package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.entity.Account;
import java.util.List;

@Mapper
public interface AccountMapper extends BaseMapper<Account> {

    List<Account> findByMobile(@Param("mobile") String mobile);

    void updateCenterIdForNegativeUserId(@Param("centerId") Integer centerId);




}
