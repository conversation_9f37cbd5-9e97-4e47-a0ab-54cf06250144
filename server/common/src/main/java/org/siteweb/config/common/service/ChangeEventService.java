package org.siteweb.config.common.service;

import java.util.List;

/**
 * 变更事件服务
 *
 * <AUTHOR> (2024-01-08)
 **/
public interface ChangeEventService {


    /**
     * 发送对象的创建变更通知到MQTT<p>
     * object对象必须实现@ChangeSource注解<p>
     * 并且主键属性需实现@TableId注解
     *
     * @param object 创建的对象
     * <AUTHOR> (2024/3/9)
     */
    <T> void sendCreate(T object);

    /**
     * 发送对象的删除变更通知到MQTT<p>
     * object对象必须实现@ChangeSource注解<p>
     * 并且主键属性需实现@TableId注解
     *
     * @param object 删除的对象
     * <AUTHOR> (2024/3/9)
     */
    <T> void sendDelete(T object);


    /**
     * 发送对象的更新变更通知到MQTT<p>
     * object对象必须实现@ChangeSource注解<p>
     * 并且主键属性需实现@TableId注解
     *
     * @param object 修改的对象
     * <AUTHOR> (2024/3/9)
     */
    <T> void sendUpdate(T object);

    <T> void sendBatchUpdate(List<T> list);
}
