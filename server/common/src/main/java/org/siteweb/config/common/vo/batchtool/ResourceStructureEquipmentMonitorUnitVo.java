package org.siteweb.config.common.vo.batchtool;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.ResourceStructure;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor
@Data
public class ResourceStructureEquipmentMonitorUnitVo {
    public ResourceStructureEquipmentMonitorUnitVo(ResourceStructure resourceStructure) {
        this.resourceStructureId = resourceStructure.getResourceStructureId();
        this.resourceStructureName = resourceStructure.getResourceStructureName();
        this.parentResourceStructureId = resourceStructure.getParentResourceStructureId();
    }

    /**
     * 层级id
     */
    private Integer resourceStructureId;
    /**
     * 层级名称
     */
    private String resourceStructureName;
    /**
     * 父级id
     */
    private Integer parentResourceStructureId;
    /**
     * 子集
     */
    private List<ResourceStructureEquipmentMonitorUnitVo> children;
    /**
     * 监控单元集合
     */
    private List<MonitorUnitVO> monitorUnitChildren;

    public List<ResourceStructureEquipmentMonitorUnitVo> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public List<MonitorUnitVO> getMonitorUnitChildren() {
        if (monitorUnitChildren == null) {
            monitorUnitChildren = new ArrayList<>();
        }
        return monitorUnitChildren;
    }
}
