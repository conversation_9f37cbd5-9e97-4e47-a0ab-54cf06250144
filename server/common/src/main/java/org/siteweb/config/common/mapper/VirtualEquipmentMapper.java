package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.dto.batchtool.SplitEquipmentInfoDTO;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.vo.batchtool.CrossVirtualEquipmentBasicInfoVO;
import org.siteweb.config.common.vo.batchtool.VirtualEquipmentBasicInfoVO;

import java.util.List;

public interface VirtualEquipmentMapper extends BaseMapper<TblEquipment> {
    List<VirtualEquipmentBasicInfoVO> findVirtualEquipmentBasicInfo(@Param("equipmentIds") List<Integer> equipmentIds);

    List<CrossVirtualEquipmentBasicInfoVO> findCrossVirtualEquipmentBasicInfo(@Param("equipmentIds") List<Integer> equipmentIds);

    SplitEquipmentInfoDTO findSplitInfo(@Param("equipmentId") Integer equipmentId);
}
