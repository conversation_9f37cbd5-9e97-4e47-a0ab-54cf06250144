package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ChangeSource;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024/3/9)
 */
@TableName("TBL_Event")
@ConfigId(10)
@ChangeSource(channel = "config", product = "siteweb", source = "event")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TblEvent {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 开始类型
     */
    private Integer startType;
    /**
     * 结束类型
     */
    private Integer endType;
    /**
     * 开始表达式
     */
    private String startExpression;
    /**
     * 抑制表达式
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String suppressExpression;
    /**
     * 事件类别
     */
    private Integer eventCategory;
    /**
     * 信号主键id
     */
    private Integer signalId;
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 是否可见
     */
    private Boolean visible;
    /**
     * 描述
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String description;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 所属模块
     */
    private Integer moduleNo;
}
