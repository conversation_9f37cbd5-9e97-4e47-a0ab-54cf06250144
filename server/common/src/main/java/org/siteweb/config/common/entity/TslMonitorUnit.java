package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ChangeSource;
import org.siteweb.config.common.annotation.ConfigId;
import org.siteweb.config.common.annotation.OpLogDic;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OpLogDicEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024/3/9)
 */
@Data
@ChangeSource(channel = "config", product = "siteweb", source = "monitor-unit")
@ConfigId(15)
@TableName("TSL_MonitorUnit")
@NoArgsConstructor
public class TslMonitorUnit {
    @TableId(value = "MonitorUnitId", type = IdType.INPUT)
    private Integer monitorUnitId;
    @Size(max = 100, message = "监控单元名称长度不能超过100")
    private String monitorUnitName;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.MONITOR_UNIT_TYPE)
    private Integer monitorUnitCategory;
    private String monitorUnitCode;
    private Integer workStationId;
    private Integer stationId;
    private String ipAddress;
    private Integer runMode;
    private String configFileCode;
    private LocalDateTime configUpdateTime;
    private String sampleConfigCode;
    private String softwareVersion;
    private String description;
    private LocalDateTime startTime;
    private LocalDateTime heartbeatTime;
    private Integer connectState;
    private LocalDateTime updateTime;
    private Boolean isSync;
    private LocalDateTime syncTime;
    private Boolean isConfigOK;
    private String configFileCode_Old;
    private String sampleConfigCode_Old;
    @TableField("appCongfigId")
    private Integer appConfigId;
    private Boolean canDistribute;
    private Boolean enable;


    /**
     * RDS服务器
     */
    @TableField("projectName")
    private String rdsServer;

    /**
     * 数据服务器
     */
    @TableField("contractNo")
    private String dataServer;

    private LocalDateTime installTime;
    private Boolean fsu;
}
