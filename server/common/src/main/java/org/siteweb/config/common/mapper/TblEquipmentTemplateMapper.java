package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.AutoEquipmentBaseTypeDTO;
import org.siteweb.config.common.dto.EquipmentTemplateTreeDTO;
import org.siteweb.config.common.dto.IdValueDTO;
import org.siteweb.config.common.entity.TblEquipmentTemplate;
import org.siteweb.config.common.vo.EquipmentTemplateBaseClassVO;
import org.siteweb.config.common.vo.EquipmentTemplateVO;
import org.siteweb.config.common.vo.SamplerVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> (2024/3/9)
 */
@Mapper
public interface TblEquipmentTemplateMapper extends BatchBaseMapper<TblEquipmentTemplate> {

    List<EquipmentTemplateTreeDTO> findTree();

    Set<String> findReferenceSamplerNameByProtocolCodes(@Param("protocolCodeList") List<String> protocolCodeList);

    List<SamplerVO> findEquipmentTemplateByProtocolCodes(@Param("protocolCodeList") List<String> protocolCodeList);

    List<EquipmentTemplateTreeDTO> findTreeByEquipmentCategory(@Param("equipmentCategory") Integer equipmentCategory);

    EquipmentTemplateVO findVoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    void updateEquipmentCategoryByCategoryIdMap(int businessId, int categoryTypeId);

    List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(int equipmentTemplateIdDiv);

    void batchInsertLianTongEquipmentTemplate();

    List<TblEquipmentTemplate> findByMonitorUnitId(@Param("monitorUnitId") Integer monitorUnitId);

    /**
     * 获取需要自动装配基类设备类型的模板
     */
    List<AutoEquipmentBaseTypeDTO> getAutoSetEquipmentBaseTypeList();

    Set<String> findReferenceEquipmentNameByProtocolCodes(@Param("protocolCodeList") List<String> protocolCodeList);

    TblEquipmentTemplate findByName(@Param("equipmentTemplateName") String equipmentTemplateName);

    List<Integer> findAllChildId(@Param("parentTemplateId") Integer parentTemplateId);

    List<TblEquipmentTemplate> findByEquipmentCategoryAndProtocolCode(@Param("equipmentCategory") Integer equipmentCategory, @Param("protocolCode") String protocolCode, @Param("equipmentTemplateName") String equipmentTemplateName);

    List<IdValueDTO<String, String>> findNameByIds(@Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    List<EquipmentTemplateBaseClassVO> findBaseClassAll();

    List<TblEquipmentTemplate> findDynamicConfigTemplate(@Param("hideDynamicConfigTemplate") Boolean hideDynamicConfigTemplate);
}
