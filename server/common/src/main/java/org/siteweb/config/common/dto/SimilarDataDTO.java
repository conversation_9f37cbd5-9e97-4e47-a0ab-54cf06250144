package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相似数据（信号、事件、控制）
 *
 * <AUTHOR> (2024/5/17)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimilarDataDTO {

    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;

    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;

    /**
     * 含义
     */
    private String meanings;

    /**
     * 基类id
     */
    private Long baseTypeId;

    /**
     * 基类含义id
     */
    private Integer baseCondId;

    /**
     * 名称通配符
     */
    private String wildcard;
    /**
     * 开始编号
     */
    private Integer startNumber;
    /**
     * 终止编号
     */
    private Integer abortNumber;
    /**
     * 覆盖标志 true则覆盖已有值的 空false不覆盖
     */
    private Boolean coverFlag;
    /**
     * 子类标志 true则为子类相似信号
     */
    private Boolean childFlag;
}
