package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.view.CommandBaseDicView;

@TableName("TBL_CommandBaseDic")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TblCommandBaseDic {
    /**
     * 基类id
     */
    @JsonView(CommandBaseDicView.Simple.class)
    @TableId(value = "baseTypeId", type = IdType.INPUT)
    private Long baseTypeId;
    /**
     * 基类名
     */
    @JsonView(CommandBaseDicView.Simple.class)
    private String baseTypeName;
    /**
     * 设备基类id
     */
    private Integer baseEquipmentId;
    /**
     * 英文名
     */
    private String englishName;
    private Integer baseLogicCategoryId;
    /**
     * 命令类型
     */
    private Integer commandType;
    /**
     * 基类状态
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer baseStatusId;
    /**
     * 扩展字段3
     */
    private String extendField1;
    /**
     * 扩展字段2
     */
    private String extendField2;
    /**
     * 扩展字段3
     */
    private String extendField3;
    /**
     * 描述
     */
    private String description;
    /**
     * 基类扩展名
     */
    @JsonView(CommandBaseDicView.Simple.class)
    private String baseNameExt;
    /**
     * 是否为系统
     */
    private Boolean isSystem;
}
