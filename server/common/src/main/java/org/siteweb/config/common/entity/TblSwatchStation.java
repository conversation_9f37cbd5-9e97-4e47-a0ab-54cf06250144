package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/8/21
 */
@Data
@NoArgsConstructor
@TableName("tbl_swatchstation")
public class TblSwatchStation {

    @TableId(value = "SwatchStationId", type = IdType.AUTO)
    private Integer swatchStationId;
    private String swatchStationName;
    private Integer stationId;
    @TableField(exist = false)
    private String stationName;
    private LocalDateTime createTime;
    private String description;
}
