package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 字节跳动测点规范表
 *
 * <AUTHOR> (YYYY-MM-DD)
 **/
@Data
@NoArgsConstructor
@TableName("bd_standardpoint")
public class BdStandardPoint {

    /**
     * 设备类型id
     */
    private Integer equipmentCategoryId;

    /**
     * 测点ID
     */
    private Integer pointId;

    /**
     * 测点名称
     */
    private String pointName;

    /**
     * 测点扩展名称
     */
    private String extendName;

    /**
     * 测点类型: 1=AI; 2=DI; 3=AO; 4=DO; 5=Alarm
     */
    private Integer type;

    /**
     * 单位
     */
    private String unit;

    /**
     * 精度
     */
    private Float precisions;

    /**
     * 基础点位
     */
    private Boolean basicPoint;

    /**
     * 阈值
     */
    private String thresholdValue;

    /**
     * 点位工作区间
     */
    private String workTnterval;

    /**
     * 告警等级
     */
    private Integer alarmLevel;

    /**
     * 点位数量
     */
    private Integer pointNumber;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 告警次数
     */
    private Integer alarmNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 测点英文名称
     */
    private String englishName;
}