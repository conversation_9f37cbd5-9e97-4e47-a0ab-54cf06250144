package org.siteweb.config.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblWorkStation;

import java.time.LocalDateTime;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/12
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkStationDTO {

    private Integer workStationId;

    private Integer workStationType;

    private String workStationName;

    private String ipAddress;

    private String ipV6Address;

    private Boolean isUsed;

    private Long baseTypeId;


    public TblWorkStation convertDtoToEntity() {
        TblWorkStation workStation = new TblWorkStation();
        workStation.setIsUsed(this.isUsed);
        workStation.setWorkStationType(this.workStationType);
        workStation.setWorkStationName(this.workStationName);
        workStation.setUpdateTime(LocalDateTime.now());
        workStation.setWorkStationId(this.workStationId);
        return workStation;
    }


}
