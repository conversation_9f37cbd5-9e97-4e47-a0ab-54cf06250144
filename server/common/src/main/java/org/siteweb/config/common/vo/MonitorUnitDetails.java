package org.siteweb.config.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/5/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorUnitDetails {

    private String monitorUnitName;
    private Integer monitorUnitId;
    private String phoneNumber;
    private String reportIntervalTime;
    private String address;
    private String setting;
    private Integer samplerUnitId;
}
