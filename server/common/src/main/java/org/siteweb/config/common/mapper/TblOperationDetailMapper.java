package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.OperationDetailDTO;
import org.siteweb.config.common.entity.TblOperationDetail;
import org.siteweb.config.common.vo.OperationDetailVO;

@Mapper
public interface TblOperationDetailMapper extends BatchBaseMapper<TblOperationDetail> {
    Page<OperationDetailVO> findPage(@Param("page") Page<TblOperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO);

    Page<OperationDetailVO> findEquipmentTemplateLogPage(@Param("page") Page<TblOperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO);

    Page<OperationDetailVO> findEquipmentLogPage(@Param("page") Page<TblOperationDetail> page, @Param("operationDetailDTO") OperationDetailDTO operationDetailDTO, @Param("equipmentTemplateId") Integer equipmentTemplateId);
}
