package org.siteweb.config.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.DriveStructureTemplate;


/**
 * 驱动结构模板表
 */
@Data
@NoArgsConstructor
public class DriveStructureTemplateDTO {
    /**
     * 驱动结构模板id
     */
    private Integer id;
    /**
     * 驱动结构模板名称
     */
    private String templateName;
    /**
     * 结构路径
     */
    private String filePath;
    /**
     * 上级驱动模板id
     */
    private Integer pid;
    /**
     * 是否是叶子节点
     */
    private Integer isLeaf;
    /**
     * 是否是目录
     */
    private Integer isDisk;
    /**
     * 模板文件id
     */
    private Integer fileId;
    /**
     * 是否需要上传
     */
    private Integer isUpload;
    /**
     * 是否需要填充内容
     */
    private Integer isFill;
    /**
     * 驱动模板id
     */
    private Integer driveTemplateId;
    /**
     * 上传时机 0-模板创建时 1-生成配置时
     */
    private Integer uploadTiming;

    public DriveStructureTemplate build() {
        DriveStructureTemplate driveStructureTemplate = new DriveStructureTemplate();
        driveStructureTemplate.setId(this.id);
        driveStructureTemplate.setFilePath(this.filePath);
        driveStructureTemplate.setFileId(this.fileId);
        driveStructureTemplate.setPid(this.pid);
        driveStructureTemplate.setIsDisk(this.isDisk);
        driveStructureTemplate.setIsUpload(this.isUpload);
        driveStructureTemplate.setIsFill(this.isFill);
        driveStructureTemplate.setDriveTemplateId(this.driveTemplateId);
        driveStructureTemplate.setUploadTiming(this.uploadTiming);
        return driveStructureTemplate;
    }
}
