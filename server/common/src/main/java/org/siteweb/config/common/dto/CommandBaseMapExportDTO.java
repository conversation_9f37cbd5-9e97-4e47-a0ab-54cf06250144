package org.siteweb.config.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblCommandBaseMap;


@AllArgsConstructor
@NoArgsConstructor
@Data
@HeadFontStyle(fontHeightInPoints = 10)
@ContentFontStyle(fontHeightInPoints = 10)
public class CommandBaseMapExportDTO {
    @ExcelProperty(value = "StandardType")
    private Integer standardType;
    @ExcelProperty(value = "StandardName")
    private String standardName;
    @ExcelProperty(value = "EquipmentLogicClassId")
    private Integer equipmentLogicClassId;
    @ExcelProperty(value = "EquipmentLogicClass")
    private String equipmentLogicClass;
    @ExcelProperty(value = "ControlLogicClassId")
    private Integer controlLogicClassId;
    @ExcelProperty(value = "ControlLogicClass")
    private String controlLogicClass;
    @ExcelProperty(value = "StandardDicId")
    private Integer standardDicId;
    @ExcelProperty(value = "ControlStandardName")
    private String controlStandardName;
    @ExcelProperty(value = "StationBaseType")
    private Integer stationBaseType;
    @ExcelProperty(value = "Type")
    private String type;
    @ExcelProperty(value = "BaseEquipmentId")
    private Integer baseEquipmentId;
    @ExcelProperty(value = "BaseEquipmentName")
    private String baseEquipmentName;
    @ExcelProperty(value = "BaseTypeId")
    private Long baseTypeId;
    @ExcelProperty(value = "BaseTypeName")
    private String baseTypeName;

    public static TblCommandBaseMap convert(CommandBaseMapExportDTO commandBaseMapExportDTO) {
        TblCommandBaseMap tblCommandBaseMap = new TblCommandBaseMap();
        tblCommandBaseMap.setStandardDicId(commandBaseMapExportDTO.getStandardDicId());
        tblCommandBaseMap.setStandardType(commandBaseMapExportDTO.getStandardType());
        tblCommandBaseMap.setStationBaseType(commandBaseMapExportDTO.getStationBaseType());
        tblCommandBaseMap.setBaseTypeId(commandBaseMapExportDTO.getBaseTypeId());
        return tblCommandBaseMap;
    }
}
