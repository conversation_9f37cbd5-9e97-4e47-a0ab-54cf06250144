package org.siteweb.config.common.enums;

/**
 * 定义授权错误消息枚举的枚举
 *
 * <AUTHOR>
 */
public enum AuthenicationMessageEnum {

    /**
     * 用户帐号过期
     */
    ACCOUNT_EXPIRED("User account has expired"),
    /**
     * 用户密码过期
     */
    PASSWORD_EXPIRED("User password is expired"),
    /**
     * 用户帐号锁定
     */
    LOCKED("User account is locked"),
    /**
     * 用户未启用
     */
    DISABLED("User is disabled"),
    /**
     * 用户帐号被冻结
     */
    FREEZE("User account is freeze:"),
    /**
     * 短信验证码错误
     */
    SMSCODE_ERR("smscode error"),
    /**
     * 用户不存在
     */
    USER_NOT_EXIST("user not exist"),
    /**
     * 用户名不存在
     */
    USERNAME_NOT_FOUND("Username not found."),
    /**
     * 密码错误
     */
    WRONG_PASSWORD("Wrong password"),
    /**
     * 用户账号暴力破解
     */
    VIOLENT_HACK("User account brute force hack"),
    /**
     * 用户名或密码错误
     */
    USERNAME_OR_PWD_ERR("Incorrect username or password");


    private String msg;

    AuthenicationMessageEnum(String msg) {
        this.msg = msg;
    }


    public String getMsg() {
        return msg;

    }
}
