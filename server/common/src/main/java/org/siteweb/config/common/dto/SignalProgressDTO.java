package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计算信号进度
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SignalProgressDTO {
    private Integer equipmentTemplateId;

    private Integer signalId;

    private Integer signalCategory;

    private Long baseTypeId;

    private Integer baseCondId;

    private Integer stateValue;

    private String subState;

}
