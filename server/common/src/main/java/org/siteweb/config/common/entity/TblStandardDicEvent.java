package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.OpLogDic;
import org.siteweb.config.common.annotation.OperationLogDefine;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OpLogDicEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_StandardDicEvent")
@OperationLogDefine(prefix = "StandardDicEvent", ignoreFields = {"netManageId","standardType","standardDicId", "equipmentLogicClassId", "eventLogicClassId","modifyType"},
        operationObjectType = OperationObjectTypeEnum.EVENT_STANDARD, objectId = {"standardDicId"})
public class TblStandardDicEvent {
    /**
     * 标准字典ID
     */
    private Integer standardDicId;

    /**
     * 标准类型
     */
    private Integer standardType;

    /**
     * 设备逻辑类ID
     */
    private Integer equipmentLogicClassId;

    /**
     * 设备逻辑类
     */
    private String equipmentLogicClass;

    /**
     * 事件逻辑类ID
     */
    private Integer eventLogicClassId;

    /**
     * 事件逻辑类
     */
    private String eventLogicClass;

    /**
     * 事件类
     */
    private String eventClass;

    /**
     * 事件标准名称
     */
    private String eventStandardName;

    /**
     * 网管ID
     */
    private String netManageId;

    /**
     * 告警等级
     */
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.EVENT_LEVEL)
    private Integer eventSeverity;

    /**
     * 告警门限
     */
    private String compareValue;

    /**
     * 告警延迟
     */
    private String startDelay;

    /**
     * 告警解释
     */
    private String meanings;

    /**
     * 设备影响
     */
    private String equipmentAffect;

    /**
     * 业务影响
     */
    private String businessAffect;

    /**
     * 局站类别
     */
    @OpLogDic(value = OpLogDicEnum.STATION_BASE_TYPE)
    private Integer stationCategory;

    /**
     * 修改类型
     */
    private Integer modifyType;

    /**
     * 提出依据
     */
    private String description;

    /**
     * 扩展字段1
     */
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.EVENT_LEVEL)
    private String extendFiled1;

    /**
     * 扩展字段2
     */
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.EVENT_LEVEL)
    private String extendFiled2;

    /**
     * 扩展字段3
     */
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.EVENT_LEVEL)
    private String extendFiled3;
}
