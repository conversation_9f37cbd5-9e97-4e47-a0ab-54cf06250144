package org.siteweb.config.common.view;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 描述: 工作站/服务器实体类
 * 作者: <EMAIL>
 * 创建日期: 2024/3/12
 */
@Data
@NoArgsConstructor
public class WorkStationView {

    private Integer workStationId;

    private String workStationName;

    private Integer workStationType;

    private String ipAddress;

    private Integer parentId;

    private Integer connectState;

    private LocalDateTime updateTime;

    private Boolean isUsed;

    private Integer cpu;

    private Integer memory;

    private Integer threadCount;

    private Integer diskFreeSpace;

    private Integer dbFreeSpace;

    private LocalDateTime lastCommTime;

    // tbl_eventbasedic
    private Long baseTypeId;

}
