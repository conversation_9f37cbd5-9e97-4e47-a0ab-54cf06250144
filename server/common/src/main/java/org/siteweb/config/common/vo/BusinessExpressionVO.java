package org.siteweb.config.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessExpressionVO {
    private Integer BusinessTypeId;
    private String BusinessTypeName;
    private Integer ExpressionId;
    private String ExpressionName;
    private String Expression;
    private String SuppressExpression;
    private Integer AssociationId;
    private Integer StateTriggerValue;
    private Integer BeforeChgStoreInterval;
    private Integer AfterChgStoreInterval;
    private Integer StoreInterval;
    private String AbsValueThreshold;
    private Integer StationId;
    private String StationName;
    private Integer EquipmentId;
    private String EquipmentName;
    private Integer SignalId;
    private String SignalName;
    private Integer MonitorUnitId;
    private String MonitorUnitName;
    private Integer SerialId;
}
