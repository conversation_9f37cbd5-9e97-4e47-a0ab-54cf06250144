package org.siteweb.config.common.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.StationBaseMapExportDTO;
import org.siteweb.config.common.entity.TblStationBaseMap;

import java.util.List;

@Mapper
public interface TblStationBaseMapMapper extends BatchBaseMapper<TblStationBaseMap> {
    /**
     * 获取TBL_StationBaseMap的导出
     *
     * @param standardId 当前标准
     */
    List<StationBaseMapExportDTO> getStationBaseMapExport(@Param("standardId") Integer standardId);
}
