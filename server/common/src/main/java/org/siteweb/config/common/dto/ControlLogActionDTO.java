package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 联动控制
 * Author: <PERSON><PERSON><PERSON><PERSON>.<EMAIL>
 * Creation Date: 2024/6/13
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ControlLogActionDTO {
    private Integer logActionId;
    private Integer actionId;
    private String actionName;
    private Integer equipmentId;
    private Integer controlId;
    /**
     * 对应控制含义的ParameterValue
     */
    private String actionValue;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 控制名
     */
    private String controlName;
    /**
     * 含义(参数值)
     */
    private String meanings;
}
