package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tbl_door")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TblDoor {
    private Integer doorId;
    private Integer doorNo;
    private String doorName;
    private Integer stationId;
    private Integer equipmentId;
    private Integer samplerUnitId;
    private Integer category;
    private String address;
    private Integer workMode;
    private Integer infrared;
    private String password;
    private Integer doorControlId;
    private Integer doorInterval;
    private Integer openDelay;
    private String description;
    private Integer openMode;
}
