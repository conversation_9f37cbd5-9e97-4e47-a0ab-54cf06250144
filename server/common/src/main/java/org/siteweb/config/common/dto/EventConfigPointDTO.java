package org.siteweb.config.common.dto;

import lombok.Data;


/**
 * 事件VO
 *
 */
@Data
public class EventConfigPointDTO {
    /**
     * 主键id
     */

    private Integer id;
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类别
     */
    private Integer eventCategory;
    /**
     * 映射字典id
     */
    private String description;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 映射字典表项名称
     */
    private String mappingPointName;

}
