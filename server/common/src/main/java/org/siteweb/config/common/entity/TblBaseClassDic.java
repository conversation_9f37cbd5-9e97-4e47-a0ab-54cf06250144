package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import org.siteweb.config.common.view.BaseClassDicView;

/**
 * 设备大类
 */
@Data
@TableName("TBL_BaseClassDic")
public class TblBaseClassDic {
    @JsonProperty("eqTypeId")
    @JsonView(BaseClassDicView.Simple.class)
    @TableId(value = "BaseClassId", type = IdType.INPUT)
    private Integer baseClassId;

    @JsonProperty("eqTypeName")
    @JsonView(BaseClassDicView.Simple.class)
    private String baseClassName;

    @JsonProperty("eqTypeIcon")
    private Integer baseClassIcon;
}
