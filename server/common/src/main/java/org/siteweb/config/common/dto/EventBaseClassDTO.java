package org.siteweb.config.common.dto;

import lombok.Data;

/**
 * 基类事件
 */
@Data
public class EventBaseClassDTO {

    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件条件
     */
    private String meanings;
    /**
     * 数量
     */
    private Integer eventNumber;

    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件条件id
     */
    private Integer eventConditionId;
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 基类扩展表达式
     */
    private String baseNameExt;
    /**
     * 基类情况 已检查
     */
    private String subState;
    /**
     * 子项情况 全相同。。
     */
    private String childState;

}
