package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TslMonitorUnitCUCC;
import org.siteweb.config.common.vo.binterface.MonitorUnitCUCCVO;

import java.util.List;

@Mapper
public interface TslMonitorUnitCUCCMapper extends BatchBaseMapper<TslMonitorUnitCUCC> {
    List<MonitorUnitCUCCVO> findAllVO();

    int updateEntity(@Param("tslMonitorUnitCUCC") TslMonitorUnitCUCC tslMonitorUnitCUCC);
}
