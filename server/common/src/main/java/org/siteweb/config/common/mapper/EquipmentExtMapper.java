package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.dto.batchtool.EquipmentSettingUpdateDTO;
import org.siteweb.config.common.dto.batchtool.NeedSamplerDTO;
import org.siteweb.config.common.entity.EquipmentExt;
import org.siteweb.config.common.vo.batchtool.EquipmentSettingVO;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/28 10:40
 */
public interface EquipmentExtMapper extends BaseMapper<EquipmentExt> {

    List<EquipmentSettingVO> findDestSettingListByEquipmentIds(@Param("portType") Integer portType, @Param("equipmentIds") List<Integer> equipmentIds);

    List<EquipmentSettingVO> getSettingManagerList(@Param("portType") Integer portType);

    /**
     * 根据设备ids查询已存在的扩展数据
     *
     * @param equipmentIds 设备ids
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> existsEquipmentIds(@Param("list") List<Integer> equipmentIds);

    /**
     * 批量插入扩展设备
     *
     * @param equipmentSaveList 设备拓展集合
     */
    void batchInsert(@Param("list") List<EquipmentExt> equipmentSaveList);

    /**
     * 查找配置列表id
     *
     * @param id 设备id
     * @return {@link EquipmentSettingVO}
     */
    EquipmentSettingVO findSettingManagerById(@Param("id") Integer id);

    /**
     * 更新设备端口信息
     *
     * @param equipmentSettingUpdateDto 更新设备dto
     * @return {@link Integer}
     */
    void updatePort(@Param("equipmentSettingUpdateDto") EquipmentSettingUpdateDTO equipmentSettingUpdateDto);

    /**
     * 更新采集单元信息
     *
     * @param equipmentSettingUpdateDto 更新设备dto
     * @return {@link Integer}
     */
    void updateSamplerUnit(@Param("equipmentSettingUpdateDto") EquipmentSettingUpdateDTO equipmentSettingUpdateDto);

    /**
     * 更新设备的引用驱动模板
     *
     * @param equipmentId     设备id
     * @param driveTemplateId 引用驱动模板id
     * @return {@link Integer} 更新条数
     */
    Integer updateTemplateByEquipmentId(@Param("equipmentId") Integer equipmentId, @Param("driveTemplateId") Integer driveTemplateId);

    /**
     * 重新设置模板设备
     *
     * @param equipmentTemplateId 设备引用模板id
     */
    void resetTemplateByTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 更新文件id根据模板id
     *
     * @param equipmentTemplateId 引用模板id
     * @param fileId              驱动模板文件id
     * @param isUpload            是否上传
     * @return {@link Integer}
     */
    Integer updateTemplateFileById(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("fileId") Integer fileId, @Param("isUpload") Integer isUpload);

    /**
     * 更新模板的驱动模板文件根域设备id
     *
     * @param equipmentId 设备id
     * @param fileId      驱动模板文件id
     * @param isUpload    是否上传
     * @return {@link Integer}
     */
    Integer updateFileIdByEquipmentId(@Param("equipmentId") Integer equipmentId, @Param("fileId") Integer fileId, @Param("isUpload") Integer isUpload);

    /**
     * 修改驱动模板根据引用模板
     *
     * @param equipmentTemplateId 设备引用模板id
     * @param driveTemplateId     驱动模板id
     * @return {@link Integer} 修改行数
     */
    Integer updateTemplateByTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("driveTemplateId") Integer driveTemplateId);

    /**
     * 通过设备ids查询采集单元信息
     *
     * @param equipmentIds 设备ids
     * @return {@link List}<{@link NeedSamplerDTO}> 采集单元信息
     */
    List<NeedSamplerDTO> getNeedInitSampler(@Param("list") List<Integer> equipmentIds);

    /**
     * 批量初始化采集单元地址与采集动态库地址
     *
     * @param portList
     */
    void batchInit(@Param("list") List<NeedSamplerDTO> portList);
}
