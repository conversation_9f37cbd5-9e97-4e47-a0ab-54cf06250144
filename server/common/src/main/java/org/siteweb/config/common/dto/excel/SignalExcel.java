package org.siteweb.config.common.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 信号Excel
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@Data
@ExcelIgnoreUnannotated
public class SignalExcel {
    @ExcelProperty("站点id")
    private Integer stationId;
    @ExcelProperty("站点名称")
    private String stationName;
    @ExcelProperty("局房id")
    private Integer houseId;
    @ExcelProperty("局房名称")
    private String houseName;
    @ExcelProperty("设备id")
    private Integer equipmentId;
    @ExcelProperty("设备名称")
    private String equipmentName;
    @ExcelProperty("设备模板id")
    private Integer equipmentTemplateId;
    @ExcelProperty("设备模板名称")
    private String equipmentTemplateName;
    @ExcelProperty("信号Id")
    private Integer signalId;
    @ExcelProperty("信号名称")
    private String signalName;
    @ExcelProperty("表达式")
    private String expression;
    @ExcelProperty("显示精度")
    private String showPrecision;
    @ExcelProperty("单位")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String unit;
    @ExcelProperty("存储周期(秒)")
    private Double storeInterval;
    @ExcelProperty("绝对值阈值")
    private Double absValueThreshold;
}
