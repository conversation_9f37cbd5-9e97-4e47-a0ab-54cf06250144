package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/3/20
 */
@Data
@TableName("TSL_MonitorUnitConfig")
@NoArgsConstructor
public class TslMonitorUnitConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private int id;

    private int appConfigId;

    private int siteWebTimeOut;

    private int retryTimes;

    private int heartBeat;

    private int equipmentTimeOut;

    private int portInterruptCount;

    private int portInitializeInternal;

    private int maxPortInitializeTimes;

    private int portQueryTimeOut;

    private int dataSaveTimes;

    private int historySignalSavedTimes;

    private int historyBatterySavedTimes;

    private int historyEventSavedTimes;

    private int cardRecordSavedCount;

    private int controlLog;

    private String ipAddressDS;
}
