package org.siteweb.config.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DataEntryEnum {
    EMPTY_VALUE(-1, ""),
    STATION_GRADE(2, "局站级别"),
    EQUIPMENT_CATEGORY(7, "设备种类"),
    WORKSTATION_CATEGORY(58, "工作台种类"),
    DATA_ENTRY(62, "监控中心(NO.)"),
    STATION_GROUP_TYPE(1, "局站树区划种类"),
    SAMPLER_TYPE(37, "采集器种类"),
    SIGNAL_CATEGORY(17, "信号种类"),
    SIGNAL_TYPE(18, "信号分类"),
    CHANNEL_TYPE(22, "通道类型"),
    SIGNAL_PROPERTY(21, "信号属性"),
    DATA_TYPE(70, "数据类型"),
    EQUIPMENT_TYPE(8, "设备分类"),
    EQUIPMENT_PROPERTY(9, "设备属性"),
    EVENT_LEVEL(23, "事件等级"),
    EVENT_CATEGORY(24, "事件类别"),
    START_TYPE(25, "事件开始类型"),
    END_TYPE(26, "事件结束类型"),
    BATTERY_TYPE(11, "电池类型"),
    BATTERY_WORKING_CONDITION(12, "电池工作状态"),
    EQUIPMENT_ASSET_STATUS(10, "设备资产状态"),
    EQUIPMENT_MANUFACTURERS(14, "设备厂商"),
    /**
     * 局站种类要根据不同的标准获取 71为维谛标准
     */
    STATION_CATEGORY(71, "局站种类"),
    COMMAND_CATEGORY(31, "命令控制种类"),
    COMMAND_SEVERITY(28, "控制命令权限级"),
    COMMAND_TYPE(32, "控制命令分类"),
    RUN_MODE(33, "监控单元运行模式"),
    CONTROL_TYPE(68, "控件类型"),
    MONITOR_UNIT_TYPE(34, "监控单元类型"),
    PORT_TYPE(39, "端口类型"),
    DRIVE_TEMPLATE_TYPE(2023, "驱动模板类型"),
    STATION_STATE(5, "局站状态"),
    DICTIONARY_ENTRY_TYPE(153, "字典项类型"),
    EQUIPMENT_MAINTENANCE_VENDOR(15, "代维厂商");


    private final int value;
    private final String describe;


    // 根据value查找枚举
    public static DataEntryEnum findByValue(int value) {
        for (DataEntryEnum entry : values()) {
            if (entry.getValue() == value) {
                return entry;
            }
        }
        throw new IllegalArgumentException("未找到对应的DataEntryEnum: " + value);
    }
}
