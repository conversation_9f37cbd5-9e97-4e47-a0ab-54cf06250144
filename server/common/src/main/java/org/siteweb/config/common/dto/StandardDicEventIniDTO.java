package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardDicEventIniDTO {

    /**
     * 标准字典ID
     */
    private Integer standardDicId;
    /**
     * 站点类别
     */
    private Integer stationCategory;
    /**
     * 事件逻辑类
     */
    private String eventLogicClass;
    /**
     * 标准化事件名称
     */
    private String eventStandardName;
    /**
     * 网络管理ID
     */
    private String netManageId;

    private List<BaseTypeInnerDTO> baseTypeList;

}
