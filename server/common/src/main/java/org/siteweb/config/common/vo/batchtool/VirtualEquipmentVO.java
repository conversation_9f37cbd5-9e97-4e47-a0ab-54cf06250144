package org.siteweb.config.common.vo.batchtool;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblEquipment;

@Data
@NoArgsConstructor
public class VirtualEquipmentVO {

    public VirtualEquipmentVO(TblEquipment equipment) {
        this.equipmentId = equipment.getEquipmentId();
        this.equipmentName = equipment.getEquipmentName();
        this.property = equipment.getProperty();
    }

    /**
     * 设备属性
     */
    @JsonIgnore
    private String property;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 是否虚拟设备
     */
    private Boolean isVirtualEquipment;

    public Boolean getIsVirtualEquipment() {
        if (CharSequenceUtil.contains(property, "8")) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
