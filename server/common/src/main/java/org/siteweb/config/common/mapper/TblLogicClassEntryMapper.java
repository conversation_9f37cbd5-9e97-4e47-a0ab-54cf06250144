package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblLogicClassEntry;

@Mapper
public interface TblLogicClassEntryMapper extends BatchBaseMapper<TblLogicClassEntry> {

    void updateLogicClassByDataItem(int standardType, int entryId);
    Integer maxEntryId();

    Integer maxLogicClassId(@Param("standardType") Integer standardType, @Param("entryCategory") Integer entryCategory);
}
