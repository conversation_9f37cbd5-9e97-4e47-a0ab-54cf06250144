package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.batchtool.EquipmentSignalPropertyDTO;
import org.siteweb.config.common.entity.TblSignalProperty;

import java.util.List;

/**
 * <AUTHOR> (2024-03-09)
 **/

@Mapper
public interface TblSignalPropertyMapper extends BatchBaseMapper<TblSignalProperty> {

    void batchInsertLianTongSignalProperties();


    /**
     * 根据信号批量删除数据
     *
     * @param equipmentTemplateId 设备模板id
     * @param signalIds           信号id集合
     */
    void batchDeleteBySignal(@Param("equipmentTemplateId") int equipmentTemplateId, @Param("signalIds") List<Integer> signalIds);

    List<EquipmentSignalPropertyDTO> findSignalPropertiesByEquipmentIdAndSignalIds(@Param("equipmentId") Integer equipmentId, @Param("signalIds") List<Integer> signalIds);

}
