package org.siteweb.config.common.change;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttMessage;

/**
 * MQTT 变更消息侦听器，负责侦听来自MQTT的变更消息，并放到messageScheduler的队列内
 *
 * <AUTHOR> (2024-02-22)
 **/
@Slf4j
public class ChangeMessageListener implements MqttCallbackExtended{


    private final ChangeMessageScheduler messageScheduler;
    private final MqttAsyncClient mqttClient;


    public ChangeMessageListener(MqttAsyncClient mqttClient, ChangeMessageScheduler messageScheduler) {
        this.mqttClient = mqttClient;
        this.messageScheduler = messageScheduler;
    }


    /**
     *
     * @see  LocalCacheEventListener 本地不需要消费mqtt了，直接去更新缓存
     * @param topic   /域/信道/产品/数据源/操作/主键？
     * @param message mq
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        log.debug("MQTT => Topic: {}", topic);
        // gateway/config/siteweb/tbl_equipment/add/300000031
        // 采用本地事件更新缓存，本地不再消费mqtt
        // pushRcord(topic, message);
    }

    private void pushRcord(String topic, MqttMessage message) throws InterruptedException {
        String[] fragments = topic.split("/");
        if (fragments.length >= 5 && fragments[0].equals("gateway")) {
            ChangeRecord cr = new ChangeRecord();
            cr.setChannel(fragments[1]);
            cr.setProduct(fragments[2]);
            cr.setDataSource(fragments[3]);
            cr.setOperator(fragments[4]);
            if (fragments.length > 5) {
                cr.setPrimaryKey(fragments[5]);
            }
            cr.setMessage(message);
            messageScheduler.putRecord(cr);
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        log.debug("MQTT: 消息已发送");
    }



    @Override
    public void connectionLost(Throwable throwable) {
        log.error("MQTT: 已断开, ClientID=[{}]", mqttClient.getClientId());
    }


    @Override
    public void connectComplete(boolean b, String s) {
        log.debug("MQTT: 已连接, ClientID=[{}]", mqttClient.getClientId());
    }
}
