package org.siteweb.config.common.dto;

import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-03-27)
 **/
@Data
public class CreateMonitorUnitDTO {
    private Integer resourceStructureId;

    @Size(max = 100, message = "监控单元名称长度不能超过100")
    private String monitorUnitName;
    private String ipAddress;
    private Integer monitorUnitCategory;

    private String description;

    private String rdsServer;
    private String dataServer;
    private Integer workStationId;

    private String contractNo;
    private String projectName;
    private Integer stationId;


    public MonitorUnitDTO toMonitorUnit(Integer stationId) {
        MonitorUnitDTO monitorUnit = new MonitorUnitDTO();
        monitorUnit.setStationId(stationId);
        monitorUnit.setMonitorUnitName(this.monitorUnitName);
        monitorUnit.setIpAddress(this.ipAddress);
        monitorUnit.setMonitorUnitCategory(this.monitorUnitCategory);
        monitorUnit.setDescription(this.description);
        monitorUnit.setRunMode(1);
        monitorUnit.setRdsServer(this.rdsServer);
        monitorUnit.setDataServer(this.dataServer);
        monitorUnit.setWorkStationId(this.workStationId);
        monitorUnit.setCanDistribute(true);
        monitorUnit.setIsSync(false);
        monitorUnit.setIsConfigOK(false);
        monitorUnit.setAppConfigId(1);
        monitorUnit.setEnable(true);
        monitorUnit.setFsu(false);
        monitorUnit.setSoftwareVersion("");
        monitorUnit.setConnectState(2);
        monitorUnit.setProjectName(this.projectName);
        monitorUnit.setContractNo(this.contractNo);
        monitorUnit.setInstallTime(LocalDateTime.now());
        return monitorUnit;
    }


}
