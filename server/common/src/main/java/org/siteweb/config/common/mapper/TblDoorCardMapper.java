package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblDoorCard;

import java.util.List;

@Mapper
public interface TblDoorCardMapper extends BatchBaseMapper<TblDoorCard> {
    void deleteByEquipmentId(@Param("equipmentId") Integer equipmentId);

    List<Integer> findCardIdByEquipmentId(@Param("equipmentId") Integer equipmentId);
}
