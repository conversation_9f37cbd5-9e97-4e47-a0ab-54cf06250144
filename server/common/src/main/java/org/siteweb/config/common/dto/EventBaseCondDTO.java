package org.siteweb.config.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Creation Date: 2024/5/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EventBaseCondDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件条件id
     */
    private Integer eventConditionId;

    /**
     * 事件名
     */
    @JsonIgnore
    private String eventName;

    /**
     * 基类id
     */
    private Long baseTypeId;


}
