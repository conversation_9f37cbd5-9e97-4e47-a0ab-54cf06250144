package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description: 精简的事件条件列表(告警过滤表达式用)
 *
 * <AUTHOR>
 * Creation Date: 2024/4/17
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SimplifyEventAndConditionDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件名称
     */
    private String eventName;
    private Integer eventConditionId;
    private String meanings;
}
