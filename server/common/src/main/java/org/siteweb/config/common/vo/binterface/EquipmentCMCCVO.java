package org.siteweb.config.common.vo.binterface;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentCMCCVO {
    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备ID（字符串）
     */
    private String deviceID;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * FSUID
     */
    private String fsuId;

    /**
     * 站点ID（字符串）
     */
    private String siteID;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 房间ID
     */
    private String roomID;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 设备类型Id
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备子类型id
     */
    private Integer deviceSubType;

    /**
     * 设备子类型名称
     */
    private String deviceSubTypeName;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 额定容量
     */
    private Double ratedCapacity;

    /**
     * 版本
     */
    private String version;

    /**
     * 开始运行时间
     */
    private LocalDateTime beginRunTime;

    /**
     * 设备描述
     */
    private String devDescribe;
}
