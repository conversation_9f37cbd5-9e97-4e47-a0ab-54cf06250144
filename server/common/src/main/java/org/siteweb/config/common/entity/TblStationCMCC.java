package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_StationCMCC")
public class TblStationCMCC {
    /**
     * 局站ID
     */
    @TableId(type = IdType.INPUT)
    private Integer stationId;

    /**
     * 站点编号
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点描述
     */
    private String description;
}
