package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 事件(告警)基类字典列表
 *
 * <AUTHOR> (2024/4/26)
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventBaseDetailDTO {
    /**
     * 事件/告警基础类别id
     */
    private Long baseTypeId;
    /**
     * 告警基类名
     */
    private String baseTypeName;
    /**
     * 基类设备ID
     */
    private Integer baseEquipmentId;
    /**
     * 基础类别名称(大类名称)
     */
    private String baseClassName;
    /**
     * 基础设备子类名称(设备类型)
     */
    private String baseEquipmentName;
    /**
     * 告警基类名（英文）
     */
    private String englishName;
    /**
     * 告警等级ID 字典23
     */
    private Integer eventSeverityId;
    /**
     * 事件等级
     */
    private String eventSeverityName;
    /**
     * 告警开始值
     */
    private Double comparedValue;
    /**
     * 告警逻辑分类
     */
    private Integer baseLogicCategoryId;
    /**
     * 告警逻辑分类名称
     */
    private String baseLogicCategoryName;
    /**
     * 告警开始延时
     */
    private Integer startDelay;
    /**
     * 告警结束延时
     */
    private Integer endDelay;
    /**
     * 扩展信息1
     */
    private String extendField1;
    /**
     * 扩展信息2
     */
    private String extendField2;
    /**
     * 扩展信息3
     */
    private String extendField3;
    /**
     * 扩展信息4
     */
    private String extendField4;
    /**
     * 扩展信息5
     */
    private String extendField5;
    /**
     * 扩展信息
     */
    private String baseNameExt;
    /**
     * 是否为系统
     */
    private Boolean isSystem;
    /**
     * 描述信息
     */
    private String description;

    /**
     * 事件条件编码
     */
    private String eventConditionCode;
    /**
     * 事件条件类别
     */
    private String eventConditionCategory;

    /**
     * 实际事件曾用名 TODO 从BaseExpertSystem.xml获取 涉及到专家系统文件
     */
    private String oldBaseTypeName;

}
