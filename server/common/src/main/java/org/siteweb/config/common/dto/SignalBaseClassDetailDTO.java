package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基类信号详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignalBaseClassDetailDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    /**
     * 父模板id
     */
    private Integer parentTemplateId;
    /**
     * 父模板名
     */
    private String parentTemplateName;
    /**
     * 协议代码【与采集器关联】
     */
    private String protocolCode;
    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 信号名
     */
    private String signalName;
    /**
     * 含义
     */
    private String meanings;

    /**
     *  字典17 信号种类 2 开关量 1模拟量
     */
    private Integer signalCategory;
    /**
     * 种类名称
     */
    private String signalCategoryName;

    /**
     * 字典18 信号分类 采集信号 虚拟信号
     */
    private Integer signalType;
    /**
     * 信号分类名称
     */
    private String signalTypeName;
    /**
     * 通道
     */
    private Integer channelNo;
    /**
     * 单位
     */
    private String unit;
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 基类扩展表达式
     */
    private String baseNameExt;
    /**
     * 基类含义id
     */
    private Integer baseCondId;
    /**
     * 基类含义
     */
    private String baseMeaning;

    private Integer stateValue;
    /**
     * 基类情况 已检查
     */
    private String subState;

}
