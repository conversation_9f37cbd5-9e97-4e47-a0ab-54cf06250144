package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tsl_monitorunitcmcc")
public class TslMonitorUnitCMCC {

    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * FSU编号
     */
    private String fsuID;

    /**
     * FSU名称
     */
    private String fsuName;

    /**
     * 站点编号
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 房间编号
     */
    private String roomID;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String passWord;

    /**
     * FSU IP地址
     */
    private String fsuIP;

    /**
     * FSU MAC地址
     */
    private String fsuMAC;

    /**
     * FSU 版本
     */
    private String fsuVER;

    /**
     * 结果
     */
    private Integer result;

    /**
     * 失败原因
     */
    private String failureCause;

    /**
     * CPU使用率
     */
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    private Double memUsage;

    /**
     * 硬盘使用率
     */
    private Double hardDiskUsage;

    /**
     * 获取FSU信息结果
     */
    private Integer getFSUInfoResult;

    /**
     * 获取FSU失败原因
     */
    private String getFSUFaliureCause;

    /**
     * 获取FSU时间
     */
    private Date getFSUTime;

    /**
     * FTP用户名
     */
    private String ftpUserName;

    /**
     * FTP密码
     */
    private String ftpPassWord;

    /**
     * 扩展字段1
     */
    private String extendField1;

    /**
     * 扩展字段2
     */
    private String extendField2;

    /**
     * 获取配置标志
     */
    private Integer getConfigFlag;
}
