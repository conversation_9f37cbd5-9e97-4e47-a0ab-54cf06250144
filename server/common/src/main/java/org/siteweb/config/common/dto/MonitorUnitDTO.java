package org.siteweb.config.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.siteweb.config.common.annotation.OperationLogDefine;
import org.siteweb.config.common.entity.TslMonitorUnit;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-04-09)
 **/

@Data
@EqualsAndHashCode(callSuper = true)
@OperationLogDefine(prefix = "monitorunit", ignoreFields = {"monitorUnitCode", "runMode", "configFileCode", "configUpdateTime", "sampleConfigCode", "softwareVersion", "description", "startTime", "heartbeatTime", "connectState", "updateTime", "isSync", "syncTime", "isConfigOK", "configFileCode_Old", "sampleConfigCode_Old", "appConfigId", "canDistribute", "enable", "installTime", "fsu","resourceStructureName"}, operationObjectType = OperationObjectTypeEnum.MONITOR_UNIT, objectId = {"monitorUnitId"})
public class MonitorUnitDTO  extends TslMonitorUnit {
    private String projectName;
    private String contractNo;
    private LocalDateTime installTime;
    @TableField(exist = false)
    private Integer state;
    @TableField(exist = false)
    private String portNos;
    @TableField(exist = false)
    private String workStationName;

    @TableField(exist = false)
    private String stationName;

    public TslMonitorUnit toEntity(MonitorUnitDTO monitorUnitDTO){
        TslMonitorUnit tslMonitorUnit = new TslMonitorUnit();
        tslMonitorUnit.setMonitorUnitName(monitorUnitDTO.getMonitorUnitName());
        tslMonitorUnit.setRunMode(monitorUnitDTO.getRunMode()== null ? 1 : monitorUnitDTO.getRunMode());
        tslMonitorUnit.setSoftwareVersion(monitorUnitDTO.getSoftwareVersion());
        tslMonitorUnit.setDescription(monitorUnitDTO.getDescription());
        tslMonitorUnit.setEnable(monitorUnitDTO.getEnable());
        return tslMonitorUnit;
    }
}
