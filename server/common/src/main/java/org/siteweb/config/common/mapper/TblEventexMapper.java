package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblEventex;

import java.util.List;

/**
 * <AUTHOR> (2024-04-15)
 **/
@Mapper
public interface TblEventexMapper extends BatchBaseMapper<TblEventex> {

    void batchDelete(@Param("eventexList") List<TblEventex> eventexList);
}
