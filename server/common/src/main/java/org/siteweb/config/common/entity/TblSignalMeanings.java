package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024/3/9)
 */
@Data
@ConfigId(8)
@TableName("TBL_SignalMeanings")
@NoArgsConstructor
@AllArgsConstructor
public class TblSignalMeanings {
    public TblSignalMeanings(Integer equipmentTemplateId, Integer signalId, Integer stateValue, String meanings) {
        this.equipmentTemplateId = equipmentTemplateId;
        this.signalId = signalId;
        this.stateValue = stateValue;
        this.meanings = meanings;
    }

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer signalId;
    private Integer stateValue;
    private String meanings;
    private Integer baseCondId;
}
