package org.siteweb.config.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.sensitive.serializer.StringToListOfIntegerDeserializer;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperationDetailDTO {
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 操作人id
     */
    private Integer userId;
    /**
     * 对象主键id
     */
    private String objectId;
    /**
     * 对象类型
     */
    @JsonDeserialize(using = StringToListOfIntegerDeserializer.class)
    private List<Integer> objectTypes;
    /**
     * 属性名称
     */
    private String propertyName;
    /**
     * 操作类型 OperationObjectTypeEnum
     */
    private String operationType;
}
