package org.siteweb.config.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblStatusBaseDic;

import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommandBaseDetailDTO {
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 设备基类id
     */
    private Integer baseEquipmentId;
    /**
     * 基础类别名称(大类名称)
     */
    private String baseClassName;
    /**
     * 基础设备子类名称(设备类型)
     */
    private String baseEquipmentName;
    /**
     * 英文名
     */
    private String englishName;
    /**
     * 逻辑分类id
     */
    private Integer baseLogicCategoryId;
    /**
     * 控制逻辑分类名称
     */
    private String baseLogicCategoryName;
    /**
     * 控制类型 2  '遥控' ； 1  '遥调'
     */
    private Integer commandType;
    /**
     * 控制类型名称
     */
    private String commandTypeName;
    /**
     * 基类状态
     */
    private Integer baseStatusId;
    /**
     * 扩展字段3
     */
    private String  extendField1;
    /**
     * 扩展字段2
     */
    private String extendField2;
    /**
     * 扩展字段3
     */
    private String extendField3;
    /**
     * 基类扩展名
     */
    private String baseNameExt;
    /**
     * 是否为系统
     */
    private Boolean isSystem;
    /**
     * 描述
     */
    private String description;

    /**
     * 控制编码
     */
    private String commandCode;
    /**
     * 控制类别
     */
    private String commandCategory;
    /**
     * 实际控制曾用名 TODO 从BaseExpertSystem.xml获取 涉及到专家系统文件
     */
    private String oldBaseTypeName;
    /**
     * 状态名
     */
    private String baseStatusName;
    /**
     * 条件id
     */
    private Integer baseCondId;
    /**
     * 符号
     */
    private String operator;
    /**
     * 值
     */
    private Integer value;
    /**
     * 含义
     */
    private String meaning;
    /**
     * 状态描述
     */
    private String statusDescription;
    /**
     * 实际信号含义曾用名 TODO 从BaseExpertSystem.xml获取 涉及到专家系统文件
     */
    private String oldMeaning;

    @JsonIgnore
    public void fetchStatusBaseDic(TblStatusBaseDic statusBaseDic) {
        if (Objects.isNull(statusBaseDic)) {
            return;
        }
        this.setBaseStatusName(statusBaseDic.getBaseStatusName());
        this.setBaseCondId(statusBaseDic.getBaseCondId());
        this.setOperator(statusBaseDic.getOperator());
        this.setValue(statusBaseDic.getValue());
        this.setMeaning(statusBaseDic.getMeaning());
        this.setStatusDescription(statusBaseDic.getDescription());
    }

}
