package org.siteweb.config.common.dto.batchtool;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;


/**
 * 配置管理更新Dto
 *
 * <AUTHOR>
 * @date 2022/03/30
 */
@Data
public class EquipmentSettingUpdateDTO {
    /**
     * 端口设置
     */
    private Integer equipmentId;
    /**
     * 端口id
     */
    private Integer portId;
    /**
     * 采集单元ID
     */
    private Integer samplerUnitId;

    /**
     * 监控单元id
     */
    private Integer monitorUnitId;
    /**
     * 端口设置
     */
    @JsonIgnore
    private String setting;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 端口号/读写属性
     */
    private String portAttribute;
    /**
     * 采集单元地址
     */
    private Integer address;
    /**
     * 采集动态库地址
     */
    private String dllPath;
    /**
     * 采集周期
     */
    private Double spUnitInterval;
    /**
     * 驱动模板id不能为空
     */
    private Integer driveTemplateId;
}
