package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.annotation.ChangeSource;
import org.siteweb.config.common.annotation.ConfigId;

/**
 * <AUTHOR> (2024-03-09)
 **/
@Data
@ConfigId(14)
@TableName(value = "TBL_ControlMeanings", autoResultMap = true)
@ChangeSource(channel = "config", product = "siteweb", source = "tbl_controlmeanings")
@NoArgsConstructor
public class TblControlMeanings {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    private Integer parameterValue;
    private String meanings;
    private Integer baseCondId;
}
