package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class StandardPointMappingDTO {
    /**
     * 类型 1 信号；2 事件；3 控制
     */
    private Integer type;
    /**
     * 需要映射的设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 需要映射的(信号,事件,控制)id
     */
    private Integer mappingId;
    /**
     * 需要映射的(信号,事件,控制)id
     */
    private List<Integer> mappingIds;
    /**
     * 设备类型id
     */
    private Integer equipmentCategoryId;
    /**
     * 测点ID
     */
    private Integer pointId;
    /**
     * 扩展起始数字 有扩展字段输入的数字 默认拼接001
     */
    private Integer extendStartNum;

}
