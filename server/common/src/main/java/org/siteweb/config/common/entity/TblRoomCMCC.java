package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_RoomCMCC")
public class TblRoomCMCC {
    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 房屋ID
     */
    private Integer houseId;

    /**
     * 房间编号
     */
    private String roomID;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 站点编号
     */
    private String siteID;

    /**
     * 房间描述
     */
    private String description;
}
