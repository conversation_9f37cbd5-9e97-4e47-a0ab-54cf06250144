package org.siteweb.config.common.dto.batchtool;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblEquipment;

/**
 * <AUTHOR>
 * @date 2022/07/29
 */
@NoArgsConstructor
@Data
public class MonitorSignalUnitSaveDTO {
    public MonitorSignalUnitSaveDTO(TblEquipment virtualEquipment, Integer signalId, Integer virtualSignalId, Integer equipmentId) {
        this.virtualEquipment = virtualEquipment;
        this.signalId = signalId;
        this.virtualSignalId = virtualSignalId;
        this.equipmentId = equipmentId;
    }

    /**
     * 虚拟设备实体
     */
    private TblEquipment virtualEquipment;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 虚拟信号id
     */
    private Integer virtualSignalId;
    /**
     * 设备id
     */
    private Integer equipmentId;
}
