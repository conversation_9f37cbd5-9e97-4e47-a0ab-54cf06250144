package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class CopyEquipmentTemplateUpdateDTO {
    /**
     * 原始设备模板id
     */
    private Integer originEquipmentTemplateId;
    /**
     * 新设备模板名称
     */
    private String newEquipmentTemplateName;
    /**
     * 复制原因
     */
    private String reason;

    private ControlDTO control;
}
