package org.siteweb.config.common.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("TBL_ConfigChangeMicroLog")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TblConfigChangeMicroLog {
    private String objectId;
    private Integer configId;
    private Integer editType;
    private LocalDateTime updateTime;
}
