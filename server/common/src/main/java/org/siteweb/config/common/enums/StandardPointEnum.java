package org.siteweb.config.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测点类型枚举 信号(1=AI; 2=DI); 控制(3=AO; 4=DO);  事件5=Alarm
 */
@Getter
@AllArgsConstructor
public enum StandardPointEnum {

    AI(1,"AI"),
    DI(2,"DI"),
    AO(3,"AO"),
    DO(4,"DO"),
    ALARM(5,"Alarm")
    ;

    private final int value;
    private final String name;

    public static String getNameByValue(Integer value) {
        for (StandardPointEnum type : StandardPointEnum.values()) {
            if (type.getValue() == value) {
                return type.getName();
            }
        }
        return null;
    }
}
