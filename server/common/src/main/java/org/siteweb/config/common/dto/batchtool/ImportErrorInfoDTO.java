package org.siteweb.config.common.dto.batchtool;

import lombok.Data;

@Data
public class ImportErrorInfoDTO {
    private Integer rowIndex;
    private String errorName;
    private String message;

    public static ImportErrorInfoDTO createImportErrorInfo(Integer rowIndex, String errorName, String message) {
        ImportErrorInfoDTO importErrorInfo = new ImportErrorInfoDTO();
        importErrorInfo.setRowIndex(rowIndex);
        importErrorInfo.setErrorName(errorName);
        importErrorInfo.setMessage(message);
        return importErrorInfo;
    }


}
