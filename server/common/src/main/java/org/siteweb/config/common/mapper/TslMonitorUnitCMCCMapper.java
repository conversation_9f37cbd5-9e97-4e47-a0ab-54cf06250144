package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TslMonitorUnitCMCC;
import org.siteweb.config.common.vo.binterface.MonitorUnitCMCCVO;

import java.util.List;

@Mapper
public interface TslMonitorUnitCMCCMapper extends BatchBaseMapper<TslMonitorUnitCMCC> {
    List<TslMonitorUnitCMCC> findMonitorUnitsByCategory(@Param("categoryId") Integer categoryId);

    List<MonitorUnitCMCCVO> findAll();

    int updateEntity(@Param("tslMonitorUnitCMCC") TslMonitorUnitCMCC tslMonitorUnitCMCC);

    void updateSiteIdByStationId(@Param("siteId") String siteId, @Param("stationId") Integer stationId);

    void updateMonitorUnitNameInSiteWeb(@Param("suName") String suName, @Param("suIp") String suIp, @Param("stationId") Integer stationId, @Param("monitorUnitId") Integer monitorUnitId);
}
