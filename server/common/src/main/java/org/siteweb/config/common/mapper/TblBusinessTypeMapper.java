package org.siteweb.config.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.siteweb.config.common.entity.TblBusinessType;
import org.siteweb.config.common.vo.BusinessExpressionVO;
import org.siteweb.config.common.vo.BusinessTypeExpressionVO;

import java.util.List;

@Mapper
public interface TblBusinessTypeMapper extends BaseMapper<TblBusinessType> {


    List<TblBusinessType> findTblBusinessTypeByMonitorUnitId(Integer monitorUnitId);


    List<BusinessTypeExpressionVO> findDistinctBusinessTypesAndExpressionsForMonitorUnit(Integer monitorUnitId);

    List<BusinessExpressionVO> findDistinctBusinessExpressionsForMonitorUnit(Integer monitorUnitId);

    List<BusinessExpressionVO> findBusinessExpressionVOByStationId(Integer stationId);
}
