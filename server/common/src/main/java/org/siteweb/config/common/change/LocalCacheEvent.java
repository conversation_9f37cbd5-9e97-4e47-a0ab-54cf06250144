package org.siteweb.config.common.change;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.context.ApplicationEvent;

/**
 * 本地缓存事件通知
 *
 * <AUTHOR>
 * Creation Date: 2024/6/3
 */
@Getter
@Setter
@ToString
public class LocalCacheEvent extends ApplicationEvent {
    private String topic;
    private MqttMessage mqttMessage;


    public LocalCacheEvent(Object source, String topic, MqttMessage mqttMessage) {
        super(source);
        this.topic = topic;
        this.mqttMessage = mqttMessage;

    }
}
