package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Author: <EMAIL>
 * Creation Date: 2024/5/13
 */
@Data
@TableName("tbl_basesignaleventcode")
public class TblBaseSignalEventCode {
    @TableId
    private Integer codeId;
    private String category;
    @TableField(value = "SIGNAL", keepGlobalFormat = true)
    private String signal;
    private String event;
    private String description;

}
