package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.*;
import org.siteweb.config.common.dto.excel.ControlExcel;
import org.siteweb.config.common.entity.TblControl;
import org.siteweb.config.common.vo.ControlVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (2024-03-09)
 **/

@Mapper
public interface TblControlMapper extends BatchBaseMapper<TblControl> {
    void createControl(@Param("control") TblControl control);

    List<Long> findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    Integer findMaxControlIdByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    Integer findMaxDisplayIndexByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    int deleteControl(@Param("equipmentTemplateId") int equipmentTemplateId, @Param("controlId") int controlId);

    int batchInsertControl(@Param("controlList") List<TblControl> controlList);

    List<TblControl> findByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<ControlVO> findVoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<ControlConfigItem> findAllControlItem();

    List<ControlConfigItem> findControlItemByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
    List<ControlConfigItem> findControlItemByEquipmentTemplateIds(@Param("equipmentTemplateIds") Collection<Integer> equipmentTemplateIds);

    ControlConfigItem findByEquipmentTemplateIdAndControlId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("controlId") Integer controlId);

    void batchInsertLianTongControls();

    List<TblControl> diffControl(@Param("oldEquipmentTemplateId") Integer oldEquipmentTemplateId, @Param("newEquipmentTemplateId") Integer newEquipmentTemplateId);

    /**
     * 获取控制进度数据
     */
    List<ControlProgressDTO> getControlProgressList();

    /**
     * 重写insert,字段中存在反引号关键字
     */
    int insertControl(TblControl control);

    /**
     * 重写update,字段中存在反引号关键字
     */
    int updateControl(TblControl control);

    /**
     * 获取基类控制
     */
    List<ControlBaseClassDetailDTO> findControlBaseClass(@Param("equipmentBaseType") Integer equipmentBaseType);
    /**
     * 获取基类控制详情
     */
    List<ControlBaseClassDetailDTO> findControlBaseClassDetails(@Param("equipmentBaseType") Integer equipmentBaseType, @Param("controlName") String controlName, @Param("meanings") String meanings);

    List<IdValueDTO<String, String>> findNamesByIds(@Param("controlUniqueIds") List<Map<String, String>> controlUniqueIds);

    /**
     * 获取应用标准化数据
     */
    List<ControlApplyStandardDTO> getApplyStandards(@Param("standardId") Integer standardId, @Param("equipmentTemplateIds") List<Integer> equipmentTemplateIds);

    /**
     * 还原标准化
     */
    long restoreStandard();
    /**
     * 获取标准化控制比较数据
     */
    List<StandardControlCompareDTO> getStandardCompareData();

    /**
     * 获取控制应用标准化检查
     */
    List<StandardApplyControlCheckDTO> getControlStandardApplyCheckData(@Param("standardId") Integer standardId);

    /**
     * 获取控制映射标准化检查
     */
    List<StandardMappingControlCheckDTO> getControStandardMappingCheck(@Param("standardId") Integer standardId, @Param("equipmentCategory") Integer equipmentCategory);

    List<TblControl> findByEquipmentCategory(Integer equipmentCategory);

    int batchUpdateField(@Param("controlList") List<TblControl> controlList);

    List<ControlExcel> findExcelDtoByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);
}
