package org.siteweb.config.common.enums;


/**
 * <AUTHOR> (2024-03-09)
 **/
public enum StructureTypes {
    /**
     * 根？ 是否还有其他表？
     */
    ROOT_STATION_STRUCTURE(102),
    /**
     * 分组结构表
     */
    STATION_STRUCTURE(103),
    /**
     * 基站表
     */
    STATION(104),
    /**
     * 房间
     */
    HOUSE(105);

    private final Integer value;

    StructureTypes(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}