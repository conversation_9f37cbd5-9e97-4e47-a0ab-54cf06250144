package org.siteweb.config.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 驱动结构占位符对象
 *
 * <AUTHOR> (2024-05-16 19:06)
 */
@Data
@NoArgsConstructor
public class DriveStructurePlaceholder {
    /**
     * 字段属性值
     */
    private String field;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 描述信息
     */
    private String desc;

    public DriveStructurePlaceholder(String field, String fieldName) {
        this.field = field;
        this.fieldName = fieldName;
        this.desc = String.format("%s (%s)", fieldName, field);
    }
}
