package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基类控制详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ControlBaseClassDetailDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称
     */
    private String equipmentTemplateName;
    /**
     * 父模板id
     */
    private Integer parentTemplateId;
    /**
     * 父模板名
     */
    private String parentTemplateName;
    /**
     * 协议代码【与采集器关联】
     */
    private String protocolCode;
    /**
     * 基类设备类型
     */
    private Integer equipmentBaseType;
    /**
     * 基类设备类型名称
     */
    private String equipmentBaseTypeName;
    /**
     * 控制id
     */
    private Integer controlId;
    /**
     * 控制名
     */
    private String controlName;
    /**
     * 含义
     */
    private String meanings;

    /**
     * 控制类型 2  '遥控' ； 1  '遥调' 字典32
     */
    private Integer commandType;
    /**
     * 控制命令类型
     */
    private String commandTypeName;

    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 基类扩展表达式
     */
    private String baseNameExt;
    /**
     * 基类含义id
     */
    private Integer baseCondId;
    /**
     * 基类含义
     */
    private String baseMeaning;
    /**
     * 命令字符串
     */
    private String cmdToken;

    /**
     * tbl_controlmeanings value
     */
    private Integer parameterValue;
    /**
     * 基类情况 已检查
     */
    private String subState;

}
