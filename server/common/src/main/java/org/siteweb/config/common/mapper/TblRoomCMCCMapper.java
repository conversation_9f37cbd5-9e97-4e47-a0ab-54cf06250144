package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.entity.TblRoomCMCC;
import org.siteweb.config.common.vo.binterface.RoomCMCCVO;

import java.util.List;

@Mapper
public interface TblRoomCMCCMapper extends BatchBaseMapper<TblRoomCMCC> {
    List<TblRoomCMCC> findHousesByCategory(@Param("categoryId") Integer categoryId);

    List<RoomCMCCVO> findAll();

    void updateSiteIdByStationId(@Param("siteId") String siteId, @Param("stationId") Integer stationId);
}
