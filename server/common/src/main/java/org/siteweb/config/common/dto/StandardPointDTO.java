package org.siteweb.config.common.dto;

import lombok.Data;
import org.siteweb.config.common.entity.StandardPoint;

/**
 * 字节标准测点
 */
@Data
public class StandardPointDTO {
    /**
     * 设备类型id
     */
    private Integer equipmentCategoryId;

    /**
     * 测点ID
     */
    private Integer pointId;

    /**
     * 测点名称
     */
    private String pointName;

    /**
     * 测点扩展名称
     */
    private String extendName;

    /**
     * 测点类型: 信号(1=AI; 2=DI); 控制(3=AO; 4=DO);  事件5=Alarm
     */
    private Integer type;
    /**
     * 测点类型名称
     */
    private String typeName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 精度
     */
    private Float precisions;

    /**
     * 基础点位
     */
    private Boolean basicPoint;

    /**
     * 阈值
     */
    private String thresholdValue;

    /**
     * 点位工作区间
     */
    private String workTnterval;

    /**
     * 告警等级
     */
    private Integer alarmLevel;

    /**
     * 点位数量
     */
    private Integer pointNumber;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 告警次数
     */
    private Integer alarmNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 测点英文名称
     */
    private String englishName;

    /**
     * 映射id(信号、事件、控制)
     */
    private String mappingId;

    /**
     * 映射名称
     */
    private String mappingName;

    public static StandardPointDTO toStandardPointDTO(StandardPoint standardPoint) {
        StandardPointDTO standardPointDTO = new StandardPointDTO();
        standardPointDTO.setEquipmentCategoryId(standardPoint.getEquipmentCategoryId());
        standardPointDTO.setPointId(standardPoint.getPointId());
        standardPointDTO.setPointName(standardPoint.getPointName());
        standardPointDTO.setExtendName(standardPoint.getExtendName());
        standardPointDTO.setType(standardPoint.getType());
        standardPointDTO.setUnit(standardPoint.getUnit());
        standardPointDTO.setPrecisions(standardPoint.getPrecisions());
        standardPointDTO.setBasicPoint(standardPoint.getBasicPoint());
        standardPointDTO.setThresholdValue(standardPoint.getThresholdValue());
        standardPointDTO.setWorkTnterval(standardPoint.getWorkTnterval());
        standardPointDTO.setAlarmLevel(standardPoint.getAlarmLevel());
        standardPointDTO.setPointNumber(standardPoint.getPointNumber());
        standardPointDTO.setCycle(standardPoint.getCycle());
        standardPointDTO.setAlarmNumber(standardPoint.getAlarmNumber());
        standardPointDTO.setRemark(standardPoint.getRemark());
        standardPointDTO.setEnglishName(standardPoint.getEnglishName());
        return standardPointDTO;

    }
}
