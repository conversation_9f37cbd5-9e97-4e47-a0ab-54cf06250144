package org.siteweb.config.common.vo.batchtool;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class VirtualEquipmentDetailVO extends VirtualEquipmentBasicInfoVO {
    /**
     * 虚拟信号名
     */
    private String virtualSignalName;
    /**
     * 源设备名
     */
    private String equipmentName;
    /**
     * 源设备信号名
     */
    private String signalName;
    /**
     * 源设备信号通道
     */
    private Integer channelNo;
}
