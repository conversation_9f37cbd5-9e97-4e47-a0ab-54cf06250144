package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.SignStatusMeaningDTO;
import org.siteweb.config.common.entity.TblStatusBaseDic;

import java.util.List;

/**
 * <AUTHOR> (2024-04-02)
 **/
@Mapper
public interface TblStatusBaseDicMapper extends BatchBaseMapper<TblStatusBaseDic> {

    /**
     * 获取信号基类状态含义
     */
    List<SignStatusMeaningDTO> findEquipmentBaseTypeMeaningsBySignal(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("signalId") Integer signalId);

    List<SignStatusMeaningDTO> findEquipmentBaseTypeMeaningsByControl(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("controlId") Integer controlId);
}
