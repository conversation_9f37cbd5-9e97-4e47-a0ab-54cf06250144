package org.siteweb.config.common.dto;


import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.siteweb.config.common.annotation.OpLogDic;
import org.siteweb.config.common.annotation.OperationLogDefine;
import org.siteweb.config.common.entity.TblControlMeanings;
import org.siteweb.config.common.enums.DataEntryEnum;
import org.siteweb.config.common.enums.OpLogDicEnum;
import org.siteweb.config.common.enums.OperationObjectTypeEnum;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/4/25
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldNameConstants
@OperationLogDefine(prefix = "control", ignoreFields = {"id", "controlMeaningsList", "description", "baseTypeName", "baseStatusId", "baseNameExt"}, operationObjectType = OperationObjectTypeEnum.CONTROL, objectId = {"equipmentTemplateId", "controlId"})
public class ControlConfigItem {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    @Size(max = 128, message = "控制名称长度不能超过128")
    private String controlName;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.COMMAND_CATEGORY)
    private Integer controlCategory;
    private String cmdToken;
    @OpLogDic(value = OpLogDicEnum.COMMAND_BASE_DIC)
    private Long baseTypeId;
    private String baseTypeName;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.COMMAND_SEVERITY)
    private Integer controlSeverity;
    @OpLogDic(value = OpLogDicEnum.SIGNAL, parameterValue = Fields.equipmentTemplateId)
    private Integer signalId;
    private Double timeOut;
    private Integer retry;
    private String description;
    private Boolean enable;
    private Boolean visible;
    private Integer displayIndex;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.COMMAND_TYPE)
    private Integer commandType;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.CONTROL_TYPE)
    private Short controlType;
    @OpLogDic(value = OpLogDicEnum.DATA_ITEM, dicParameterValue = DataEntryEnum.DATA_TYPE)
    private Short dataType;
    private Double maxValue;
    private Double minValue;
    private Double defaultValue;
    private Integer moduleNo;
    private Integer baseStatusId;
    private String baseNameExt;
    private List<TblControlMeanings> controlMeaningsList;


    @JsonIgnore
    public String getControlKey() {
        return this.equipmentTemplateId + "." + this.controlId;
    }

    @JsonIgnore
    public List<TblControlMeanings> getNotEmptyControlMeaningsList() {
        if (CollUtil.isEmpty(this.controlMeaningsList)) {
            return List.of(new TblControlMeanings());
        }
        return controlMeaningsList;
    }

}
