package org.siteweb.config.common.utils;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class ImprovedIPValidator {

    /**
     * 校验 IPv4 地址格式（不允许前导零）
     * @param ip IP 地址字符串
     * @return 是否为有效的 IPv4 地址
     */
    public static boolean isValidIpv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // 检查是否为有效的 IPv4 地址
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            if (!(inetAddress instanceof Inet4Address)) {
                return false;
            }

            // 检查是否包含前导零
            String[] octets = ip.split("\\.");
            if (octets.length != 4) {
                return false;
            }

            for (String octet : octets) {
                // 检查前导零
                if (octet.length() > 1 && octet.startsWith("0")) {
                    return false;
                }

                // 额外检查每个八位字节的范围
                int value = Integer.parseInt(octet);
                if (value < 0 || value > 255) {
                    return false;
                }
            }

            return true;
        } catch (UnknownHostException | NumberFormatException e) {
            return false;
        }
    }

    /**
     * 校验 IPv6 地址格式
     * @param ip IP 地址字符串
     * @return 是否为有效的 IPv6 地址
     */
    public static boolean isValidIpv6(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            return inetAddress instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 标准化 IPv6 地址格式以便比较
     * @param ipv6 IPv6 地址字符串
     * @return 标准化后的 IPv6 地址
     */
    public static String normalizeIpv6(String ipv6) {
        if (!isValidIpv6(ipv6)) {
            return ipv6; // 如果不是有效的 IPv6，则返回原始字符串
        }

        try {
            // 使用 InetAddress 解析，然后获取标准格式
            InetAddress inetAddress = InetAddress.getByName(ipv6);
            String normalizedIP = inetAddress.getHostAddress();

            // 对于 IPv6，我们可以将所有地址转换为小写的标准格式
            return normalizedIP.toLowerCase();
        } catch (UnknownHostException e) {
            return ipv6; // 解析失败时返回原始字符串
        }
    }

    /**
     * 检查两个 IPv6 地址是否实际相同
     * @param ipv6A 第一个 IPv6 地址
     * @param ipv6B 第二个 IPv6 地址
     * @return 如果两个地址实际相同则返回 true
     */
    public static boolean isSameIpv6(String ipv6A, String ipv6B) {
        return normalizeIpv6(ipv6A).equals(normalizeIpv6(ipv6B));
    }

    /**
     * 综合校验 IP 地址
     * @param ip IP 地址字符串
     * @return 是否为有效的 IP 地址
     */
    public static boolean isValidIpAddress(String ip) {
        return isValidIpv4(ip) || isValidIpv6(ip);
    }
}
