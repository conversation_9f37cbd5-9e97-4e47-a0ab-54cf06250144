package org.siteweb.config.common.vo.binterface;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentCUCCVO {
    /**
     * 站点ID
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备编码
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备资产编号
     */
    private String deviceRId;

    /**
     * SUID
     */
    private String suId;
    /**
     * SiteWeb监控单元
     */
    private String suName;
}
