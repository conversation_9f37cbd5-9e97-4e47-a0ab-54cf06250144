package org.siteweb.config.common.change;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户侧的变更事件监听器管理器
 *
 * <AUTHOR> (2024/3/9)
 */
@Slf4j
public class ChangeEventListener {

    private final List<ObjectChangeHandler> handlers = new ArrayList<>();


    /**
     * 监听对象变更事件
     *
     * @param handler 监听处理接口
     * <AUTHOR> (2024/3/9)
     */
    public void listen(ObjectChangeHandler handler) {
        if (!handlers.contains(handler) && handler != null) {
            handlers.add(handler);
        }
    }


    /**
     * 取消对象的变更事件
     *
     * @param handler 监听处理接口
     * <AUTHOR> (2024/3/9)
     */
    public void unlisten(ObjectChangeHandler handler) {
        handlers.remove(handler);
    }

    /**
     * 派遣对象创建事件
     *
     * @param record 变更记录
     * <AUTHOR> (2024/3/9)
     */
    protected void dispatchEvent(ChangeRecord record) {
        for (ObjectChangeHandler element : handlers) {
            switch (record.getOperator()) {
                case "create":
                    element.onCreate(record);
                    break;
                case "delete":
                    element.onDelete(record);
                    break;
                case "update":
                    element.onUpdate(record);
                    break;
                default:
                    // 未知事件类型
                    log.warn("Unknown event type: {}", record.getOperator());
            }
        }
    }

}
