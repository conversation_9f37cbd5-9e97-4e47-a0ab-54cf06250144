package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.CommandBaseMapDTO;
import org.siteweb.config.common.dto.CommandBaseMapExportDTO;
import org.siteweb.config.common.entity.TblCommandBaseMap;

import java.util.List;

@Mapper
public interface TblCommandBaseMapMapper extends BatchBaseMapper<TblCommandBaseMap> {
    List<CommandBaseMapDTO> getControlBaseMap(@Param("standardId") Integer standardId);

    List<CommandBaseMapExportDTO> getCommandBaseMapExport(@Param("standardId") Integer standardId);
}
