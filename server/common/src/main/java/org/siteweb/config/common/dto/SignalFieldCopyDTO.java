package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SignalFieldCopyDTO {
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 信号ID
     */
    private Integer signalId;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 字段值 如果是数组，直接传如目标对象的模板id+信号id
     */
    private String fieldValue;
}
