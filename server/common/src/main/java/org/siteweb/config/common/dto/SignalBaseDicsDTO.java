package org.siteweb.config.common.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 信号基类列表
 *
 * <AUTHOR> (2024/4/23)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@ExcelIgnoreUnannotated
public class SignalBaseDicsDTO {

    /**
     * 信号基础类别id
     */
    @ExcelProperty(value = "信号基础类别ID")
    private Long baseTypeId;
    /**
     * 基类信号名
     */
    @ExcelProperty(value = "信号基础类别名称")
    private String baseTypeName;
    /**
     * 基类设备ID
     */
    private Integer baseEquipmentId;
    /**
     * 基础类别名称(大类名称)
     */
    @ExcelProperty(value = "基础设备大类")
    private String baseClassName;
    /**
     * 基础设备子类名称(设备类型)
     */
    @ExcelProperty(value = "基础设备子类")
    private String baseEquipmentName;
    /**
     * 基类信号名（英文名）
     */
    private String englishName;
    /**
     * 信号逻辑分类
     */
    private Integer baseLogicCategoryId;
    /**
     * 信号逻辑分类名称
     */
    @ExcelProperty(value = "信号逻辑分类")
    private String baseLogicCategoryName;
    /**
     * 单位id
     */
    private Integer unitId;
    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String baseUnitSymbol;
    /**
     * 存储周期
     */
    @ExcelProperty(value = "存储周期")
    private Integer storeInterval;
    /**
     * 绝对值阈值
     */
    @ExcelProperty(value = "绝对值阀值")
    private Double absValueThreshold;
    /**
     * 百分比阈值
     */
    @ExcelProperty(value = "百分比阀值")
    private Double percentThreshold;
    /**
     * 放电存储周期
     */
    @ExcelProperty(value = "存储周期2")
    private Integer storeInterval2;
    /**
     * 放电存储绝对值阈值
     */
    @ExcelProperty(value = "绝对值阀值2")
    private Double absValueThreshold2;
    /**
     * 百分比存储阈值
     */
    @ExcelProperty(value = "百分比阀值2")
    private Double percentThreshold2;
    /**
     * 扩展字段1
     */
    private String extendField1;
    /**
     * 扩展字段2
     */
    private String extendField2;
    /**
     * 扩展字段3
     */
    private String extendField3;

    /**
     * 状态
     */
    private Integer baseStatusId;
    /**
     *
     */
    private Double baseHysteresis;
    /**
     *
     */
    private Integer baseFreqPeriod;
    /**
     *
     */
    private Integer baseFreqCount;
    /**
     *
     */
    private String baseShowPrecision;
    /**
     * 统计周期(小时)
     */
    @ExcelProperty(value = "统计周期")
    private Integer baseStatPeriod;
    /**
     *
     */
    private String cgElement;
    /**
     * 可扩展信号名(名称扩展表达式)
     */
    @ExcelProperty(value = "名称扩展表达式")
    private String baseNameExt;

    /**
     * 是否为系统
     */
    @ExcelProperty(value = "是否系统内置")
    private Boolean isSystem;

    /**
     * 备注 描述
     */
    @ExcelProperty(value = "备注")
    private String description;
}
