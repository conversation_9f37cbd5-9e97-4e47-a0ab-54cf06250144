package org.siteweb.config.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.BdStandardPoint;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/3/7
 */
@Data
@NoArgsConstructor
public class EquipmentCategoryWithPoints {
    private Integer typeId;       // dataItem的itemId
    private String typeName;   // dataItem的itemValue
    private List<BdStandardPoint> standardPoints;


    public EquipmentCategoryWithPoints(Integer typeId, String typeName, List<BdStandardPoint> standardPoints) {
        this.typeId = typeId;
        this.typeName = typeName;
        this.standardPoints = standardPoints;
    }
}
