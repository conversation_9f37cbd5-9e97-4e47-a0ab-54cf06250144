package org.siteweb.config.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.siteweb.config.common.config.BatchBaseMapper;
import org.siteweb.config.common.dto.SignalBaseDicsDTO;
import org.siteweb.config.common.dto.SignalBaseStatusDTO;
import org.siteweb.config.common.entity.TblSignalBaseDic;
import org.siteweb.config.common.vo.BaseDicFilterVO;

import java.util.List;

@Mapper
public interface TblSignalBaseDicMapper extends BatchBaseMapper<TblSignalBaseDic> {
    List<TblSignalBaseDic> findSignalBaseDic(BaseDicFilterVO baseDicFilterVO);

    void generateSignalBaseDic(@Param("baseTypeId") Long baseTypeId, @Param("sourceId") Long sourceId);

    /**
     * 获取信号基类列表
     */
    List<SignalBaseDicsDTO> findSignalBaseDicList();

    SignalBaseDicsDTO findSignalBaseDicByBaseTypeId(Long baseTypeId);

    /**
     * 信号基类状态含义
     */
    List<SignalBaseStatusDTO> findSignalBaseStatusList();
}
