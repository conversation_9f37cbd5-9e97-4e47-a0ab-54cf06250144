package org.siteweb.config.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.view.SignalBaseDicView;

@Data
@NoArgsConstructor
@TableName("TBL_SignalBaseDic")
public class TblSignalBaseDic {

    /**
     *
     */
    @JsonView(SignalBaseDicView.Simple.class)
    @TableId(value = "baseTypeId", type = IdType.INPUT)
    private Long baseTypeId;
    /**
     * 基类信号名
     */
    @JsonView(SignalBaseDicView.Simple.class)
    private String baseTypeName;
    /**
     * 基类设备ID
     */
    private Integer baseEquipmentId;
    /**
     * 基类信号名（英文名）
     */
    private String englishName;
    /**
     * 信号逻辑分类
     */
    private Integer baseLogicCategoryId;
    /**
     * 存储周期
     */
    private Integer storeInterval;
    /**
     * 绝对值阈值
     */
    private Double absValueThreshold;
    /**
     * 百分比阈值
     */
    private Double percentThreshold;
    /**
     * 放电存储周期
     */
    private Integer storeInterval2;
    /**
     * 放电存储绝对值阈值
     */
    private Double absValueThreshold2;
    /**
     * 百分比存储阈值
     */
    private Double percentThreshold2;
    /**
     * 扩展字段1
     */
    private String extendField1;
    /**
     * 扩展字段2
     */
    private String extendField2;
    /**
     * 扩展字段3
     */
    private String extendField3;
    /**
     * 单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer unitId;
    /**
     * 开关状态
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer baseStatusId;
    /**
     *
     */
    private Double baseHysteresis;
    /**
     *
     */
    private Integer baseFreqPeriod;
    /**
     *
     */
    private Integer baseFreqCount;
    /**
     *
     */
    private String baseShowPrecision;
    /**
     *
     */
    private Integer baseStatPeriod;
    /**
     *
     */
    private String cgElement;
    /**
     *
     */
    @JsonView(SignalBaseDicView.Simple.class)
    private String baseNameExt;
    /**
     *
     */
    private String description;
    /**
     * 是否为系统
     */
    private Boolean isSystem;
}
