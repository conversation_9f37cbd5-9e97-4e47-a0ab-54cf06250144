package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseClassStatisticsDTO<T> {
    /**
     * 确认数量
     */
    private Integer confirmCount;
    /**
     * 总的数量
     */
    private Integer sumCount;
    /**
     * 数据
     */
    private List<T> dataList;
}
