package org.siteweb.config.clientTest;


import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.client.provider.*;
import org.siteweb.config.common.change.ChangeMessage;
import org.siteweb.config.common.change.ChangeRecord;
import org.siteweb.config.common.change.ObjectChangeHandlerAdapter;
import org.siteweb.config.common.entity.TblEquipment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Component
public class StateManager extends ObjectChangeHandlerAdapter {
    private static final DateTimeFormatter defaultFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ResourceStructureProvider resourceStructureProvider;

    @Autowired
    private MonitorUnitProvider monitorUnitProvider;

    @Autowired
    private CommandProvider commandProvider;

    @Autowired
    private FaceDataProvider faceDataProvider;


    @Autowired
    private FingerPrintProvider fingerPrintProvider;


    @Autowired
    private CardProvider cardProvider;




    @Scheduled(fixedRate = 5000)
    public void timed() {

        try {

           var c1 =  cardProvider.findCardById(371000001);
            var c2=  cardProvider.findCardByCode("0000012AAD");




            var lst = faceDataProvider.findById("1000");
            var lst2 = fingerPrintProvider.findListById("1000");
            var lst3 = fingerPrintProvider.findListById("1000", "2000");


            var s = commandProvider.getControlForEquipment(-451, 451000002, 510000340);

            var tree = resourceStructureProvider.getTree();

            var mus = monitorUnitProvider.findAll();
            var nu = monitorUnitProvider.findByID(112233);
            log.info("mus");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(TblEquipment.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        ChangeMessage msg = changeRecord.readChangeMessage();
        TblEquipment eq = msg.readBody(TblEquipment.class);
        log.debug("{} 变更通知：Create，PrimaryKey：{}， Message：{}", defaultFormatter.format(msg.getChangeTime()), changeRecord.getPrimaryKey(), eq);
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        log.debug("变更通知：Delete，PrimaryKey：{}， Message：{}", changeRecord.getPrimaryKey(), changeRecord.getMessage());
        throw new RuntimeException("哎,我挂不了。");
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        log.debug("变更通知：Update，PrimaryKey：{}， Message：{}", changeRecord.getPrimaryKey(), changeRecord.getMessage());
    }
}
