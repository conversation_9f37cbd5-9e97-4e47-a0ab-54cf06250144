<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List">
  <EquipmentTemplate EquipmentTemplateId="755000011" ParentTemplateId="0" EquipmentTemplateName="eStoneII-IO Equipment" ProtocolCode="eStoneII-IO6-00" EquipmentCategory="51" EquipmentType="1"  Memo="2018/8/23 12:47:22" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
    <Signals Name="Template Signal">
      <Signal SignalId="510000221" SignalName="I2C Humidity" SignalCategory="1" SignalType="1" ChannelNo="13" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="%RH" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004003001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000211" SignalName="I2C Templerature" SignalCategory="1" SignalType="1" ChannelNo="12" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004001001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="22" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000585" SignalName="SD Card Slot State" SignalCategory="2" SignalType="1" ChannelNo="71" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="52" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No SD Card;1:With SD Card" />
      <Signal SignalId="510000586" SignalName="SD capacity" SignalCategory="1" SignalType="1" ChannelNo="72" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="53" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000587" SignalName="SD available capacity" SignalCategory="1" SignalType="1" ChannelNo="73" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="54" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000091" SignalName="battery 1 total voltage" SignalCategory="1" SignalType="1" ChannelNo="8" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101170001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="10" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000111" SignalName="battery 2 total voltage" SignalCategory="1" SignalType="1" ChannelNo="10" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101170002" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="12" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000551" SignalName="Battery#1 voltage imbalance" SignalCategory="1" SignalType="1" ChannelNo="75" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="39" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000561" SignalName="Battery#2 voltage imbalance" SignalCategory="1" SignalType="1" ChannelNo="76" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="40" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000270" SignalName="Infrared" SignalCategory="2" SignalType="1" ChannelNo="62" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004008001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="26" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:No Alarm" />
      <Signal SignalId="510000340" SignalName="Relay 1 State" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="28" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000350" SignalName="Relay 2 State" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="29" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000370" SignalName="Relay 4 State" SignalCategory="2" SignalType="1" ChannelNo="27" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="31" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000570" SignalName="Air Conditioner 1 State" SignalCategory="2" SignalType="1" ChannelNo="69" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="46" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Stop;1:Fan work;2:Compressor work" />
      <Signal SignalId="510000580" SignalName="Air Conditioner 2 State" SignalCategory="2" SignalType="1" ChannelNo="70" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="47" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Stop;1:Fan work;2:Compressor work" />
      <Signal SignalId="510000260" SignalName="Doorsensor" SignalCategory="2" SignalType="1" ChannelNo="61" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004007001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="25" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:No Alarm" />
      <Signal SignalId="510000588" SignalName="Door lock State-CH6" SignalCategory="2" SignalType="1" ChannelNo="21" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="55" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Door lock Open;1:Door lock Close" />
      <Signal SignalId="510000360" SignalName="Door lock State-DO3" SignalCategory="2" SignalType="1" ChannelNo="26" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="30" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000011" SignalName="Analog input 01 value" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="2" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000021" SignalName="Analog input 02 value" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000031" SignalName="Analog input 03 value" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="4" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000041" SignalName="Analog input 04 value" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="5" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000051" SignalName="Analog input 05 value" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000061" SignalName="Analog input 06 value" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000071" SignalName="Analog input 07 value" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="8" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000081" SignalName="Analog input 08 value" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="9" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="-3" SignalName="Communication State" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004999001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:communication abnormal;1:communication normal" />
      <Signal SignalId="510000550" SignalName="City Electric A Road" SignalCategory="2" SignalType="1" ChannelNo="67" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="44" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Phase Shortage ;1:Power On" />
      <Signal SignalId="510000560" SignalName="City Electric B Road" SignalCategory="2" SignalType="1" ChannelNo="68" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="45" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Phase Shortage ;1:Power On" />
      <Signal SignalId="510000280" SignalName="Water State" SignalCategory="2" SignalType="1" ChannelNo="59" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004005001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="27" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No Alarm;1:Alarm" />
      <Signal SignalId="510000581" SignalName="Battery#1-voltage(group#2)" SignalCategory="1" SignalType="1" ChannelNo="63" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101320001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="48" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000101" SignalName="Battery#1-voltage(group#1)" SignalCategory="1" SignalType="1" ChannelNo="9" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101321001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="11" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000582" SignalName="Battery#2-voltage(group#2)" SignalCategory="1" SignalType="1" ChannelNo="65" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101320002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="49" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000121" SignalName="Battery#2-voltage(group#1)" SignalCategory="1" SignalType="1" ChannelNo="11" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1101321002" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="13" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000250" SignalName="Smoke" SignalCategory="2" SignalType="1" ChannelNo="60" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="0" StaticsPeriod="0" Enable="True" Visible="True" Discription="" BaseTypeId="1004006001" ChargeStoreInterVal="" ChargeAbsValue="0" DisplayIndex="24" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No Alarm;1:Alarm" />
    </Signals>
    <Events Name="Template Event">
      <Event EventId="-3" EventName="Communication Status" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="communication abnormal" EquipmentState="" BaseTypeId="1004999001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000250" EventName="Smoke" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000250]" SuppressExpression="" SignalId="510000250" Enable="True" Visible="True" Description="" DisplayIndex="24" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000260" EventName="Doorsensor" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000260]" SuppressExpression="" SignalId="510000260" Enable="True" Visible="True" Description="" DisplayIndex="25" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004007001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000270" EventName="Infrared" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000270]" SuppressExpression="" SignalId="510000270" Enable="True" Visible="True" Description="" DisplayIndex="26" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000280" EventName="Water State" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000280]" SuppressExpression="" SignalId="510000280" Enable="True" Visible="True" Description="" DisplayIndex="27" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000550" EventName="City Electric A Road" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000550]" SuppressExpression="" SignalId="510000550" Enable="True" Visible="True" Description="" DisplayIndex="44" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="phase shortage " EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000560" EventName="City Electric B Road" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000560]" SuppressExpression="" SignalId="510000560" Enable="True" Visible="True" Description="" DisplayIndex="45" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="phase shortage " EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000561" EventName="Total 1 voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000091]" SuppressExpression="" SignalId="" Enable="True" Visible="True" Description="" DisplayIndex="46" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101370001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101170001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000562" EventName="Total 1 voltage middle-imbalance Alarm" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000551]" SuppressExpression="" SignalId="510000551" Enable="True" Visible="True" Description="" DisplayIndex="47" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101330001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000563" EventName="Total 2 voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000111]" SuppressExpression="" SignalId="" Enable="True" Visible="True" Description="" DisplayIndex="48" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101370002" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101170002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000564" EventName="Total 2 voltage middle-imbalance Alarm" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000561]" SuppressExpression="" SignalId="510000561" Enable="True" Visible="True" Description="" DisplayIndex="49" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1101330002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000565" EventName="Templerature" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000211]" SuppressExpression="" SignalId="510000211" Enable="True" Visible="True" Description="" DisplayIndex="50" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="36" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1004307001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&gt;" StartCompareValue="32" StartDelay="0" EndOperation="&gt;" EndCompareValue="36" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1004001001" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="0" StartOperation="&lt;" StartCompareValue="16" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1004002001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000566" EventName="Humidity" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000221]" SuppressExpression="" SignalId="510000211" Enable="True" Visible="True" Description="" DisplayIndex="51" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="&gt;" StartCompareValue="75" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1004003001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="0" StartOperation="&lt;" StartCompareValue="20" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="1004004001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000567" EventName="SDCard Slot State" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000585]" SuppressExpression="" SignalId="510000585" Enable="True" Visible="True" Description="" DisplayIndex="52" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000568" EventName="Door lock-CH6" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000588]" SuppressExpression="" SignalId="510000588" Enable="True" Visible="True" Description="" DisplayIndex="53" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="" BaseTypeId="1004308001" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="Template Control">
      <Control ControlId="510000340" ControlName="Lighting control" ControlCategory="1" CmdToken="10,0" BaseTypeId="1004303001" ControlSeverity="1" SignalId="510000340" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
      <Control ControlId="510000350" ControlName="Relay 2 State" ControlCategory="1" CmdToken="11,0" BaseTypeId="" ControlSeverity="1" SignalId="510000350" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
      <Control ControlId="510000360" ControlName="Remote Open Door-DO3" ControlCategory="1" CmdToken="12,250" BaseTypeId="1004010001" ControlSeverity="1" SignalId="510000360" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="3" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
      <Control ControlId="510000370" ControlName="Relay 4 State" ControlCategory="1" CmdToken="13,0" BaseTypeId="" ControlSeverity="1" SignalId="510000370" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="4" CommandType="2" ControlType="1" DataType="0" MaxValue="3000" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
    </Controls>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000013" SamplerName="eStoneII-IO Equipment" SamplerType="18" ProtocolCode="eStoneII-IO6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="eStoneII-IO.so" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>