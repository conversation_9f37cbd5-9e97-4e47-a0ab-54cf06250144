<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List">
  <EquipmentTemplate EquipmentTemplateId="755000013" ParentTemplateId="0" EquipmentTemplateName="IDU-IO Equipment" ProtocolCode="IDU-IO6-00" EquipmentCategory="51" EquipmentType="1" Memo="2018/8/23 12:47:22" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1004" StationCategory="0">
    <Signals Name="Template Signal">
      <Signal SignalId="510000141" SignalName="I2C Humidity" SignalCategory="1" SignalType="1" ChannelNo="13" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="%RH" StoreInterval="28800" AbsValueThreshold="10" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004003001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000131" SignalName="I2C Templerature" SignalCategory="1" SignalType="1" ChannelNo="12" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="℃" StoreInterval="28800" AbsValueThreshold="5" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004001001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000340" SignalName="Light Control" SignalCategory="2" SignalType="1" ChannelNo="24" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:turn off;1:turn on" />
      <Signal SignalId="510000111" SignalName="Battery 1 total voltage" SignalCategory="1" SignalType="1" ChannelNo="10" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000121" SignalName="Battery 2 total voltage" SignalCategory="1" SignalType="1" ChannelNo="11" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="V" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1101170002" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000280" SignalName="Infrared" SignalCategory="2" SignalType="1" ChannelNo="19" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004008001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:No Alarm" />
      <Signal SignalId="510000350" SignalName="Relay 2 status" SignalCategory="2" SignalType="1" ChannelNo="25" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000370" SignalName="Relay 4 status" SignalCategory="2" SignalType="1" ChannelNo="27" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000270" SignalName="Doorsensor" SignalCategory="2" SignalType="1" ChannelNo="18" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004007001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Alarm;1:No Alarm" />
      <Signal SignalId="510000061" SignalName="Door lock status-CH6" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000360" SignalName="Door lock status-DO3" SignalCategory="2" SignalType="1" ChannelNo="26" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:Power On;1:Power Loss" />
      <Signal SignalId="510000011" SignalName="Analog input 01 value" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000021" SignalName="Analog input 02 value" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000031" SignalName="Analog input 03 value" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000041" SignalName="Analog input 04 value" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000051" SignalName="Analog input 05 value" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000071" SignalName="Analog input 07 value" SignalCategory="1" SignalType="1" ChannelNo="6" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000081" SignalName="Analog input 08 value" SignalCategory="1" SignalType="1" ChannelNo="7" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000091" SignalName="Analog input 09 value" SignalCategory="1" SignalType="1" ChannelNo="8" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000101" SignalName="Analog input 10 value" SignalCategory="1" SignalType="1" ChannelNo="9" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="-3" SignalName="Communication Status" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004999001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:communication abnormal;1:communication normal" />
      <Signal SignalId="510000260" SignalName="Water" SignalCategory="2" SignalType="1" ChannelNo="17" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004005001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No Alarm;1:Alarm" />
      <Signal SignalId="510000250" SignalName="Smoke" SignalCategory="2" SignalType="1" ChannelNo="16" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="1004006001" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="0" ModuleNo="0" SignalProperty="27" SignalMeanings="0:No Alarm;1:Alarm" />
    </Signals>
    <Events Name="Template Event">
      <Event EventId="-3" EventName="Communication Status" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="communication abnormal" EquipmentState="" BaseTypeId="1004999001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000250" EventName="Smoke" EventCategory="5" StartType="1" EndType="3" StartExpression="[-1,510000250]" SuppressExpression="" SignalId="510000250" Enable="True" Visible="True" Description="" DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004006001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000260" EventName="Water" EventCategory="37" StartType="1" EndType="3" StartExpression="[-1,510000260]" SuppressExpression="" SignalId="510000260" Enable="True" Visible="True" Description="" DisplayIndex="17" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004005001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000270" EventName="Doorsensor" EventCategory="35" StartType="1" EndType="3" StartExpression="[-1,510000270]" SuppressExpression="" SignalId="510000270" Enable="True" Visible="True" Description="" DisplayIndex="18" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004007001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000280" EventName="Infrared" EventCategory="36" StartType="1" EndType="3" StartExpression="[-1,510000280]" SuppressExpression="" SignalId="510000280" Enable="True" Visible="True" Description="" DisplayIndex="19" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="2" BaseTypeId="1004008001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000281" EventName="Battery 1 total voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000111]" SuppressExpression="" SignalId="510000111" Enable="True" Visible="True" Description="" DisplayIndex="20" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="voltage Too high" EquipmentState="" BaseTypeId="1101370001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="voltage Too low" EquipmentState="" BaseTypeId="1101170001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000282" EventName="Battery 2 total voltage" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000121]" SuppressExpression="" SignalId="510000121" Enable="True" Visible="True" Description="" DisplayIndex="21" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="60" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="voltage Too high" EquipmentState="" BaseTypeId="1101370002" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="46" StartDelay="60" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="voltage Too low" EquipmentState="" BaseTypeId="1101170002" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000283" EventName="I2C Templerature" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000131]" SuppressExpression="" SignalId="510000131" Enable="True" Visible="True" Description="" DisplayIndex="22" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="32" StartDelay="0" EndOperation="&gt;" EndCompareValue="36" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Templerature high" EquipmentState="" BaseTypeId="1004001001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="3" StartOperation="&gt;" StartCompareValue="36" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Templerature Super high" EquipmentState="" BaseTypeId="1004307001" StandardName="" />
          <EventCondition EventConditionId="2" EventSeverity="1" StartOperation="&lt;" StartCompareValue="16" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Templerature Too low" EquipmentState="" BaseTypeId="1004002001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000284" EventName="I2C Humidity" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000141]" SuppressExpression="" SignalId="510000141" Enable="True" Visible="True" Description="" DisplayIndex="23" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="&gt;" StartCompareValue="75" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Humidity Too high" EquipmentState="" BaseTypeId="1004003001" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&lt;" StartCompareValue="20" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Humidity Too low" EquipmentState="" BaseTypeId="1004004001" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000285" EventName="Door lock status-CH6" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000061]" SuppressExpression="" SignalId="510000061" Enable="True" Visible="True" Description="" DisplayIndex="24" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="1" EventSeverity="1" StartOperation="&lt;" StartCompareValue="5" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="Alarm" EquipmentState="" BaseTypeId="1004308001" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="Template Control">
      <Control ControlId="510000340" ControlName="Lighting control" ControlCategory="1" CmdToken="10,0" BaseTypeId="1004303001" ControlSeverity="1" SignalId="510000340" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="1" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:turn off the light;1:turn on the light " />
      <Control ControlId="510000350" ControlName="Relay 2 status" ControlCategory="1" CmdToken="11,0" BaseTypeId="" ControlSeverity="1" SignalId="510000350" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="2" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
      <Control ControlId="510000360" ControlName="Remote Open Door-DO3" ControlCategory="1" CmdToken="12,250" BaseTypeId="1004010001" ControlSeverity="1" SignalId="510000360" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="3" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
      <Control ControlId="510000370" ControlName="Relay 4 status" ControlCategory="1" CmdToken="13,0" BaseTypeId="" ControlSeverity="1" SignalId="510000370" TimeOut="" Retry="" Description="" Enable="True" Visible="True" DisplayIndex="4" CommandType="2" ControlType="1" DataType="0" MaxValue="255" MinValue="0" DefaultValue="" ModuleNo="0" ControlMeanings="0:Power On;1:Power Loss" />
    </Controls>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000015" SamplerName="IDUIO Equipment" SamplerType="18" ProtocolCode="IDU-IO6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="IDUIO.so" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>