<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="1" ParentTemplateId="0" EquipmentTemplateName="LSC工作站自诊断设备" ProtocolCode="LSC工作站自诊断设备6-00" EquipmentCategory="99" EquipmentType="2" Memo="2024-02-08 09:55:04:首次导入模板" Property="" Decription=" " EquipmentStyle=" " Unit=" " Vendor="" EquipmentBaseType="1301" StationCategory="0">
    <Signals Name="模板信号">
      <Signal SignalId="20" SignalName="RMU1#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="16" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="29" SignalName="RMU10#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="25" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="30" SignalName="RMU11#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="26" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="31" SignalName="RMU12#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="27" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="32" SignalName="RMU13#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="28" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="33" SignalName="RMU14#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="29" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="34" SignalName="RMU15#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="30" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="35" SignalName="RMU16#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="31" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="36" SignalName="RMU17#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="32" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="37" SignalName="RMU18#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="33" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="38" SignalName="RMU19#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="34" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="21" SignalName="RMU2#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="17" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="39" SignalName="RMU20#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="35" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="40" SignalName="RMU21#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="36" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="41" SignalName="RMU22#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="37" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="42" SignalName="RMU23#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="38" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="43" SignalName="RMU24#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="39" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="44" SignalName="RMU25#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="40" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="45" SignalName="RMU26#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="41" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="46" SignalName="RMU27#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="42" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="47" SignalName="RMU28#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="43" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="48" SignalName="RMU29#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="44" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="22" SignalName="RMU3#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="18" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="49" SignalName="RMU30#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="45" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="50" SignalName="RMU31#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="46" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="51" SignalName="RMU32#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="47" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="52" SignalName="RMU33#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="48" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="53" SignalName="RMU34#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="49" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="54" SignalName="RMU35#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="50" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="55" SignalName="RMU36#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="51" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="56" SignalName="RMU37#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="52" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="57" SignalName="RMU38#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="53" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="58" SignalName="RMU39#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="54" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="23" SignalName="RMU4#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="19" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="59" SignalName="RMU40#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="55" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="60" SignalName="RMU41#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="56" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="61" SignalName="RMU42#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="57" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="62" SignalName="RMU43#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="58" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="63" SignalName="RMU44#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="59" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="64" SignalName="RMU45#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="60" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="65" SignalName="RMU46#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="61" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="66" SignalName="RMU47#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="62" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="67" SignalName="RMU48#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="63" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="24" SignalName="RMU5#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="20" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="25" SignalName="RMU6#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="21" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="26" SignalName="RMU7#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="22" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="27" SignalName="RMU8#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="23" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="28" SignalName="RMU9#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="24" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="4" SignalName="业务服务器II工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="67" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="3" SignalName="业务服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="2" SignalName="应用服务器II工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="66" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="1" SignalName="应用服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="8" SignalName="数据库服务器空间告警状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:正常;2:告警" />
      <Signal SignalId="5" SignalName="数据服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="10" SignalName="实时数据服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="64" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="11" SignalName="手机接口服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="65" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="12" SignalName="通知主机1#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="13" SignalName="通知主机2#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="14" SignalName="通知主机3#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="15" SignalName="通知主机4#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="16" SignalName="通知主机5#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="17" SignalName="通知主机6#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="18" SignalName="通知主机7#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="19" SignalName="通知主机8#工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="15" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
      <Signal SignalId="9" SignalName="通知服务器工作状态" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="0" ModuleNo="0" SignalProperty="" SignalMeanings="1:运行;2:停机" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="1" EventName="应用服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,1]" SuppressExpression=" " SignalId="1" Enable="True" Visible="True" Description=" " DisplayIndex="1" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302310001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="3" EventName="业务服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,3]" SuppressExpression=" " SignalId="3" Enable="True" Visible="True" Description=" " DisplayIndex="2" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302311001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="5" EventName="数据服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,5]" SuppressExpression=" " SignalId="5" Enable="True" Visible="True" Description=" " DisplayIndex="3" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302309001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="8" EventName="数据库服务器空间不足告警" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,8]" SuppressExpression=" " SignalId="8" Enable="True" Visible="True" Description=" " DisplayIndex="6" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="数据库空间低" EquipmentState="" BaseTypeId="1302322001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="9" EventName="通知服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,9]" SuppressExpression=" " SignalId="9" Enable="True" Visible="True" Description=" " DisplayIndex="7" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="12" EventName="通知主机1#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,12]" SuppressExpression=" " SignalId="12" Enable="True" Visible="True" Description=" " DisplayIndex="8" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="13" EventName="通知主机2#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,13]" SuppressExpression=" " SignalId="13" Enable="True" Visible="True" Description=" " DisplayIndex="9" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="14" EventName="通知主机3#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,14]" SuppressExpression=" " SignalId="14" Enable="True" Visible="True" Description=" " DisplayIndex="10" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="15" EventName="通知主机4#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,15]" SuppressExpression=" " SignalId="15" Enable="True" Visible="True" Description=" " DisplayIndex="11" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="16" EventName="通知主机5#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,16]" SuppressExpression=" " SignalId="16" Enable="True" Visible="True" Description=" " DisplayIndex="12" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="17" EventName="通知主机6#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,17]" SuppressExpression=" " SignalId="17" Enable="True" Visible="True" Description=" " DisplayIndex="13" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="18" EventName="通知主机7#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,18]" SuppressExpression=" " SignalId="18" Enable="True" Visible="True" Description=" " DisplayIndex="14" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="19" EventName="通知主机8#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,19]" SuppressExpression=" " SignalId="19" Enable="True" Visible="True" Description=" " DisplayIndex="15" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="20" EventName="RMU1#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,20]" SuppressExpression=" " SignalId="20" Enable="True" Visible="True" Description=" " DisplayIndex="16" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="21" EventName="RMU2#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,21]" SuppressExpression=" " SignalId="21" Enable="True" Visible="True" Description=" " DisplayIndex="17" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305002" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="22" EventName="RMU3#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,22]" SuppressExpression=" " SignalId="22" Enable="True" Visible="True" Description=" " DisplayIndex="18" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305003" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="23" EventName="RMU4#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,23]" SuppressExpression=" " SignalId="23" Enable="True" Visible="True" Description=" " DisplayIndex="19" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305004" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="24" EventName="RMU5#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,24]" SuppressExpression=" " SignalId="24" Enable="True" Visible="True" Description=" " DisplayIndex="20" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305005" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="25" EventName="RMU6#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,25]" SuppressExpression=" " SignalId="25" Enable="True" Visible="True" Description=" " DisplayIndex="21" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305006" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="26" EventName="RMU7#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,26]" SuppressExpression=" " SignalId="26" Enable="True" Visible="True" Description=" " DisplayIndex="22" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305007" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="27" EventName="RMU8#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,27]" SuppressExpression=" " SignalId="27" Enable="True" Visible="True" Description=" " DisplayIndex="23" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305008" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="28" EventName="RMU9#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,28]" SuppressExpression=" " SignalId="28" Enable="True" Visible="True" Description=" " DisplayIndex="24" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305009" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="29" EventName="RMU10#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,29]" SuppressExpression=" " SignalId="29" Enable="True" Visible="True" Description=" " DisplayIndex="25" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305010" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="30" EventName="RMU11#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,30]" SuppressExpression=" " SignalId="30" Enable="True" Visible="True" Description=" " DisplayIndex="26" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305011" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="31" EventName="RMU12#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,31]" SuppressExpression=" " SignalId="31" Enable="True" Visible="True" Description=" " DisplayIndex="27" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305012" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="32" EventName="RMU13#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,32]" SuppressExpression=" " SignalId="32" Enable="True" Visible="True" Description=" " DisplayIndex="28" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305013" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="33" EventName="RMU14#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,33]" SuppressExpression=" " SignalId="33" Enable="True" Visible="True" Description=" " DisplayIndex="29" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305014" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="34" EventName="RMU15#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,34]" SuppressExpression=" " SignalId="34" Enable="True" Visible="True" Description=" " DisplayIndex="30" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305015" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="35" EventName="RMU16#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,35]" SuppressExpression=" " SignalId="35" Enable="True" Visible="True" Description=" " DisplayIndex="31" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305016" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="36" EventName="RMU17#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,36]" SuppressExpression=" " SignalId="36" Enable="True" Visible="True" Description=" " DisplayIndex="32" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305017" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="37" EventName="RMU18#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,37]" SuppressExpression=" " SignalId="37" Enable="True" Visible="True" Description=" " DisplayIndex="33" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305018" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="38" EventName="RMU19#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,38]" SuppressExpression=" " SignalId="38" Enable="True" Visible="True" Description=" " DisplayIndex="34" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305019" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="39" EventName="RMU20#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,39]" SuppressExpression=" " SignalId="39" Enable="True" Visible="True" Description=" " DisplayIndex="35" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305020" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="40" EventName="RMU21#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,40]" SuppressExpression=" " SignalId="40" Enable="True" Visible="True" Description=" " DisplayIndex="36" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305021" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="41" EventName="RMU22#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,41]" SuppressExpression=" " SignalId="41" Enable="True" Visible="True" Description=" " DisplayIndex="37" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305022" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="42" EventName="RMU23#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,42]" SuppressExpression=" " SignalId="42" Enable="True" Visible="True" Description=" " DisplayIndex="38" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305023" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="43" EventName="RMU24#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,43]" SuppressExpression=" " SignalId="43" Enable="True" Visible="True" Description=" " DisplayIndex="39" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305024" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="44" EventName="RMU25#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,44]" SuppressExpression=" " SignalId="44" Enable="True" Visible="True" Description=" " DisplayIndex="40" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305025" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="45" EventName="RMU26#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,45]" SuppressExpression=" " SignalId="45" Enable="True" Visible="True" Description=" " DisplayIndex="41" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305026" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="46" EventName="RMU27#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,46]" SuppressExpression=" " SignalId="46" Enable="True" Visible="True" Description=" " DisplayIndex="42" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305027" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="47" EventName="RMU28#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,47]" SuppressExpression=" " SignalId="47" Enable="True" Visible="True" Description=" " DisplayIndex="43" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305028" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="48" EventName="RMU29#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,48]" SuppressExpression=" " SignalId="48" Enable="True" Visible="True" Description=" " DisplayIndex="44" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305029" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="49" EventName="RMU30#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,49]" SuppressExpression=" " SignalId="49" Enable="True" Visible="True" Description=" " DisplayIndex="45" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305030" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="50" EventName="RMU31#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,50]" SuppressExpression=" " SignalId="50" Enable="True" Visible="True" Description=" " DisplayIndex="46" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305031" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="51" EventName="RMU32#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,51]" SuppressExpression=" " SignalId="51" Enable="True" Visible="True" Description=" " DisplayIndex="47" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305032" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="52" EventName="RMU33#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,52]" SuppressExpression=" " SignalId="52" Enable="True" Visible="True" Description=" " DisplayIndex="48" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305033" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="53" EventName="RMU34#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,53]" SuppressExpression=" " SignalId="53" Enable="True" Visible="True" Description=" " DisplayIndex="49" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305034" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="54" EventName="RMU35#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,54]" SuppressExpression=" " SignalId="54" Enable="True" Visible="True" Description=" " DisplayIndex="50" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305035" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="55" EventName="RMU36#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,55]" SuppressExpression=" " SignalId="55" Enable="True" Visible="True" Description=" " DisplayIndex="51" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305036" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="56" EventName="RMU37#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,56]" SuppressExpression=" " SignalId="56" Enable="True" Visible="True" Description=" " DisplayIndex="52" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305037" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="57" EventName="RMU38#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,57]" SuppressExpression=" " SignalId="57" Enable="True" Visible="True" Description=" " DisplayIndex="53" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305038" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="58" EventName="RMU39#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,58]" SuppressExpression=" " SignalId="58" Enable="True" Visible="True" Description=" " DisplayIndex="54" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305039" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="59" EventName="RMU40#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,59]" SuppressExpression=" " SignalId="59" Enable="True" Visible="True" Description=" " DisplayIndex="55" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305040" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="60" EventName="RMU41#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,60]" SuppressExpression=" " SignalId="60" Enable="True" Visible="True" Description=" " DisplayIndex="56" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305041" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="61" EventName="RMU42#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,61]" SuppressExpression=" " SignalId="61" Enable="True" Visible="True" Description=" " DisplayIndex="57" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305042" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="62" EventName="RMU43#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,62]" SuppressExpression=" " SignalId="62" Enable="True" Visible="True" Description=" " DisplayIndex="58" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305043" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="63" EventName="RMU44#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,63]" SuppressExpression=" " SignalId="63" Enable="True" Visible="True" Description=" " DisplayIndex="59" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305044" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="64" EventName="RMU45#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,64]" SuppressExpression=" " SignalId="64" Enable="True" Visible="True" Description=" " DisplayIndex="60" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305045" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="65" EventName="RMU46#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,65]" SuppressExpression=" " SignalId="65" Enable="True" Visible="True" Description=" " DisplayIndex="61" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305046" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="66" EventName="RMU47#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,66]" SuppressExpression=" " SignalId="66" Enable="True" Visible="True" Description=" " DisplayIndex="62" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305047" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="67" EventName="RMU48#工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,67]" SuppressExpression=" " SignalId="67" Enable="True" Visible="True" Description=" " DisplayIndex="63" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302305048" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="10" EventName="实时数据服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,10]" SuppressExpression=" " SignalId="10" Enable="True" Visible="True" Description=" " DisplayIndex="64" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="11" EventName="手机接口服务器工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,11]" SuppressExpression=" " SignalId="11" Enable="True" Visible="True" Description=" " DisplayIndex="65" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="2" EventName="应用服务器II工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,2]" SuppressExpression=" " SignalId="2" Enable="True" Visible="True" Description=" " DisplayIndex="66" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302310001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="4" EventName="业务服务器II工作状态" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,4]" SuppressExpression=" " SignalId="4" Enable="True" Visible="True" Description=" " DisplayIndex="67" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="停机" EquipmentState="" BaseTypeId="1302311001" StandardName="0" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="1" SamplerName="工作站自诊断采集器" SamplerType="0" ProtocolCode="LSC工作站自诊断设备6-00" DllCode="" DLLVersion=" " ProtocolFilePath=" " DLLFilePath=" " DllPath="KoloBusinessServer.exe" Setting="" Description=" " />
  </Samplers>
</EquipmentTemplates>