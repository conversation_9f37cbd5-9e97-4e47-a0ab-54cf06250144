<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List ">
  <EquipmentTemplate EquipmentTemplateId="1" ParentTemplateId="0" EquipmentTemplateName="LSC Workstation Self Diagnostic Equipment" ProtocolCode="Self Diagnostic Equipment 6-00" EquipmentCategory="99" EquipmentType="2" Memo="" Property="" Decription=" " EquipmentStyle=" " EquipmentBaseType="1301" Unit=" " Vender=" ">
    <Signals Name="Template Signal">
      <Signal SignalId="1" SignalName="AppServer Work State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="2" SignalName="Business Server State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="2" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="3" SignalName="Data Server State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="3" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="6" SignalName="Database Space Alarm State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="6" SignalProperty="" SignalMeanings="1:Normal;2:Alarm" />
      <Signal SignalId="7" SignalName="Notification Server State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="7" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="8" SignalName="Notification Host1# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="8" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="9" SignalName="Notification Host2# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="9" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="10" SignalName="Notification Host3# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="10" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="11" SignalName="Notification Host4# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="11" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="12" SignalName="Notification Host5# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="12" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="13" SignalName="Notification Host6# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="13" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="14" SignalName="Notification Host7# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="14" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="15" SignalName="Notification Host8#State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="15" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="16" SignalName="RMU1# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="16" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="17" SignalName="RMU2# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="17" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="18" SignalName="RMU3# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="18" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="19" SignalName="RMU4# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="19" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="20" SignalName="RMU5# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="20" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="21" SignalName="RMU6# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="21" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="22" SignalName="RMU7# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="22" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="23" SignalName="RMU8# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="23" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="24" SignalName="RMU9# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="24" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="25" SignalName="RMU10# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="25" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="26" SignalName="RMU11# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="26" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="27" SignalName="RMU12# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="27" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="28" SignalName="RMU13# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="28" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="29" SignalName="RMU14# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="29" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="30" SignalName="RMU15# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="30" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="31" SignalName="RMU16# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="31" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="32" SignalName="RMU17# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="32" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="33" SignalName="RMU18# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="33" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="34" SignalName="RMU19# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="34" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="35" SignalName="RMU20# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="35" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="36" SignalName="RMU21# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="36" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="37" SignalName="RMU22# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="37" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="38" SignalName="RMU23# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="38" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="39" SignalName="RMU24# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="39" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="40" SignalName="RMU25# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="40" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="41" SignalName="RMU26# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="41" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="42" SignalName="RMU27# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="42" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="43" SignalName="RMU28# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="43" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="44" SignalName="RMU29# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="44" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="45" SignalName="RMU30# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="45" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="46" SignalName="RMU31# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="46" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="47" SignalName="RMU32# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="47" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="48" SignalName="RMU33# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="48" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="49" SignalName="RMU34# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="49" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="50" SignalName="RMU35# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="50" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="51" SignalName="RMU36# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="51" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="52" SignalName="RMU37# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="52" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="53" SignalName="RMU38# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="53" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="54" SignalName="RMU39# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="54" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="55" SignalName="RMU40# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="55" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="56" SignalName="RMU41# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="56" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="57" SignalName="RMU42# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="57" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="58" SignalName="RMU43# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="58" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="59" SignalName="RMU44# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="59" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="60" SignalName="RMU45# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="60" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="61" SignalName="RMU46# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="61" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="62" SignalName="RMU47# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="62" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="63" SignalName="RMU48# State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="63" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
      <Signal SignalId="64" SignalName="Real-time Data Server State" SignalCategory="2" SignalType="2" ChannelNo="0" ChannelType="2" Expression="" DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="64" SignalProperty="" SignalMeanings="1:Run;2:Stop" />
    </Signals>
    <Events Name="Template Event">
      <Event EventId="1" EventName="AppServer State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,1]" SuppressExpression=" " SignalId="1" Enable="True" Visible="True" Description=" " DisplayIndex="1">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302310001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="2" EventName="Business Server State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,2]" SuppressExpression=" " SignalId="2" Enable="True" Visible="True" Description=" " DisplayIndex="2">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302311001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="3" EventName="Data Server State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,3]" SuppressExpression=" " SignalId="3" Enable="True" Visible="True" Description=" " DisplayIndex="3">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302309001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="6" EventName="Database Space Insufficient Alarm" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,6]" SuppressExpression=" " SignalId="6" Enable="True" Visible="True" Description=" " DisplayIndex="6">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Database Space Low" EquipmentState="" BaseTypeId="1302322001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="7" EventName="Notification Server State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,7]" SuppressExpression=" " SignalId="7" Enable="True" Visible="True" Description=" " DisplayIndex="7">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="8" EventName="Notification Host1# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,8]" SuppressExpression=" " SignalId="8" Enable="True" Visible="True" Description=" " DisplayIndex="8">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="9" EventName="Notification Host 2# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,9]" SuppressExpression=" " SignalId="9" Enable="True" Visible="True" Description=" " DisplayIndex="9">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="10" EventName="Notification Host3# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,10]" SuppressExpression=" " SignalId="10" Enable="True" Visible="True" Description=" " DisplayIndex="10">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="11" EventName="Notification Host4# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,11]" SuppressExpression=" " SignalId="11" Enable="True" Visible="True" Description=" " DisplayIndex="11">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="12" EventName="Notification Host5# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,12]" SuppressExpression=" " SignalId="12" Enable="True" Visible="True" Description=" " DisplayIndex="12">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="13" EventName="Notification Host 6# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,13]" SuppressExpression=" " SignalId="13" Enable="True" Visible="True" Description=" " DisplayIndex="13">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="14" EventName="Notification Host7# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,14]" SuppressExpression=" " SignalId="14" Enable="True" Visible="True" Description=" " DisplayIndex="14">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="15" EventName="Notification Host 8# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,15]" SuppressExpression=" " SignalId="15" Enable="True" Visible="True" Description=" " DisplayIndex="15">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="16" EventName="RMU1# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,16]" SuppressExpression=" " SignalId="16" Enable="True" Visible="True" Description=" " DisplayIndex="16">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="17" EventName="RMU2# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,17]" SuppressExpression=" " SignalId="17" Enable="True" Visible="True" Description=" " DisplayIndex="17">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305002" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="18" EventName="RMU3# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,18]" SuppressExpression=" " SignalId="18" Enable="True" Visible="True" Description=" " DisplayIndex="18">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305003" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="19" EventName="RMU4# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,19]" SuppressExpression=" " SignalId="19" Enable="True" Visible="True" Description=" " DisplayIndex="19">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305004" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="20" EventName="RMU5# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,20]" SuppressExpression=" " SignalId="20" Enable="True" Visible="True" Description=" " DisplayIndex="20">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305005" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="21" EventName="RMU6# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,21]" SuppressExpression=" " SignalId="21" Enable="True" Visible="True" Description=" " DisplayIndex="21">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305006" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="22" EventName="RMU7# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,22]" SuppressExpression=" " SignalId="22" Enable="True" Visible="True" Description=" " DisplayIndex="22">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305007" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="23" EventName="RMU8# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,23]" SuppressExpression=" " SignalId="23" Enable="True" Visible="True" Description=" " DisplayIndex="23">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305008" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="24" EventName="RMU9# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,24]" SuppressExpression=" " SignalId="24" Enable="True" Visible="True" Description=" " DisplayIndex="24">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305009" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="25" EventName="RMU10# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,25]" SuppressExpression=" " SignalId="25" Enable="True" Visible="True" Description=" " DisplayIndex="25">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305010" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="26" EventName="RMU11# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,26]" SuppressExpression=" " SignalId="26" Enable="True" Visible="True" Description=" " DisplayIndex="26">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305011" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="27" EventName="RMU12# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,27]" SuppressExpression=" " SignalId="27" Enable="True" Visible="True" Description=" " DisplayIndex="27">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305012" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="28" EventName="RMU13# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,28]" SuppressExpression=" " SignalId="28" Enable="True" Visible="True" Description=" " DisplayIndex="28">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305013" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="29" EventName="RMU14# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,29]" SuppressExpression=" " SignalId="29" Enable="True" Visible="True" Description=" " DisplayIndex="29">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305014" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="30" EventName="RMU15# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,30]" SuppressExpression=" " SignalId="30" Enable="True" Visible="True" Description=" " DisplayIndex="30">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305015" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="31" EventName="RMU16# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,31]" SuppressExpression=" " SignalId="31" Enable="True" Visible="True" Description=" " DisplayIndex="31">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305016" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="32" EventName="RMU17# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,32]" SuppressExpression=" " SignalId="32" Enable="True" Visible="True" Description=" " DisplayIndex="32">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305017" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="33" EventName="RMU18# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,33]" SuppressExpression=" " SignalId="33" Enable="True" Visible="True" Description=" " DisplayIndex="33">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305018" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="34" EventName="RMU19# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,34]" SuppressExpression=" " SignalId="34" Enable="True" Visible="True" Description=" " DisplayIndex="34">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305019" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="35" EventName="RMU20# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,35]" SuppressExpression=" " SignalId="35" Enable="True" Visible="True" Description=" " DisplayIndex="35">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305020" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="36" EventName="RMU21# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,36]" SuppressExpression=" " SignalId="36" Enable="True" Visible="True" Description=" " DisplayIndex="36">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305021" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="37" EventName="RMU22# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,37]" SuppressExpression=" " SignalId="37" Enable="True" Visible="True" Description=" " DisplayIndex="37">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305022" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="38" EventName="RMU23# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,38]" SuppressExpression=" " SignalId="38" Enable="True" Visible="True" Description=" " DisplayIndex="38">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305023" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="39" EventName="RMU24# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,39]" SuppressExpression=" " SignalId="39" Enable="True" Visible="True" Description=" " DisplayIndex="39">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305024" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="40" EventName="RMU25# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,40]" SuppressExpression=" " SignalId="40" Enable="True" Visible="True" Description=" " DisplayIndex="40">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305025" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="41" EventName="RMU26# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,41]" SuppressExpression=" " SignalId="41" Enable="True" Visible="True" Description=" " DisplayIndex="41">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305026" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="42" EventName="RMU27# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,42]" SuppressExpression=" " SignalId="42" Enable="True" Visible="True" Description=" " DisplayIndex="42">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305027" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="43" EventName="RMU28# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,43]" SuppressExpression=" " SignalId="43" Enable="True" Visible="True" Description=" " DisplayIndex="43">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305028" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="44" EventName="RMU29# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,44]" SuppressExpression=" " SignalId="44" Enable="True" Visible="True" Description=" " DisplayIndex="44">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305029" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="45" EventName="RMU30# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,45]" SuppressExpression=" " SignalId="45" Enable="True" Visible="True" Description=" " DisplayIndex="45">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305030" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="46" EventName="RMU31# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,46]" SuppressExpression=" " SignalId="46" Enable="True" Visible="True" Description=" " DisplayIndex="46">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305031" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="47" EventName="RMU32# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,47]" SuppressExpression=" " SignalId="47" Enable="True" Visible="True" Description=" " DisplayIndex="47">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305032" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="48" EventName="RMU33# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,48]" SuppressExpression=" " SignalId="48" Enable="True" Visible="True" Description=" " DisplayIndex="48">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305033" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="49" EventName="RMU34# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,49]" SuppressExpression=" " SignalId="49" Enable="True" Visible="True" Description=" " DisplayIndex="49">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305034" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="50" EventName="RMU35# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,50]" SuppressExpression=" " SignalId="50" Enable="True" Visible="True" Description=" " DisplayIndex="50">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305035" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="51" EventName="RMU36# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,51]" SuppressExpression=" " SignalId="51" Enable="True" Visible="True" Description=" " DisplayIndex="51">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305036" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="52" EventName="RMU37# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,52]" SuppressExpression=" " SignalId="52" Enable="True" Visible="True" Description=" " DisplayIndex="52">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305037" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="53" EventName="RMU38# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,53]" SuppressExpression=" " SignalId="53" Enable="True" Visible="True" Description=" " DisplayIndex="53">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305038" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="54" EventName="RMU39# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,54]" SuppressExpression=" " SignalId="54" Enable="True" Visible="True" Description=" " DisplayIndex="54">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305039" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="55" EventName="RMU40# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,55]" SuppressExpression=" " SignalId="55" Enable="True" Visible="True" Description=" " DisplayIndex="55">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305040" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="56" EventName="RMU41# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,56]" SuppressExpression=" " SignalId="56" Enable="True" Visible="True" Description=" " DisplayIndex="56">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305041" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="57" EventName="RMU42# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,57]" SuppressExpression=" " SignalId="57" Enable="True" Visible="True" Description=" " DisplayIndex="57">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305042" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="58" EventName="RMU43# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,58]" SuppressExpression=" " SignalId="58" Enable="True" Visible="True" Description=" " DisplayIndex="58">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305043" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="59" EventName="RMU44# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,59]" SuppressExpression=" " SignalId="59" Enable="True" Visible="True" Description=" " DisplayIndex="59">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305044" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="60" EventName="RMU45# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,60]" SuppressExpression=" " SignalId="60" Enable="True" Visible="True" Description=" " DisplayIndex="60">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305045" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="61" EventName="RMU46# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,61]" SuppressExpression=" " SignalId="61" Enable="True" Visible="True" Description=" " DisplayIndex="61">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305046" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="62" EventName="RMU47# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,62]" SuppressExpression=" " SignalId="62" Enable="True" Visible="True" Description=" " DisplayIndex="62">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305047" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="63" EventName="RMU48# State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,63]" SuppressExpression=" " SignalId="63" Enable="True" Visible="True" Description=" " DisplayIndex="63">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="1302305048" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="64" EventName="Real Time Data Server State" EventCategory="6" StartType="1" EndType="3" StartExpression="[-1,64]" SuppressExpression=" " SignalId="64" Enable="True" Visible="True" Description=" " DisplayIndex="64">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Stop" EquipmentState="" BaseTypeId="" StandardName="0" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="Template Control" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="1" SamplerName="Workstation Self Diagnostic Sampler" SamplerType="0" ProtocolCode="Self Diagnostic Equipment 6-00" DllCode="" DLLVersion=" " ProtocolFilePath=" " DLLFilePath=" " DllPath="KoloBusinessServer.exe" Setting="" Description=" " />
  </Samplers>
</EquipmentTemplates>