{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"config-tool": {"root": "", "sourceRoot": "src", "projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}, "@schematics/angular:application": {"strict": true}}, "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./webpack.config.ts", "mergeRules": {"rules": "prepend"}}, "preserveSymlinks": true, "outputPath": "../wwwroot", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "inlineStyleLanguage": "less", "stylePreprocessorOptions": {"includePaths": ["src/styles", "src/styles/themes"]}, "assets": ["src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}, "src/favicon.ico", "src/favicon.png"], "styles": ["src/styles/style.less", "node_modules/ng-devui/devui.min.css", "node_modules/@devui-design/icons/icomoon/devui-icon.css", "node_modules/jspreadsheet-ce/dist/jspreadsheet.css", "node_modules/jsuites/dist/jsuites.css", {"input": "src/styles/default.less", "bundleName": "theme-default", "inject": false}, {"input": "src/styles/blue_white.less", "bundleName": "theme-blue_white", "inject": false}, {"input": "src/styles/deep_blue.less", "bundleName": "theme-deep_blue", "inject": false}], "scripts": ["node_modules/jspreadsheet-ce/dist/index.js", "node_modules/jsuites/dist/jsuites.js", "node_modules/echarts/dist/echarts.min.js"], "allowedCommonJsDependencies": ["angular2-chartjs", "polygon-clipping", "echarts", "lodash", "crypto-js"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "aot": true, "sourceMap": true, "optimization": false, "namedChunks": true, "webWorkerTsConfig": "tsconfig.worker.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "buildOptimizer": false, "sourceMap": true, "baseHref": ""}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "defaultConfiguration": "development", "options": {"browserTarget": "config-tool:build:development", "proxyConfig": "proxy.conf.json", "ssl": false, "sslKey": "./.cert/default.key", "sslCert": "./.cert/default.crt"}, "configurations": {"production": {"browserTarget": "config-tool:build:production"}, "development": {"browserTarget": "config-tool:build:development", "proxyConfig": "proxy.conf.json"}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "schematics": {"@schematics/angular:component": {"prefix": "ngx", "style": "less"}, "@schematics/angular:directive": {"prefix": "ngx"}}}