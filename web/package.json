{"name": "config-tool", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng", "dev": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng serve --configuration development --open", "build": "ng build  ", "lint": "ng lint", "lint:style": "stylelint \"app/**/*.less\"", "lint:fix": "ng lint config-tool --fix", "build:prod": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --configuration production --aot", "rmmodules": "rimraf node_modules", "generate": "ng generate component", "clear": "rm -rf ./.angular", "upgrade": "ng update @angular/core@17 @angular/cli@17 --force"}, "dependencies": {"@angular/animations": "17.2.3", "@angular/cdk": "^17.2.1", "@angular/common": "17.2.3", "@angular/compiler": "17.2.3", "@angular/core": "17.2.3", "@angular/forms": "17.2.3", "@angular/platform-browser": "17.2.3", "@angular/platform-browser-dynamic": "17.2.3", "@angular/router": "17.2.3", "@ant-design/icons-angular": "^17.0.0", "@codemirror/lang-json": "^6.0.1", "@codemirror/language": "^6.9.1", "@ctrl/tinycolor": "^4.1.0", "@devui-design/icons": "1.3.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "angular-split": "^15.0.0", "axios": "^1.1.3", "bootstrap": "5.1.3", "codemirror": "^6.0.1", "crypto-js": "^4.1.1", "dom-to-image": "^2.6.0", "echarts": "^5.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "intl": "1.2.5", "jspreadsheet-ce": "^4.2.1", "lodash": "^4.17.21", "ng-devui": "17.0.0", "ng-terminal": "^6.1.1", "ng-zorro-antd": "^17.2.0", "ngx-clipboard": "^16.0.0", "ngx-echarts": "^17.1.0", "normalize.css": "8.0.1", "polygon-clipping": "^0.15.3", "roboto-fontface": "0.10.0", "rxjs": "7.5.5", "script-eval-js": "^1.0.7", "tslib": "2.4.0", "weboot-plugin": "^1.1.2", "xlsx": "^0.18.5", "zone.js": "0.14.4"}, "devDependencies": {"@angular-builders/custom-webpack": "17.0.1", "@angular-builders/dev-server": "^7.3.1", "@angular-devkit/build-angular": "17.2.2", "@angular-devkit/core": "^17.2.2", "@angular-devkit/schematics": "^17.2.2", "@angular-eslint/builder": "17.2.1", "@angular-eslint/eslint-plugin": "17.2.1", "@angular-eslint/eslint-plugin-template": "17.2.1", "@angular-eslint/schematics": "17.2.1", "@angular-eslint/template-parser": "17.2.1", "@angular/cli": "17.2.2", "@angular/compiler-cli": "17.2.3", "@angular/language-service": "17.2.3", "@cucumber/html-formatter": "^20.2.1", "@fortawesome/fontawesome-free": "^6.2.0", "@types/crypto-js": "^4.1.1", "@types/dom-to-image": "^2.6.4", "@types/file-saver": "^2.0.7", "@types/jasmine": "4.0.3", "@types/lodash": "^4.14.202", "@types/node": "17.0.32", "@types/systemjs": "^6.13.1", "@typescript-eslint/eslint-plugin": "6.7.0", "@typescript-eslint/parser": "6.7.0", "codelyzer": "^0.0.28", "css-loader": "^6.8.1", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "npm-run-all": "4.1.5", "raw-loader": "^4.0.2", "style-loader": "^3.3.3", "stylelint": "14.14.1", "ts-loader": "^9.4.2", "typescript": "5.3.3", "webpack-bundle-analyzer": "^4.9.0"}}