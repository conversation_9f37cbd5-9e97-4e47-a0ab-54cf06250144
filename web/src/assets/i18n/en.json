{"code": 0, "common": {"keywordSearch": "Keywords Search", "selectDevice": "Select Device", "resetFactory": "Reset", "addEvent": "Add Event", "broadcast": "Broadcast", "save": "Save", "saveSuccess": "Saved Successfully", "copied": "Copied to clipboard", "ok": "Confirm", "cancel": "Cancel", "second": "Second", "quit": "Quit", "saveAndExit": "Save And Exit", "tip": "Tip", "importSuccess": "Imported Successfully", "importFail": "import Failed", "fail": "Fail", "selectIcon": "Select Icon", "selectTooltipDouble": "Double click to select", "clear": "Clear", "select": "Select", "upload": "Upload", "import": "Import", "export": "Export", "nodata": "No Data", "operation": "Operation", "yes": "yes", "no": "no"}, "routerTags": {"close": "Close", "closeOther": "Close Other"}, "menus": {"levelManage": "levelManage", "equipmentTemp": "EquipmentTemplate", "equipmentManagement": "EquipmentManagement"}, "levelManage": {"add": "Add Level", "delete": "Delete Node", "refresh": "Refresh Tree", "edit": "<PERSON>", "confirmDelete": "Delete Current Level?", "levelName": "Node Name", "addEquipment": "Add Equipment", "addMu": "Add MonitorUnit", "editMu": "Edit MonitorUnit", "deleteMu": "Delete MonitorUnit", "levelType": "Level Type", "samplerInfo": {"title": "Sampler Info", "addPort": "Add Port", "updatePort": "Modify Port", "deletePort": "Delete Port", "addSamplerUnit": "Add Sampler Unit", "updateSamplerUnit": "Modify Sampler Unit", "deleteSamplerUnit": "Delete Sampler Unit", "updateDevice": "Modify Equipment", "deleteDevice": "Delete Equipment"}, "deviceList": {"title": "Equipment List", "name": "Equipment Name", "template": "Equipment Template", "address": "Address", "editDivece": "Edit Equipment", "sort": "Sort->", "switchView": "Switch View"}}, "dialog": {}, "toast": {}, "protocol": {"name": "Sampler Unit Name", "dynamicLibrary": "DLL", "associatedTemplate": "Associated Template", "updateState": "Update State", "uploaded": "Uploaded", "notUploaded": "Not Uploaded", "model": "Model", "checkBox": ""}, "server": {"title": "Server Management", "type": "Type", "name": "Name", "number": "Number", "IP": "IP Address", "status": "Enable Status"}, "template": {"confirm": {"stationID": "Station ID", "stationName": "Station Name", "monitoringId": "Monitoring ID", "monitoringName": "Monitoring Name", "monitoringIP": "Monitoring IP", "deviceName": "Equipment Name", "station": "Station Name", "equipment": "Equipment Name", "configType": "Effect Config Type", "configName": "Effect Config Name", "configDescription": "Effect Config Description"}, "event": {"eventName": "Event Name", "displayOrder": "Display Order", "eventID": "Event ID", "eventCategory": "Event Category", "startType": "Start Type", "endType": "End Type", "condition": "Condition", "startExpression": "Start Expression", "abnormalExpression": "Abnormal Expression", "associatedSignal": "Associated Signal", "desc": "Description", "enable": "Enable", "visable": "Visable", "module": "<PERSON><PERSON><PERSON>", "turnoverTime": "Turnover Time", "eventSeverity": "Event Severity", "startOperator": "Start Operator", "startComparator": "Start Comparator", "startDelay": "Start Delay", "endOperator": "End Operator", "endComparator": "End Comparator", "endDelay": "End Delay", "eventDuration": "Event Duration", "eventThreshold": "Event Threshold", "meaning": "Meaning", "basicEvent": "Basic Event", "batteryStatus": "Battery Status", "floatingCharge": "Floating Charge", "discharge": "Discharge", "averageCharge": "Average Charge"}, "selector": {"name": "Template Name", "reason": "Template ID"}}, "baseClass": {"signalName": "Signal Name", "associatedBaseSignal": "Associate Base Signal", "confirmAgainTipCancelBaseSignal": "Confirm to cancle associated base signal?", "baseSignalId": "Base Signal Id", "baseSignalName": "Base Signal Name", "eventName": "Event Name", "associatedBaseEvent": "Associate Base Event", "confirmAgainTipCancelBaseEvent": "Confirm to cancle associated base event?", "baseEventId": "Base Event ID", "baseEventName": "Base Event Name", "controlName": "Control Name", "associatedBaseControl": "Associate Base Control", "confirmAgainTipCancelBaseControl": "Confirm to cancle associated base control?", "baseControlId": "Base Control ID", "baseControlName": "Base Control Name", "equipmentType": "Equipment Type", "extendedExpression": "Extended Expression", "eventCondition": "Event Condition", "index": "Index", "indexRule": "The index must be a positive integer", "noAssociatedBaseData": "No associated base data"}, "deviceManagement": {"event": {"equipmentName": "Equipment Name", "eventId": "Event Id", "eventName": "Event Name", "startExpression": "Start Expression", "suppressExpression": "Suppress Expression"}, "signal": {"equipmentName": "Equipment Name", "signalId": "Signal Id", "signalName": "Signal Name", "referenceUnit": "Reference Sampler Unit", "referenceChannelNo": "Reference Channel No", "expression": "Expression"}, "toTemplate": "Jump to Temp"}, "basicClassStandard": {"template": {"id": "Template ID", "name": "Template Name", "parentName": "Parent Template Name", "protocolCode": "Protocol Code", "deviceType": "Device Type", "basicDeviceType": "Basic Device Type", "signalProgress": "Signal Processing Progress(%)", "eventProgress": "Event Processing Progress(%)", "controlProgress": "Control Processing Progress(%)"}}, "virtualEquipment": {"add": {"houseName": "Room Name", "eqName": "Equipment Name", "signalName": "Signal Name", "eventName": "Event Name", "virtualEqName": "Virtual Eq Name", "virtualEqCOMport": "Virtual Eq COM Port", "virtualEqSamplerUnit": "Virtual Eq Sampler Unit", "virtualEqCategory": "Virtual Eq Category", "inputEqName": "Please input virtual eq name", "virtualEqMonitorUnit": "Virtual Eq Monitor Unit"}}, "customerStandardization": {"stationMapping": {"id": "Standardization St ID", "type": "Standardization St Type", "mapping": "Mapping", "baseID": "Base Station ID", "baseType": "Base Station Type", "count": "Count", "standardizationID": "Standardization ID", "standardizationType": "Standardization Type", "templateName": "Template Name", "templateId": "Template ID", "stationTempType": "Station Temp Type", "tempType": "Temp Type"}, "signalMapping": {"eqTpye": "Standardization Eq Type", "signalType": "Standardization Signal Type", "signalName": "Standardization Signal Name", "stationType": "Standardization St Type", "baseName": "Base Type Signal Name"}, "eventMapping": {"eqTpye": "Standardization Eq Type", "eventType": "Standardization Ev Type", "eventName": "Standardization Ev Name", "eventMeaning": "Standardization Ev Meaning", "eventThreshold": "Standardization Ev Threshold", "stationType": "Standardization St Type", "baseName": "Base Type Event Name", "id": "Standardization ID", "baseId": "Base Type ID"}, "controlMapping": {"eqTpye": "Standardization Eq Type", "controlType": "Standardization Con Type", "name": "Standardization Name", "stationType": "Standardization St Type", "baseName": "Base Type Control Name"}, "signalCheck": {"applicationStandardization": "Application Standardization", "templateId": "Eq Temp ID", "templateName": "Eq Temp Name", "templateType": "Temp St Type", "signalId": "Signal ID", "signalName": "Signal Name", "storageCycle": "Storage Cycle", "absoluteThreshold": "Absolute Threshold", "percentageThreshold": "Percentage Threshold", "statisticsCycle": "Statistics Cycle", "baseId": "Basic Signal ID", "baseName": "Basic Signal Name", "standardId": "Standard Signal ID", "standardName": "Standard Signal Name", "stationID": "Station ID", "stationName": "Station Name", "stationType": "Station Type", "eqId": "Equipment ID", "eqName": "Equipment Name", "eqCategory": "Equipment Category"}, "eventCheck": {"applicationStandardization": "Application Standardization", "templateId": "Eq Temp ID", "templateName": "Eq Temp Name", "templateType": "Temp St Type", "eventId": "Event ID", "eventName": "Event Name", "eventLevel": "Event Level", "eventDoorRestrictions": "Event Door Restrictions", "eventDelay": "Event Delay", "meaning": "Meaning", "baseId": "Basic Event ID", "baseName": "Basic Event Name", "standardId": "Standard Event ID", "standardName": "Standard Event Name", "stationID": "Station ID", "stationName": "Station Name", "stationType": "Station Type", "eqId": "Equipment ID", "eqName": "Equipment Name", "eqCategory": "Equipment Category"}, "controlCheck": {"applicationStandardization": "Application Standardization", "templateId": "Eq Temp ID", "templateName": "Eq Temp Name", "templateType": "Temp St Type", "controlId": "Control ID", "controlName": "Control Name", "baseId": "Basic Control ID", "baseName": "Basic Control Name", "standardId": "Standard Control ID", "standardName": "Standard Control Name", "stationID": "Station ID", "stationName": "Station Name", "stationType": "Station Type", "eqId": "Equipment ID", "eqName": "Equipment Name", "eqCategory": "Equipment Category"}}, "bytedanceMapping": {"signal": {"signalId": "Signal ID", "dictionaryEntry": "Dictionary Entry", "signalName": "Signal Name", "baseTypeName": "BaseType Name", "pointId": "Point ID", "pointName": "Point Name", "pointType": "Point Type", "mappingSignal": "Mapping Signal Name", "moduleNum": "<PERSON><PERSON><PERSON>", "description": "Description", "applyMapping": "Apply Selected Point Mapping", "unbindMapping": "Unbind Selected Point Mapping", "extendName": "Extend Name", "batchApplyEqCategory": "Batch Apply Eq Category", "bindUnmappingPoints": "Bind Unmapping Points", "unBindUnmappingPoints": "Unbind Unmapping Points"}, "event": {"eventId": "Event ID", "eventName": "Event Name", "mappingEvent": "Mapping Event Name"}, "control": {"controlId": "Control ID", "controlName": "Control Name", "mappingControl": "Mapping Control Name"}}}