// import { RouteTitle } from "@core/models/route.title";
import { Route, Data } from '@angular/router';
import { SessionService } from '@core/services/session.service'

declare module '!raw-loader!*' {
  const contents: string;
  export = contents;
}

declare module '*.txt' {
  const content: string;
  export default content;
}

declare module '*.json' {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const value: any;
  export default value;
}

export interface RouteTitle {
  value: string;
  needsTranslator?: boolean;
}

export interface PackageJson {
  name: string;
  version: string;
  dependencies: Reocrd<string, string>;
  devDependencies: Reocrd<string, string>;
}

declare module '@angular/core' {
  export   interface ComponentRef<C> {
    icon: string;
  }
}


declare module '@angular/router' {
  /**
   * document title
   */
  export interface Route {
    /**
     * menu title configure
     */
    title?: RouteTitle;
  }
}

declare global {

  export interface WheelEvent {
    wheelDelta: number;
  }

}
