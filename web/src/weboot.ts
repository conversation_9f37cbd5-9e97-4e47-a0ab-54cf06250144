import { AppModule } from 'app/app.module';
import { setupApplication, IApplicationEvents, MessageEvent, SharedChangeEvent } from 'weboot-plugin'

const _options: IApplicationEvents = {
    onMount: () => {
        console.warn('应用挂载');
    },
    onUnMount() {
        console.warn('应用卸载');
    },


    onMessage(args: MessageEvent<any>) {
        console.warn('应用消息', args);

        if  (AppModule.translateService){
            AppModule.translateService.use('en')
        }



    },


    onSharedChange(args: SharedChangeEvent) {
        for(const s of args.list){

            if(s.name === 'theme' && ['deep_blue', 'blue_white'].includes(s.value)) {
                AppModule.themeService?.changeTheme(s.value);
            }
        }
        console.warn('数据共享', args);
    },
    onDestroy() {
        console.warn('应用注销');
    },
}

setupApplication(_options);
