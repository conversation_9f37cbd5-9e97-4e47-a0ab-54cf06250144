<div class="modal-content">
  <div class="modal-body">
    <form novalidate class="form" #ngForm="ngForm">
      <div class="form-group">
        <label for="workStationType" class="col-sm-3 form-control-label"><i class="star"></i>服务器类型</label>
        <nz-select required [(ngModel)]="server.workStationType" [disabled]="server.workStationType"
          #workStationType="ngModel" name="workStationType" [nzOptions]="typeOptions"
          [ngClass]="{'errors':!server.workStationType && (workStationType.touched || submitted === false)}">
        </nz-select>
        <span class="error" *ngIf="!server.workStationType && (workStationType.touched || submitted===false)">必填</span>
      </div>

      <div class="form-group">
        <label for="workStationName" class="col-sm-3 form-control-label"><i class="star"></i>服务器名称</label>
        <input nz-input required name="workStationName" type="text" maxlength="128" class="col-sm-5 form-control"
          #workStationName="ngModel" [(ngModel)]="server.workStationName" (keyup)="onInputChange()"
          [ngClass]="{'errors':workStationName.errors && (workStationName.touched || submitted === false)}">
        <span class="error" *ngIf="workStationName.errors && (workStationName.touched || submitted===false)">必填</span>
      </div>

      <div class="form-group">
        <label for="center" class="col-sm-3 form-control-label">所属中心</label>
        <input nz-input name="center" readonly type="text" maxlength="128" class="col-sm-5 form-control"
          [(ngModel)]="input.center">
      </div>

      <div class="form-group">
        <label for="ipAddress" class="col-sm-3 form-control-label">IP地址</label>
        <input nz-input name="ipAddress" type="text" maxlength="128" class="col-sm-5 form-control" #ipAddress="ngModel"
          [(ngModel)]="server.ipAddress" pattern="^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$"
          [ngClass]="{'errors':ipAddress.errors?.required && (ipAddress.touched || submitted === false), 'patternError': ipAddress.errors?.pattern}">
        <span class="error" *ngIf="ipAddress.errors?.pattern && (ipAddress.touched || submitted === false)">格式错误</span>
      </div>

      <div class="form-group">
        <label for="ipV6Address" class="col-sm-3 form-control-label">IPV6地址</label>
        <input nz-input name="ipV6Address" type="text" maxlength="128" class="col-sm-5 form-control"
          #ipV6Address="ngModel" [(ngModel)]="server.ipV6Address" pattern="^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
          [ngClass]="{'patternError': ipV6Address.errors?.pattern}">
        <span class="error"
          *ngIf="ipV6Address.errors?.pattern && (ipAddress.touched || submitted === false)">格式错误</span>
      </div>

      <div class="form-group">
        <label for="isUsed" class="col-sm-3 form-control-label"><i class="star"></i>启用状态</label>
        <nz-select required [(ngModel)]="server.isUsed" #isUsed="ngModel" name="isUsed" [nzOptions]="statusOptions"
          [ngClass]="{'errors':(server.isUsed !== false) && (server.isUsed !== true) && (isUsed.touched || submitted === false)}">
        </nz-select>
        <span class="error"
          *ngIf="(server.isUsed !== false) && (server.isUsed !== true) && (isUsed.touched || submitted === false)">必填</span>
      </div>

      <div class="form-group">
        <label for="workStationName" class="col-sm-3 form-control-label"><i class="star"></i>基类告警名称</label>
        <nz-select required [(ngModel)]="server.baseTypeId" #baseTypeId="ngModel" name="baseTypeId"
          [nzOptions]="eventBasedicOptions"
          [ngClass]="{'errors':!server.baseTypeId && (baseTypeId.touched || submitted === false)}">
        </nz-select>
        <span class="error" *ngIf="!server.baseTypeId && (baseTypeId.touched || submitted === false)">必填</span>
      </div>
    </form>
  </div>
  <div *nzModalFooter>
    <button nz-button nzType="default" (click)="close()">取消</button>
    <button nz-button nzType="primary" (click)="submit(this.submitted=true && ngForm.valid)">确定</button>
  </div>
</div>