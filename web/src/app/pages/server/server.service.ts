import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'

@Injectable()
export class ServerService extends RestfulService {

    /**
    * 获取服务器列表
    * @returns
    */
    public async getList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/workstation/list`)
        return res.data
    }

    /**
    * 获取告警基类列表
    * @returns
    */
    public async getEventBasedicList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/eventbasedic/list?baseEquipmentId=1302`)
        return res.data
    }
    /**
    * 新增服务器
    * @returns
    */
    public async addServer(data: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/workstation/create`, data)
        return res.data
    }

    /**
    * 修改服务器
    * @returns
    */
    public async updateServer(data: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/workstation/update`, data)
        return res.data
    }

    /**
    * 删除服务器
    * @returns
    */
    public async deleteServer(id: any): Promise<[]> {
        const params = { ids: id };
        const res = await this.delete<[]>(`api/config/workstation/delete`, params as any)
        return res.data
    }

    /**
    * 表达式验证
    * @returns
    */
    public async getExpressionValidate(expression: any): Promise<any[]> {
        const params = { expression: expression }
        const res = await this.post<any[]>('/api/config/expression/validate', params)
        return res.data
    }
}
