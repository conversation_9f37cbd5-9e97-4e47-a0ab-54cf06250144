<div class="server">
    <div class="line">
        <button nz-button nzType="primary" (click)="openModal()">
            <i class="icon icon-add"></i> 新增
        </button>
        <input nz-input type="text" placeholder="输入内容检索..." [(ngModel)]="filterValue"
            (ngModelChange)="onFilterChange()">
    </div>
    <d-data-table [dataSource]="tableData" [fixHeader]="true" tableHeight="calc(100% - 52px)"
        [tableWidthConfig]="tableWidthConfig" [striped]="true" [scrollable]="true" [tableOverflowType]="'overlay'"
        dLoading [loadingStyle]="'default'" [loading]="isUpdating">
        <thead dTableHead>
            <tr dTableRow>
                <th dHeadCell *ngFor="let colOption of columns">{{ colOption.header}}</th>
                <th dHeadCell>操作</th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow (contextmenu)="rowContextmenu($event, rowMenu)"
                    [ngClass]="{'disuse':!rowItem.isUsed,'table-row-selected': rowItem.$checked}"
                    (mousedown)="onRowClick(rowItem, rowIndex)">
                    <ng-container *ngFor="let col of columns">
                        <td dTableCell *ngIf="col['field'] === 'workStationType'">
                            {{ typeArr[rowItem[col.field]] }}
                        </td>
                        <td dTableCell *ngIf="col['field'] === 'isUsed'">
                            <ng-container *ngIf="rowItem[col.field]">
                                <nz-tag [nzColor]="'green'">启用</nz-tag>
                            </ng-container>
                            <ng-container *ngIf="!rowItem[col.field]">
                                <nz-tag [nzColor]="'red'">停用</nz-tag>
                            </ng-container>
                        </td>
                        <td dTableCell *ngIf="col['field'] !== 'workStationType' &&
                        col['field'] !== 'isUsed'">
                            {{ rowItem[col.field] }}
                        </td>
                    </ng-container>
                    <td class="iconfont">
                        <iconfont [icon]="'icon-edit-set'" title="修改" (click)="openModal(rowItem)"></iconfont>
                        <!-- <iconfont [icon]="'icon-delete2'" title="删除" (click)="delete(rowItem)"></iconfont> -->
                    </td>
                </tr>
                <nz-dropdown-menu #rowMenu="nzDropdownMenu">
                    <ul nz-menu nzSelectable>
                        <li nz-menu-item (click)="openModal(rowItem)">
                            <iconfont icon="icon-edit-set" class="right-menu-icon" title="查看RMU下监控单元"></iconfont>
                            修改服务器
                        </li>
                        <li nz-menu-item
                            *ngIf="rowItem.workStationType==2 || rowItem.workStationType==23 || rowItem.workStationType==31 || rowItem.workStationType==32"
                            (click)="delete(rowItem)">
                            <iconfont icon="icon-delete2" class="right-menu-icon" title="查看RMU下监控单元"></iconfont>
                            删除服务器
                        </li>
                        <li nz-menu-item *ngIf="rowItem.workStationType==8" (click)="showMulistRmu(rowItem)">
                            <iconfont icon="icon-kejian" class="right-menu-icon" title="查看RMU下监控单元"></iconfont>
                            查看RMU下监控单元
                        </li>
                        <li nz-menu-item *ngIf="rowItem.workStationType==8" (click)="batchDistribute(rowItem)">
                            <iconfont icon="icon-deliveryconf" class="right-menu-icon" title="批量分发RMU配置"></iconfont>
                            批量分发RMU配置
                        </li>
                    </ul>
                </nz-dropdown-menu>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div *ngIf="!isUpdating" style="text-align: center; margin-top: 20px">
                <nz-empty nzNotFoundImage="simple" />
            </div>
        </ng-template>
    </d-data-table>
</div>