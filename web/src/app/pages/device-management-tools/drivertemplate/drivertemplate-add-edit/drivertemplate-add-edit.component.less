:host ::ng-deep {
    .form-group {
        display: grid;
        grid-template-columns: 30% 50% 20%;
        margin-bottom: 15px
    }

    label {
        text-align: right;
        margin-right: 20px;
        align-self: center;
    }

    .error {
        color: red;
        padding-left: 12px;
        align-self: center;
    }

    .errors {
        border-color: red;
    }

    .ant-select.errors .ant-select-selector {
        border-color: red;
    }

    .star {
        padding-right: 12px;
        align-self: center;
    }

    .star::after {
        content: '*';
        font-size: 10px;
        color: red;
    }
}