import { Component, Injector, Input } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { DeviceManagementToolsService } from '../../device-management-tools.services';
import { DataDictionaryService } from '@services/data-dictionary.service';

@Component({
  selector: 'app-drivertemplate-add-edit',
  templateUrl: './drivertemplate-add-edit.component.html',
  styleUrl: './drivertemplate-add-edit.component.less'
})
export class DrivertemplateAddEditComponent extends GenericModalComponent<{}, Boolean>{
  @Input() input: any;
  deviceTemlate: any;
  submitted: any;
  driveTemplateTypeSnap: any = [];
  isDefaultTemplateSnap: any = [{ label: '是', value: 1 }, { label: '否', value: 0 }];

  constructor(injector: Injector,
    private service: DeviceManagementToolsService,
    private dataDictionaryService: DataDictionaryService,
  ) { super(injector); }


  onInit() {
    this.dataDictionaryService.getDataItems(2023).then(res => {
      this.driveTemplateTypeSnap = res.map((item: any) => ({
        label: item['itemValue'], value: item['itemId']
      }));
    })
    this.deviceTemlate = this.input.data || { driveTemplateType: 1, isDefaultTemplate: 0 }
  }

  async submit(ngForm: any) {
    if (!ngForm) return;
    const text = this.input.title;
    try {
      let response;
      if (this.input.data) {
        response = await this.service.updateDeviceTemlate(this.deviceTemlate);
      } else {
        response = await this.service.addDeviceTemlate(this.deviceTemlate);
      }
      this.messageService.create('success', `${text}成功`);
      this.close(true);
    } catch (error) {
      this.messageService.create('error', `${text}失败`);
      this.close();
    }
  }

}
