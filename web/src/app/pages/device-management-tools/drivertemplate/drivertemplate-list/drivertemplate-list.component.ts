import { Component, ElementRef, Injector, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { tableResizeFunc } from 'ng-devui/data-table';
import { DrivertemplateAddEditComponent } from '../drivertemplate-add-edit/drivertemplate-add-edit.component';
import { DeviceManagementToolsService } from '../../device-management-tools.services';
import { DrivertemplateEdittreeComponent } from '../drivertemplate-edittree/drivertemplate-edittree.component';
import { filter } from 'rxjs';
import { NavigationEnd } from '@angular/router';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-drivertemplate-list',
  templateUrl: './drivertemplate-list.component.html',
  styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './drivertemplate-list.component.less']
})
export class DrivertemplateListComponent extends DevuiTableFilterComponent implements OnInit {
  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: DeviceManagementToolsService,
  ) {
    super(injector, ele)
  }

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'driveTemplateName',
      width: '120px',
      title: '模板名称',
    },
    {
      field: 'driveTemplateType',
      width: '120px',
      title: '模板类型',
    },
    {
      field: 'isDefaultTemplate',
      width: '100px',
      title: '是否默认模板',
    },
    {
      field: 'driverTemplateDescribe',
      width: '180px',
      title: '描述',
    },
    {
      field: 'operation',
      width: '120px',
      title: '操作',
    },
  ]

  // 方法
  protected onInit(): void {
    super.onInit()
    this.getList()
    this.subscribe(this.router.events.pipe(
      filter(event => event instanceof NavigationEnd && event.url == '/pages/drivertemplate-list')),
      (event) => {
        this.getList();
      })
  }

  // 请求设备类型数据
  public getList(): void {
    this.service.geDriveTemplatestList().then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
      }
    })
  }

  async openModal(item?: any): Promise<void> {
    const data = item ? cloneDeep(item) : null;
    const text = data ? '修改' : '新增'
    const VV = this.openDialog({
      nzTitle: `${text}驱动模板`,
      nzContent: DrivertemplateAddEditComponent,
      nzData: { data: data, title: text },
    });
    const result = await this.waitDialog(VV);
    if (result) this.getList();
  }

  async edittree(item?: any): Promise<void> {
    const VV = this.openDialog({
      nzTitle: '模板结构',
      nzWidth: '900px',
      nzContent: DrivertemplateEdittreeComponent,
      nzData: { id: item.id },
    });
    const result = await this.waitDialog(VV);
    if (result) this.getList();
  }

  delete(event: any) {
    if (event.isDefaultTemplate) {
      this.messageService.error('默认模板不能删除');
    } else {
      this.modalService.confirm({
        nzTitle: '提示',
        nzCentered: true,
        nzContent: `请确认是否要删除模板：${event.driveTemplateName}?`,
        nzOkText: '确定',
        nzOnOk: () => {
          this.service.deleteDeviceTemlate(event.id).then((res: any) => {
            if (res) {
              this.messageService.create('success', `操作成功`);
              this.getList();
            } else {
              this.messageService.create('error', `操作失败`);
            }
          })
        },
      });
    }
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
