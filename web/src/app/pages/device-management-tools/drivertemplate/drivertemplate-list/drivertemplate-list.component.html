<div>
    <div class="text-left">
        <button nz-button nzType="primary" (click)="openModal()">
            <i class="icon icon-add"></i> 新增
        </button>
    </div>
    <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
        [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'100%'"
        [containFixHeaderHeight]="true" [fixHeader]="true" (tableScrollEvent)="tableScrollEvent($event)">
        <thead dTableHead>
            <tr dTableRow>
                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== 'operation'"
                    (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, colConfig.field)">
                    {{ colConfig.title }}
                    <!-- 空白 -->
                    <div *ngIf="colConfig.field === 'operation'" style="height: 32px;"></div>
                    <!-- 输入框 -->
                    <input *ngIf="colConfig.field !== 'operation'" nz-input [(ngModel)]="filterData[colConfig.field]"
                        (ngModelChange)="filterChange()" />
                </th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow>
                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell>
                        @if (colConfig.field === 'driveTemplateType') {
                        {{ rowItem[colConfig.field] === 1 ? 'SNMP' : 'BACNet'}}
                        }@else if(colConfig.field === 'isDefaultTemplate') {
                        {{ rowItem[colConfig.field]? '是' : '否'}}
                        }@else if(colConfig.field === 'operation') {
                        <iconfont [icon]="'icon-setting-icon'" title="编辑树结构" (click)="edittree(rowItem)"></iconfont>
                        <iconfont [icon]="'icon-edit-set'" title="修改" (click)="openModal(rowItem)"></iconfont>
                        <iconfont [icon]="'icon-delete2'" title="删除" (click)="delete(rowItem)"></iconfont>
                        }@else {
                        {{ rowItem[colConfig.field] }}
                        }
                    </td>
                </tr>
            </ng-template>
        </tbody>
    </d-data-table>
</div>