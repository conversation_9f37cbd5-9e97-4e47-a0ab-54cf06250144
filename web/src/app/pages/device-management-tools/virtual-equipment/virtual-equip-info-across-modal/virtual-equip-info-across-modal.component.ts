import { Component, ElementRef, Injector, Input, OnInit, inject } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { DeviceManagementToolsService } from '../../device-management-tools.services';

@Component({
  selector: 'ngx-virtual-equip-across-info-modal',
  templateUrl: './virtual-equip-info-across-modal.component.html',
  styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './virtual-equip-info-across-modal.component.less']
})
export class VirualEquipInfoAcrossModalComponent extends DevuiTableFilterComponent implements OnInit {
  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: DeviceManagementToolsService,

  ) {
    super(injector, ele)
  }

  @Input()
  public readonly input: any = inject(NZ_MODAL_DATA);
  #modal = inject(NzModalRef);

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    
    {
      field: 'virtualStationName',
      width: '150px',
      title: '虚拟设备下挂局站',
    },
    {
      field: 'virtualHouseName',
      width: '150px',
      title: '虚拟设备下挂局房',
    },
    {
      field: 'virtualMonitorUnitName',
      width: '200px',
      title: '虚拟设备下挂设备监控单元',
    },
    {
      field: 'virtualEquipmentTemplateName',
      width: '250px',
      title: '虚拟设备设备模板',
    },
    {
      field: 'virtualEquipmentName',
      width: '150px',
      title: '虚拟设备名',
    },
    {
      field: 'virtualSignalName',
      width: '150px',
      title: '虚拟信号名',
    },
    {
      field: 'crossSiteExpression',
      width: '250px',
      title: '跨站表达式',
    },
  ]

  // 方法
  protected onInit(): void {
    super.onInit()
    console.log(123);

    this.getInfo()
  }

  // 请求设备类型数据
  public getInfo(): void {
    this.service.getVirtualEquipmentInfoAcross(this.input.eqId).then((res: any) => {
      if (res) {
        this.displayTableDataSource = res
        // this.filterTableDataSource = [...this.orgTableDataSource]
        // this.displayTableDataSource = [...this.orgTableDataSource]
        console.log(this.displayTableDataSource);

      }
    })
  }

  cancel() {
    this.#modal.destroy();
  }

}
