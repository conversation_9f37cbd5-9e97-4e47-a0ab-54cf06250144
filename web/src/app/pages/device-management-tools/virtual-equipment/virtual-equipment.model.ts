export namespace VirtualEquipModel {
    export interface virtualEquipExcelItem {
        virtualEquipmentCategoryName: string;
        virtualEquipmentName: string;
        virtualEquipmentTemplateName: string;
        virtualHouseName: string;
        virtualMonitorUnitName: string;
        virtualSamplerUnitName: string;
        virtualStationName: string;
        virtualPortName: string;
    }

    export interface virtualSignalExcelItem {
        channelNo: number;
        equipmentName: string;
        virtualEquipmentName: string;
        virtualSignalName: string;
    }

    export interface importVirtualEquipExcel {
        virtualEquipmentExcelList: virtualEquipExcelItem[];
        virtualSignalExcelList: virtualSignalExcelItem[];
    }

    export interface StructTree {
        children: StructTree[];
        monitorUnitChildren: MonitorNode[];
        resourceStructureName: string;
        resourceStructureId: number;
        parentResourceStructureId: number;
    }

    export interface MonitorNode {
        equipmentChildren: EquipmentNode[];
        monitorUnitId: number;
        monitorUnitName: string;
    }

    export interface EquipmentNode {
        equipmentId: number;
        equipmentName: string;
        isVirtualEquipment: boolean;
    }

    export interface TreeNodeItem {
        rid: string;
        name: string;
        children?: TreeNodeItem[];
        parentRid?: string;
        monitorUnitId?: string;
        equipmentId?: string;
        isVirtualEquipment?: boolean;
        isFirstSelected?: boolean;
        isSelectedSignal?: boolean;
        parentNodeNoChild?: boolean;
        parentMonitorId?: string;
        houseName?: string;
    }

    export interface VirtualPreviewItem {
        uniqueId: string;
        houseName: string;
        eqId: string;
        eqName: string;
        signalId: string;
        signalName: string;
        eventId?: string;
        eventName?: string;
    }

    export interface SplitEquipmentItem {
        equipmentId: string;
        equipmentSplitInfoVos: EquipmentSplitInfoVos[];
        equipmentCategory: string;
        samplerUnitId: string;
        virtualEquipmentName: string;
    }

    export interface EquipmentSplitInfoVos {
        equipmentId: string;
        eventId: string;
        signalId: string;
    }

    export interface EquipmentSplitItem {
        equipmentCategory: string;
        monitorUnitId: string;
        portId: string;
        samplerUnitId: string;
    }
}