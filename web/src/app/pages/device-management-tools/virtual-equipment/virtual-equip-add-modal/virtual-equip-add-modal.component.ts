import { DeviceManagementToolsService } from 'app/pages/device-management-tools/device-management-tools.services';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Component, Injector, OnInit, inject } from '@angular/core';

interface NgSelectOptionItem {
    value: string;
    label: string;
}

@Component({
  selector: 'app-virtual-equip-add-modal',
  templateUrl: './virtual-equip-add-modal.component.html',
  styleUrls: ['./virtual-equip-add-modal.component.less']
})

export class VirtualEquipAddModalComponent implements OnInit {

    #modal = inject(NzModalRef);

    virtualPreviewList: any = [];
    selectMonitorUnitId!: string;

    virtualEquipmentNameText: string = '';
    virtualComPort!: string;
    virtualSamplerUnit!: string;
    virtualEquipmentType!: string;

    getEqCategory: boolean = false;

    equipmentSplitInfo: any;

    comPort: {label: string, value: string}[] = [];
    monitorUnit: {label: string, value: string}[] = [];
    equipmentType: {label: string, value: string}[] = [];

    public constructor(
        injector: Injector,
        private translate: TranslateService,
        private service: DeviceManagementToolsService,
        private message: NzMessageService,
        private modal: NzModalService,
    ) {
        
    }

    ngOnInit(): void {
        this.getModalList();
    }



    getModalList() {
        this.service.getSplitEquipment(this.virtualPreviewList[0].eqId).then((res: any) => {
            this.equipmentSplitInfo = {
                equipmentCategory: res.equipmentCategory.toString(),
                monitorUnitId: res.monitorUnitId.toString(),
                portId: res.portId.toString(),
                samplerUnitId: res.samplerUnitId.toString(),
            }
            this.getPortByMonitorUnit();
        })
    }

    buildNzOptionList(list: any, label: string, value: string): NgSelectOptionItem[]{
        let result: NgSelectOptionItem[] = [];
        list.forEach((obj: any) => {
            let item = {
                value: obj[value].toString(),
                label: obj[label].toString(),
            }
            result.push(item);
        })
        return result;
    }

    getPortByMonitorUnit() {
        this.service.getComPortList(this.selectMonitorUnitId).then((res: any) => {
            this.comPort = this.buildNzOptionList(res, 'portName', 'portId');
            if (this.comPort && this.comPort.length) {
              this.virtualComPort = this.equipmentSplitInfo.portId;
              this.selectComPort();
            }
        })
    }

    selectComPort() {
        this.virtualSamplerUnit = '';
        this.service.getSamplerUnitList(this.selectMonitorUnitId, this.virtualComPort).then(res => {
            this.monitorUnit = this.buildNzOptionList(res, 'samplerUnitName', 'samplerUnitId');
            if (this.monitorUnit && this.monitorUnit.length) {
              this.virtualSamplerUnit = this.equipmentSplitInfo.samplerUnitId;
            }
        })
        this.getEquipmentCategoryList();
    }

    getEquipmentCategoryList() {
        if(!this.getEqCategory) {
            this.service.getEqCategoryList().then(res => {
                this.equipmentType = this.buildNzOptionList(res, 'itemValue', 'itemId');
                this.virtualEquipmentType = this.equipmentSplitInfo.equipmentCategory;
                this.getEqCategory = true;
            })
        } else {
            this.virtualEquipmentType = this.equipmentSplitInfo.equipmentCategory;
        }
    }

    confirm() {
        if (!this.virtualEquipmentNameText
            || !this.virtualSamplerUnit
            || !this.virtualComPort
            || !this.virtualEquipmentType
            ) {
            this.message.warning('请完善配置信息！');
            return;
        }

        let firstEqId: any;
        const equipmentSplitInfoVos: any = [];
        this.virtualPreviewList.forEach((item: any) => {
            if (!firstEqId) {
                firstEqId = item.eqId.toString();
            }
            const temp = {
                equipmentId: item.eqId ? item.eqId.toString() : null,
                signalId: item.signalId ? item.signalId.toString() : null,
                eventId: item.eventId ? item.eventId.toString() : null,
            }
            equipmentSplitInfoVos.push(temp);
        })

        const params: any = {
            equipmentId: firstEqId,
            equipmentCategory: this.virtualEquipmentType.toString(),
            samplerUnitId: this.virtualSamplerUnit.toString(),
            virtualEquipmentName: this.virtualEquipmentNameText,
            equipmentSplitInfoVos: equipmentSplitInfoVos 
        }

        this.service.createSplitEquipment(params).then(
            res => {
                if(res.code === 0) {
                    this.message.success('创建成功！');
                    this.#modal.destroy('success');
                }
                if(res.code === 1001) {
                    this.message.warning(res.msg);
                }
            },
        )
    }

    cancel() {
        this.#modal.destroy();
    }

    onInputChange(): void {
        // 替换掉特殊字符    
        let name = this.virtualEquipmentNameText.replace(/[\\\/\?<>*:.|""]/g, '');
        this.virtualEquipmentNameText = name;
    }
}