<div class="veam_container">
    <div class="veam_item">
        <div class="veam_label">
            {{ 'virtualEquipment.virtualEquipment' | translate}}
        </div>
        <div class="veam_value">
            <input
                class="veam_input"
                nz-input
                placeholder="{{ 'virtualEquipment.add.inputEqName' | translate}}"
                maxlength="108"
                [(ngModel)]="virtualEquipmentNameText"
                (keyup)="onInputChange()"
                type="text" />
        </div>
    </div>
    <div class="veam_item">
        <div class="veam_label">
            {{ 'virtualEquipment.add.virtualEqCOMport' | translate}}
        </div>
        <div class="veam_value">
            <nz-select class="veam_selector" [nzOptions]="comPort"
                [(ngModel)]="virtualComPort" (ngModelChange)="selectComPort()">
            </nz-select>
        </div>
    </div>
    <div class="veam_item">
        <div class="veam_label">
            {{ 'virtualEquipment.add.virtualEqSamplerUnit' | translate}}
        </div>
        <div class="veam_value">
            <nz-select class="veam_selector" [nzOptions]="monitorUnit"
                [(ngModel)]="virtualSamplerUnit">
            </nz-select>
        </div>
    </div>
    <div class="veam_item">
        <div class="veam_label">
            {{ 'virtualEquipment.add.virtualEqCategory' | translate}}
        </div>
        <div class="veam_value">
            <nz-select class="veam_selector" [nzOptions]="equipmentType"
                [(ngModel)]="virtualEquipmentType">
            </nz-select>
        </div>
    </div>
</div>