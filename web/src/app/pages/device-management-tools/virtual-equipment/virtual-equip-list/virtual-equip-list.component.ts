import { Component, ElementRef, Injector, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { DeviceManagementToolsService } from '../../device-management-tools.services';
import { tableResizeFunc } from 'ng-devui/data-table';
import { DateUtil } from '@core/util/date.util';
import { VirualEquipInfoModalComponent } from '../virual-equip-info-modal/virual-equip-info-modal.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import * as XLSX from 'xlsx';
import { VirtualEquipModel } from '../virtual-equipment.model';
import { NavigationEnd } from '@angular/router';
import { filter } from 'rxjs';

@Component({
  selector: 'app-virtual-equip-list',
  templateUrl: './virtual-equip-list.component.html',
  styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', 'virtual-equip-list.component.less']

})
export class VirtualEquipListComponent extends DevuiTableFilterComponent implements OnInit {
  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: DeviceManagementToolsService,
    private modal: NzModalService,
  ) {
    super(injector, ele)
    this.subscribe(this.router.events.pipe(
      filter((event: any) => event instanceof NavigationEnd && event.url == '/pages/virtual-equip-list')),
      (event) => {
        this.getList();
      })
  }

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'checkBox',
      width: '50px',
      title: '',
    },
    {
      field: 'virtualStationName',
      width: '150px',
      title: '虚拟设备下挂局站',
    },
    {
      field: 'virtualHouseName',
      width: '150px',
      title: '虚拟设备下挂局房',
    },
    {
      field: 'virtualMonitorUnitName',
      width: '150px',
      title: '虚拟设备下挂设备监控单元',
    },
    {
      field: 'virtualEquipmentTemplateName',
      width: '150px',
      title: '虚拟设备设备模板',
    },
    {
      field: 'virtualEquipmentName',
      width: '150px',
      title: '虚拟设备名',
    },
    {
      field: 'operation',
      width: '120px',
      title: '操作',
    }
  ]
  checkAll: boolean = false;
  checkedIds: any = [];

  columnTitlesEquip: any = [];
  columnTitlesSignal: any = [];
  columnExportEquip: any = [];

  // 方法
  protected onInit(): void {
    super.onInit()
    this.columnTitlesEquip = [
      this.translate.instant('virtualEquipment.virtualEquipmentName'),
      this.translate.instant('virtualEquipment.virtualEquipmentType'),
      this.translate.instant('virtualEquipment.virtualMonitorUnit'),
      this.translate.instant('virtualEquipment.virtualSamplerUnitName'),
      this.translate.instant('virtualEquipment.virtualEquipmentTemplate'),
      this.translate.instant('virtualEquipment.virtualHouseName'),
      this.translate.instant('virtualEquipment.virtualStationName'),
    ];

    this.columnExportEquip = [
      this.translate.instant('virtualEquipment.virtualEquipmentName'),
      this.translate.instant('virtualEquipment.virtualEquipmentType'),
      this.translate.instant('virtualEquipment.virtualMonitorUnit'),
      this.translate.instant('virtualEquipment.virtualSamplerUnitName'),
      this.translate.instant('virtualEquipment.virtualPortName'),
      this.translate.instant('virtualEquipment.virtualEquipmentTemplate'),
      this.translate.instant('virtualEquipment.virtualHouseName'),
      this.translate.instant('virtualEquipment.virtualStationName'),
    ];

    this.columnTitlesSignal = [
      this.translate.instant('virtualEquipment.virtualEquipmentName'),
      this.translate.instant('virtualEquipment.virtualSignalName'),
      this.translate.instant('virtualEquipment.originalEquipmentName'),
      this.translate.instant('virtualEquipment.originalChannelNo'),
    ];

    this.getList()
  }

  // 请求设备类型数据
  public getList(): void {
    this.service.getVirtualEquipmentList().then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
      }
    })
  }

  add() {
    this.router.navigateByUrl('/pages/virtual-equip-add').finally();
  }

  batchAdd() {
    this.ele.nativeElement.querySelector(".hidden-file").click();
  }

  fileChange(event: any) {
    const target: DataTransfer = event.target as DataTransfer;
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: 'binary' });
      const wsNameArr = wb.SheetNames;
      const data: any[][] = [];
      wsNameArr.forEach(item => {
        const ws: XLSX.WorkSheet = wb.Sheets[item];
        data.push(XLSX.utils.sheet_to_json(ws, { header: 1 }))
      })
      this.processImportData(data);
    }
    reader.readAsBinaryString(target.files[0]);
    event.target.value = null;
  }

  processImportData(data: any[][]) {
    const virtualEquipExcelList: VirtualEquipModel.virtualEquipExcelItem[] = [];
    const virtualSignalExcelList: VirtualEquipModel.virtualSignalExcelItem[] = [];
    data[0].forEach((equipItem, index) => {
      // 首行为标题
      if (index) {
        const row: VirtualEquipModel.virtualEquipExcelItem = {
          virtualEquipmentName: equipItem[0],
          virtualEquipmentCategoryName: equipItem[1],
          virtualMonitorUnitName: equipItem[2],
          virtualSamplerUnitName: equipItem[3],
          virtualPortName: equipItem[4],
          virtualEquipmentTemplateName: equipItem[5],
          virtualHouseName: equipItem[6],
          virtualStationName: equipItem[7],
        }
        virtualEquipExcelList.push(row);
      }
    });
    data[1].forEach((signalItem, index) => {
      // 首行为标题
      if (index) {
        const row: VirtualEquipModel.virtualSignalExcelItem = {
          virtualEquipmentName: signalItem[0],
          virtualSignalName: signalItem[1],
          equipmentName: signalItem[2],
          channelNo: signalItem[3],
        }
        if (row.virtualEquipmentName) {
          virtualSignalExcelList.push(row);
        }
      }
    });

    this.service.virtualEquipImport({
      virtualEquipmentExcelList: virtualEquipExcelList,
      virtualSignalExcelList: virtualSignalExcelList
    }).then(res => {
      if (res) {
        if (!res.length) {
          this.messageService.success('操作成功');
          this.getList();
        } else {
          const tips = this.translate.instant('virtualEquipment.importErrotMsg');
          this.modalService.confirm({
            nzTitle: '提示',
            nzCentered: true,
            nzContent: tips,
            nzOkText: '确定',
            nzOnOk: () => {
              this.handleImportError(res, virtualSignalExcelList);
            },
          });
        }
      }
    }, err => {
      this.messageService.error(this.translate.instant('common.error'));
    })
  }

  handleImportError(errorList: { rowIndex: number, message: string }[], virtualSignalExcelList: VirtualEquipModel.virtualSignalExcelItem[]) {
    const fileName = this.translate.instant('virtualEquipment.instanceSignal') + '_' + this.translate.instant('common.error');

    const xlsTemplateJsonSignal: any = [];
    xlsTemplateJsonSignal.push([...this.columnTitlesSignal,
    this.translate.instant('virtualEquipment.succeedOrNot'),
    this.translate.instant('virtualEquipment.feedback')
    ]);

    virtualSignalExcelList.forEach(signalItem => {
      const rowSignal = [];

      rowSignal.push(signalItem.virtualEquipmentName);
      rowSignal.push(signalItem.virtualSignalName);
      rowSignal.push(signalItem.equipmentName);
      rowSignal.push(signalItem.channelNo);
      rowSignal.push('☑');

      xlsTemplateJsonSignal.push([...rowSignal]);
    });

    errorList.forEach(item => {
      xlsTemplateJsonSignal[item.rowIndex + 1][4] = '☒';
      xlsTemplateJsonSignal[item.rowIndex + 1][5] = item.message;
    });

    const wsSignal: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJsonSignal);
    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, wsSignal, this.translate.instant('virtualEquipment.instanceSignal') + '_' + this.translate.instant('common.error'));
    /* save to file */
    XLSX.writeFile(wb, fileName + '.xlsx');
  }

  export() {
    const currentDate = DateUtil.format(new Date(), 'yyyy-MM-dd hh:mm:ss');
    const fileName = this.translate.instant('virtualEquipment.virtualEquipment') + '_' + this.translate.instant('virtualEquipment.instanceSignal') + '_' + currentDate;

    const xlsTemplateJsonEquip: any = [], xlsTemplateJsonSignal: any = [];
    xlsTemplateJsonEquip.push([...this.columnExportEquip]);
    xlsTemplateJsonSignal.push([...this.columnTitlesSignal]);

    this.service.getVirtualEquipExport(this.checkedIds).then((res: any) => {
      if (res) {
        res.virtualEquipmentExcelList.forEach((equipItem: any) => {
          const rowEquip = [];
          rowEquip.push(equipItem.virtualEquipmentName);
          rowEquip.push(equipItem.virtualEquipmentCategoryName);
          rowEquip.push(equipItem.virtualMonitorUnitName);
          rowEquip.push(equipItem.virtualSamplerUnitName);
          rowEquip.push(equipItem.virtualPortName);
          rowEquip.push(equipItem.virtualEquipmentTemplateName);
          rowEquip.push(equipItem.virtualHouseName);
          rowEquip.push(equipItem.virtualStationName);

          xlsTemplateJsonEquip.push([...rowEquip]);
        });

        res.virtualSignalExcelList.forEach((signalItem: any) => {
          const rowSignal = [];

          rowSignal.push(signalItem.virtualEquipmentName);
          rowSignal.push(signalItem.virtualSignalName);
          rowSignal.push(signalItem.equipmentName);
          rowSignal.push(signalItem.channelNo);

          xlsTemplateJsonSignal.push([...rowSignal]);
        });


        /* generate worksheet */
        const wsEquip: { [key: string]: any } = XLSX.utils.aoa_to_sheet(xlsTemplateJsonEquip);

        const wsSignal: { [key: string]: any } = XLSX.utils.aoa_to_sheet(xlsTemplateJsonSignal);

        // 设置宽度
        wsEquip['!cols'] = [];
        this.columnExportEquip.forEach((item: any) => {
          if (item === this.translate.instant('virtualEquipment.virtualSamplerUnitName') ||
            item === this.translate.instant('virtualEquipment.virtualEquipmentTemplate')) {
            wsEquip['!cols'].push({ wch: item.toString().length * 6 });
          } else {
            wsEquip['!cols'].push({ wch: item.toString().length * 2.1 });
          }
        })
        wsSignal['!cols'] = [];
        this.columnTitlesSignal.forEach((item: any) => {
          if (item === this.translate.instant('virtualEquipment.originalEquipmentName')) {
            wsSignal['!cols'].push({ wch: item.toString().length * 4.7 });
          } else {
            wsSignal['!cols'].push({ wch: item.toString().length * 2.1 });
          }
        })

        /* generate workbook and add the worksheet */
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, wsEquip, this.translate.instant('virtualEquipment.virtualEquipment'));
        XLSX.utils.book_append_sheet(wb, wsSignal, this.translate.instant('virtualEquipment.instanceSignal'));
        /* save to file */
        XLSX.writeFile(wb, fileName + '.xlsx');
      }
    }, err => {
      this.messageService.error(this.translate.instant('common.error'));
    })
  }

  exportAll() {
    const currentDate = DateUtil.format(new Date(), 'yyyy-MM-dd hh:mm:ss');
    const fileName = this.translate.instant('virtualEquipment.virtualEquipment') + currentDate;

    const columnTitlesEquip = [
      this.translate.instant('virtualEquipment.virtualStationName'),
      this.translate.instant('virtualEquipment.virtualHouseName'),
      this.translate.instant('virtualEquipment.virtualMonitorUnit'),
      this.translate.instant('virtualEquipment.virtualEquipmentTemplate'),
      this.translate.instant('virtualEquipment.virtualEquipmentName'),
    ];

    const xlsTemplateJsonEquip: any = [];
    xlsTemplateJsonEquip.push([...columnTitlesEquip]);
    this.displayTableDataSource.forEach((row: any) => {
      const rowEquip = [];

      const data = row;
      rowEquip.push(data.virtualStationName);
      rowEquip.push(data.virtualHouseName);
      rowEquip.push(data.virtualMonitorUnitName);
      rowEquip.push(data.virtualEquipmentTemplateName);
      rowEquip.push(data.virtualEquipmentName);

      xlsTemplateJsonEquip.push([...rowEquip]);
    });

    /* generate worksheet */
    const wsEquip: { [key: string]: any } = XLSX.utils.aoa_to_sheet(xlsTemplateJsonEquip);

    // 设置宽度
    wsEquip['!cols'] = [];
    columnTitlesEquip.forEach(item => {
      if (item === this.translate.instant('virtualEquipment.virtualSamplerUnitName') ||
        item === this.translate.instant('virtualEquipment.virtualEquipmentTemplate')) {
        wsEquip['!cols'].push({ wch: item.toString().length * 6 });
      } else {
        wsEquip['!cols'].push({ wch: item.toString().length * 2.1 });
      }
    })

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, wsEquip, this.translate.instant('virtualEquipment.virtualEquipment'));
    /* save to file */
    XLSX.writeFile(wb, fileName + '.xlsx');
  }

  checkDetail(event: any) {
    const modal = this.modal.create<VirualEquipInfoModalComponent>({
      nzTitle: "设备详情",
      nzContent: VirualEquipInfoModalComponent,
      nzData: {
        eqId: event.virtualEquipmentId,
      },
      nzWidth: 1100,
      nzFooter: [
        {
          label: '关闭',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        }
      ],
      nzMaskClosable: false
    });
  }

  delete(event: any) {
    this.modalService.confirm({
      nzTitle: '提示',
      nzCentered: true,
      nzContent: `请确认是否删除：${event.virtualEquipmentName}?`,
      nzOkText: '确定',
      nzOnOk: () => {
        this.service.deleteVirtualEquipment(event.virtualEquipmentId).then((res: any) => {
          this.messageService.create('success', `操作成功`);
          this.getList();
          this.onItemChecked(event.virtualEquipmentId, false)
        })
      },
    });
  }

  //表格选择事件
  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedIds(id, checked);
    this.refreshCheckedBoxStatus();
  }

  updateCheckedIds(id: string, checked: boolean): void {
    if (checked) {
      if (!this.checkedIds.includes(id)) {
        this.checkedIds.push(id);
      }
    } else {
      this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
    }
  }

  onAllChecked(checked: boolean): void {
    this.filterTableDataSource.forEach((item: any) => {
      this.updateCheckedIds(item.virtualEquipmentId, checked)
    });
    this.refreshCheckedBoxStatus();
  }

  refreshCheckedBoxStatus(): void {
    this.checkAll = this.filterTableDataSource.length > 0 && this.filterTableDataSource.every((item: any) => {
      return this.checkedIds.includes(item.virtualEquipmentId)
    });
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
