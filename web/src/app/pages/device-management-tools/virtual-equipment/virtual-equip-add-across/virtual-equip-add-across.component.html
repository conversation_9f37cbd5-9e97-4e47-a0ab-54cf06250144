<div class="vea_bar">
    <div class="vea_container" style="width: 20%;">
        <div class="vea_container_title">
            <div>设备列表</div>
        </div>
        <div class="vea_container_searcher">
            <input nz-input placeholder="输入关键字查询" [(ngModel)]="searchText" type="text" />
        </div>
        <div class="vea_container_tree" id="vea_bottom">
            <nz-tree
                #nzTree
                [nzData]="nodes"
                [nzTreeTemplate]="nzTreeTemplate"
                [nzVirtualHeight]="treeHeight"
                [nzSearchValue]="searchText"
                [nzHideUnMatched]="true"
                (nzDblClick)="openFolder($event)"
                (nzClick)="selectNode($event)"
            >
            </nz-tree>
            <ng-template #nzTreeTemplate let-node let-origin="origin">
                <div class="vea_treenode">
                    <!-- <span *ngIf="!origin.hasChildren && !origin.parentNodeNoChild"><i
                    class="fa fa-server child"></i></span> -->
                    <iconfont [icon]="'icon-serverinfo'" class="child" *ngIf="!origin.children && !origin.parentNodeNoChild"></iconfont>
                    <!-- <span *ngIf="(origin.hasChildren || origin.parentNodeNoChild) && !origin.monitorUnitId"><i
                    class="fa fa-sitemap child"></i></span> -->
                    <iconfont [icon]="'icon-zuzhiguanli'" class="child" *ngIf="(origin.children || origin.parentNodeNoChild) && !origin.monitorUnitId"></iconfont>
                    <!-- <span *ngIf="origin.monitorUnitId"><i
                    class="iconfont icon-jiankong child"></i></span> -->
                    <iconfont [icon]="'icon-jiankong'" class="child" *ngIf="origin.monitorUnitId"></iconfont>
                    <!-- <span *ngIf="origin.isVirtualEquipment"><i
                    class="iconfont icon-protect-record child"></i></span> -->
                    <iconfont [icon]="'icon-protect-record'" class="child icon-virtual" *ngIf="origin.isVirtualEquipment"></iconfont>
                    <!-- <span *ngIf="origin.rid === firstSelectRid"><i
                    class="iconfont icon-time child icon-firstSelect"></i></span> -->
                    <iconfont [icon]="'icon-status'" class="icon-firstSelect child" *ngIf="origin.rid === firstSelectRid"></iconfont>
                    <!-- <span *ngIf="origin.rid !== firstSelectRid && selectedRids.includes(origin.rid)"><i
                    class="iconfont icon-time child icon-select-equip"></i></span> -->
                    <iconfont [icon]="'icon-status'" class="icon-select-equip child" *ngIf="origin.rid !== firstSelectRid && selectedRids.includes(origin.rid)"></iconfont>
                    <span class="childItem">
                        {{ origin.name }}
                    </span>
                </div>
            </ng-template>
        </div>
    </div>
    <div class="vea_container" style="width: 16%; margin-left: 8px;">
        <div class="vea_container_title">
            <div>信号列表</div>
        </div>
        <div class="vea_eqName">
            <div class="div-signal">
                <span class="span-text" [title]="currentSelectEqName">{{currentSelectEqName}}</span>
                <span></span>
            </div>
            <d-data-table
                [dataSource]="signalList"
                [scrollable]="true" 
                [virtualScroll]="true"
                [tableHeight]="tableHeight"
                [fixHeader]="true"
                [tableWidthConfig]="sigCols" 
                [tableOverflowType]="'overlay'"
                [onlyOneColumnSort]="true"
                [borderType]="'borderless'"
                [striped]="false"
            >
                <thead dTableHead>
                    <tr dTableRow>
                        <ng-container *ngFor="let col of sigCols">
                            <th dHeadCell *ngIf="col['field'] === 'checkBox'">
                                <label class="checkbox-inline custom-checkbox nowrap">
                                    <input type="checkbox"
                                        [ngModel]="checkAllSig"
                                        (change)="onSigAllChecked(!checkAllSig)"
                                    >
                                    <span></span>
                                </label>
                            </th>
                            <th dHeadCell *ngIf="col['field'] !== 'checkBox'"
                                [sortable]="false"
                                [showSortIcon]="false"
                            >
                                <div class="devui_title">{{ col.title }}</div>
                                <div class="devui_searcher">
                                    <nz-input-group [nzSuffix]="suffixIconSearch">
                                        <input
                                            type="text"
                                            class="devui-form-control"
                                            nz-input
                                            [(ngModel)]="searchSigText"
                                            placeholder="请输入关键字..." 
                                            (ngModelChange)="searchSignal()"/>
                                    </nz-input-group>
                                    <ng-template #suffixIconSearch>
                                        <span nz-icon nzType="search"></span>
                                    </ng-template>
                                </div>
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody dTableBody>
                    <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                        <tr dTableRow [ngClass]="{'devui-table-row-selected': selectSignals.includes(rowItem.signalId)}">
                            <ng-container *ngFor="let col of sigCols">
                                <td dTableCell *ngIf="col['field'] === 'checkBox'">
                                    <label class="checkbox-inline custom-checkbox nowrap">
                                        <input type="checkbox"
                                            [ngModel]="selectSignals.includes(rowItem.signalId)"
                                            (change)="onSigChecked(rowItem.signalId, !selectSignals.includes(rowItem.signalId))"
                                        >
                                        <span></span>
                                    </label>
                                </td>
                                <td dTableCell *ngIf="col['field'] !== 'checkBox'"
                                    class="span-text"
                                    [title]="rowItem[col.field]"
                                >
                                    {{ rowItem[col.field] }}
                                </td>
                            </ng-container>
                        </tr>
                    </ng-template>
                </tbody>
                <ng-template #noResultTemplateRef>
                    <div style="text-align: center; margin-top: 20px">{{'common.nodata' | translate}}</div>
                </ng-template>
            </d-data-table>
        </div>
    </div>
    <div class="vea_container" style="width: 16%; margin-left: 8px;">
        <div class="vea_container_title">
            <div>事件列表</div>
        </div>
        <div class="vea_eqName">
            <div class="div-signal">
                <span class="span-text" [title]="currentSelectEqName">{{currentSelectEqName}}</span>
                <span>
                    <button nz-button nzType="primary" title="保存选中配置" (click)="addToRight()">
                      <span>&gt;&gt;&gt;</span>
                    </button>
                </span>
            </div>
            <d-data-table
                [dataSource]="eventList"
                [scrollable]="true" 
                [virtualScroll]="true"
                [tableHeight]="tableHeight"
                [fixHeader]="true"
                [tableWidthConfig]="eveCols" 
                [tableOverflowType]="'overlay'"
                [onlyOneColumnSort]="true"
                [borderType]="'borderless'"
                [striped]="false"
            >
                <thead dTableHead>
                    <tr dTableRow>
                        <ng-container *ngFor="let col of eveCols">
                            <th dHeadCell *ngIf="col['field'] === 'checkBox'">
                                <label class="checkbox-inline custom-checkbox nowrap">
                                    <input type="checkbox"
                                        [ngModel]="checkAllEve"
                                        (change)="onEveAllChecked(!checkAllEve)"
                                    >
                                    <span></span>
                                </label>
                            </th>
                            <th dHeadCell *ngIf="col['field'] !== 'checkBox'"
                                [sortable]="false"
                                [showSortIcon]="false"
                            >
                                <div class="devui_title">{{ col.title }}</div>
                                <div class="devui_searcher">
                                    <nz-input-group [nzSuffix]="suffixIconSearch">
                                        <input
                                            type="text"
                                            class="devui-form-control"
                                            nz-input
                                            [(ngModel)]="searchEveText"
                                            placeholder="请输入关键字..." 
                                            (ngModelChange)="searchEvent()"/>
                                    </nz-input-group>
                                    <ng-template #suffixIconSearch>
                                        <span nz-icon nzType="search"></span>
                                    </ng-template>
                                </div>
                            </th>
                        </ng-container>
                    </tr>
                </thead>
                <tbody dTableBody>
                    <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                        <tr dTableRow [ngClass]="{'devui-table-row-selected': selectEvents.includes(rowItem.eventId)}">
                            <ng-container *ngFor="let col of eveCols">
                                <td dTableCell *ngIf="col['field'] === 'checkBox'">
                                    <label class="checkbox-inline custom-checkbox nowrap">
                                        <input type="checkbox"
                                            [ngModel]="selectEvents.includes(rowItem.eventId)"
                                            (change)="onEveChecked(rowItem, !selectEvents.includes(rowItem.eventId))"
                                        >
                                        <span></span>
                                    </label>
                                </td>
                                <td dTableCell *ngIf="col['field'] !== 'checkBox'"
                                    class="span-text"
                                    [title]="rowItem[col.field]"
                                >
                                    {{ rowItem[col.field] }}
                                </td>
                            </ng-container>
                        </tr>
                    </ng-template>
                </tbody>
                <ng-template #noResultTemplateRef>
                    <div style="text-align: center; margin-top: 20px">{{'common.nodata' | translate}}</div>
                </ng-template>
            </d-data-table>
        </div>
    </div>
    <div class="vea_container" style="width: 47%; margin-left: 8px;">
        <div class="vea_container_title">
            <div>虚拟设备预览</div>
        </div>
        <div class="div-signal">
            <span>
                <button nz-button nzType="primary" title="移除选中配置" (click)="removePreview()">
                    <span>&lt;&lt;&lt;</span>
                </button>
            </span>
            <span>
                <button nz-button nzType="primary" style="margin-right: 4px;" (click)="removeAllPreview()">
                    <span>移除所有配置</span>
                </button>
                <button nz-button nzType="primary" (click)="createVirtualEquipment()">
                    <span>生成配置</span>
                </button>
            </span>
        </div>
        <d-data-table
            [dataSource]="virtualList"
            [scrollable]="true" 
            [virtualScroll]="true"
            [tableHeight]="tableHeight"
            [fixHeader]="true"
            [tableWidthConfig]="preCols" 
            [tableOverflowType]="'overlay'"
            [onlyOneColumnSort]="true"
            [borderType]="'borderless'"
            [striped]="false"
        >
            <thead dTableHead>
                <tr dTableRow>
                    <ng-container *ngFor="let col of preCols">
                        <th dHeadCell *ngIf="col['field'] === 'checkBox'"
                            [resizeEnabled]="true"
                            (resizeEndEvent)="onResize($event, col.field)">
                            <label class="checkbox-inline custom-checkbox nowrap">
                                <input type="checkbox"
                                    [ngModel]="checkAllPre"
                                    (change)="onPreAllChecked(!checkAllPre)"
                                >
                                <span></span>
                            </label>
                        </th>
                        <th dHeadCell *ngIf="col['field'] !== 'checkBox'"
                            [sortable]="false"
                            [showSortIcon]="false"
                            [resizeEnabled]="true"
                            (resizeEndEvent)="onResize($event, col.field)"
                        >
                            <div class="devui_title">{{ col.title }}</div>
                            <div class="devui_searcher">
                                <nz-input-group [nzSuffix]="suffixIconSearch">
                                    <input
                                        type="text"
                                        class="devui-form-control"
                                        nz-input
                                        [(ngModel)]="searchPreText[col.field]"
                                        placeholder="请输入关键字..." 
                                        (ngModelChange)="searchPreview()"/>
                                </nz-input-group>
                                <ng-template #suffixIconSearch>
                                    <span nz-icon nzType="search"></span>
                                </ng-template>
                            </div>
                        </th>
                    </ng-container>
                </tr>
            </thead>
            <tbody dTableBody>
                <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                    <tr dTableRow [ngClass]="{'devui-table-row-selected': selectPreviews.includes(rowItem.uniqueId)}">
                        <ng-container *ngFor="let col of preCols">
                            <td dTableCell *ngIf="col['field'] === 'checkBox'">
                                <label class="checkbox-inline custom-checkbox nowrap">
                                    <input type="checkbox"
                                        [ngModel]="selectPreviews.includes(rowItem.uniqueId)"
                                        (change)="onPreChecked(rowItem.uniqueId, !selectPreviews.includes(rowItem.uniqueId))"
                                    >
                                    <span></span>
                                </label>
                            </td>
                            <td dTableCell *ngIf="col['field'] !== 'checkBox'"
                                class="span-text"
                                [title]="rowItem[col.field]"
                            >
                                {{ rowItem[col.field] }}
                            </td>
                        </ng-container>
                    </tr>
                </ng-template>
            </tbody>
            <ng-template #noResultTemplateRef>
                <div style="text-align: center; margin-top: 20px">{{'common.nodata' | translate}}</div>
            </ng-template>
        </d-data-table>
    </div>
</div>
  