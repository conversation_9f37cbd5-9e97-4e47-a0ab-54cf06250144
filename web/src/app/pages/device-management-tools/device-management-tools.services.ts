import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'
import { Observable } from 'rxjs'
import { VirtualEquipModel } from './virtual-equipment/virtual-equipment.model'

@Injectable()
export class DeviceManagementToolsService extends RestfulService {

    /**
    * 获取配置管理列表
    * 32 BACNet配置
    * 33 SNMP配置
    * @returns
    */
    public async getList(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/settingmanager?portType=${id}`)
        return res.data
    }

    /**
    * 获取驱动模板
    * @returns
    */
    public async getDriveTtemplates(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/drivetemplates?type=${id}`)
        return res.data
    }

    /**
    * 修改设备
    * @returns
    */
    public async updateEquipment(params: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/updateequipment`, params)
        return res.data
    }

    /**
    * 上传配置
    * @returns
    */
    public async uploadSetting(params: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/uploadsetting`, params)
        return res.data
    }

    /**
    * 上传文件
    * @returns
    */
    public async upload(params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/settingmanager/upload`, params)
        return res.data
    }

    /**
    * 上传配置
    * @returns
    */
    public async deleteSetting(params: any): Promise<[]> {
        const res = await this.delete<[]>(`api/config/deletesetting?equipmentId=${params.equipmentId}&isAll=${params.isAll}`, {} as any)
        return res.data
    }


    public async deleteServer(id: any): Promise<[]> {
        const params = { ids: id };
        const res = await this.delete<[]>(`api/config/workstation/delete`, params as any)
        return res.data
    }

    /**
    * 生成配置
    * @returns
    */
    public buildconfig(params: any): Observable<any> {
        return this.client.post('api/config/buildconfig', params, { responseType: 'blob', observe: 'response' });
    }

    /**
   * 初始化采集配置
   * @returns
   */
    public async initsampler(params: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/initsampler`, params)
        return res.data
    }

    /**
    * 获取驱动模板列表
    * @returns
    */
    public async geDriveTemplatestList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/drivetemplates`)
        return res.data
    }

    /**
    * 新增驱动模板
    * @returns
    */
    public async addDeviceTemlate(params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/drivetemplates`, params)
        return res.data
    }

    /**
    * 修改驱动模板
    * @returns
    */
    public async updateDeviceTemlate(params: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/drivetemplates`, params)
        return res.data
    }

    /**
    * 删除驱动模板
    * @returns
    */
    public async deleteDeviceTemlate(id: any): Promise<[]> {
        const res = await this.delete<[]>(`api/config/drivetemplates/${id}`, id)
        return res.data
    }

    /**
    * 获取模板结构
    * @returns
    */
    public async getByDriverTempalteId(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/drivestructuretemplates/findbydrivetemplateid/${id}`)
        return res.data
    }

    /**
    * 获取模板结构占位符
    * @returns
    */
    public async getDriveStructurePlaceholders(): Promise<[]> {
        const res = await this.get<[]>(`api/config/drivestructureplaceholders`)
        return res.data
    }

    /**
    * 创建模板结构节点
    * @returns
    */
    public async createDriveStructureTemplates(params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/drivestructuretemplates`, params)
        return res.data
    }

    /**
    * 修改模板结构节点
    * @returns
    */
    public async updateDriveStructureTemplates(params: any): Promise<[]> {
        const res = await this.put<[]>(`api/config/drivestructuretemplates`, params)
        return res.data
    }

    /**
    * 修改模板结构节点
    * @returns
    */
    public async deleteDriveStructureTemplates(id: number): Promise<[]> {
        const res = await this.delete<[]>(`api/config/drivestructuretemplates/${id}`, {} as any)
        return res.data
    }

    /**
    * 获取虚拟设备列表
    * @returns
    */
    public async getVirtualEquipmentList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/virtualequipment`)
        return res.data
    }

    /**
    * 导出虚拟设备
    * @returns
    */
    public async getVirtualEquipExport(ids: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/export/equipmentsetting`, ids)
        return res.data
    }

    /**
    * 删除虚拟设备
    * @returns
    */
    public async deleteVirtualEquipment(id: any): Promise<[]> {
        const res = await this.delete<[]>(`api/config/virtualequipment/${id}`, {} as any)
        return res.data
    }

    /**
    * 获取虚拟设备详情
    * @returns
    */
    public async getVirtualEquipmentInfo(id: any): Promise<[]> {
        const res = await this.get<[]>(`api/config/virtualequipment/${id}`, {} as any)
        return res.data
    }

    /**
    * 获取跨站虚拟设备详情
    * @returns
    */
    public async getVirtualEquipmentInfoAcross(id: any): Promise<[]> {
        const res = await this.get<[]>(`api/config/crosssitevirtualequipment/virtualequipment/${id}`, {} as any)
        return res.data
    }

    /**
    * 批量生成虚拟设备
    * @returns
    */
    public async virtualEquipImport(params: VirtualEquipModel.importVirtualEquipExcel) {
        const res = await this.post<[]>(`api/config/import/equipmentsetting`, params)
        return res.data
    }

    /**
    * 获取跨站虚拟设备列表
    * @returns
    */
    public async getAcrossVirtualEquipmentList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/crosssitevirtualequipment`)
        return res.data
    }

    public async getMonitorUnitList() {
        const res = await this.get<[]>(`api/config/monitor-unit?category=24`)
        return res.data
    }

    public async createAcrossSplitEquipment(params: any) {
        const res = await this.post<[]>(`api/config/crosssitevirtualequipment/splitequipment`, params)
        return res
    }

    /**
    * 删除跨站虚拟设备
    * @returns
    */
    public async deleteAcrossVirtualEquipment(id: any): Promise<[]> {
        const res = await this.delete<[]>(`api/config/crosssitevirtualequipment/${id}`, {} as any)
        return res.data
    }

    public async getAcrossVirtualEquipExport(ids: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/crosssitevirtualequipment/export/equipmentsetting`, ids)
        return res.data
    }

    public async virtualAcrossEquipImport(params: VirtualEquipModel.importVirtualEquipExcel) {
        const res = await this.post<[]>(`api/config/crosssitevirtualequipment/import/equipmentsetting`, params)
        return res.data
    }

    public async getMonitorUnitTree() {
        const res = await this.get<[]>(`api/config/resourcestructuremonitorunit`)
        return res.data
    }

    public async getSignalByEqId(eqId: number) {
        const res = await this.get<[]>(`api/config/signal/cfgsignalevent?equipmentId=${eqId}`)
        return res.data
    }

    public async postGetEventByEqSignals(eqId: string, signalIds: string[]) {
        const params = {
            equipmentId: eqId,
            signalIds: signalIds
        }
        const res = await this.post<[]>(`api/config/event/cfgeventbysignalids`, params)
        return res.data
    }

    public async getComPortList(monitUnitId: string) {
        const res = await this.get<[]>(`api/config/port?monitorUnitId=${monitUnitId}`)
        return res.data
    }

    public async getSamplerUnitList(monitUnitId: string, portId: string) {
        const res = await this.get<[]>(`api/config/sampler-unit?monitorUnitId=${monitUnitId}&portId=${portId}`)
        return res.data
    }

    public async getEqCategoryList() {
        const res = await this.get<[]>(`api/config/dataitems?entryId=7`)
        return res.data
    }

    public async getSplitEquipment(eqId: string) {
        const res = await this.get<[]>(`api/config/splitequipment/${eqId}`)
        return res.data
    }

    public async createSplitEquipment(params: any) {
        const res = await this.post<[]>(`api/config/splitequipment`, params)
        return res
    }
}