import { Component, Injector, Input } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { DeviceManagementToolsService } from '../../device-management-tools.services';

@Component({
  selector: 'app-bacnet-edit',
  templateUrl: './bacnet-edit.component.html',
  styleUrl: './bacnet-edit.component.less'
})
export class BacnetEditComponent extends GenericModalComponent<{}, Boolean>{
  @Input() input: any;
  configurationTool: any;
  submitted: any;
  templateOptions: any = [];

  constructor(injector: Injector,
    private service: DeviceManagementToolsService) { super(injector); }

  onInit() {
    this.configurationTool = this.input.data || {}
    this.configurationTool.isUpload = this.configurationTool.isUpload === 1 ? '是' : '否';
    this.configurationTool.isReset = this.configurationTool.isReset === 1 ? '是' : '否';
    this.getTemplateList();
  }

  getTemplateList() {
    this.service.getDriveTtemplates(2).then(res => {
      this.templateOptions = res.map((item: any) => ({
        label: item['driveTemplateName'], value: item['id']
      }));
      if(!this.configurationTool.driveTemplateId) {
        this.configurationTool.driveTemplateId = this.templateOptions[0]['value'];
      }
    })
  }

  async submit(ngForm: any) {
    if (!ngForm) return;
    try {
      await this.service.updateEquipment(this.configurationTool);
      this.messageService.create('success', `修改成功`);
      this.close(true);
    } catch (error) {
      this.messageService.create('error', `修改失败`);
      this.close();
    }
  }
}
