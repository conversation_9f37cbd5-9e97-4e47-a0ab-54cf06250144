<div baCardClass="viewport100 with-scroll border-top-radius-none">
    <div class="top-button">
        <button nz-button nzType="primary" (click)="buildConfig()"> 生成配置 </button>
        <button nz-button nzType="primary" (click)="initSampler()"> 初始化采集配置 </button>
        <button nz-button nzType="primary" (click)="filterError()"> 过滤错误行 </button>
    </div>
    <div class="overflow-y-auto">
        <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
            [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="tabelHeightString"
            [containFixHeaderHeight]="true" [fixHeader]="true" (tableScrollEvent)="tableScrollEvent($event)">
            <thead dTableHead>
                <tr dTableRow>
                    <ng-container *ngFor="let colConfig of tableColumnConfig">
                        @if (colConfig.field === 'checkBox') {
                        <th dHeadCell>
                            <input type="checkbox" [ngModel]="checkAll" (change)="onAllChecked(!checkAll)">
                        </th>
                        }@else {
                        <th dHeadCell (sortChange)="onSortChange($event, colConfig.field, colConfig)"
                            [resizeEnabled]="true" (resizeEndEvent)="onResize($event, colConfig.field)">
                            {{ colConfig.title }}
                            @if (orgTableDataSource.length) {
                            <div *ngIf="colConfig.field === 'checkBox' || colConfig.field === 'operation'"
                                style="height: 32px;"></div>
                            <input *ngIf="colConfig.field !== 'checkBox' && colConfig.field !== 'operation'" nz-input
                                [(ngModel)]="filterData[colConfig.field]" (ngModelChange)="filterChange()" />
                            }
                        </th>
                        }
                    </ng-container>
                </tr>
            </thead>
            <tbody dTableBody>
                <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                    <tr dTableRow>
                        <td *ngFor="let colConfig of tableColumnConfig" dTableCell>
                            @if (colConfig.field === 'checkBox') {
                            <input type="checkbox" [ngModel]="checkedIds.includes(rowItem.equipmentId)"
                                (change)="onItemChecked(rowItem.equipmentId, !checkedIds.includes(rowItem.equipmentId))">
                            }@else if(colConfig.field === 'portName' || colConfig.field === 'portAttribute' ||
                            colConfig.field === 'samplerUnitName' || colConfig.field === 'address' || colConfig.field === 'dllPath') {
                            <span [ngClass]="{'errorFlag': rowItem.warningList.includes(colConfig.field)}">{{
                                rowItem[colConfig.field]}}</span>
                            }@else if(colConfig.field === 'isUpload' || colConfig.field === 'isReset') {
                            {{ rowItem[colConfig.field]? '是' : '否'}}
                            }@else if(colConfig.field === 'operation') {
                            <iconfont [icon]="'icon-export'" title="上传配置" (click)="uploadConfig(rowItem)"></iconfont>
                            <iconfont [icon]="'icon-edit-set'" title="修改" (click)="edit(rowItem)"></iconfont>
                            <iconfont [icon]="'icon-delete2'" title="删除" (click)="delete(rowItem)"></iconfont>
                            }@else {
                            {{ rowItem[colConfig.field] }}
                            }
                        </td>
                    </tr>
                </ng-template>
            </tbody>
            <ng-template #noResultTemplateRef>
                <div style="text-align: center; margin-top: 20px">
                    <nz-empty nzNotFoundImage="simple" />
                </div>
            </ng-template>
        </d-data-table>
    </div>
</div>