import { Component, ElementRef, Injector, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { tableResizeFunc } from 'ng-devui/data-table';
import { DeviceManagementToolsService } from '../../device-management-tools.services';
import { SnmpEditComponent } from '../snmp-edit/snmp-edit.component';
import { FileUtil } from '@core/util/file.util';
import { NzModalService } from 'ng-zorro-antd/modal';
import { filter } from 'rxjs';
import { NavigationEnd } from '@angular/router';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-snmp-list',
  templateUrl: './snmp-list.component.html',
  styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './snmp-list.component.less']
})
export class SnmpListComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private modal: NzModalService,
    private service: DeviceManagementToolsService
  ) {
    super(injector, ele)
  }

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'checkBox',
      width: '50px',
      title: '',
    },
    {
      field: 'stationName',
      width: '100px',
      title: '局站',
    },
    {
      field: 'houseName',
      width: '100px',
      title: '局房',
    },
    {
      field: 'monitorUnitName',
      width: '120px',
      title: '监控单元',
    },
    {
      field: 'portName',
      width: '100px',
      title: '端口',
    },
    {
      field: 'ipAddress',
      width: '120px',
      title: 'IP地址',
    },
    {
      field: 'portAttribute',
      width: '120px',
      title: '读写属性',
    },
    {
      field: 'samplerUnitName',
      width: '200px',
      title: '采集单元',
    },
    {
      field: 'equipmentName',
      width: '200px',
      title: '设备名',
    },
    {
      field: 'equipmentTemplateName',
      width: '240px',
      title: '引用设备模板',
    },
    {
      field: 'address',
      width: '120px',
      title: '地址',
    },
    {
      field: 'dllPath',
      width: '120px',
      title: '动态库名称',
    },
    {
      field: 'spUnitInterval',
      width: '120px',
      title: '采集周期',
    },
    {
      field: 'isUpload',
      width: '100px',
      title: '是否已上传',
    },
    {
      field: 'driveTemplateName',
      width: '120px',
      title: '引用驱动模板',
    },
    {
      field: 'isReset',
      width: '120px',
      title: '是否需要下发',
    },
    {
      field: 'operation',
      width: '120px',
      title: '操作',
    }
  ]
  checkAll: boolean = false;
  checkedIds: any = [];
  fileInfo: any = {};

  protected onInit(): void {
    super.onInit()
    this.getList()
    this.subscribe(this.router.events.pipe(
      filter(event => event instanceof NavigationEnd && event.url == '/pages/snmp-list')),
      (event) => {
        this.getList();
      })
  }

  getList() {
    this.service.getList(33).then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
      }
    })
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)

  onAllChecked(checked: boolean): void {
    this.filterTableDataSource.forEach((item: any) => {
      this.updateCheckedIds(item.equipmentId, checked)
    });
    this.refreshCheckedBoxStatus();
  }

  updateCheckedIds(id: string, checked: boolean): void {
    if (checked) {
      if (!this.checkedIds.includes(id)) {
        this.checkedIds.push(id);
      }
    } else {
      this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
    }
  }

  refreshCheckedBoxStatus(): void {
    this.checkAll = this.filterTableDataSource.length > 0 && this.filterTableDataSource.every((item: any) => {
      return this.checkedIds.includes(item.equipmentId)
    });
  }

  uploadConfig(event: any) {
    if (event.isUpload) {
      this.messageService.error('您已上传配置，不能重复上传');
      return
    }
    this.importProfile(event);
  }

  private async importProfile(event: any) {
    const files = await FileUtil.selectLocalFile('.oid,.ini ');
    if (files.length > 0) {
      const blobData = await FileUtil.loadBlobFromFile(files[0]);
      this.uploadModel(files[0])
      if (blobData) {
        const modal = this.modal.create({
          nzTitle: '上传配置',
          nzContent: `请确认是否要上传与"${event.equipmentName}"引用相同模板设备的配置?`,
          nzFooter: [
            {
              label: '上传所有设备',
              type: 'primary',
              onClick: () => {
                modal.close('all')
              },
            },
            {
              label: '上传当前设备',
              type: 'default',
              onClick: () => {
                modal.close('one')
              },
            }
          ]
        })
        modal.afterClose.subscribe((res: any) => {
          if (res) {
            const data = {
              equipmentId: event.equipmentId,
              fileId: this.fileInfo.fileId,
              isAll: res === 'all' ? 1 : 0
            }
            this.service.uploadSetting(data).then(res => {
              if (res) {
                this.messageService.success('上传成功');
                this.getList();
              }
            })
          }
        })
      }
    }
  }

  public async uploadModel(file: File): Promise<void> {
    try {
      if (file === null) return;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('filePath', 'batchtool');
      formData.append('cover', 'true');
      await this.service.upload(formData).then(res => {
        if (res) this.fileInfo = res;
      });
    } catch (e) {
      console.error("↑ ↑ Calling interface failed!");
    }
  }

  async edit(event: any): Promise<void> {
    const data = cloneDeep(event);
    const VV = this.openDialog({
      nzTitle: '修改',
      nzWidth: 750,
      nzContent: SnmpEditComponent,
      nzData: { data: data },
    });
    const result = await this.waitDialog(VV);
    if (result) this.getList();
  }

  delete(event: any) {
    if (!event.isUpload) {
      this.messageService.error('您未上传过配置');
      return
    }
    const modal: any = this.modal.create({
      nzTitle: '删除配置',
      nzContent: `请确认是否要删除与"${event.equipmentName}"引用相同模板设备的配置?`,
      nzFooter: [
        {
          label: '删除所有设备',
          type: 'primary',
          onClick: () => modal.close('all')
        },
        {
          label: '删除当前设备',
          type: 'default',
          onClick: () => modal.close('one')
        }
      ]
    })
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        const data = {
          equipmentId: event.equipmentId,
          driveTemplateId: event.driveTemplateId,
          isAll: res === 'all' ? 1 : 0
        }
        this.service.deleteSetting(data).then(res => {
          if (res) {
            this.messageService.success('删除成功！');
            this.getList();
          }
        })
      }
    })
  }

  //表格选择事件
  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedIds(id, checked);
    this.refreshCheckedBoxStatus();
  }

  buildConfig() {
    if (this.checkedIds.length) {
      const list = this.displayTableDataSource.filter(item => this.checkedIds.includes(item.equipmentId))

      let errorArr = list.filter(e => e.warningList.length > 0);
      let isUploadArr = list.filter(e => e.isUpload == 0 || e.isUpload == null);

      if (errorArr.length > 0) {
        this.messageService.error('生成配置当前选中行里存在错误，不执行生成配置操作，请检查基础配置');
        return
      }
      if (errorArr.length === 0 && isUploadArr.length > 0) {
        this.messageService.error('生成配置当前选中行里存在未上传过配置，不执行生成配置操作，请检查基础配置');
        return
      }
      const data = {
        equipmentIds: this.checkedIds,
        portType: 33
      }
      this.service.buildconfig(data).subscribe(res => {
        if (res) {
          const url = window.URL.createObjectURL(new Blob([res.body]));
          const filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1];
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          this.messageService.success('已生成配置');
          this.getList()
        } else {
          this.messageService.error(res.msg);
        }
      });
    } else {
      this.messageService.error('您未勾选任何配置选项');
    }
  }

  initSampler() {
    if (this.checkedIds.length > 0) {
      const data = {
        equipmentIds: [],
        portType: 33
      }
      data.equipmentIds = this.checkedIds;
      this.service.initsampler(data).then(res => {
        if (res) {
          this.messageService.success('初始化成功');
          this.getList()
        }
      });
    } else {
      this.messageService.error('您未勾选任何配置选项');
    }
  }

  filterError() {
    const list = this.displayTableDataSource.filter(item => item.warningList.length > 0);
    if (list.length > 0) {
      this.displayTableDataSource = list;
    } else {
      this.messageService.error('当前无错误行');
    }
  }

}
