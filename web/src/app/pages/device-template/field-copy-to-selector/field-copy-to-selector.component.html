<div class="fcts_container">
    <d-data-table
        #dataTable
        [dataSource]="filterSource"
        [tableOverflowType]="'overlay'"
        [scrollable]="true"
        [virtualScroll]="true"
        [tableHeight]="tabelHeightString"
        [tableWidthConfig]="cols">
        <thead dTableHead>
            <tr dTableRow>
                <ng-container *ngFor="let col of cols">
                    <th dHeadCell *ngIf="col['field'] === 'checkBox'">
                        <label class="checkbox-inline custom-checkbox nowrap">
                            <input type="checkbox"
                            [ngModel]="checkAll"
                            (change)="onAllChecked(!checkAll)"
                        >
                            <span></span>
                        </label>
                    </th>
                    <th dHeadCell *ngIf="col['field'] !== 'checkBox'">
                        <div class="devui_title">{{ col.title }}</div>
                        <div class="devui_searcher">
                            <nz-input-group [nzSuffix]="suffixIconSearch">
                                <input
                                    type="text"
                                    class="devui-form-control"
                                    nz-input
                                    [(ngModel)]="searchText[col.field]"
                                    placeholder="请输入关键字..." 
                                    (ngModelChange)="search()"/>
                            </nz-input-group>
                            <ng-template #suffixIconSearch>
                                <span nz-icon nzType="search"></span>
                            </ng-template>
                        </div>
                    </th>
                </ng-container>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow [ngClass]="{'devui-table-row-selected': checkedIds.includes(rowItem[checkeIdStr])}">
                    <ng-container *ngFor="let col of cols">
                        <td dTableCell *ngIf="col['field'] === 'checkBox'">
                            <label class="checkbox-inline custom-checkbox nowrap">
                                <input type="checkbox"
                                    [ngModel]="checkedIds.includes(rowItem[checkeIdStr])"
                                    (change)="onItemChecked(rowItem[checkeIdStr], !checkedIds.includes(rowItem[checkeIdStr]))"
                                >
                                <span></span>
                            </label>
                        </td>
                        <td dTableCell 
                            *ngIf="col['field'] !== 'checkBox'"
                            class="span-text"
                            [title]="rowItem[col.field]"
                        >
                            {{ rowItem[col.field] }}
                        </td>
                    </ng-container>
                </tr>
            </ng-template>
        </tbody>
    </d-data-table>
</div>