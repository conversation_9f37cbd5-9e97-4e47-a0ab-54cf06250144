<nz-layout style="height: 100%;">
    <nz-sider [nzWidth]="siderWidth" nz-resizable [nzMinWidth]="150" [nzMaxWidth]="450"
        (nzResize)="onSideResize($event)">
        <nz-resize-handle nzDirection="right" nzCursorType="grid">
            <div class="sider-resize-line">
                <div></div>
            </div>
        </nz-resize-handle>
        <div style="display: flex; flex-wrap: wrap; overflow: hidden; width: 100%; height: 100%;">
            <div class="dtm_top_top" style="width: 100%;">
                {{ 'menus.equipmentTemp' | translate }}
            </div>

            <div class="dtm_top" style="width: 100%;">
                <div class="dtm_operations">
                    <div class="dtm_icon_mask">
                        <span nz-icon nzType="close" nzTheme="outline" class="dtm_o_icon" title="删除模板"
                            (click)="deleteSelectedTemplate()"></span>
                    </div>
                    <div class="dtm_icon_mask">
                        <span nz-icon nzType="export" nzTheme="outline" class="dtm_o_icon" title="导出模板"
                            (click)="exportSelectedTemplate()"></span>
                    </div>
                    <div class="dtm_icon_mask">
                        <span nz-icon nzType="copy" nzTheme="outline" class="dtm_o_icon" title="复制模板"
                            (click)="copySelectedTemplate()"></span>
                    </div>
                    <div class="dtm_icon_mask">
                        <span nz-icon nzType="sync" nzTheme="outline" class="dtm_o_icon" title="刷新模板"
                            (click)="refreshTemplate()"></span>
                    </div>
                    <div class="dtm_icon_mask">
                        <span nz-icon nzType="folder" nzTheme="outline" class="dtm_o_icon" title="隐藏动态配置模板"
                            *ngIf="showDynamic" (click)="hideDynamicTemp()"></span>
                    </div>
                    <div class="dtm_icon_mask" style="margin-left: 0;">
                        <span nz-icon nzType="folder-open" nzTheme="outline" class="dtm_o_icon" title="显示动态配置模板"
                            *ngIf="!showDynamic" (click)="showDynamicTemp()"></span>
                    </div>
                </div>
            </div>
            <div class="dtm_mid">
                <input nz-input placeholder="输入首字母或关键字查询" [(ngModel)]="searchText" (ngModelChange)="onSearchChange($event)" (keyup.enter)="selectNode()" type="text" />
            </div>
            <div class="dtm_bottom" id="dtm_bottom">
                <nz-tree #nzTree nzBlockNode [nzData]="nodes" [nzTreeTemplate]="nzTreeTemplate"
                    [nzSearchValue]="searchText" [nzVirtualHeight]="treeHeight" (nzClick)="selectTemp($event, true)"
                    [nzExpandedKeys]="expendKeys" [nzSelectedKeys]="selectedKeys" (nzDblClick)="openFolder($event)"></nz-tree>
                <ng-template #nzTreeTemplate let-node let-origin="origin">
                    <span class="custom-node">
                        <span *ngIf="!origin.template" (contextmenu)="contextMenu($event, menu)">
                            <span nz-icon nzType="folder"></span>
                            <span class="folder-name" [innerHTML]="highlightSearchText(node.title)">{{ node.title }}</span>
                        </span>
                        <span *ngIf="origin.template" (contextmenu)="contextMenu($event, menu)">
                            <span nz-icon nzType="file"></span>
                            <span class="file-name" [innerHTML]="highlightSearchText(node.title)">{{ node.title }}</span>
                        </span>
                    </span>
                    <nz-dropdown-menu #menu="nzDropdownMenu">
                        <ul nz-menu>
                            <li nz-menu-item *ngIf="origin.template" (click)="deleteTemplate(origin)"><iconfont [icon]="'icon-delete2'" class="right-menu-icon"></iconfont>删除模板</li>
                            <!-- <li nz-menu-item (click)="selectDropdown(node)" *ngIf="!origin.template">批量删除模板</li> -->
                            <li nz-menu-item *ngIf="origin.template" (click)="exportTempate(origin)"><iconfont [icon]="'icon-export'" class="right-menu-icon"></iconfont>导出模板</li>
                            <!-- <li nz-menu-item (click)="selectDropdown(node)" *ngIf="!origin.template">批量导出模板</li> -->
                            <li nz-menu-item *ngIf="origin.template" (click)="copyTemplate(origin)"><iconfont [icon]="'icon-copy'" class="right-menu-icon"></iconfont>复制模板</li>
                            <!-- <li nz-menu-item (click)="selectDropdown(node)" *ngIf="origin.template">模板对比</li> -->
                            <li nz-menu-item *ngIf="origin.template" (click)="showAssociatedDevice(origin)">
                                <iconfont [icon]="'icon-chashebei'" class="right-menu-icon"></iconfont>查看引用该模板的设备
                            </li>
                            <li nz-menu-item *ngIf="origin.template && origin.equipmentCategory !== origin.parentId" (click)="upgradeTemplate(origin)">
                                <iconfont [icon]="'icon-hidden-l1'" class="right-menu-icon"></iconfont>升级成根模板
                            </li>
                            <li nz-menu-item *ngIf="origin.template" (click)="exportTempateConfig(origin)"><iconfont [icon]="'icon-export'" class="right-menu-icon"></iconfont>导出模板配置信息</li>
                            <!-- <li nz-menu-item (click)="selectDropdown(node)" *ngIf="origin.template">智能所属模块设置</li>
                            <li nz-menu-item (click)="selectDropdown(node)" *ngIf="origin.template">导出标准化设备梳理表</li>
                            <li nz-menu-item (click)="selectDropdown(node)" *ngIf="origin.template">导出信号完整映射关系</li> -->
                        </ul>
                    </nz-dropdown-menu>
                </ng-template>
            </div>
        </div>
    </nz-sider>
    <nz-content *ngIf="currentNode">
        <div style="height: 100%;">
            <nz-tabset nzType="card"  (nzSelectedIndexChange)="tabChange($event)" [nzTabBarExtraContent]="extraTemplate" [(nzSelectedIndex)]="selectedTabIndex">
                <nz-tab nzTitle="设备模板">
                    <app-device-template-info [temp]="currentNode"
                        (refresh)="refreshTemplate()"></app-device-template-info>
                </nz-tab>
                <nz-tab nzTitle="信号">
                    <app-device-template-signal [temp]="currentNode" [tabelSearchText]="tableSearchText" (refresh)="refreshTemplate()" [tabIndex]="selectedTabIndex" (selectTab)="selectTab($event)"></app-device-template-signal>
                </nz-tab>
                <nz-tab nzTitle="事件">
                    <app-device-management-event [temp]="currentNode" [tabelSearchText]="tableSearchText" (refresh)="refreshTemplate()" [tabIndex]="selectedTabIndex"></app-device-management-event>
                </nz-tab>
                <nz-tab nzTitle="控制">
                    <app-device-template-control [template]="currentNode" [tabelSearchText]="tableSearchText" (refreshTree)="refreshTemplate()" [tabIndex]="selectedTabIndex" ></app-device-template-control>
                </nz-tab>
                <nz-tab nzTitle="变更记录">
                    <app-device-template-log [tabIndex]="selectedTabIndex" [objectType]="10" [objectId]="currentNode.id"></app-device-template-log>
                </nz-tab>
            </nz-tabset>
            <ng-template #extraTemplate>
                <div class="dtm_extra_temp">
                    <div class="dtm_temp_name">
                        当前模板：{{ tempName }}
                    </div>
                    <div class="dtm_table_searcher" *ngIf="selectedTabIndex == 1 || selectedTabIndex == 2 || selectedTabIndex == 3">
                        <nz-input-group [nzSuffix]="suffixIconSearch">
                            <input type="text" nz-input placeholder="请输入关键字查询..." [(ngModel)]="tableSearchText"/>
                          </nz-input-group>
                          <ng-template #suffixIconSearch>
                            <span nz-icon nzType="search"></span>
                          </ng-template>
                    </div>
                </div>
            </ng-template>
        </div>
    </nz-content>
</nz-layout>