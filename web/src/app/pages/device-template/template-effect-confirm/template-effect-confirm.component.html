<div class="tec_container">
    <d-data-table
    [dataSource]="filterSource"
    [scrollable]="true" 
    [virtualScroll]="true"
    tableHeight="450px"
    [fixHeader]="true"
    [tableWidthConfig]="cols" 
    [tableOverflowType]="'overlay'"
    [resizeable]="true"
    [onlyOneColumnSort]="true"
    [borderType]="'borderless'"
    [striped]="false"
>
    <thead dTableHead>
        <tr dTableRow>
            <ng-container *ngFor="let col of cols">
                <th dHeadCell
                    [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, col.field)"
                    [sortable]="false"
                    [showSortIcon]="false"
                >
                    <div class="devui_title">{{ col.title }}</div>
                    <div class="devui_searcher">
                        <nz-input-group [nzSuffix]="suffixIconSearch">
                            <input
                                type="text"
                                class="devui-form-control"
                                nz-input
                                [(ngModel)]="searchText[col.field]"
                                placeholder="请输入关键字..." 
                                (ngModelChange)="search()"/>
                        </nz-input-group>
                        <ng-template #suffixIconSearch>
                            <span nz-icon nzType="search"></span>
                        </ng-template>
                    </div>
                </th>
            </ng-container>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
          <tr dTableRow [ngClass]="{'devui-table-row-selected': checkedIds.includes(rowItem.samplerId)}">
            <ng-container *ngFor="let col of cols">
                <td dTableCell 
                    class="span-text"
                    [title]="rowItem[col.field]"
                >
                    {{ rowItem[col.field] }}
                </td>
            </ng-container>
          </tr>
        </ng-template>
      </tbody>
</d-data-table>
</div>