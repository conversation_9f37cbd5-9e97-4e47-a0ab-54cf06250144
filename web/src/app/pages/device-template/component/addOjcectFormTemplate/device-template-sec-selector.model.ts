// modal输入
export interface DeviceTemplateSecSelectorIn {
  // 选择器类型
  type: DeviceTemplateSecSelectorType,
  // 设备分类
  equipmentCategory: number,
  // 原模板id
  originEquipmentTemplateId: number,
  // 原信号/事件/控制列表（用于从模板增加时，判断是否已存在）
  originSecList: Array<any>,
}

// modal输出
export interface DeviceTemplateSecSelectorOut {
  // 模板id
  templateId?: number,
  // 信号/事件/控制，选择行数据
  rows?: Array<any>,
}

// 选择器类型枚举
export enum DeviceTemplateSecSelectorType {
  signal = 0,
  event = 1,
  control = 2
}
