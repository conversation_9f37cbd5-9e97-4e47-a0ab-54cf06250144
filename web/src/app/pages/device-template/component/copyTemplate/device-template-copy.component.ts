import { Component, Injector } from '@angular/core'
import { GenericModalComponent } from '@core/components/basic/generic.modal'
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms'
import { DeviceTemplateCopyIn, DeviceTemplateCopyOut } from './device-template-copy.model'
import { DeviceTemplateService } from '../../device-template.service'

@Component({
  selector: 'app-device-template-copy',
  templateUrl: './device-template-copy.component.html',
  styleUrls: ['./device-template-copy.component.less']
})

export class DeviceTemplateCopyComponent extends GenericModalComponent<DeviceTemplateCopyIn, DeviceTemplateCopyOut> {

  public constructor(
    private fb: NonNullableFormBuilder,
    injector: Injector,
    private service: DeviceTemplateService,
  ) {
    super(injector)
  }

  // ng-form
  public validateForm: FormGroup<{
    nameValue: FormControl<string>
    reasonValue: FormControl<string>
  }> = this.fb.group({
    nameValue: ['', [Validators.required]],
    reasonValue: ['', [Validators.required]]
  })

  // 新建模板名称
  public nameValue: string = ''

  // 创建原因
  public reasonValue: string = ''

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }

  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    if (!valid) {
      return
    }
    const params = {
      originEquipmentTemplateId: this.input.originEquipmentTemplateId,
      newEquipmentTemplateName: this.nameValue,
      reason: this.reasonValue
    }
    return new Promise<void>(stopLoading => {
      this.service.copyTemplate(params).then(res => {
        if (res.data) {
          // 返回新的模板id
          this.close({ newTemplateId: res.data })
        }
      }).finally(() => {
        stopLoading()
      })
    })
  }
}
