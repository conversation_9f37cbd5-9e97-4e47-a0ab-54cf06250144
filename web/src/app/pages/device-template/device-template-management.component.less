:host {
    nz-sider.nz-resizable-resizing {
        transition: none;
    }

    nz-content>div {
        width: 100%;
    }

    nz-content .resizable-box {
        flex: none;
    }

    .nz-resizable-handle.nz-resizable-handle-right.nz-resizable-handle-cursor-type-grid {
        width: 5px;
        right: -2.5px;

        .sider-resize-line {
            margin-top: 32px;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            transition-delay: 0.1s;

            &:hover {
                background-color: #afb8c133;
            }

            &>div {
                width: 1px;
                height: 100%;
                background-color: #d0d7de;
            }
        }
    }

    .dtm_top_top {
        color: #333333;
        padding: 4px;
        border-bottom: 1px solid #CCCCCC;
    }

    .dtm_top {
        display: flex;
        flex-direction: row;
        height: 36px;
        padding: 8px;
        border-bottom: 1px solid #CCCCCC;

        .dtm_operations {
            width: 100%;
            display: flex;
            flex-direction: row;

            .dtm_icon_mask {
                margin-left: 8px;
                height: 24px;
                line-height: 24px;
                cursor: pointer;

                &:first-child {
                    margin-left: 0;
                }
            }

            .dtm_o_icon {
                font-size: 16px;
                color: #007FFF;

                &:hover {
                    color: #00BFFF;
                }
            }
        }
    }

    .dtm_bottom {
        width: 100%;
        height: calc(100% - 31px - 36px - 40px);

        nz-tree {
            overflow: hidden;
        }

        .custom-node {
            cursor: pointer;
            line-height: 24px;

            .ng-star-inserted {
                display: flex !important;
                flex-direction: row;
                flex-wrap: nowrap;
                align-items: center;
            }
        }
    }

    .dtm_mid {
        width: 100%;
        padding: 4px;
    }

    .dtm_right {
        height: 100%;
        width: calc(100vw - 528px);
        margin-left: 16px;
        background-color: #FFF;
    }

    .ant-tabs {
        height: 100%;
        line-height: 14px;

        /deep/.ant-tabs-content {
            height: 100%;
        }
    }

    .file-name,
    .folder-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    nz-content {
        margin: 0 0 0 12px !important;

        ::ng-deep .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap {
            flex-shrink: 0;
        }
    }

    ::ng-deep .ant-tabs-tabpane {
        height: 100%;
    }

    .dtm_extra_temp {
        display: flex;
        flex-direction: row;
        align-items: center;

        .dtm_temp_name {
            margin-right: 8px;
        }

        .dtm_table_searcher {
            width: 200px;
            margin-top: -4px;

            .ant-input {
                line-height: 18px;
            }
        }
    }
}

.right-menu-icon {
    font-size: 16px;

    ::ng-deep svg {
        margin-right: 8px;
    }
}