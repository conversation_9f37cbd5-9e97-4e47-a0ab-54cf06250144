/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, Input, OnInit, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef } from 'ng-zorro-antd/modal';
@Component({
    selector: 'app-device-template-create',
    templateUrl: './device-template-create.component.html',
    styleUrls: ['./device-template-create.component.less']
})
export class DeviceTemplateCreateComponent extends GenericComponent implements OnInit {

    tempName?: string;
    tempReason?: string;
    readonly #modal = inject(NzModalRef);
    input: any;

    constructor(
        injector: Injector,
        private message: NzMessageService,
    ) {
        super(injector);
    }

    onInit() {
        if (this.input.tempName != null) {
            this.tempName = this.input.tempName;
        }
    }

    confirm() {
        if (this.tempName && this.tempName.length > 0) {
            this.#modal.destroy({ name: this.tempName, reason: this.tempReason });
        } else {
            this.message.warning('请输入新模板名称！')
        }
    }

    close() {
        this.#modal.destroy();
    }
}