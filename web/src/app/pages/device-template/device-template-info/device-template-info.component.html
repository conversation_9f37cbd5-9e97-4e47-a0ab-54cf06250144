<div class="dti_container" style="width: 100%; height: 100%; overflow: scroll;">
    <div class="dti_info">
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">设备模板ID</label>
                <input class="dti_info_input" nz-input readonly disabled [(ngModel)]="tempData.equipmentTemplateId" type="number" />
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">名称</label>
                <input class="dti_info_input" nz-input maxlength="128" [(ngModel)]="tempData.equipmentTemplateName" type="text"/>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">类型</label>
                <nz-select class="dti_select" nzShowSearch [disabled]="!isOriginTemp" [(ngModel)]="tempData.equipmentCategory">
                    <nz-option
                        *ngFor="let item of deviceCategoryList"
                        [nzValue]="item.itemId"
                        [nzLabel]="item.itemValue"></nz-option>
                </nz-select>
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">分类</label>
                <nz-select class="dti_select" [(ngModel)]="tempData.equipmentType">
                    <nz-option
                        *ngFor="let item of deviceTypeList"
                        [nzValue]="item.itemId"
                        [nzLabel]="item.itemValue"></nz-option>
                </nz-select>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">厂商</label>
                <nz-select class="dti_select" nzShowSearch [(ngModel)]="tempData.vendor" [nzOptions]="vendorArr" name="vendor">
                </nz-select>
                <!-- <input class="dti_info_input" nz-input [(ngModel)]="tempData.vendor" type="text"/> -->
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">单位</label>
                <input class="dti_info_input" nz-input [(ngModel)]="tempData.unit" type="text"/>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item" [title]="tempData.protocolCode">
                <label class="dti_info_title">协议编码</label>
                <input class="dti_info_input" nz-input readonly disabled [(ngModel)]="tempData.protocolCode" type="text" />
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">设备型号</label>
                <input class="dti_info_input" nz-input [(ngModel)]="tempData.equipmentStyle" type="text"/>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">采集器</label>
                <input class="dti_info_input" nz-input readonly disabled [(ngModel)]="tempData.samplerName" type="text" />
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">属性</label>
                <nz-select class="dti_select" [(ngModel)]="tempData.propertyList" nzMode="multiple">
                    <nz-option
                        *ngFor="let item of propertyList"
                        [nzValue]="item.itemId"
                        [nzLabel]="item.itemValue"></nz-option>
                </nz-select>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">设备基类</label>
                <nz-select class="dti_select" nzShowSearch [(ngModel)]="tempData.equipmentBaseType" (ngModelChange)="changeEqBaseType()">
                    <nz-option
                        nzDisabled
                        nzCustomContent
                        style="background-color: #e6f8ff">
                            <div 
                                class="select_title"
                                style="display: flex; flex-direction: row; align-items: center;">
                                <div class="select_title_no" style="width: 40px; font-weight: bold;">编号</div>
                                <div class="select_title_name" style="margin-left: 4px; font-weight: bold;">名称</div>
                            </div>
                    </nz-option>
                    <nz-option
                        nzCustomContent
                        *ngFor="let item of baseTypeList"
                        [nzValue]="item.baseEquipmentId"
                        [nzLabel]="item.baseEquipmentName">
                            <div 
                                class="select_value"
                                style="display: flex; flex-direction: row; align-items: center;">
                                <div class="select_value_no" style="width: 40px;">{{ item.baseEquipmentId }}</div>
                                <div class="select_value_name" style="margin-left: 4px;">{{ item.baseEquipmentName }}</div>
                            </div>
                    </nz-option>
                </nz-select>
            </div>
            <div class="dti_info_item">
                <label class="dti_info_title">局站类型</label>
                <nz-select class="dti_select" [(ngModel)]="tempData.stationCategory">
                    <nz-option
                        *ngFor="let item of stationCategoryList"
                        [nzValue]="item.id"
                        [nzLabel]="item.value">
                    </nz-option>
                </nz-select>
            </div>
        </div>
        <div class="dti_info_item_group">
            <div class="dti_info_item">
                <label class="dti_info_title">说明</label>
                <textarea rows="4" class="dti_info_textarea" nz-input [(ngModel)]="tempData.description"></textarea>
            </div>
        </div>
    </div>
    <div class="dti_button">
        <button nz-button nzType="primary" (click)="beforeSubmit()" [disabled]="!isInit">更新</button>
    </div>
</div>