// modal输入
export interface DeviceTemplateConfirmInput {
  // 模板信息
  template: any,
  // 信号|事件|控制列表（用于从模板增加时，判断是否已存在）
  secList: Array<any>,
}

// modal输出
export interface DeviceTemplateConfirmOut {
  // 操作
  action: DeviceTemplateConfirmActionOptions
  // 新模板id
  newTemplateId?: number
  data?: string
  refresh?: boolean,
  newTempData?: any,
  notShowAgain?: boolean,
}

// 操作按钮枚举
export enum DeviceTemplateConfirmActionOptions {
  update = '更新原模板',
  saveAs = '另存为新模板',
  cancel = '取消',
}