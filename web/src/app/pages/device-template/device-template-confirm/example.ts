// 以下是【增加控制】的示例
// public addControl(): void {
//     const modal = this.openDialog<DeviceTemplateConfirmComponent, DeviceTemplateConfirmInput, DeviceTemplateConfirmOut>({
//         nzTitle: '模板修改确认',
//         nzContent: DeviceTemplateConfirmComponent,
//         nzWidth: 900,
//         nzData: {
//             templateId: this.templateNode.id
//         },
//         nzFooter: [
//             {
//                 label: '更新原模板',
//                 type: 'primary',
//                 onClick: async (_: DeviceTemplateConfirmComponent): Promise<void> => {
//                     await this.createControl()
//                     modal.close()
//                 }
//             },
//             {
//                 label: '另存为新模板',
//                 type: 'primary',
//                 onClick: async (componentInstance: DeviceTemplateConfirmComponent): Promise<void> => {
//                     const result = await componentInstance.copyTemplate()
//                     if (!result.newTemplateId) return
//                     await this.createControl(result.newTemplateId)
//                     modal.close()
//                     this.refresh.emit()
//                 }
//             },
//             {
//                 label: '取消',
//                 type: 'default',
//                 onClick: componentInstance => componentInstance!.close()
//             },
//         ]
//     })
// }