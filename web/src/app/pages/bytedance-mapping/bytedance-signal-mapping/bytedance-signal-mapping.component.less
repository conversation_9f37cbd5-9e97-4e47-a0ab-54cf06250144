:host {
    nz-sider.nz-resizable-resizing {
        transition: none;
    }

    nz-content>div {
        width: 100%;
    }

    nz-content .resizable-box {
        flex: none;
    }

    .nz-resizable-handle.nz-resizable-handle-right.nz-resizable-handle-cursor-type-grid {
        width: 5px;
        right: -2.5px;

        .sider-resize-line {
            margin-top: 32px;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            transition-delay: 0.1s;

            &:hover {
                background-color: #afb8c133;
            }

            &>div {
                width: 1px;
                height: 100%;
                background-color: #d0d7de;
            }
        }
    }

    nz-content {
        margin: 0 0 0 12px !important;

        ::ng-deep .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap {
            flex-shrink: 0;
        }
    }

    .span-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

}

.modal_body {
    display: flex;
    flex-direction: row;
}

.star {
    padding-left: 12px;
    align-self: center;
}

.star::after {
    content: '*';
    font-size: 10px;
    color: red;
}

.right-menu-icon {
    font-size: 16px;

    ::ng-deep svg {
        margin-right: 8px;
    }
}
