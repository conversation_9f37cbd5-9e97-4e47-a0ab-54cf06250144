<nz-layout style="height: 100%;">
  <nz-sider [nzWidth]="siderWidth" nz-resizable [nzMinWidth]="150" [nzMaxWidth]="1000"
    (nzResize)="onSideResize($event)">
    <nz-resize-handle nzDirection="right" nzCursorType="grid">
      <div class="sider-resize-line">
        <div></div>
      </div>
    </nz-resize-handle>
    <div id="temp-container" style="display: flex; flex-wrap: wrap; overflow: hidden; width: 100%; height: 100%;">
      <d-data-table #myTable2 [dataSource]="pointFilterSource" [scrollable]="true" [virtualScroll]="true"
        style="overflow: auto;" [tableHeight]="tabelHeightString" [fixHeader]="true" [containFixHeaderHeight]="true"
        [tableWidthConfig]="pointcols" [tableOverflowType]="'overlay'" [resizeable]="true" [onlyOneColumnSort]="true"
        [borderType]="'borderless'" [striped]="false" dLoading [loadingStyle]="'default'" [loading]="isUpdating">
        <thead dTableHead>
          <tr dTableRow>
            <th *ngFor="let colConfig of pointcols" dHeadCell [sortable]="false" [resizeEnabled]="true"
              (resizeEndEvent)="onPointResize($event, colConfig.field)">
              {{ colConfig.title }}
              <input nz-input [(ngModel)]="pointSearchText[colConfig.field]" (ngModelChange)="pointSearch()" />
            </th>
          </tr>
        </thead>
        <tbody dTableBody>
          <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow [ngClass]="{ 'table-row-selected': selectedPointRow.pointId === rowItem.pointId }"
              (click)="onPointRowClick(rowItem)" (contextmenu)="contextMenu($event, menu)">
              <ng-container *ngFor="let col of pointcols">
                <td dTableCell>
                  <div class="span-text">{{ rowItem[col.field] }}</div>
                </td>
              </ng-container>
            </tr>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <ul nz-menu>
                <li nz-menu-item (click)="unbindPointMapping(rowItem)">
                  <iconfont [icon]="'icon-peizhi1'" class="right-menu-icon"></iconfont>{{
                  'bytedanceMapping.signal.unbindMapping' | translate }}
                </li>
              </ul>
            </nz-dropdown-menu>
          </ng-template>
        </tbody>
      </d-data-table>
    </div>
  </nz-sider>
  <nz-content>
    <div style="display: flex; flex-wrap: wrap; width: 100%; height: 100%; overflow: hidden;">
      <d-data-table #myTable [dataSource]="tempFilterSource" [scrollable]="true" [virtualScroll]="true"
        style="overflow: auto;" [tableHeight]="tabelHeightString" [fixHeader]="true" [containFixHeaderHeight]="true"
        [tableWidthConfig]="tempcols" [tableOverflowType]="'overlay'" [resizeable]="true" [onlyOneColumnSort]="true"
        [borderType]="'borderless'" [striped]="false" dLoading [loadingStyle]="'default'" [loading]="isUpdating">
        <thead dTableHead>
          <tr dTableRow>
            <ng-container *ngFor="let colConfig of tempcols">
              <th dHeadCell *ngIf="colConfig['field'] !== 'checkBox'" [sortable]="false" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)">
                {{ colConfig.title }}
                <input nz-input [(ngModel)]="tempSearchText[colConfig.field]" (ngModelChange)="tempSearch()" />
              </th>
              <th dHeadCell *ngIf="colConfig['field'] === 'checkBox'">
                <label class="checkbox-inline custom-checkbox nowrap">
                  <input type="checkbox" [ngModel]="checkAll" (change)="onAllChecked(!checkAll)">
                  <span></span>
                </label>
              </th>
            </ng-container>
          </tr>
        </thead>
        <tbody dTableBody>
          <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow
              [ngClass]="{ 'table-row-selected': selectedTempRow.signalId === rowItem.signalId || selectedTempRowList.includes(rowItem.signalId) }"
              (click)="onTempRowClick(rowItem)" (dblclick)="onTempRowDBClick(rowItem)"
              (contextmenu)="contextMenu($event, menu)">
              <ng-container *ngFor="let col of tempcols">
                <td dTableCell *ngIf="col['field'] === 'checkBox'">
                </td>
                <td dTableCell *ngIf="col['field'] !== 'checkBox'">
                  <div class="span-text">{{ rowItem[col.field] }}</div>
                </td>
              </ng-container>
            </tr>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <ul nz-menu>
                <li nz-menu-item (click)="unbindTempMapping(rowItem)">
                  <iconfont [icon]="'icon-peizhi1'" class="right-menu-icon"></iconfont>{{
                  'bytedanceMapping.signal.unbindMapping' | translate }}
                </li>
                <li nz-menu-item (click)="onTempRowDBClick(rowItem)">
                  <iconfont [icon]="'icon-peizhi2'" class="right-menu-icon"></iconfont>{{
                  'bytedanceMapping.signal.applyMapping' | translate }}
                </li>
              </ul>
            </nz-dropdown-menu>
          </ng-template>
        </tbody>
      </d-data-table>
      <nz-modal [(nzVisible)]="showExtendNum" [nzTitle]="modalTitle" [nzContent]="modalContent" [nzFooter]="modalFooter"
        (nzOnCancel)="closeModal(false)">
        <ng-template #modalTitle>{{ 'bytedanceMapping.signal.moduleNum' | translate }}</ng-template>
        <ng-template #modalContent>
          <div class="modal_body">
            <input nz-input type="text" maxLength="3" [(ngModel)]="extendNum" /><i class="star"></i>
          </div>
        </ng-template>
        <ng-template #modalFooter>
          <button nz-button nzType="default" (click)="closeModal(false)">{{ 'common.cancel' | translate }}</button>
          <button nz-button nzType="primary" (click)="closeModal(true)">{{ 'common.ok' | translate }}</button>
        </ng-template>
      </nz-modal>
    </div>
  </nz-content>
</nz-layout>