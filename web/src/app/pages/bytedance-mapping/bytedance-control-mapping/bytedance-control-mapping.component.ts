/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, ElementRef, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { NzModalService } from 'ng-zorro-antd/modal';
import { cloneDeep } from 'lodash';
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { ByteDanceMappingService } from '../bytedance-mapping.service';
import { DeviceTemplateService } from 'app/pages/device-template/device-template.service';
import { NzMessageService } from '@node_modules/ng-zorro-antd/message';
import { NzResizeEvent } from '@node_modules/ng-zorro-antd/resizable';
import { NzContextMenuService, NzDropdownMenuComponent } from '@node_modules/ng-zorro-antd/dropdown';

@Component({
    selector: 'app-bytedance-control-mapping',
    templateUrl: './bytedance-control-mapping.component.html',
    styleUrls: ['../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './bytedance-control-mapping.component.less']
})
export class BytedanceControlMappingComponent extends GenericComponent implements OnInit {

    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @ViewChild('myTable2') public myDevTable2!: DevuiTableFilterComponent | any;
    @Input()
    get temp(): any { return this.template }
    set temp(temp: any) {
        this.template = temp;
        if (this.template && this.template.template) {
            this.message.remove();
            if (this._tabIndex === 2) {
                this.selectedTempRow = {};
                this.selectedPointRow = {};
                this.getInitCols();
                this.getTempList();
                this.getPointList();
            }
        }
    }
    // Tabs下标，解决jexcel固定列bug
    @Input()
    public set tabIndex(index: number | undefined) {
        this._tabIndex = index;
        if (index === 2) {
            this.selectedTempRow = {};
            this.selectedPointRow = {};
            this.getInitCols();
            this.getTempList();
            this.getPointList();
        }
    }
    @Input()
    public set refresh(refresh: boolean) {
        if (refresh && this._tabIndex === 2) {
            this.selectedTempRow = {};
            this.selectedPointRow = {};
            this.selectedTempRowList = [];
            this.getTempList();
            this.getPointList();
        }
    }
    // sider 宽度
    siderWidth = 660;
    // sider resize id
    sideResizeId = -1;
    template: any;
    _tabIndex: any;

    editableTip = EditableTip.hover;

    //temp表
    tempcols: any = [];
    tempCols = [
        {
            field: 'checkBox',
            titleKey: 'protocol.checkBox',
            width: '40px'
        },
        {
            field: 'controlId',
            titleKey: 'bytedanceMapping.control.controlId',
            width: '120px'
        },
        {
            field: 'controlName',
            titleKey: 'bytedanceMapping.control.controlName',
            width: '120px'
        },
        {
            field: 'mappingPointName',
            titleKey: 'bytedanceMapping.signal.dictionaryEntry',
            width: '120px'
        },
        {
            field: 'description',
            titleKey: 'bytedanceMapping.signal.description',
            width: '120px'
        },
        {
            field: 'baseTypeName',
            titleKey: 'bytedanceMapping.signal.baseTypeName',
            width: '120px'
        },
    ]
    tempSearchText: any = {
        controlId: null,
        signalName: null,
        mappingPointName: null,
        description: null,
        baseTypeName: null,
    }
    searchTimer: any;
    tempSource: any;
    tempFilterSource: any;
    minWidth = 40;
    selectedTempRow: any;
    selectedTempRowList: any = [];
    checkAll: boolean = false;

    //point表
    pointcols: any = [];
    pointCols = [
        {
            field: 'pointId',
            titleKey: 'bytedanceMapping.signal.pointId',
            width: '120px'
        },
        {
            field: 'pointName',
            titleKey: 'bytedanceMapping.signal.pointName',
            width: '120px'
        },
        {
            field: 'typeName',
            titleKey: 'bytedanceMapping.signal.pointType',
            width: '60px'
        },
        {
            field: 'extendName',
            titleKey: 'bytedanceMapping.signal.extendName',
            width: '120px'
        },
        {
            field: 'mappingName',
            titleKey: 'bytedanceMapping.control.mappingControl',
            width: '120px'
        },
    ];
    pointSearchText: any = {
        pointId: null,
        pointName: null,
        type: null,
        mappingName: null,
    }
    pointSearchTimer: any;
    pointSource: any;
    pointFilterSource: any;
    selectedPointRow: any;


    tabelHeightString = '450px';
    isUpdating: boolean = false;


    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []
    //拓展数字框
    showExtendNum: boolean = false;
    extendNum: string = '';

    constructor(
        injector: Injector,
        private ele: ElementRef,
        private modal: NzModalService,
        private message: NzMessageService,
        private service: DeviceTemplateService,
        private nzContextMenuService: NzContextMenuService,
        private byteService: ByteDanceMappingService) {
        super(injector);
    }

    protected onInit() {
        this.selectedTempRow = {};
        this.selectedPointRow = {};
        this.getInitCols();
        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                if (event.url == '/pages/bytedance-mapping') {
                    this.getTempList();
                    this.getPointList();
                }
            }
        })
    }

    protected onDestroy(): void {

    }

    onSideResize({ width }: NzResizeEvent): void {
        cancelAnimationFrame(this.sideResizeId)
        this.sideResizeId = requestAnimationFrame(() => {
            this.siderWidth = width!
        })
    }

    getInitCols() {
        this.tempcols = this.tempCols.map(item => ({ ...item, title: item['titleKey'] ? this.translate.instant(item['titleKey']) : '' }));
        this.pointcols = this.pointCols.map(item => ({ ...item, title: item['titleKey'] ? this.translate.instant(item['titleKey']) : '' }));
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.tempcols, this.ele);
        if ($event.width < this.minWidth) {
            $event.width = this.minWidth;
        }
        func($event, field);
    }

    getClickTimes(): boolean {
        // 模拟鼠标双击事件
        if (this.clickTimes.length === 0) {
            this.clickTimes.push(new Date())
            return false
        }
        if (this.clickTimes.length === 1 && new Date().valueOf() - this.clickTimes[0].valueOf() > 500) {
            this.clickTimes[0] = new Date()
            return false
        }
        return true
    }

    getTempList() {
        this.byteService.getControlMappingList(this.template.id).then((res: any) => {
            if (res && res.length) {
                this.tempSource = res;
                this.tempSearch();
            } else {
                this.tempSource = [];
                this.tempSearch();
            }
            this.tabelHeightString = document.documentElement.scrollHeight - 165 + 'px';
        })
    }

    tempSearch() {
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.tempSource);
            for (const field in this.tempSearchText) {
                if (this.tempSearchText[field]) {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.tempSearchText[field]) : false;
                    })
                }
            }
            this.tempFilterSource = filterList;
        }, 500)
    }

    onTempRowClick(item: any) {
        if(this.selectedPointRow.extendName) {
            this.selectedTempRow = {};
            let ind = this.selectedTempRowList.findIndex((obj: any) => {
                return obj === item.controlId;
            })
            if(ind > -1) {
                this.selectedTempRowList.splice(ind, 1);
            } else {
                this.selectedTempRowList.push(item.controlId);
            }
            this.refreshCheckedBoxStatus();
        } else {
            this.selectedTempRowList = [];
            this.selectedTempRow = item;
            this.refreshCheckedBoxStatus();
        }
    }

    onTempRowDBClick(item: any) {
        if (!this.selectedPointRow) return;
        if (this.selectedPointRow.extendName && this.selectedTempRowList.length > 0) {
            this.openModal();
        } else {
            if(!this.selectedTempRow.controlId) return;
            this.selectedPointRow.extendNum = null;
            const params = {
                type: 3,
                equipmentTemplateId: item.equipmentTemplateId,
                mappingId: item.controlId,
                equipmentCategoryId: this.selectedPointRow.equipmentCategoryId,
                pointId: this.selectedPointRow.pointId,
                extendNum: this.selectedPointRow.extendNum
            }
            this.byteService.applyStandardPointMapping(params).subscribe((res: any) => {
                if (res.code === 0) {
                    this.message.success('更新成功！')
                }
                this.getTempList();
                this.getPointList();
            })
        }
    }

    onAllChecked(checked: boolean): void {
        if(!this.selectedPointRow.extendName) return;
        this.tempFilterSource.forEach( (item: any) => {
            this.updateCheckedIds(item.controlId, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.selectedTempRowList.includes(id)) {
              this.selectedTempRowList.push(id);
            }
        } else {
            this.selectedTempRowList.splice(this.selectedTempRowList.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = this.tempFilterSource.length && this.tempFilterSource.every((item: any ) => {
            return this.selectedTempRowList.includes(item.controlId)
        });
        if(this.selectedTempRowList.length === 0) {
            this.checkAll = false;
        }
    }

    getPointList() {
        this.byteService.getByteDanceStandardPointsList(3, this.template.equipmentCategory).then(res => {
            if (res && res.length) {
                this.pointSource = res;
                this.pointSearch();
            } else {
                this.pointSource = [];
                this.pointSearch();
            }
        })
    }

    pointSearch() {
        if (this.pointSearchTimer) {
            clearTimeout(this.pointSearchTimer);
        }
        this.pointSearchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.pointSource);
            for (const field in this.pointSearchText) {
                if (this.pointSearchText[field]) {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.pointSearchText[field]) : false;
                    })
                }
            }
            this.pointFilterSource = filterList;
        }, 500)
    }

    onPointRowClick(item: any) {
        this.selectedPointRow = item;
        if(!this.selectedPointRow.extendName) {
            this.selectedTempRowList = [];
        }
        this.refreshCheckedBoxStatus();
    }

    onPointResize($event: any, field: any) {
        const func = tableResizeFunc(this.pointcols, this.ele);
        if ($event.width < this.minWidth) {
            $event.width = this.minWidth;
        }
        func($event, field);
    }

    openModal() {
        this.extendNum = '';
        this.showExtendNum = true;
    }

    closeModal(type: boolean) {
        if (type) {
            if(!this.extendNum) return;
            this.showExtendNum = false;
            let params = {};
            if(this.selectedPointRow.extendName) {
                params = {
                    type: 3,
                    equipmentTemplateId: this.temp.id,
                    mappingIds: this.selectedTempRowList,
                    equipmentCategoryId: this.selectedPointRow.equipmentCategoryId,
                    pointId: this.selectedPointRow.pointId,
                    extendStartNum: this.extendNum
                }
            } else {
                params = {
                    type: 3,
                    equipmentTemplateId: this.temp.id,
                    mappingId: this.selectedTempRow.controlId,
                    equipmentCategoryId: this.selectedPointRow.equipmentCategoryId,
                    pointId: this.selectedPointRow.pointId,
                    extendNum: this.extendNum
                }
            }
            this.byteService.applyStandardPointMapping(params).subscribe((res: any) => {
                if (res.code === 0) {
                    this.message.success('更新成功！')
                }
                this.getTempList();
                this.getPointList();
            })
        } else {
            this.showExtendNum = false;
        }
    }

    contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        this.nzContextMenuService.create($event, menu);
    }

    unbindTempMapping(item: any) {
        let params = {};
        if (this.selectedTempRowList.length > 0) {
            params = {
                type: 3,
                equipmentTemplateId: this.temp.id,
                mappingIds: this.selectedTempRowList.join(','),
            }
        } else {
            if(!this.selectedTempRow.controlId) return;
            params = {
                type: 3,
                equipmentTemplateId: this.temp.id,
                mappingIds: this.selectedTempRow.controlId,
            }
        }
        this.byteService.unbindStandardPointMapping(params).subscribe((res: any) => {
            if (res.code === 0) {
                this.message.success('更新成功！')
            }
            this.getTempList();
            this.getPointList();
        })
    }

    unbindPointMapping(item: any) {
        if(!this.selectedPointRow.pointId) return;
        let params = {
            type: 3,
            equipmentTemplateId: this.temp.id,
            mappingIds: item.mappingId,
        }
        this.byteService.unbindStandardPointMapping(params).subscribe((res: any) => {
            if (res.code === 0) {
                this.message.success('更新成功！')
            }
            this.getTempList();
            this.getPointList();
        })
    }


}