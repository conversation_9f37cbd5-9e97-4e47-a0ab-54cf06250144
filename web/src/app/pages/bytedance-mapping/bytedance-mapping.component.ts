/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { AfterContentChecked, Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeNode, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { GlobalState } from '@services/global.state';
import { ByteDanceMappingService } from './bytedance-mapping.service';
import { NzResizeEvent } from '@node_modules/ng-zorro-antd/resizable';
import { NavigationEnd } from '@angular/router';
import { cloneDeep } from 'lodash';
import { DeviceTemplateService } from '../device-template/device-template.service';
@Component({
    selector: 'app-bytedance-mapping',
    templateUrl: './bytedance-mapping.component.html',
    styleUrls: ['./bytedance-mapping.component.less']
})

export class BytedanceMappingComponent extends GenericComponent implements OnInit, AfterContentChecked {

    // sider 宽度
    siderWidth = 259
    // sider resize id
    sideResizeId = -1

    treeHeight: string | null = null;

    searchText: string = '';
    nodes: any;
    @ViewChild('nzTree') nzTree!: NzTreeComponent;

    @Input() dataType: string = 'event';
    @Output() select: EventEmitter<void> = new EventEmitter<void>();

    expendKeys: any = [];
    selectedKeys: any = [];
    beforeExpend: any = [];
    beforeSelect: any = [];

    getFirstNode: boolean = false;
    getFirstLeaf: any;
    currentNode: any;
    routeToName!: string;

    // 监听选择了哪个tab
    selectedTabIndex?: number = 0;
    tempName: any = '';
    //树滚动索引
    treeIndex = 0;
    //搜索
    seachChanged = false;
    mathNodes: NzTreeNodeOptions[] = [];
    mathIndex = 0;
    refresh: boolean = false;

    constructor(injector: Injector,
        private nzContextMenuService: NzContextMenuService,
        private byteService: ByteDanceMappingService,
        private service: DeviceTemplateService,
        private global: GlobalState,
        private message: NzMessageService) {
        super(injector);
    }

    onInit() {
        this.getTreeData();
        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                // 滚动条回显
                if (event.url == '/pages/device-template') {
                    this.selectTemp({ node: { origin: this.currentNode } }, false);
                }
            }
        })
    }

    // 不知道有没有性能问题
    public ngAfterContentChecked(): void {
        this.treeHeight = document.getElementById('dtm_bottom')?.offsetHeight + 'px'
    }

    onDestroy() {
        this.service.setTempId('');
    }

    onSearchChange(value: string): void {
        this.searchText = value;
        this.seachChanged = true;
    }

    getTreeData() {
        this.service.getTemplateTree().then((res: any) => {
            let node = res.data;
            this.buildTree(node);
            this.nodes = node;
            setTimeout(() => {
                if (this.service.getTempId().toString().length > 0) {
                    this.getNodeById(this.service.getTempId().toString())
                }
            }, 0);
            this.expendKeys = this.beforeExpend;
            if (this.routeToName) {
                this.searchText = cloneDeep(this.routeToName);
                this.routeToName = '';
            }
            this.selectedKeys = this.beforeSelect.length ? this.beforeSelect : this.getFirstLeaf.key;
            if (!this.currentNode) this.selectTemp({ node: { origin: this.getFirstLeaf } } as any, false)
        })
    }

    highlightSearchText(title: string): string {
        if (!this.searchText.trim()) {
            return title;
        }
        const regEx = new RegExp(this.searchText.trim(), 'gi');
        return title.replace(regEx, `<span class="font-highlight">$&</span>`);
    }

    selectNode(): void {
        if (this.seachChanged) {
            this.seachChanged = false;
            this.mathNodes = [];
            this.mathIndex = 0;
            this.findNodesWithTitle(this.searchText.trim(), this.nodes, this.mathNodes);
            if (this.mathNodes.length > 0) {
                // Assuming we select the first matching node
                const node = this.mathNodes[this.mathIndex];
                // Trigger selection of the node        
                this.selectedKeys = [node.key];
                this.selectTemp({ node: { origin: node } }, false);
                // Replace with your actual logic to select the node
            }
        } else {
            this.mathIndex++;
            if (this.mathIndex == this.mathNodes.length)
                this.mathIndex = 0;
            if (this.mathIndex < this.mathNodes.length) {
                const node = this.mathNodes[this.mathIndex];
                // Trigger selection of the node
                this.selectedKeys = [node.key];
                this.selectTemp({ node: { origin: node } }, false);
            }
        }
    }

    private findNodesWithTitle(title: string, parent: any[], mathNodes: NzTreeNodeOptions[]) {
        parent.forEach((p: any) => {
            if (p.title.toLowerCase().includes(title.toLowerCase())) {
                mathNodes.push(p);
            }
            if (p.children && p.children.length > 0) {
                this.findNodesWithTitle(title, p.children, mathNodes);
            }
        });
    }

    buildTree(nodes: any) {
        for (let i = 0; i < nodes.length; i++) {
            //判断是否为层级节点
            nodes[i]['title'] = nodes[i]['name'];
            nodes[i]['key'] = nodes[i]['id'].toString();
            //展开节点
            if (!this.getFirstNode) {
                if (i === 0) {
                    nodes[i]['expanded'] = true;
                }
            }
            if (nodes[i]['children'] && nodes[i]['children'].length > 0) {
                this.buildTree(nodes[i]['children']);
            } else {
                if (!this.getFirstNode) {
                    if (i === 0) {
                        this.getFirstNode = true;
                        this.getFirstLeaf = nodes[i]
                    }
                }
                nodes[i]['isLeaf'] = true;
            }
        }
    }

    getNodeById(id: any) {
        let node = this.nzTree.getTreeNodeByKey(id.toString());
        this.selectedKeys = node ? [node.key] : this.selectedKeys;
        this.selectTemp({ node: node }, false);
    }

    selectTemp($event: any, isclick: boolean) {
        this.currentNode = $event.node.origin;
        this.tempName = this.currentNode.name;
        this.beforeSelect = [this.currentNode.key];
        if (!isclick) {
            this.treeIndex = 0;
            this.buildIndexTree(this.nodes);
            this.nzTree.cdkVirtualScrollViewport.scrollToIndex(this.currentNode.index);
        }
    }

    buildIndexTree(list: any) {
        list.forEach((item: any) => {
            item['index'] = this.treeIndex;
            this.treeIndex++;
            if (item['expanded'] && item['children'] && item['children'].length > 0) {
                this.buildIndexTree(item['children']);
            }
        })
    }

    // 双击展开/收回子节点
    openFolder(data: NzTreeNode | NzFormatEmitEvent): void {
        // do something if u want
        if (data instanceof NzTreeNode) {
            data.isExpanded = !data.isExpanded
        } else {
            const node = data.node
            if (node) {
                node.isExpanded = !node.isExpanded
            }
        }
    }

    onSideResize({ width }: NzResizeEvent): void {
        cancelAnimationFrame(this.sideResizeId)
        this.sideResizeId = requestAnimationFrame(() => {
            this.siderWidth = width!
        })
    }

    public tabChange(index: number): void {
        this.selectedTabIndex = index;
    }

    batchApplyEqCategory() {
        if(!this.currentNode.id) return;
        const params = {
            type: this.selectedTabIndex ? this.selectedTabIndex + 1 : 1,
            equipmentCategoryId: this.currentNode.equipmentCategory,
            equipmentTemplateId: this.currentNode.id,
        }
        this.byteService.batchApplyEquipmentCategory(params).subscribe((res: any) => {
            if (res.code === 0) {
                this.message.success('更新成功！')
                this.refresh = true;
                setTimeout(() => {
                    this.refresh = false;
                }, 2000);
            }
        })
    }

    bindUnmappingPoints() {
        if(!this.currentNode.id) return;
        const params = {
            type: this.selectedTabIndex ? this.selectedTabIndex + 1 : 1,
            equipmentCategoryId: this.currentNode.equipmentCategory,
            equipmentTemplateId: this.currentNode.id,
        }
        this.byteService.bindUnmappingPoints(params).subscribe((res: any) => {
            if (res.code === 0) {
                this.message.success('更新成功！')
                this.refresh = true;
                setTimeout(() => {
                    this.refresh = false;
                }, 2000);
            }
        })
    }

    unBindUnmappingPoints() {
        if(!this.currentNode.id) return;
        const params = {
            type: this.selectedTabIndex ? this.selectedTabIndex + 1 : 1,
            equipmentTemplateId: this.currentNode.id,
        }
        this.byteService.unBindUnmappingPoints(params).subscribe((res: any) => {
            if (res.code === 0) {
                this.message.success('更新成功！')
                this.refresh = true;
                setTimeout(() => {
                    this.refresh = false;
                }, 2000);
            }
        })
    }
}