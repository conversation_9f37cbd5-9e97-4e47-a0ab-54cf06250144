import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';


import { PagesComponent } from './pages.component';
// import { DashboardModule } from './dashboard/dashboard.module';
import { PagesRoutingModule } from './pages-routing.module';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
// import { ButtonsComponent } from './buttons/buttons.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { CoreModule } from '@core/core.module';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';

// import { BrowserModule } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';
import { NzPageHeaderModule } from 'ng-zorro-antd/page-header';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzQRCodeModule } from 'ng-zorro-antd/qr-code';
import { WorkersComponent } from './workers/workers.component';
import { HomeComponent } from './home/<USER>';
import { UserComponent } from './user/user.component';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { DevUIModule, IconModule } from 'ng-devui';
import { I18nModule } from 'ng-devui/i18n';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { ConfigInitComponent } from './init/config-init.component';
import { ConfigInitService } from './init/config-init.service';
import { ApiService } from '@services/api-service';
import { ServerComponent } from './server/server.component';
import { AddEditModalComponent } from './server/add-edit-modal/add-edit-modal.component';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { ServerService } from './server/server.service';
import { TranslateModule } from '@ngx-translate/core';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { DeviceTemplateManagementComponent } from './device-template/device-template-management.component';
import { DeviceTemplateInfoComponent } from './device-template/device-template-info/device-template-info.component';
import { DeviceTemplateConfirmComponent } from './device-template/device-template-confirm/device-template-confirm.component';
import { DeviceTemplateCreateComponent } from './device-template/device-template-create/device-template-create.component';
import { ProtocolManagementComponent } from './protocol-management/protocol-management.component';
import { ProtocolImportComponent } from './protocol-management/protocol-import/protocol-import.component';
import { ProtocolManagementService } from './protocol-management/protocol-management.service';
import { DeviceTemplateService } from './device-template/device-template.service';
import { GlobalState } from '@services/global.state';
import { ComponentsModule } from '@components/components.module';
import { NzListModule } from 'ng-zorro-antd/list';
import { DeviceTemplateCopyComponent } from './device-template/component/copyTemplate/device-template-copy.component';
import { DeviceTemplateSecSelectorComponent } from './device-template/component/addOjcectFormTemplate/device-template-sec-selector.component';
import { DeviceAssociatedTemplateComponent } from './device-template/device-associated-template/device-associated-template.component';
import { DeviceTemplateSelectorComponent } from './device-template/device-template-selector/device-template-selector.component';
import { EventConditionEditorComponent } from './device-template/event-condition-editor/event-condition-editor.component';
import { TemplateEffectConfirmComponent } from './device-template/template-effect-confirm/template-effect-confirm.component';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { DataDictionaryService } from '@services/data-dictionary.service';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { SelectLevelComponent as CSelectLevelComponent } from './collection-management/add-device/select-level/select-level.component';
import { SelectEqTemplateComponent as CSelectEqTemplateComponent } from './collection-management/add-device/select-eqtemplate/select-eqtemplate.component';
import { DeviceManagementModule } from './device-management/device-management.module';
import { MuDistributionComponent } from './mu-management/mu-distribution/mu-distribution.component';
import { DeviceEventInstanceComponent } from './device-management/device-event-instance/device-event-instance.component';
import { DeviceEventInstanceListComponent } from './device-management/device-event-instance-list/device-event-instance-list.component';
import { DeviceSignalInstanceComponent } from './device-management/device-signal-instance/device-signal-instance.component';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { DeviceSignalInstanceListComponent } from './device-management/device-signal-instance-list/device-signal-instance-list.component';
import { BaseclassManagementModule } from './baseclass-management/baseclass-management.module';
import { ComponentsExampleModule } from '@components/componentsExample.module';
import { LogComponent } from './log/log.component';
import { LogService } from './log/logservice';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { DeviceManagementToolsModule } from './device-management-tools/device-management-tools.module';
import { MuManagementModule } from './mu-management/mu-management.module';
import { AlarmLinkageModule } from './alarm-linkage/alarm-linkage.module';
import { SystemCategoryManagementModule } from './system-category-management/system-category-management.module';
import { CustomerStandardizationModule } from './customer-standardization/customer-standardization.module';
import { BInterfaceManagementModule } from './b-interface-management/b-interface-management.module';
import { LevelManagementModule } from './level-management/level-management.module';
import { CollectionManagementModule } from './collection-management/collection-management.module';
import { ByteDanceMappingModule } from './bytedance-mapping/bytedance-mapping.module';
import { FieldCopyToSelector } from './device-template/field-copy-to-selector/field-copy-to-selector.component';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    // TranslationModule.forRoot({ root: './i18n/' }),
    NzIconModule,
    IconModule,
    ReactiveFormsModule,
    TranslateModule,
    CoreModule,
    NzMenuModule,
    NzLayoutModule,
    NzRadioModule,
    PagesRoutingModule,
    NzTreeModule,
    NzFormModule,
    NzPageHeaderModule,
    NzTabsModule,
    NzStatisticModule,
    NzDescriptionsModule,
    DevUIModule,
    I18nModule,
    NzButtonModule,
    NzPopconfirmModule,
    NzStepsModule,
    NzDropDownModule,
    NzInputModule,
    NzDividerModule,
    NzBadgeModule,
    NzQRCodeModule,
    NzSelectModule,
    NzModalModule,
    NzTagModule,
    TranslateModule,
    NzUploadModule,
    NzPaginationModule,
    ComponentsModule,
    NzListModule,
    NzResizableModule,
    NzSpinModule,
    NzCheckboxModule,
    DeviceManagementModule,
    LevelManagementModule,
    CollectionManagementModule,
    BaseclassManagementModule,
    ComponentsExampleModule,
    NzDatePickerModule,
    NzEmptyModule,
    DeviceManagementToolsModule,
    AlarmLinkageModule,
    SystemCategoryManagementModule,
    CustomerStandardizationModule,
    BInterfaceManagementModule,
    ByteDanceMappingModule,
  ],
  declarations: [
    PagesComponent,
    HomeComponent,
    UserComponent,
    WorkersComponent,
    ConfigInitComponent,
    DeviceTemplateManagementComponent,
    ServerComponent,
    AddEditModalComponent,
    DeviceTemplateManagementComponent,
    DeviceTemplateInfoComponent,
    DeviceTemplateConfirmComponent,
    DeviceTemplateCreateComponent,
    ProtocolManagementComponent,
    ProtocolImportComponent,
    DeviceTemplateCopyComponent,
    DeviceTemplateSecSelectorComponent,
    DeviceAssociatedTemplateComponent,
    DeviceTemplateSelectorComponent,
    CSelectLevelComponent,
    CSelectEqTemplateComponent,
    EventConditionEditorComponent,
    MuDistributionComponent,
    TemplateEffectConfirmComponent,
    DeviceEventInstanceComponent,
    DeviceEventInstanceListComponent,
    DeviceSignalInstanceComponent,
    DeviceSignalInstanceListComponent,
    LogComponent,
    FieldCopyToSelector,
  ],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA
  ],
  providers: [
    ApiService,
    ConfigInitService,
    ConfigInitService,
    ProtocolManagementService,
    DataDictionaryService,
    DeviceTemplateService,
    ServerService,
    GlobalState,
    LogService
  ]
})
export class PagesModule {



}
