import { Component, <PERSON>ement<PERSON><PERSON>, Injector, OnInit, ViewChild } from '@angular/core'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'
import { StationTypeMappingComponent } from './station-type-mapping/station-type-mapping.component';
import { TemplateStationMappingComponent } from './template-station-mapping/template-station-mapping.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { SignalStandardizationMappingComponent } from './signal-standardization-mapping/signal-standardization-mapping.component';
import { EventStandardizationMappingComponent } from './event-standardization-mapping/event-standardization-mapping.component';
import { ControlStandardizationMappingComponent } from './control-standardization-mapping/control-standardization-mapping.component';
import { TemplateApplicationCheckComponent } from './template-application-check/template-application-check.component';
import { CustomerStandardizationService } from './customer-standardization.service';
import { StandardizationCompareComponent } from './standardization-compare/standardization-compare.component';
import { TemplateMappingCheckComponent } from './template-mapping-check/template-mapping-check.component';
import * as XLSX from 'xlsx';
import { StandardizationSelectorComponent } from './standardization-selector/standardization-selector.component';

@Component({
    selector: 'app-customer-standardization-management',
    templateUrl: './customer-standardization-management.component.html',
    styleUrls: ['../../@components/basic/devui-table-filter/devui-table-filter.component.less', './customer-standardization-management.component.less']
})

export class CustomerStandardizationManagementComponent extends DevuiTableFilterComponent implements OnInit {

    selectedTabIndex?: number = 0;
    beforeSelectedTabIndex?: number = 0;
    currentSelectedFile?: File;
    currentType: number = -1;

    exporting: any;

    tableCount: number = 0;

    @ViewChild('tabs_1') public tabs_1!: StationTypeMappingComponent;
    @ViewChild('tabs_2') public tabs_2!: TemplateStationMappingComponent;
    @ViewChild('tabs_3') public tabs_3!: SignalStandardizationMappingComponent;
    @ViewChild('tabs_4') public tabs_4!: EventStandardizationMappingComponent;
    @ViewChild('tabs_5') public tabs_5!: ControlStandardizationMappingComponent;

    public constructor(
        injector: Injector,
        ele: ElementRef,
        private modal: NzModalService,
        private service: CustomerStandardizationService,
    ) {
        super(injector, ele)
    }

    onInit() {
        this.getCurrentType();
    }

    public tabChange(index: number): void {
        if (this.selectedTabIndex === 0 && this.beforeSelectedTabIndex === 0) {
            let flag = this.tabs_1.checkUnmappingData();
            if (flag) {
                const modal = this.modal.warning({
                    nzTitle: '提示',
                    nzContent: '存在没做标准化映射的局站类型，请选择映射关系！',
                    nzOkText: '确认',
                    nzOkType: 'primary',
                    nzOkDanger: true,
                    nzOnOk: () => {
                        return 'yes'
                    },
                })
                modal.afterClose.subscribe((res: any) => {
                    if (res === 'yes') {
                        this.beforeSelectedTabIndex = this.selectedTabIndex;
                        this.selectedTabIndex = 0;
                        return;
                    }
                })
            }
        }
        this.beforeSelectedTabIndex = index;
        this.selectedTabIndex = index;
        if (index === 0) {
            this.tabs_1.setTabelScroll();
        }
        if (index === 1) {
            this.tabs_2.setTabelScroll();
            this.tabs_2.search();
        }
        if (index === 2) {
            this.tabs_3.setTabelScroll();
            this.tabs_3.search();
        }
        if (index === 3) {
            this.tabs_4.setTabelScroll();
            this.tabs_4.search();
        }
        if (index === 4) {
            this.tabs_5.setTabelScroll();
            this.tabs_5.search();
        }
    }

    getCurrentType() {
        if(this.currentType > -1) return;
        this.service.getCurrentType().then((res: any) => {
            if (res) {
                this.currentType = res.standardId;
            }
        })
    }

    public refreshTab() {
        if (this.selectedTabIndex === 0) {
            this.tabs_1.getMappingData();
        }
        if (this.selectedTabIndex === 1) {
            this.tabs_2.getTabelData();
        }
        if (this.selectedTabIndex === 2) {
            this.tabs_3.getTabelData();
        }
        if (this.selectedTabIndex === 3) {
            this.tabs_4.getTabelData();
        }
        if (this.selectedTabIndex === 4) {
            this.tabs_5.getTabelData();
        }
    }

    updateCount(event: any) {
        if (this.selectedTabIndex === 2 && event.type === 1) {
            this.tableCount = event.length;
        }
        if (this.selectedTabIndex === 3 && event.type === 2) {
            this.tableCount = event.length;
        }
        if (this.selectedTabIndex === 4 && event.type === 3) {
            this.tableCount = event.length;
        }
    }

    refreshAll() {
        this.tabs_1.getMappingData();
        this.tabs_2.getTabelData();
        this.tabs_3.getTabelData();
        this.tabs_4.getTabelData();
        this.tabs_5.getTabelData();
    }

    selectFile() {
        const modal = this.modal.confirm({
            nzTitle: '提示',
            nzContent: '导入客户标准化映射文件将覆盖原有映射关系，请确定是否导入？',
            nzOkText: '确认',
            nzOkType: 'primary',
            nzOkDanger: true,
            nzMaskClosable: false,
            nzOnOk: () => {
                return 'yes'
            },
            nzCancelText: '取消',
            nzOnCancel: () => {
                return 'no'
            },
        })
        modal.afterClose.subscribe((res: any) => {
            if (res === 'yes') {
                const inputElement = document.getElementById('upload_file');
                if (inputElement) inputElement.click();
            }
        })
    }

    fileChange($event: any) {
        const fileList: FileList = $event.target.files;
        this.currentSelectedFile = fileList[0];
        const formData = new FormData();
        formData.append('file', this.currentSelectedFile);
        this.service.importCustomerStandardizationFile(formData).subscribe((res: any) => {
            this.messageService.success('上传成功！');
            this.refreshAll();
        })
    }

    exportStandardizationFile() {
        if (this.exporting) return;
        this.exporting = this.messageService.loading('正在进行导出，请勿重复执行导出操作...', { nzDuration: 0 }).messageId;
        this.service.exportCustomerStandardizationFile().subscribe((res: any) => {
            this.messageService.success('导出成功！');
            const a = document.createElement('a');
            a.href = URL.createObjectURL(res);
            a.download = "客户标准化映射文件.xlsx";
            a.click();
            this.messageService.remove(this.exporting);
            this.exporting = null;
        })
    }

    exportINIMappingFile() {
        if (this.exporting) return;
        this.exporting = this.messageService.loading('正在进行导出，请勿重复执行导出操作...', { nzDuration: 0 }).messageId;
        this.service.exportInIMappingFile().subscribe((res: any) => {
            this.messageService.success('导出成功！');
            const a = document.createElement('a');
            a.href = URL.createObjectURL(res);
            a.download = "SignalIdMap.ini";
            a.click();
            this.messageService.remove(this.exporting);
            this.exporting = null;
        })
    }

    applyStandardization() {
        const modal = this.modal.confirm({
            nzTitle: '提示',
            nzContent: '应用标准化需要修改模板信息，请确认标准化映射关系以及配置正确，否则将产生错误告警。是否继续应用标准化？',
            nzOkText: '确认',
            nzOkType: 'primary',
            nzOkDanger: true,
            nzMaskClosable: false,
            nzOnOk: () => {
                return 'yes'
            },
            nzCancelText: '取消',
            nzOnCancel: () => {
                return 'no'
            },
        })
        modal.afterClose.subscribe((res: any) => {
            if (res === 'yes') {
                const modal = this.modal.create<StandardizationSelectorComponent>({
                    nzTitle: "选择应用标准化模板（不选择则默认为全选）",
                    nzContent: StandardizationSelectorComponent,
                    nzWidth: 1060,
                    nzFooter: [
                        {
                            label: '取消',
                            type: 'default',
                            onClick: componentInstance => componentInstance!.cancel()
                        },
                        {
                            label: '确认',
                            type: 'primary',
                            onClick: componentInstance => componentInstance!.confirm()
                        },
                    ],
                    nzMaskClosable: false
                })
                const instance = modal.getContentComponent();
                instance.type = this.selectedTabIndex ? this.selectedTabIndex - 1 : 1;
                modal.afterClose.subscribe((res: any) => {
                    if (res) {
                        const params = {
                            //  类型 1 信号；2 事件；3 控制
                            type: this.selectedTabIndex ? this.selectedTabIndex - 1 : 1,
                            equipmentTemplateIds: res,
                        }
                        this.exporting = this.messageService.loading('正在应用更改，请勿重复执行操作...', { nzDuration: 0 }).messageId;
                        this.service.applyStandardization(params).then(res => {
                            this.messageService.success('更新成功！');
                            this.messageService.remove(this.exporting);
                            this.exporting = null;
                        })
                    }
                })
            }
        })
    }

    reduceStandardization() {
        const modal = this.modal.confirm({
            nzTitle: '提示',
            nzContent: '还原将覆盖现有模板信息，请确认是否进行还原？',
            nzOkText: '确认',
            nzOkType: 'primary',
            nzOkDanger: true,
            nzMaskClosable: false,
            nzOnOk: () => {
                return 'yes'
            },
            nzCancelText: '取消',
            nzOnCancel: () => {
                return 'no'
            },
        })
        modal.afterClose.subscribe((res: any) => {
            if (res === 'yes') {
                const params = {
                    //  类型 1 信号；2 事件；3 控制
                    type: this.selectedTabIndex ? this.selectedTabIndex - 1 : 1,
                }
                this.exporting = this.messageService.loading('正在应用更改，请勿重复执行操作...', { nzDuration: 0 }).messageId;
                this.service.reduceStandardization(params).then(res => {
                    this.messageService.success('更新成功！');
                    this.messageService.remove(this.exporting);
                    this.exporting = null;
                })
            }
        })
    }

    viewApplicationCheck() {
        const modal = this.modal.create<TemplateApplicationCheckComponent>({
            nzTitle: "模板应用标准化检查",
            nzContent: TemplateApplicationCheckComponent,
            nzWidth: 1060,
            nzFooter: [
                {
                    label: '导出EXCEL',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.export()
                },
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.confirm()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
    }

    viewTemplateMappingCheck() {
        const modal = this.modal.create<TemplateMappingCheckComponent>({
            nzTitle: "模板映射标准化检查",
            nzContent: TemplateMappingCheckComponent,
            nzWidth: 1060,
            nzFooter: [
                {
                    label: '导出EXCEL',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.export()
                },
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.confirm()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
    }

    viewStandardizationCompare() {
        const modal = this.modal.create<StandardizationCompareComponent>({
            nzTitle: "应用标准化前后内容比较",
            nzContent: StandardizationCompareComponent,
            nzWidth: 1200,
            nzFooter: [
                {
                    label: '导出EXCEL',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.export()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
    }
}