import { Component, ElementRef, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { BasicTypeSelectorComponent } from '../basic-type-selector/basic-type-selector.component';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';

@Component({
  selector: 'app-event-standardization-mapping',
  templateUrl: './event-standardization-mapping.component.html',
  styleUrls: ['./event-standardization-mapping.component.less']
})

export class EventStandardizationMappingComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @Output() update: EventEmitter<any> = new EventEmitter<any>();

    editableTip = EditableTip.hover;
    source: any;
    filterSource: any;
    checkedIds: any = [];

    searchText: any = {
        equipmentLogicClass: null,
        eventLogicClass: null,
        eventStandardName: null,
        meanings: null,
        compareValue: null,
        type: [],
        baseTypeName: null,
        standardDicId: null,
        baseTypeId: null,
    }
    searchTimer: any;

    eventCols: any = [
        {
            field: 'equipmentLogicClass',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.eqTpye'),
        },
        {
            field: 'eventLogicClass',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.eventType'),
        },
        {
            field: 'eventStandardName',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.eventName'),
        },
        {
            field: 'meanings',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.eventMeaning'),
        },
        {
            field: 'compareValue',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.eventThreshold'),
        },
        {
            field: 'type',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.stationType'),
        },
        {
            field: 'baseTypeName',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.baseName'),
        },
        {
            field: 'standardDicId',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.id'),
        },
        {
            field: 'baseTypeId',
            width: '80px',
            title: this.translate.instant('customerStandardization.eventMapping.baseId'),
        },
    ];

    modelList: any = [];

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '675px';

    // keepalive后记住滚动条位置
    keepScrollTop?: number;

    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []

    rowItemCurrent: any;
    fieldCurrent: any;
    modalBefore: any;
    isConfirming: boolean = false;
    isEditing: boolean = false;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
        private nzContextMenuService: NzContextMenuService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                this.setTabelScroll(event);
            }
        })

        this.getStationType();
    }

    setTabelScroll(event?: any) {
        // 滚动条回显
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
            if(event && event.url == '/pages/customer-standardization') {
                this.getTabelData();
            }
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
        } catch { /* 呃 好饱 */ }
    }

    getStationType() {
        this.service.getStationBaseType().then(res => {
            this.modelList = res;
            this.getTabelData();
        })
    }

    getTabelData() {
        this.isUpdating = true;
        this.service.getEventStandardizationMapping().then(res => {
            this.isUpdating = false;
            let result = res;
            result.forEach((item: any) => {
                item['uniqueId'] = item['standardDicId'].toString() + '-' + item['id'].toString();
            })
            this.source = result;
            this.search();
            this.tabelHeightString = document.documentElement.scrollHeight - 285 + 'px';
        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            for(const field in this.searchText) {
                if(this.searchText[field] && field !== 'type') {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field] ? item[field].toString().includes(this.searchText[field]) : false;
                    })
                } else if (this.searchText.type && this.searchText.type.length > 0) {
                    filterList = filterList.filter((item: any) => {
                        let flag = false;
                        this.searchText.type.forEach((obj: any) => {
                            if(obj === item.type) {
                                flag = true;
                            }
                        })
                        return flag;
                    })
                }
            }
            this.filterSource = filterList;
            const obj = {
                type: 2,
                length: this.source.length
            }
            this.update.emit(obj);
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: string): void {
        this.checkedIds = id;
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.eventCols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        this.nzContextMenuService.create($event, menu);
    }

    editBasicClass(rowItem: any) {
        const modal = this.modal.create<BasicTypeSelectorComponent>({
            nzTitle: rowItem['equipmentLogicClass'] + '>>' + rowItem['eventLogicClass'] + '>>' + (rowItem['eventStandardName'] ? rowItem['eventStandardName'] : ''),
            nzContent: BasicTypeSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '清除选择',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.clearSelected()
                },
                {
                    label: '添加基类事件',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.addNewBasic()
                },
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.type = 'event';
        instance.stationType = rowItem.id;
        instance.standardDicId = rowItem.standardDicId;
        instance.selectedNodes = rowItem.baseTypeId ? rowItem.baseTypeId.split(',') : [];
        modal.afterClose.subscribe((res: any) => {
            if(res && res.type === 'clear') {
                const params = {
                    standardDicId: rowItem.standardDicId,
                    equipmentLogicClass: rowItem.equipmentLogicClass,
                    eventLogicClass: rowItem.eventLogicClass,
                    eventStandardName: rowItem.eventStandardName,
                    meanings: rowItem.meanings,
                    compareValue: rowItem.compareValue,
                    id: rowItem.id,
                    type: rowItem.type,
                    baseTypeId: null,
                    isDirty: rowItem.isDirty
                }
                this.service.updateEventStandardizationMapping(params).then(res => {
                    this.messageService.success('更新成功！');
                    this.getTabelData();
                })
            } else if (res && res.type === 'update') {
                const params = {
                    standardDicId: rowItem.standardDicId,
                    equipmentLogicClass: rowItem.equipmentLogicClass,
                    eventLogicClass: rowItem.eventLogicClass,
                    eventStandardName: rowItem.eventStandardName,
                    meanings: rowItem.meanings,
                    compareValue: rowItem.compareValue,
                    id: rowItem.id,
                    type: rowItem.type,
                    baseTypeId: res.data.join(','),
                    isDirty: rowItem.isDirty
                }
                this.service.updateEventStandardizationMapping(params).then(res => {
                    this.messageService.success('更新成功！');
                    this.getTabelData();
                })
            }
        })
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: "选择局站类型",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = this.searchText.type;
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                this.searchText.type = res;
            }
            this.search();
        })
    }
}