import { Component, ElementRef, Injector, OnInit, ViewChild, inject } from '@angular/core'
import { GenericComponent } from '@core/components/basic/generic.component'
import { CustomerStandardizationService } from '../customer-standardization.service';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { SignalStandardizationCompareComponent } from './signal-standardization-compare/signal-standardization-compare.component';
import { EventStandardizationCompareComponent } from './event-standardization-compare/event-standardization-compare.component';
import { ControlStandardizationCompareComponent } from './control-standardization-compare/control-standardization-compare.component';
import * as _ from 'lodash';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-standardization-compare',
  templateUrl: './standardization-compare.component.html',
  styleUrls: ['./standardization-compare.component.less']
})

export class StandardizationCompareComponent extends GenericComponent implements OnInit {

    #modal = inject(NzModalRef);
    @ViewChild('tabs_1') private tabs_1!: SignalStandardizationCompareComponent;
    @ViewChild('tabs_2') private tabs_2!: EventStandardizationCompareComponent;
    @ViewChild('tabs_3') private tabs_3!: ControlStandardizationCompareComponent;
    
    selectedTabIndex?: number = 0;
    exporting: any;

    tableCount: number = 0;

    signalColumns = [
        'EquipmentTemplateId_B',
        'EquipmentTemplateName_B',
        'SignalId_B',
        'SignalName_B',
        'StoreInterval_B',
        'AbsValueThreshold_B',
        'PercentThreshold_B',
        'EquipmentTemplateId_A',
        'EquipmentTemplateName_A',
        'SignalId_A',
        'SignalName_A',
        'StoreInterval_A',
        'AbsValueThreshold_A',
        'PercentThreshold_A',
    ]
    eventColumns = [
        'EquipmentTemplateId_B',
        'EquipmentTemplateName_B',
        'EventId_B',
        'EventName_B',
        'EventSeverity_B',
        'StartCompareValue_B',
        'StartDelay_B',
        'StandardName_B',
        'Meanings_B',
        'EquipmentTemplateId_A',
        'EquipmentTemplateName_A',
        'EventId_A',
        'EventName_A',
        'EventSeverity_A',
        'StartCompareValue_A',
        'StartDelay_A',
        'StandardName_A',
        'Meanings_A',
    ]
    controlColumns = [
        'EquipmentTemplateId_B',
        'EquipmentTemplateName_B',
        'ControlId_B',
        'ControlName_B',
        'EquipmentTemplateId_A',
        'EquipmentTemplateName_A',
        'ControlId_A',
        'ControlName_A'
    ]

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {

    }

    public tabChange(index: number): void {
        this.selectedTabIndex = index;
        if(index === 0) {
            this.tabs_1.setTabelScroll();
            this.tabs_1.search_1();
            this.tabs_1.search_2();
        }
        if(index === 1) {
            this.tabs_2.setTabelScroll();
            this.tabs_2.search_1();
            this.tabs_2.search_2();
        }
        if(index === 2) {
            this.tabs_3.setTabelScroll();
            this.tabs_3.search_1();
            this.tabs_3.search_2();
        }
    }

    updateCount(event: any) {
        if(this.selectedTabIndex === 0 && event.type === 1) {
            this.tableCount = event.length;
        }
        if(this.selectedTabIndex === 1 && event.type === 2) {
            this.tableCount = event.length;
        }
        if(this.selectedTabIndex === 2 && event.type === 3) {
            this.tableCount = event.length;
        }
    }

    getSignalExcelData(res: any) {
        const xlsTemplateJson = [];
        xlsTemplateJson.push(this.signalColumns)
        if(res && res.signalCompares && res.signalCompares.length > 0) {
            res.signalCompares.forEach((item: any) => {
                const obj = {
                    EquipmentTemplateId_B: item.equipmentTemplateIdBefore,
                    EquipmentTemplateName_B: item.equipmentTemplateNameBefore,
                    SignalId_B: item.signalIdBefore,
                    SignalName_B: item.signalNameBefore,
                    StoreInterval_B: item.storeIntervalBefore,
                    AbsValueThreshold_B: item.absValueThresholdBefore,
                    PercentThreshold_B: item.percentThresholdBefore,
                    EquipmentTemplateId_A: item.equipmentTemplateIdAfter,
                    EquipmentTemplateName_A: item.equipmentTemplateNameAfter,
                    SignalId_A: item.signalIdAfter,
                    SignalName_A: item.signalNameAfter,
                    StoreInterval_A: item.storeIntervalAfter,
                    AbsValueThreshold_A: item.absValueThresholdAfter,
                    PercentThreshold_A: item.percentThresholdAfter,
                };
                xlsTemplateJson.push(_.values(obj));
            })
        }

        return xlsTemplateJson;
    }

    getEventExcelData(res: any) {
        const xlsTemplateJson = [];
        xlsTemplateJson.push(this.eventColumns)
        if(res && res.eventCompares && res.eventCompares.length > 0) {
            res.eventCompares.forEach((item: any) => {
                const obj = {
                    EquipmentTemplateId_B: item.equipmentTemplateIdBefore,
                    EquipmentTemplateName_B: item.equipmentTemplateNameBefore,
                    EventId_B: item.eventIdBefore,
                    EventName_B: item.eventNameBefore,
                    EventSeverity_B: item.eventSeverityNameBefore,
                    StartCompareValue_B: item.startCompareValueBefore,
                    StartDelay_B: item.startDelayBefore,
                    StandardName_B: item.standardNameBefore,
                    Meanings_B: item.meaningsBefore,
                    EquipmentTemplateId_A: item.equipmentTemplateIdAfter,
                    EquipmentTemplateName_A: item.equipmentTemplateNameAfter,
                    EventId_A: item.eventIdAfter,
                    EventName_A: item.eventNameAfter,
                    EventSeverity_A: item.eventSeverityNameAfter,
                    StartCompareValue_A: item.startCompareValueAfter,
                    StartDelay_A: item.startDelayAfter,
                    StandardName_A: item.standardNameAfter,
                    Meanings_A: item.meaningsAfter,
                };
                xlsTemplateJson.push(_.values(obj));
            })
        }

        return xlsTemplateJson;
    }

    getControlExcelData(res: any) {
        const xlsTemplateJson = [];
        xlsTemplateJson.push(this.controlColumns)
        if(res && res.controlCompares && res.controlCompares.length > 0) {
            res.controlCompares.forEach((item: any) => {
                const obj = {
                    EquipmentTemplateId_B: item.equipmentTemplateIdBefore,
                    EquipmentTemplateName_B: item.equipmentTemplateNameBefore,
                    ControlId_B: item.controlIdBefore,
                    ControlName_B: item.controlNameBefore,
                    EquipmentTemplateId_A: item.equipmentTemplateIdAfter,
                    EquipmentTemplateName_A: item.equipmentTemplateNameAfter,
                    ControlId_A: item.controlIdAfter,
                    ControlName_A: item.controlNameAfter,
                };
                xlsTemplateJson.push(_.values(obj));
            })
        }

        return xlsTemplateJson;
    }

    export() {
        if(this.exporting) return;
        this.exporting = this.messageService.loading('正在进行导出，请勿重复执行导出操作...', { nzDuration: 0 }).messageId;
        this.service.getStandardizationCompare(0).then(res => {
            const xlsTemplateJson1 = this.getSignalExcelData(res);
            const ws1: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson1);
            const cols1: any = [];
            xlsTemplateJson1[0].forEach(item => {
                cols1.push({ wch: 20 });
            })
            ws1["!cols"] = cols1;

            const xlsTemplateJson2 = this.getEventExcelData(res);
            const ws2: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson2);
            const cols2: any = [];
            xlsTemplateJson2[0].forEach(item => {
                cols2.push({ wch: 20 });
            })
            ws2["!cols"] = cols2;

            const xlsTemplateJson3 = this.getControlExcelData(res);
            const ws3: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson3);
            const cols3: any = [];
            xlsTemplateJson3[0].forEach(item => {
                cols3.push({ wch: 20 });
            })
            ws3["!cols"] = cols3;

            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws1, 'SignalCompare');
            XLSX.utils.book_append_sheet(wb, ws2, 'EventCompare');
            XLSX.utils.book_append_sheet(wb, ws3, 'ControlCompare');
            XLSX.writeFile(wb, '应用标准化前后对比文件.xlsx');
            
            this.messageService.remove(this.exporting);
            this.exporting = null;
        })
    }

    confirm() {
        this.#modal.destroy();
    }

}