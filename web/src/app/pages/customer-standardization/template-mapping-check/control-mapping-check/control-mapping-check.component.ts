import { Component, ElementRef, EventEmitter, Injector, Input, OnInit, Output, ViewChild } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CustomerStandardizationService } from '../../customer-standardization.service';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';

@Component({
  selector: 'app-control-mapping-check',
  templateUrl: './control-mapping-check.component.html',
  styleUrls: ['./control-mapping-check.component.less']
})

export class ControlMappingCheckComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @Output() update: EventEmitter<any> = new EventEmitter<any>();
    @Input('equipmentCategory')
    public set equipmentCategory(value: any) {
        this._equipmentCategory = value;
        if (this._equipmentCategory) {
            this.getTabelData();
        }
    }

    _equipmentCategory: any;
    editableTip = EditableTip.hover;
    source: any;
    filterSource: any;
    checkedIds: any = [];

    searchText: any = {
        standardedName: null,
        stationId: null,
        stationName: null,
        stationCategoryName: [],
        equipmentId: null,
        equipmentName: null,
        equipmentCategoryName: null,
        equipmentTemplateId: null,
        equipmentTemplateName: null,
        controlId: null,
        controlName: null,
        baseTypeId: null,
        baseTypeName: null,
        standardDicId: null,
        controlStandardName: null,
    }
    searchTimer: any;

    cols: any = [
        {
            field: 'standardedName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.applicationStandardization'),
        },
        {
            field: 'stationId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.stationID'),
        },
        {
            field: 'stationName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.stationName'),
        },
        {
            field: 'stationCategoryName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.stationType'),
        },
        {
            field: 'equipmentId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.eqId'),
        },
        {
            field: 'equipmentName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.eqName'),
        },
        {
            field: 'equipmentCategoryName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.eqCategory'),
        },
        {
            field: 'equipmentTemplateId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.templateId'),
        },
        {
            field: 'equipmentTemplateName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.templateName'),
        },
        {
            field: 'controlId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.controlId'),
        },
        {
            field: 'controlName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.controlName'),
        },
        {
            field: 'baseTypeId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.baseId'),
        },
        {
            field: 'baseTypeName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.baseName'),
        },
        {
            field: 'standardDicId',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.standardId'),
        },
        {
            field: 'controlStandardName',
            width: '150px',
            title: this.translate.instant('customerStandardization.controlCheck.standardName'),
        },
    ];

    modelList: any = [];

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '465px';

    // keepalive后记住滚动条位置
    keepScrollTop?: number;

    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []

    rowItemCurrent: any;
    fieldCurrent: any;
    modalBefore: any;
    isConfirming: boolean = false;
    isEditing: boolean = false;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.getStationType();
    }

    setTabelScroll() {
        // 滚动条回显
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
        } catch { /* 呃 好饱 */ }
    }

    getStationType() {
        this.service.getStationBaseType().then(res => {
            this.modelList = res;
        })
    }

    getTabelData() {
        this.isUpdating = true;
        this.service.getTempMappingCheck(3, this._equipmentCategory).then(res => {
            this.isUpdating = false;
            let result = res.controlChecks;
            result.forEach((item: any) => {
                item['standardedName'] = item['standarded'] ? '是' : '否';
                item['uniqueId'] = item['equipmentTemplateId'].toString() + '-' + item['equipmentId'].toString() + '-' + item['controlId'].toString();
            })
            this.source = result;
            this.search();
        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            for(const field in this.searchText) {
                if(this.searchText[field] && field !== 'stationCategoryName') {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.searchText[field]) : false;
                    })
                } else if (this.searchText.stationCategoryName && this.searchText.stationCategoryName.length > 0) {
                    filterList = filterList.filter((item: any) => {
                        let flag = false;
                        this.searchText.stationCategoryName.forEach((obj: any) => {
                            if(obj === item.stationCategoryName) {
                                flag = true;
                            }
                        })
                        return flag;
                    })
                }
            }
            this.filterSource = filterList;
            const obj = {
                type: 3,
                length: this.source.length
            }
            this.update.emit(obj);
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: string): void {
        this.checkedIds = id;
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.cols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: "选择局站类型",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = this.searchText.stationCategoryName;
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                this.searchText.stationCategoryName = res;
            }
            this.search();
        })
    }
}