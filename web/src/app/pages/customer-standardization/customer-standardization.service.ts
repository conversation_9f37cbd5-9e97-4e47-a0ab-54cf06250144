import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'
import { RestfulResponse } from '@core/types/restful'
import { ApiService } from '@services/api-service'
import { Observable } from 'rxjs'
@Injectable()
export class CustomerStandardizationService extends RestfulService {

  public constructor(private apiSerivce: ApiService, private http: HttpClient) {
    super(http)
  }

  public async getStandardType() {
    const res = await this.get<any>(`api/config/standardtype/current`)
    return res.data
  }

  public async getStationBaseType() {
    const res = await this.get<any>(`api/config/stationbasetype`)
    return res.data
  }

  public async getBaseStationMappingById(id: any) {
    const res = await this.get<any>(`api/config/stationbasetype/basestationmap?id=${id}`)
    return res.data
  }

  updateStationTypeMapping(item: any) {
    return this.http.post(`api/config/stationbasetype/basestationmap`, item);
  }

  public async getTemplateStationMapping() {
    const res = await this.get<any>(`api/config/stationbasetype/equipmenttemplate`)
    return res.data
  }

  updateTemplateStationMapping(item: any) {
    return this.http.put(`api/config/equipmenttemplate`, item);
  }

  public async getSignalStandardizationMapping() {
    const res = await this.get<any>(`api/config/stationbasetype/signal`)
    return res.data
  }

  public async updateSignalStandardizationMapping(params: any) {
    const res = await this.post<any>(`api/config/stationbasetype/signalbasemap`, params);
    return res.data
  }

  public async getSignalBasicTree(): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/signalbasedic/tree?isSystem=true`)
    return res.data
  }

  public async getEventStandardizationMapping() {
    const res = await this.get<any>(`api/config/stationbasetype/event`)
    return res.data
  }

  public async updateEventStandardizationMapping(params: any) {
    const res = await this.post<any>(`api/config/stationbasetype/eventbasemap`, params);
    return res.data
  }

  public async getEventBasicTree(): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/eventbasedic/tree?isSystem=true`)
    return res.data
  }

  public async getControlStandardizationMapping() {
    const res = await this.get<any>(`api/config/stationbasetype/control`)
    return res.data
  }

  public async updateControlStandardizationMapping(params: any) {
    const res = await this.post<any>(`api/config/stationbasetype/controlbasemap`, params);
    return res.data
  }

  public async getControlBasicTree(): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/commandbasedic/tree?isSystem=true`)
    return res.data
  }

  //type        1 信号；2 事件；3 控制
  public async checkDuplicateId(stationTypeId: any, baseTypeId: any, type: any, stId: any): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/stationbasetype/basemapwarn?stationBaseType=${stationTypeId}&baseTypeId=${baseTypeId}&baseType=${type}&standardDicId=${stId}`)
    return res.data
  }

  public async applyStandardization(params: any): Promise<any> {
    const res = await this.apiSerivce.post<any>(`api/config/stationbasetype/applystandard`, params)
    return res.data
  }

  public async reduceStandardization(params: any): Promise<any> {
    const res = await this.apiSerivce.post<any>(`api/config/stationbasetype/restorestandard?type=${params.type}`, {})
    return res.data
  }

  //type        1 信号；2 事件；3 控制
  public async getStandardizationCompare(type: any): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/stationbasetype/standardcompare?type=${type}`)
    return res.data
  }

  //type        1 信号；2 事件；3 控制
  public async getTempStandardizationCheck(type: any): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/stationbasetype/standardapplycheck?type=${type}`)
    return res.data
  }

  exportTempStandardizationCheck(): Observable<any> {
    return this.http.get(`api/config/stationbasetype/standardapplycheck/export`, { responseType: 'blob' });
  }

  getDeviceCategoryList(): Promise<RestfulResponse<any>> {
    return this.apiSerivce.get('api/config/dataitems?entryId=7');
  }

  getStationTypeList(): Promise<RestfulResponse<any>> {
    return this.apiSerivce.get('api/config/dataitems?entryId=71');
  }

  //type        1 信号；2 事件；3 控制
  public async getTempMappingCheck(type: any, id: any): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/stationbasetype/standardmappingcheck?type=${type}&equipmentCategory=${id}`)
    return res.data
  }

  importCustomerStandardizationFile(file: any): Observable<any> {
    return this.http.post(`api/config/stationbasetype/customstandardmapping/import`, file);
  }

  exportCustomerStandardizationFile(): Observable<any> {
    return this.http.get(`api/config/stationbasetype/customstandardmapping/export`, { responseType: 'blob' });
  }

  exportInIMappingFile(): Observable<any> {
    return this.http.get(`api/config/stationbasetype/exportini`, { responseType: 'blob' });
  }

  public async getStandardizationTemplateList(): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/equipmenttemplate/standard`)
    return res.data
  }

  public async getTempEquipmentType(): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/dataitems?entryId=7`)
    return res.data
  }

  //type        1 信号；2 事件；3 控制
  public async getRewriteList(type: any): Promise<any> {
    const res = await this.apiSerivce.get<any>(`api/config/writebackentry?entryCategory=${type}`)
    return res.data
  }

  public async updateRewriteList(params: any) {
    const res = await this.apiSerivce.put<any>(`api/config/writebackentry`, params)
    return res.data
  }

  /**
  * 获取当前客户类型
  * @returns
  */
  public async getCurrentType(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standardtype/current`)
    return res.data
  }

  /**
  * 获取客户类型
  * @returns
  */
  public async getCustomerType(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standarddicexport/customertype`)
    return res.data
  }

  /**
  * 获取信号标准化字典表
  * @returns
  */
  public async getSignalList(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standarddicsig`)
    return res.data
  }

  /**
  * 获取局站类型
  * @returns
  */
  public async getStationCategoryList(): Promise<[]> {
    const res = await this.get<[]>(`api/config/stationbasetype`)
    return res.data
  }

  /**
  * 获取设备逻辑类型
  * 设备类型 1
  * 信号量类型 2
  * 告警逻辑分类 3
  * @returns
  */
  public async getLogicclassById(id: number): Promise<[]> {
    const res = await this.get<[]>(`api/config/logicclassentry/logicclass?entryCategory=${id}`)
    return res.data
  }

  /**
  * 新增逻辑类型
  * @returns
  */
  public async addLogicClassEntry(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/logicclassentry`, data)
    return res.data
  }

  /**
  * 新增信号字典项
  * @returns
  */
  public async addSignalItem(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/standarddicsig`, data)
    return res.data
  }

  /**
  * 修改信号字典项
  * @returns
  */
  public async updateSignalItem(data: any): Promise<[]> {
    const res = await this.put<[]>(`api/config/standarddicsig`, data)
    return res.data
  }

  /**
  * 删除信号字典项
  * @returns
  */
  public async deleteSignalItem(id: any): Promise<[]> {
    const res = await this.delete<[]>(`api/config/standarddicsig?standardDicId=${id}`, {} as any)
    return res.data
  }

  /**
   * 获取告警标准化字典表
   * @returns
   */
  public async getEventList(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standarddicevent`)
    return res.data
  }

  /**
   * 获取告警等级
   * @returns
   */
  public async getEventSeverityList(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standarddicevent`)
    return res.data
  }

  /**
  * 新增告警字典项
  * @returns
  */
  public async addEventItem(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/standarddicevent`, data)
    return res.data
  }

  /**
  * 修改告警字典项
  * @returns
  */
  public async updateEventItem(data: any): Promise<[]> {
    const res = await this.put<[]>(`api/config/standarddicevent`, data)
    return res.data
  }

  /**
  * 删除告警字典项
  * @returns
  */
  public async deleteEventItem(id: any): Promise<[]> {
    const res = await this.delete<[]>(`api/config/standarddicevent?standardDicId=${id}`, {} as any)
    return res.data
  }

  /**
  * 获取控制标准化字典表
  * @returns
  */
  public async getControlList(): Promise<[]> {
    const res = await this.get<[]>(`api/config/standarddiccontrol`)
    return res.data
  }

  /**
  * 新增控制字典项
  * @returns
  */
  public async addControlItem(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/standarddiccontrol`, data)
    return res.data
  }

  /**
  * 修改控制字典项
  * @returns
  */
  public async updateControlItem(data: any): Promise<[]> {
    const res = await this.put<[]>(`api/config/standarddiccontrol`, data)
    return res.data
  }

  /**
  * 删除控制字典项
  * @returns
  */
  public async deleteControlItem(id: any): Promise<[]> {
    const res = await this.delete<[]>(`api/config/standarddiccontrol?standardDicId=${id}`, {} as any)
    return res.data
  }

  /**
  * 批量信号绑定基类
  * @returns
  */
  public async batchSignalBasemap(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/signalbasemap/batch`, data)
    return res.data
  }

  /**
  * 批量告警绑定基类
  * @returns
  */
  public async batchEventBasemap(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/eventbasemap/batch`, data)
    return res.data
  }

  /**
  * 批量控制绑定基类
  * @returns
  */
  public async batchCommandBasemap(data: any): Promise<[]> {
    const res = await this.post<[]>(`api/config/commandbasemap/batch`, data)
    return res.data
  }

  /**
 * 导出数据库脚本
 * @returns
 */
  exportSql(): Observable<any> {
    return this.http.get(`api/config/standarddicexport/sql`, { responseType: 'blob' })
  }

  /**
  * 导出数据库脚本
  * @returns
  */
  exportXML(id?: any): Observable<any> {
    if(id) {
      return this.http.get(`api/config/standarddicexport/xml?customerType=${id}`, { responseType: 'blob' })
    } else {
      return this.http.get(`api/config/standarddicexport/xml?customerType=`, { responseType: 'blob' })
    }
  }

  setData(type: any, data: any) {
    (this as any)[type] = data;
  }

  getData(type: any) {
    return (this as any)[type]
  }

}
