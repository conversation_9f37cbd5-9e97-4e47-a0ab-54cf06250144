<div class="tsm_container">
    <d-data-table
        #myTable
        [dataSource]="filterSource"
        [scrollable]="true" 
        [virtualScroll]="true"
        [tableHeight]="tabelHeightString"
        [fixHeader]="true"
        [tableWidthConfig]="cols" 
        [tableOverflowType]="'overlay'"
        [resizeable]="true"
        [onlyOneColumnSort]="true"
        [borderType]="'borderless'"
        [striped]="false"
        dLoading 
        [loadingStyle]="'infinity'"
        [loading]="isUpdating"
        (tableScrollEvent)="tableScrollEvent($event)"
    >
        <thead dTableHead>
            <tr dTableRow>
                <ng-container *ngFor="let col of cols">
                    <th dHeadCell *ngIf="col['field'] === 'checked'">
                        <div class="devui_title">{{ col.title }}</div>
                        <label class="checkbox-inline custom-checkbox nowrap">
                            <input type="checkbox"
                            [ngModel]="checkAll"
                            (change)="onAllChecked(!checkAll)"
                        >
                            <span></span>
                        </label>
                    </th>
                    <th dHeadCell [resizeEnabled]="true"
                        (resizeEndEvent)="onResize($event, col.field)"
                        [sortable]="false"
                        [showSortIcon]="false"
                        *ngIf="col['field'] === 'entryName' "
                    >
                        <div class="devui_title">{{ col.title }}</div>
                        <div class="devui_searcher">
                            <nz-input-group [nzSuffix]="suffixIconSearch">
                                <input
                                    type="text"
                                    class="devui-form-control"
                                    nz-input
                                    [(ngModel)]="searchText.entryName"
                                    placeholder="请输入关键字..." 
                                    (ngModelChange)="search()"/>
                            </nz-input-group>
                            <ng-template #suffixIconSearch>
                                <span nz-icon nzType="search"></span>
                            </ng-template>
                        </div>
                    </th>
                </ng-container>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow
                [ngClass]="{'devui-table-row-selected': checkedIds.includes(rowItem.entryId)}"
            >
                <ng-container *ngFor="let col of cols">
                    <td dTableCell *ngIf="col['field'] === 'checked'">
                        <label class="checkbox-inline custom-checkbox nowrap">
                            <input type="checkbox"
                                [ngModel]="checkedIds.includes(rowItem.entryId)"
                                (change)="onItemChecked(rowItem.entryId, !checkedIds.includes(rowItem.entryId))"
                            >
                            <span></span>
                        </label>
                    </td>
                    <td dTableCell 
                        class="span-text"
                        [title]="rowItem[col.field]"
                        *ngIf="col['field'] !== 'checked'"
                    >
                        {{ rowItem[col.field] }}
                    </td>
                </ng-container>
            </tr>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div style="text-align: center; margin-top: 20px">{{'common.nodata' | translate}}</div>
        </ng-template>
    </d-data-table>
</div>