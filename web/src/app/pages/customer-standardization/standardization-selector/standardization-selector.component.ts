import { Component, ElementRef, Injector, Input, OnInit, ViewChild, inject } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { RewriteSelectorComponent } from './rewrite-selector/rewrite-selector.component';

@Component({
  selector: 'app-standardization-selector',
  templateUrl: './standardization-selector.component.html',
  styleUrls: ['./standardization-selector.component.less']
})

export class StandardizationSelectorComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @Input() type: any;
    #modal = inject(NzModalRef);

    editableTip = EditableTip.hover;
    source: any;
    filterSource: any;
    checkedIds: any = [];
    checkAll: boolean = false;

    searchText = {
        checked: null,
        equipmentTemplateId: null,
        equipmentTemplateName: null,
        stationBaseType: [],
        equipmentTemplateType: [],
    }
    searchTimer: any;

    cols: any = [
        {
            field: 'checked',
            width: '60px',
            title: '',
        },
        {
            field: 'equipmentTemplateId',
            width: '150px',
            title: this.translate.instant('customerStandardization.stationMapping.templateId'),
        },
        {
            field: 'equipmentTemplateName',
            width: '150px',
            title: this.translate.instant('customerStandardization.stationMapping.templateName'),
        },
        {
            field: 'stationBaseType',
            width: '150px',
            title: this.translate.instant('customerStandardization.stationMapping.stationTempType'),
        },
        {
            field: 'equipmentTemplateType',
            width: '150px',
            title: this.translate.instant('customerStandardization.stationMapping.tempType'),
        },
    ];

    stationTempTypeList: any = [];
    tempTypeList: any = [];

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '515px';

    // keepalive后记住滚动条位置
    keepScrollTop?: number;

    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []

    rowItemCurrent: any;
    fieldCurrent: any;
    modalBefore: any;
    isConfirming: boolean = false;
    isEditing: boolean = false;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.getModelList();
    }

    setTabelScroll() {
        // 滚动条回显
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
        } catch { /* 呃 好饱 */ }
    }

    getModelList() {
        Promise.all([this.service.getStationBaseType(), this.service.getTempEquipmentType()]).then(res => {
            this.stationTempTypeList = res[0];
            this.tempTypeList = res[1];
            this.getTabelData();
        })
    }

    getTabelData() {
        this.isUpdating = true;
        this.service.getStandardizationTemplateList().then(res => {
            this.isUpdating = false;
            let result = res;
            // result.forEach((item: any) => {
            //     item['checked'] = true;
            // })
            this.source = result;
            this.search();
        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            if(this.searchText.equipmentTemplateId) {
                filterList = filterList.filter((item: any) => {
                    return item.equipmentTemplateId ? item.equipmentTemplateId.toString().includes(this.searchText.equipmentTemplateId) : false;
                })
            }
            if(this.searchText.equipmentTemplateName) {
                filterList = filterList.filter((item: any) => {
                    return item.equipmentTemplateName ? item.equipmentTemplateName.includes(this.searchText.equipmentTemplateName) : false;
                })
            }
            if(this.searchText.stationBaseType && this.searchText.stationBaseType.length > 0) {
                filterList = filterList.filter((item: any) => {
                    let flag = false;
                    this.searchText.stationBaseType.forEach(obj => {
                        if(obj === item.stationBaseType) {
                            flag = true;
                        }
                    })
                    return flag;
                })
            }
            if(this.searchText.equipmentTemplateType && this.searchText.equipmentTemplateType.length > 0) {
                filterList = filterList.filter((item: any) => {
                    let flag = false;
                    this.searchText.equipmentTemplateType.forEach(obj => {
                        if(obj === item.equipmentTemplateType) {
                            flag = true;
                        }
                    })
                    return flag;
                })
            }
            this.filterSource = filterList;
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: string, checked: boolean): void {
        this.updateCheckedIds(id, checked);
        this.refreshCheckedBoxStatus();
    }

    onAllChecked(checked: boolean): void {
        this.filterSource.forEach( (item: any) => {
            this.updateCheckedIds(item.equipmentTemplateId, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.checkedIds.includes(id)) {
              this.checkedIds.push(id);
            }
        } else {
            this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = this.filterSource.length && this.filterSource.every((item: any ) => {
            return this.checkedIds.includes(item.equipmentTemplateId)
        });
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.cols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: label === 'value' ? "选择局站类型" : "选择模板类型",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = label === 'value' ? this.searchText.stationBaseType : this.searchText.equipmentTemplateType;
        modal.afterClose.subscribe((res: any) => {
            if(label === 'value') {
                this.searchText.stationBaseType = res;
            } else {
                this.searchText.equipmentTemplateType = res;
            }
            this.search();
        })
    }

    confirm() {
        const modal = this.modal.create<RewriteSelectorComponent>({
            nzTitle: "确定回写字段",
            nzContent: RewriteSelectorComponent,
            nzWidth: 400,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.type = this.type;
        modal.afterClose.subscribe((res: any) => {
            let list: any = [];
            if(res && res.length > 0) {
                res.forEach((item: any) => {
                    const obj = {
                        entryId: item,
                        enable: true
                    }
                    list.push(obj);
                })
                this.service.updateRewriteList(list).then(res => {
                    this.messageService.success('回写字段设置成功！');
                    this.#modal.close(this.checkedIds);
                })
            }
        })
    }

    cancel() {
        this.#modal.destroy();
    }
}