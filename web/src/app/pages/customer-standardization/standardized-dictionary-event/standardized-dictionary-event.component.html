<div class="event">
    <div class="line">
        <button nz-button nzType="primary" (click)="openModal()">
            <i class="icon icon-add"></i> 新增
        </button>
        <button nz-button nzType="primary" (click)="exportSql()">
            导出数据库脚本
        </button>
        <button nz-button nzType="primary" (click)="exportXML()">
            导出为XML
        </button>
    </div>
    <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
        [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'calc(100vh - 210px)'"
        [containFixHeaderHeight]="true" [fixHeader]="true" dLoading [loadingStyle]="'default'" [loading]="isUpdating"
        (tableScrollEvent)="tableScrollEvent($event)">
        <thead dTableHead>
            <tr dTableRow>
                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== 'operation'"
                    (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, colConfig.field)">
                    {{ colConfig.title }}
                    <!-- 空白 -->
                    <div *ngIf="colConfig.field === 'operation'" style="height: 32px;"></div>
                    <!-- 输入框 -->
                    <input *ngIf="colConfig.field !== 'operation'" nz-input [(ngModel)]="filterData[colConfig.field]"
                        (ngModelChange)="filterChange()" />
                </th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow (contextmenu)="rowContextmenu($event, rowMenu)">
                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell [title]="rowItem[colConfig.field]">
                        @if (currentType === 1 && (colConfig.field === 'eventSeverity' || colConfig.field === 'extendFiled1' || colConfig.field === 'extendFiled2' || colConfig.field === 'extendFiled3')) {
                            {{ eventSeverityArr[rowItem[colConfig.field]] }}
                        }@else if (colConfig.field === 'stationCategory') {
                            {{ stationCategoryArr[rowItem[colConfig.field]] }}
                        }@else if (colConfig.field === 'modifyType') {
                            {{ modifyTypeArr[rowItem[colConfig.field]] }}
                        }@else if(colConfig.field === 'operation') {
                        <iconfont [icon]="'icon-edit-set'" title="修改" (click)="openModal(rowItem)"></iconfont>
                        <iconfont [icon]="'icon-delete2'" title="删除" (click)="delete(rowItem)"></iconfont>
                        }@else {
                        {{ rowItem[colConfig.field] }}
                        }
                    </td>
                </tr>
                <nz-dropdown-menu #rowMenu="nzDropdownMenu">
                    <ul nz-menu nzSelectable>
                        <li nz-menu-item (click)="openModal()">
                            <iconfont icon="icon-add" class="right-menu-icon"></iconfont>
                            新增字典项
                        </li>
                        <li nz-menu-item (click)="openModal(rowItem)">
                            <iconfont icon="icon-edit-set" class="right-menu-icon"></iconfont>
                            修改字典项
                        </li>
                        <li nz-menu-item (click)="delete(rowItem)">
                            <iconfont icon="icon-delete2" class="right-menu-icon"></iconfont>
                            删除字典项
                        </li>
                    </ul>
                </nz-dropdown-menu>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div *ngIf="!isUpdating" style="text-align: center; margin-top: 30px">
                <nz-empty nzNotFoundImage="simple" />
            </div>
        </ng-template>
    </d-data-table>
</div>