import { Component, Injector } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { CustomerStandardizationService } from '../customer-standardization.service';

@Component({
  selector: 'app-export-type-modal',
  templateUrl: './export-type-modal.component.html',
  styleUrl: './export-type-modal.component.less'
})
export class ExportTypeModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private service: CustomerStandardizationService
  ) {
    super(injector)
  }
  customerTypeList: any;
  customerType: any;
  onInit() {
    this.service.getCustomerType().then((res: any) => {
      if (res) {
        this.customerTypeList = res;
        this.customerType = res[0].id;
      }
    })
  }
}
