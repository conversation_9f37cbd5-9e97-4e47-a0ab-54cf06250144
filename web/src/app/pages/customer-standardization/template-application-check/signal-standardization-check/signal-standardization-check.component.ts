import { Component, ElementRef, EventEmitter, Injector, OnInit, Output, ViewChild } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CustomerStandardizationService } from '../../customer-standardization.service';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';

@Component({
  selector: 'app-signal-standardization-check',
  templateUrl: './signal-standardization-check.component.html',
  styleUrls: ['./signal-standardization-check.component.less']
})

export class SignalStandardizationCheckComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @Output() update: EventEmitter<any> = new EventEmitter<any>();
    
    editableTip = EditableTip.hover;
    source: any;
    filterSource: any;
    checkedIds: any = [];

    searchText: any = {
        standardedName: null,
        equipmentTemplateId: null,
        equipmentTemplateName: null,
        stationCategoryName: null,
        signalId: null,
        signalName: null,
        storeInterval: null,
        absValueThreshold: null,
        percentThreshold: null,
        staticsPeriod: null,
        baseTypeId: null,
        baseTypeName: null,
        standardDicId: null,
        signalStandardName: null,
    }
    searchTimer: any;

    cols: any = [
        {
            field: 'standardedName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.applicationStandardization'),
        },
        {
            field: 'equipmentTemplateId',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.templateId'),
        },
        {
            field: 'equipmentTemplateName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.templateName'),
        },
        {
            field: 'stationCategoryName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.templateType'),
        },
        {
            field: 'signalId',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.signalId'),
        },
        {
            field: 'signalName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.signalName'),
        },
        {
            field: 'storeInterval',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.storageCycle'),
        },
        {
            field: 'absValueThreshold',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.absoluteThreshold'),
        },
        {
            field: 'percentThreshold',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.percentageThreshold'),
        },
        {
            field: 'staticsPeriod',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.statisticsCycle'),
        },
        {
            field: 'baseTypeId',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.baseId'),
        },
        {
            field: 'baseTypeName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.baseName'),
        },
        {
            field: 'standardDicId',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.standardId'),
        },
        {
            field: 'signalStandardName',
            width: '150px',
            title: this.translate.instant('customerStandardization.signalCheck.standardName'),
        },
    ];

    modelList: any = [];

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '515px';

    // keepalive后记住滚动条位置
    keepScrollTop?: number;

    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []

    rowItemCurrent: any;
    fieldCurrent: any;
    modalBefore: any;
    isConfirming: boolean = false;
    isEditing: boolean = false;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.getTabelData();
    }

    setTabelScroll() {
        // 滚动条回显
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
        } catch { /* 呃 好饱 */ }
    }

    getTabelData() {
        this.isUpdating = true;
        this.service.getTempStandardizationCheck(1).then(res => {
            this.isUpdating = false;
            let result = res.signalChecks;
            result.forEach((item: any) => {
                item['standardedName'] = item['standarded'] ? '是' : '否';
                item['uniqueId'] = item['equipmentTemplateId'].toString() + '-' + item['signalId'].toString();
            })
            this.source = result;
            this.search();
        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            for(const field in this.searchText) {
                if(this.searchText[field] && field !== 'type') {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.searchText[field]) : false;
                    })
                } else if (this.searchText.type && this.searchText.type.length > 0) {
                    filterList = filterList.filter((item: any) => {
                        let flag = false;
                        this.searchText.type.forEach((obj: any) => {
                            if(obj === item.type) {
                                flag = true;
                            }
                        })
                        return flag;
                    })
                }
            }
            this.filterSource = filterList;
            const obj = {
                type: 1,
                length: this.source.length
            }
            this.update.emit(obj);
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: string): void {
        this.checkedIds = id;
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.cols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: "选择局站类型",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = this.searchText.type;
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                this.searchText.type = res;
            }
            this.search();
        })
    }
}