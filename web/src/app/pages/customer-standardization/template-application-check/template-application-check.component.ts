import { Component, ElementRef, Injector, OnInit, ViewChild, inject } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { SignalStandardizationCheckComponent } from './signal-standardization-check/signal-standardization-check.component';
import { EventStandardizationCheckComponent } from './event-standardization-check/event-standardization-check.component';
import { ControlStandardizationCheckComponent } from './control-standardization-check/control-standardization-check.component';

@Component({
  selector: 'app-template-application-check',
  templateUrl: './template-application-check.component.html',
  styleUrls: ['./template-application-check.component.less']
})

export class TemplateApplicationCheckComponent extends GenericComponent implements OnInit {

    #modal = inject(NzModalRef);
    @ViewChild('tabs_1') private tabs_1!: SignalStandardizationCheckComponent;
    @ViewChild('tabs_2') private tabs_2!: EventStandardizationCheckComponent;
    @ViewChild('tabs_3') private tabs_3!: ControlStandardizationCheckComponent;
    
    selectedTabIndex?: number = 0;
    exporting: any;
    tableCount: number = 0;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {

    }

    public tabChange(index: number): void {
        this.selectedTabIndex = index;
        if(index === 0) {
            this.tabs_1.setTabelScroll();
            this.tabs_1.search();
        }
        if(index === 1) {
            this.tabs_2.setTabelScroll();
            this.tabs_2.search();
        }
        if(index === 2) {
            this.tabs_3.setTabelScroll();
            this.tabs_3.search();
        }
    }

    updateCount(event: any) {
        if(this.selectedTabIndex === 0 && event.type === 1) {
            this.tableCount = event.length;
        }
        if(this.selectedTabIndex === 1 && event.type === 2) {
            this.tableCount = event.length;
        }
        if(this.selectedTabIndex === 2 && event.type === 3) {
            this.tableCount = event.length;
        }
    }

    export() {
        if(this.exporting) return;
        this.exporting = this.messageService.loading('正在进行导出，请勿重复执行导出操作...', { nzDuration: 0 }).messageId;
        this.service.exportTempStandardizationCheck().subscribe((res: any) => {
            this.messageService.success('导出成功！');
            const a = document.createElement('a');
            a.href = URL.createObjectURL(res);
            a.download = "标准化检查文件.xlsx";
            a.click();
            this.messageService.remove(this.exporting);
            this.exporting = null;
        })
    }

    confirm() {
        this.#modal.destroy();
    }

}