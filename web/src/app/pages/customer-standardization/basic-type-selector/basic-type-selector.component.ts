/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, EventEmitter, Injector, Input, OnInit, Output, ViewChild, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { cloneDeep } from 'lodash';
import { forkJoin } from 'rxjs';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeNode } from 'ng-zorro-antd/tree';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { BaseclassAddComponent } from '@components/modal/baseclass-add/baseclass-add.component';
import { BaseClassAddAction, BaseclassAddInput } from '@components/modal/baseclass-add/baseclass-add-model';
import { BaseClassType } from '@common/enum/BaseClassType';
@Component({
    selector: 'app-basic-type-selector',
    templateUrl: './basic-type-selector.component.html',
    styleUrls: ['./basic-type-selector.component.less']
})

export class BasicTypeSelectorComponent extends GenericComponent implements OnInit {

    #modal = inject(NzModalRef);
    searchText: string = '';
    nodes: any;
    selectedNodes: any;
    @ViewChild('nzTree') nzTree!: NzTreeComponent;

    defaultSelectedKey: any = [];
    defaultCheckedKey: any = [];
    getFirstNode: boolean = false;
    type: string = '';
    stationType: any;
    standardDicId: any;

    constructor(injector: Injector,
        private message: NzMessageService,
        private service: CustomerStandardizationService,
        private modal: NzModalService,) {
        super(injector);
    }

    onInit() {
        this.getTreeData();
    }

    getTreeData() {
        if (this.type === 'signal') {
            this.getSignalTree();
        } else if (this.type === 'event') {
            this.getEventTree();
        } else {
            this.getControlTree();
        }
    }

    async getSignalTree() {
        await this.service.getSignalBasicTree().then(async res => {
            let result = res;
            this.buildTree(result);
            if (this.selectedNodes.length > 0) {
                this.expandToNodes(result);
            }
            this.nodes = result;
            this.defaultSelectedKey = cloneDeep(this.selectedNodes);
            this.defaultCheckedKey = cloneDeep(this.selectedNodes);
        })
    }

    async getEventTree() {
        await this.service.getEventBasicTree().then(async res => {
            let result = res;
            this.buildTree(result);
            if (this.selectedNodes.length > 0) {
                this.expandToNodes(result);
            }
            this.nodes = result;
            this.defaultSelectedKey = cloneDeep(this.selectedNodes);
            this.defaultCheckedKey = cloneDeep(this.selectedNodes);
        })
    }

    async getControlTree() {
        await this.service.getControlBasicTree().then(async res => {
            let result = res;
            this.buildTree(result);
            if (this.selectedNodes.length > 0) {
                this.expandToNodes(result);
            }
            this.nodes = result;
            this.defaultSelectedKey = cloneDeep(this.selectedNodes);
            this.defaultCheckedKey = cloneDeep(this.selectedNodes);
        })
    }

    expandToNodes(child: any) {
        child.forEach((item: any) => {
            if (item['children'] && item['children'].length > 0) {
                let flag = false;
                item['children'].forEach((obj: any) => {
                    if (obj['children'] && obj['children'].length > 0) {
                        let index = obj['children'].findIndex((thg: any) => {
                            return thg['key'] === this.selectedNodes[0];
                        })
                        if (index >= 0) {
                            obj['expanded'] = true;
                            flag = true;
                        }
                    }
                })
                if (flag) {
                    item['expanded'] = true;
                }
            }
        })
    }

    buildTree(nodes: any) {
        for (let i = 0; i < nodes.length; i++) {
            nodes[i]['title'] = nodes[i]['label'];
            if (!this.getFirstNode && this.defaultSelectedKey.length > 0) {
                nodes[i]['expanded'] = true;
            }

            if (nodes[i]['children'] && nodes[i]['children'].length > 0 || nodes[i]['index'] < 2) {
                this.buildTree(nodes[i]['children']);
            } else {
                if (!this.getFirstNode && this.defaultSelectedKey.length > 0) {
                    this.getFirstNode = true;
                    nodes[i]['isLeaf'] = true;
                } else {
                    nodes[i]['isLeaf'] = true;
                }
            }
        }
    }

    // 双击展开/收回子节点
    openFolder(data: NzTreeNode | NzFormatEmitEvent): void {
        // do something if u want
        if (data instanceof NzTreeNode) {
            data.isExpanded = !data.isExpanded
        } else {
            const node = data.node
            if (node) {
                node.isExpanded = !node.isExpanded
            }
        }
    }

    clearSelected(clearSelected?: boolean) {
        const modal = this.modal.confirm({
            nzTitle: '提示',
            nzContent: '请确认是否清除所有选择项并保存退出?',
            nzOkText: '确认',
            nzOkType: 'primary',
            nzOkDanger: true,
            nzMaskClosable: false,
            nzOnOk: () => {
                return 'yes'
            },
            nzCancelText: '取消',
            nzOnCancel: () => {
                return 'no'
            },
        })
        modal.afterClose.subscribe((res: any) => {
            if (res === 'yes') {
                if (clearSelected) {
                    this.defaultSelectedKey = [];
                    this.defaultCheckedKey = [];
                } else {
                    this.#modal.close({
                        type: 'clear'
                    })
                }
            }
        })
    }

    addNewBasic() {
        let type = BaseClassType.signal;
        let title = '增加基类信号';
        if (this.type === 'signal') {
            type = BaseClassType.signal;
            title = '增加基类信号';
        } else if (this.type === 'event') {
            type = BaseClassType.event;
            title = '增加基类事件';
        } else if (this.type === 'control') {
            type = BaseClassType.command;
            title = '增加基类控制';
        }
        this.openDialog<BaseclassAddComponent, BaseclassAddInput, undefined>({
            nzTitle: title,
            nzWidth: 650,
            nzData: {
                type: type,
                action: BaseClassAddAction.add,
                isSystem: true,
            },
            nzContent: BaseclassAddComponent,
            nzOnOk: async componentInstance => {
                await componentInstance?.confirm()
                this.messageService.success('增加成功');
                this.selectedNodes = await this.getCheckedNodes();
                this.getTreeData();
            }
        })
    }

    getCheckedNodes() {
        let nodes = this.nzTree.getCheckedNodeList();
        let checkeds: any = [];
        nodes.forEach((item: any) => {
            if (item.origin.index === 2) {
                checkeds.push(item.origin.key);
            } else if (item.origin.index === 1) {
                item.origin.children.forEach((obj: any) => {
                    checkeds.push(obj.key);
                })
            } else {
                item.origin.children.forEach((obj: any) => {
                    obj.children.forEach((thg: any) => {
                        checkeds.push(thg.key);
                    })
                })
            }
        })
        return checkeds;
    }

    checkValid(ids: any) {
        let type = 1;
        if (this.type === 'signal') {
            type = 1;
        } else if (this.type === 'event') {
            type = 2;
        } else if (this.type === 'control') {
            type = 3;
        }
        this.service.checkDuplicateId(this.stationType, ids, type, this.standardDicId).then(res => {
            if (res.status) {
                this.message.warning(res.warnMsg);
            } else {
                this.#modal.close({
                    type: 'update',
                    data: ids,
                });
            }
        })
    }

    cancel() {
        this.#modal.destroy();
    }

    confirm() {
        let checkeds = this.getCheckedNodes();
        if (checkeds.length > 0) {
            this.checkValid(checkeds);
        } else {
            this.#modal.close({
                type: 'clear'
            })
        }
    }

    getCheckedIds() {
        let checkeds = this.getCheckedNodes();
        if (checkeds.length > 0) {
            this.#modal.close({
                type: 'update',
                data: checkeds,
            });
        } else {
            this.#modal.close({
                type: 'clear'
            })
        }
    }
}