import { Component, Injector, inject } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { NZ_MODAL_DATA, NzModalService } from 'ng-zorro-antd/modal';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { AddItemModalComponent } from '../add-item-modal/add-item-modal.component';
import { BasicTypeSelectorComponent } from '../basic-type-selector/basic-type-selector.component';

@Component({
  selector: 'ngx-add-edit-modal',
  templateUrl: './add-edit-modal.component.html',
  styleUrl: './add-edit-modal.component.less'
})
export class AddEditModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private modal: NzModalService,
    private service: CustomerStandardizationService
  ) {
    super(injector)
  }
  public readonly input = inject(NZ_MODAL_DATA);
  item: any;
  setBaseClass: boolean = true;
  equipmentLogicClassOptions: any;
  logicClassOptions: any;
  stationCategoryOptions: any;
  currentType: any;

  validateSignalForm: FormGroup<{
    standardDicId: FormControl<number>;
    netManageId: FormControl<number>;
    equipmentLogicClassId: FormControl<number>;
    signalLogicClassId: FormControl<number>;
    signalLogicClass: FormControl<string>;
    signalStandardName: FormControl<string>;
    storeInterval: FormControl<string>;
    absValueThreshold: FormControl<string>;
    percentThreshold: FormControl<string>;
    statisticsPeriod: FormControl<string>;
    stationCategory: FormControl<number>;
    description: FormControl<string>;
    extendFiled1: FormControl<string>;
    extendFiled2: FormControl<string>
  }> = this.fb.group({
    standardDicId: [0, [Validators.required, Validators.maxLength(6), Validators.minLength(6)]],
    netManageId: [0, [Validators.required]],
    equipmentLogicClassId: [0, [Validators.required]],
    signalLogicClassId: [0, [Validators.required]],
    signalLogicClass: [''],
    signalStandardName: ['', [Validators.required]],
    storeInterval: [''],
    absValueThreshold: [''],
    percentThreshold: [''],
    statisticsPeriod: [''],
    stationCategory: [0, [Validators.required]],
    description: [''],
    extendFiled1: [''],
    extendFiled2: ['']
  });

  validateEventForm: FormGroup<{
    standardDicId: FormControl<number>;
    eventSeverity: FormControl<number>;
    eventClass: FormControl<string>;
    netManageId: FormControl<number>;
    equipmentLogicClassId: FormControl<number>;
    eventLogicClassId: FormControl<number>;
    eventLogicClass: FormControl<string>;
    eventStandardName: FormControl<string>;
    storeInterval: FormControl<string>;
    absValueThreshold: FormControl<string>;
    percentThreshold: FormControl<string>;
    statisticsPeriod: FormControl<string>;
    stationCategory: FormControl<number>;
    description: FormControl<string>;
    extendFiled1: FormControl<string>;
    extendFiled2: FormControl<string>;
    extendFiled3: FormControl<string>;
    businessAffect: FormControl<string>;
    compareValue: FormControl<string>;
    equipmentAffect: FormControl<string>;
    meanings: FormControl<string>;
    modifyType: FormControl<string>;
    standardType: FormControl<string>;
    startDelay: FormControl<string>
  }> = this.fb.group({
    standardDicId: [0, [Validators.required, Validators.maxLength(6), Validators.minLength(6)]],
    eventSeverity: [0, [Validators.required]],
    eventClass: ['', [Validators.required]],
    netManageId: [0],
    equipmentLogicClassId: [0, [Validators.required]],
    eventLogicClassId: [0, [Validators.required]],
    eventLogicClass: [''],
    eventStandardName: ['', [Validators.required]],
    storeInterval: [''],
    absValueThreshold: [''],
    percentThreshold: [''],
    statisticsPeriod: [''],
    stationCategory: [0, [Validators.required]],
    description: [''],
    extendFiled1: [''],
    extendFiled2: [''],
    extendFiled3: [''],
    businessAffect: [''],
    compareValue: [''],
    equipmentAffect: [''],
    meanings: [''],
    modifyType: [''],
    standardType: [''],
    startDelay: ['']
  });

  validateControlForm: FormGroup<{
    standardDicId: FormControl<number>;
    netManageId: FormControl<number>;
    equipmentLogicClassId: FormControl<number>;
    controlLogicClassId: FormControl<number>;
    controlLogicClass: FormControl<string>;
    controlStandardName: FormControl<string>;
    stationCategory: FormControl<number>;
    description: FormControl<string>;
    extendFiled1: FormControl<string>;
    extendFiled2: FormControl<string>
  }> = this.fb.group({
    standardDicId: [0, [Validators.required, Validators.maxLength(6), Validators.minLength(6)]],
    netManageId: [0, [Validators.required]],
    equipmentLogicClassId: [0, [Validators.required]],
    controlLogicClassId: [0, [Validators.required]],
    controlLogicClass: [''],
    controlStandardName: ['', [Validators.required]],
    stationCategory: [0, [Validators.required]],
    description: [''],
    extendFiled1: [''],
    extendFiled2: ['']
  });

  onInit() {
    this.currentType = this.service.getData('currentType')
    this.stationCategoryOptions = this.service.getData('stationCategoryOptions')
    this.getEquipmentLogicClassOptions();
    this.getLogicClassOptions();
    if(this.currentType === 3) {
      if(this.input.type === 'signal'){
        this.validateSignalForm.controls.standardDicId.clearValidators();
        this.validateSignalForm.controls.netManageId.setValidators([Validators.required, Validators.maxLength(12), Validators.minLength(12)]);
      }else if(this.input.type === 'event'){
        this.validateEventForm.controls.standardDicId.clearValidators();
        this.validateEventForm.controls.eventSeverity.clearValidators();
        this.validateEventForm.controls.eventClass.clearValidators();
        this.validateEventForm.controls.netManageId.setValidators([Validators.required, Validators.maxLength(12), Validators.minLength(12)]);
      }else if(this.input.type === 'control'){
        this.validateSignalForm.controls.standardDicId.clearValidators();
        this.validateSignalForm.controls.netManageId.setValidators([Validators.required, Validators.maxLength(12), Validators.minLength(12)]);
      }
    }

    if (this.input.data) {
      this.item = this.input.data;
      if (this.input.type === 'event') {
        this.item.extendFiled1 = Number(this.item.extendFiled1)
        this.item.extendFiled2 = Number(this.item.extendFiled2)
        this.item.extendFiled3 = Number(this.item.extendFiled3)
      }
      this.setBaseClass = false;
    }
    else {
      this.item = {};
    }
  }

  getEquipmentLogicClassOptions() {
    this.service.getLogicclassById(1).then((res: any) => {
      if (res) {
        this.equipmentLogicClassOptions = res.map((item: any) => {
          return { "value": item.id, "label": item.value };
        });
      }
    })
  }

  getLogicClassOptions() {
    const id = this.input.type === 'event' ? 3 : 2;
    this.service.getLogicclassById(id).then((res: any) => {
      if (res) {
        this.logicClassOptions = res.map((item: any) => {
          return { "value": item.id, "label": item.value };
        });
        if(this.input.type === 'signal' && this.currentType === 3) {
          let list = this.logicClassOptions.filter((item: any) => {
            return !item.label.includes('告警') && !item.label.includes('遥控');
          })
          this.logicClassOptions = list;
        }
        if(this.input.type === 'event' && this.currentType === 3) {
          let list = this.logicClassOptions.filter((item: any) => {
            return item.label.includes('告警');
          })
          this.logicClassOptions = list;
        }
        if(this.input.type === 'control' && this.currentType === 3) {
          let list = this.logicClassOptions.filter((item: any) => {
            return item.label.includes('遥控');
          })
          this.logicClassOptions = list;
        }
      }
    })
  }

  openModal(event: any) {
    let type, text = '';
    if (event === 'equipmentLogicClass') {
      type = 1;
      text = '设备类型';
    } else if (event === 'signalLogicClass') {
      type = 2;
      text = '逻辑分类';
    } else if (event === 'eventLogicClass') {
      type = 3;
      text = '告警逻辑分类';
    } else if (event === 'controlLogicClass') {
      type = 2;
      text = '信号量类型';
    }
    this.openDialog({
      nzTitle: `新增${text}`,
      nzData: { type: type, text: text },
      nzContent: AddItemModalComponent,
      nzOnOk: async componentInstance => {
        await componentInstance?.confirm()
        this.messageService.success('新增成功')
        if (event === 'equipmentLogicClass') {
          this.getEquipmentLogicClassOptions();
        } else {
          this.getLogicClassOptions();
        }
      }
    });
  }

  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    return new Promise<void>(async (resolve, reject) => {
      if (!valid) {
        reject()
        return
      }
      if (this.input.data) {
        if (this.input.type === 'signal') {
          await this.service.updateSignalItem(this.item).catch(() => {
            reject()
          })
        } else if (this.input.type === 'event') {
          await this.service.updateEventItem(this.item).catch(() => {
            reject()
          })
        } else if (this.input.type === 'control') {
          await this.service.updateControlItem(this.item).catch(() => {
            reject()
          })
        }
      } else {
        if (this.input.type === 'signal') {
          await this.service.addSignalItem(this.item).then((res)=>{
            if(res) this.openBasicTypeSelector(res, 'signal')
          }).catch(() => {
            reject()
          })
        } else if (this.input.type === 'event') {
          await this.service.addEventItem(this.item).then((res)=>{
            if(res) this.openBasicTypeSelector(res, 'event')
          }).catch(() => {
            reject()
          })
        } else if (this.input.type === 'control') {
          await this.service.addControlItem(this.item).then((res)=>{
            if(res) this.openBasicTypeSelector(res, 'control')
          }).catch(() => {
            reject()
          })
        }
      }
      resolve()
    })
  }

  openBasicTypeSelector(item: any, type: any) {
    if (!this.setBaseClass) return
    let title = item['equipmentLogicClass'] + '>>' ;
    if(type === 'signal'){
      title += item['signalLogicClass'] + '>>' + (item['signalStandardName'] ? item['signalStandardName'] : '')
    }else if(type === 'event'){
      title += item['eventLogicClass'] + '>>' + (item['eventStandardName'] ? item['eventStandardName'] : '')
    }else if(type === 'control'){
      title += item['controlLogicClass'] + '>>' + (item['controlStandardName'] ? item['controlStandardName'] : '')
    }
    const modal = this.modal.create<BasicTypeSelectorComponent>({
      nzTitle: title,
      nzContent: BasicTypeSelectorComponent,
      nzWidth: 660,
      nzFooter: [
        {
          label: '清除选择',
          type: 'default',
          onClick: componentInstance => componentInstance!.clearSelected(true)
        },
        {
          label: '添加基类信号',
          type: 'default',
          onClick: componentInstance => componentInstance!.addNewBasic()
        },
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        },
        {
          label: '确认',
          type: 'primary',
          onClick: componentInstance => componentInstance!.getCheckedIds()
        },
      ],
      nzMaskClosable: false
    })
    const instance = modal.getContentComponent();
    instance.type = type;
    instance.stationType = item.id;
    instance.selectedNodes = item.baseTypeId ? item.baseTypeId.split(',') : [];
    modal.afterClose.subscribe((res: any) => {
      if (res && res.type === 'update') {
        const params = {
          standardDicId: item.standardDicId,
          stationBaseType: item.stationCategory,
          baseTypeIds: res.data.join(',')
        }
        if(type === 'signal'){
          this.service.batchSignalBasemap(params).then(res => {})
        }else if(type === 'event'){
          this.service.batchEventBasemap(params).then(res => {})
        }else if(type === 'control'){
          this.service.batchCommandBasemap(params).then(res => {})
        }
      }
    })
  }

  equipmentLogicClassChange(data: any) {
    if(data)
    this.item.equipmentLogicClass = this.equipmentLogicClassOptions.find((item: any) => item.value === data)?.label
  }

  logicClassChange(data: any, field: any) {
    if(data)
    this.item[field] = this.logicClassOptions.find((item: any) => item.value === data).label
  }

  // ng-form 校验
  public submitForm(): Boolean {
    let validateForm: FormGroup = new FormGroup({})
    if (this.input.type === 'signal') {
      validateForm = this.validateSignalForm
    } else if (this.input.type === 'event') {
      validateForm = this.validateEventForm
    } else if (this.input.type === 'control') {
      validateForm = this.validateControlForm
    }
    if (validateForm.valid) {
      return true
    } else {
      Object.values(validateForm.controls).forEach((control: any) => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}