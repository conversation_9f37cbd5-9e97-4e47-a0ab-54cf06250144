<div class="modal">
    <form nz-form [formGroup]="validateSignalForm" *ngIf="input.type === 'signal'">
        <nz-form-item *ngIf="currentType === 1">
            <nz-form-label [nzSpan]="4" nzRequired>信号ID</nz-form-label>
            <nz-form-control *ngIf="!input.data?.standardDicId" [nzSpan]="12" [nzErrorTip]="'信号ID必须为6位数字'">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId" maxlength="6"
                    pattern="^\d{6}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.standardDicId" [nzSpan]="12">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId"
                    [readonly]="input.data?.standardDicId">
            </nz-form-control>
            <div class="msg">
                <label>(请输入6位信号编码ID)</label>
            </div>
        </nz-form-item>
        <nz-form-item *ngIf="currentType === 3">
            <nz-form-label [nzSpan]="4" nzRequired nzDisable>信号编码</nz-form-label>
            <nz-form-control *ngIf="!input.data?.netManageId" [nzSpan]="12" [nzErrorTip]="'信号编码必须为12位数字且第4位为1-6'">
                <input nz-input formControlName="netManageId" [disabled]="input.data" [(ngModel)]="item.netManageId"
                    maxlength="12" pattern="^\d{3}[1-6]\d{8}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.netManageId" [nzSpan]="12">
                <input nz-input formControlName="netManageId" [(ngModel)]="item.netManageId"
                    [readOnly]="input.data?.netManageId">
            </nz-form-control>
            <div class="msg">
                <label>(含顺序号,12位)</label>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>{{ currentType === 3 ? '标准名': '信号标准名'}}</nz-form-label>
            <nz-form-control [nzSpan]="16" nzErrorTip="{{ currentType === 3 ? '标准名不能为空': '信号标准名不能为空'}}">
                <input nz-input formControlName="signalStandardName" [(ngModel)]="item.signalStandardName" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>设备类型</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16" [nzErrorTip]="'设备类型不能为空'">
                <nz-select formControlName="equipmentLogicClassId" nzShowSearch [(ngModel)]="item.equipmentLogicClassId"
                    [nzOptions]="equipmentLogicClassOptions" (ngModelChange)="equipmentLogicClassChange($event)"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('equipmentLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>{{ currentType === 3 ? '逻辑分类': '信号量类型'}}</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16"
                nzErrorTip="{{ currentType === 3 ? '逻辑分类不能为空': '信号量类型不能为空'}}">
                <nz-select formControlName="signalLogicClassId" nzShowSearch [(ngModel)]="item.signalLogicClassId"
                    [nzOptions]="logicClassOptions" (ngModelChange)="logicClassChange($event,'signalLogicClass')"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('signalLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>局站类型</nz-form-label>
            <nz-form-control [nzSpan]="16" [nzErrorTip]="'局站类型不能为空'">
                <nz-select formControlName="stationCategory" [(ngModel)]="item.stationCategory"
                    [nzDisabled]="currentType === 3 && input.data" [nzOptions]="stationCategoryOptions">
                </nz-select>
            </nz-form-control>
        </nz-form-item>
        @if(currentType === 1){
        <nz-form-item>
            <nz-form-label [nzSpan]="4">存储周期(s)</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="storeInterval" [(ngModel)]="item.storeInterval" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">绝对值阀值</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="absValueThreshold" [(ngModel)]="item.absValueThreshold" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">百分比阀值(%)</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="percentThreshold" [(ngModel)]="item.percentThreshold" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">统计周期(s)</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="statisticsPeriod" [(ngModel)]="item.statisticsPeriod" />
            </nz-form-control>
        </nz-form-item>
        }
        <nz-form-item>
            <nz-form-label [nzSpan]="4">备注</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="description" [(ngModel)]="item.description" />
            </nz-form-control>
        </nz-form-item>
        @if(currentType === 1){
        <nz-form-item>
            <nz-form-label [nzSpan]="4">单位</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="extendFiled1" [(ngModel)]="item.extendFiled1" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">设备子类</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="extendFiled2" [(ngModel)]="item.extendFiled2" />
            </nz-form-control>
        </nz-form-item>
        }
    </form>

    <form nz-form [formGroup]="validateEventForm" *ngIf="input.type === 'event'">
        <nz-form-item *ngIf="currentType === 1">
            <nz-form-label [nzSpan]="5" nzRequired>告警ID</nz-form-label>
            <nz-form-control *ngIf="!input.data?.standardDicId" [nzSpan]="12" [nzErrorTip]="'告警ID必须为6位数'">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId" pattern="^\d{6}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.standardDicId" [nzSpan]="12">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId"
                    [readonly]="input.data?.standardDicId">
            </nz-form-control>
            <div class="msg">
                <label>(请输入6位告警编码ID)</label>
            </div>
        </nz-form-item>
        <nz-form-item *ngIf="currentType === 3">
            <nz-form-label [nzSpan]="5" nzRequired nzDisable>告警编码</nz-form-label>
            <nz-form-control *ngIf="!input.data?.netManageId" [nzSpan]="12" [nzErrorTip]="'告警编码必须为12位数字且第4位为1-6'">
                <input nz-input formControlName="netManageId" [disabled]="input.data" [(ngModel)]="item.netManageId"
                    maxlength="12" pattern="^\d{3}[1-6]\d{8}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.netManageId" [nzSpan]="12">
                <input nz-input formControlName="netManageId" [(ngModel)]="item.netManageId"
                    [readOnly]="input.data?.netManageId">
            </nz-form-control>
            <div class="msg">
                <label>(含顺序号,12位)</label>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>{{ currentType === 3 ? '标准名': '告警标准名'}}</nz-form-label>
            <nz-form-control [nzSpan]="16" nzErrorTip="{{ currentType === 3 ? '标准名不能为空': '告警标准名不能为空'}}">
                <input nz-input formControlName="eventStandardName" [(ngModel)]="item.eventStandardName" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>设备类型</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16" [nzErrorTip]="'设备类型不能为空'">
                <nz-select formControlName="equipmentLogicClassId" nzShowSearch [(ngModel)]="item.equipmentLogicClassId"
                    [nzOptions]="equipmentLogicClassOptions" (ngModelChange)="equipmentLogicClassChange($event)"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('equipmentLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>{{ currentType === 3 ? '逻辑分类': '告警逻辑分类'}}</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16"
                nzErrorTip="{{ currentType === 3 ? '逻辑分类不能为空': '告警逻辑分类不能为空'}}">
                <nz-select formControlName="eventLogicClassId" nzShowSearch [(ngModel)]="item.eventLogicClassId"
                    [nzOptions]="logicClassOptions" (ngModelChange)="logicClassChange($event,'eventLogicClass')"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('eventLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>局站类型</nz-form-label>
            <nz-form-control [nzSpan]="16" [nzErrorTip]="'局站类型不能为空'">
                <nz-select formControlName="stationCategory" [(ngModel)]="item.stationCategory"
                    [nzOptions]="stationCategoryOptions" [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
        </nz-form-item>
        @if(currentType === 1){
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>告警等级</nz-form-label>
            <nz-form-control [nzSpan]="16" [nzErrorTip]="'告警等级不能为空'">
                <nz-select formControlName="eventSeverity" [(ngModel)]="item.eventSeverity"
                    [nzOptions]="input.eventSeverityOptions">
                </nz-select>
            </nz-form-control>
        </nz-form-item>

        <nz-form-item>
            <nz-form-label [nzSpan]="5">节点告警级别</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <nz-select formControlName="extendFiled1" [(ngModel)]="item.extendFiled1"
                    [nzOptions]="input.eventSeverityOptions">
                </nz-select>
            </nz-form-control>
        </nz-form-item><nz-form-item>
            <nz-form-label [nzSpan]="5">基站告警击级别</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <nz-select formControlName="extendFiled2" [(ngModel)]="item.extendFiled2"
                    [nzOptions]="input.eventSeverityOptions">
                </nz-select>
            </nz-form-control>
        </nz-form-item><nz-form-item>
            <nz-form-label [nzSpan]="5">数据中心告警级别</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <nz-select formControlName="extendFiled3" [(ngModel)]="item.extendFiled3"
                    [nzOptions]="input.eventSeverityOptions">
                </nz-select>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>告警逻辑子类</nz-form-label>
            <nz-form-control [nzSpan]="16" [nzErrorTip]="'告警逻辑子类不能为空'">
                <input nz-input formControlName="eventClass" [(ngModel)]="item.eventClass" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5">告警门限</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="compareValue" [(ngModel)]="item.compareValue" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5">告警延迟</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="startDelay" [(ngModel)]="item.startDelay" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5">告警解释</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="meanings" [(ngModel)]="item.meanings" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5">对设备影响</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="equipmentAffect" [(ngModel)]="item.equipmentAffect" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5">对业务影响</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="businessAffect" [(ngModel)]="item.businessAffect" />
            </nz-form-control>
        </nz-form-item>
        }
        <nz-form-item>
            <nz-form-label [nzSpan]="5">{{ currentType === 3 ? '备注': '提出依据'}}</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="description" [(ngModel)]="item.description" />
            </nz-form-control>
        </nz-form-item>
    </form>

    <form nz-form [formGroup]="validateControlForm" *ngIf="input.type === 'control'">
        <nz-form-item *ngIf="currentType === 1">
            <nz-form-label [nzSpan]="4" nzRequired>控制ID</nz-form-label>
            <nz-form-control *ngIf="!input.data?.standardDicId" [nzSpan]="12" [nzErrorTip]="'控制ID必须为6位数'">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId" pattern="^\d{6}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.standardDicId" [nzSpan]="12">
                <input nz-input formControlName="standardDicId" [(ngModel)]="item.standardDicId"
                    [readonly]="input.data?.standardDicId">
            </nz-form-control>
            <div class="msg">
                <label>(请输入6位控制编码ID)</label>
            </div>
        </nz-form-item>
        <nz-form-item *ngIf="currentType === 3">
            <nz-form-label [nzSpan]="4" nzRequired nzDisable>控制编码</nz-form-label>
            <nz-form-control *ngIf="!input.data?.netManageId" [nzSpan]="12" [nzErrorTip]="'控制编码必须为12位数字且第4位为1-6'">
                <input nz-input formControlName="netManageId" [disabled]="input.data" [(ngModel)]="item.netManageId"
                    maxlength="12" pattern="^\d{3}[1-6]\d{8}$">
            </nz-form-control>
            <nz-form-control *ngIf="input.data?.netManageId" [nzSpan]="12">
                <input nz-input formControlName="netManageId" [(ngModel)]="item.netManageId"
                    [readOnly]="input.data?.netManageId">
            </nz-form-control>
            <div class="msg">
                <label>(含顺序号,12位)</label>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>{{ currentType === 3 ? '标准名': '控制标准名'}}</nz-form-label>
            <nz-form-control [nzSpan]="16" nzErrorTip="{{ currentType === 3 ? '标准名不能为空': '控制标准名不能为空'}}">
                <input nz-input formControlName="controlStandardName" [(ngModel)]="item.controlStandardName" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>设备类型</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16" [nzErrorTip]="'设备类型不能为空'">
                <nz-select formControlName="equipmentLogicClassId" nzShowSearch [(ngModel)]="item.equipmentLogicClassId"
                    [nzOptions]="equipmentLogicClassOptions" (ngModelChange)="equipmentLogicClassChange($event)"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('equipmentLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>{{ currentType === 3 ? '逻辑分类': '信号量类型'}}</nz-form-label>
            <nz-form-control [nzSpan]="currentType === 1 ? 15 : 16"
                nzErrorTip="{{ currentType === 3 ? '逻辑分类不能为空': '信号量类型不能为空'}}">
                <nz-select formControlName="controlLogicClassId" nzShowSearch [(ngModel)]="item.controlLogicClassId"
                    [nzOptions]="logicClassOptions" (ngModelChange)="logicClassChange($event,'controlLogicClass')"
                    [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
            <div class="add" *ngIf="currentType === 1">
                <i class="icon icon-add" (click)="openModal('controlLogicClass')"></i>
            </div>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4" nzRequired>局站类型</nz-form-label>
            <nz-form-control [nzSpan]="16" [nzErrorTip]="'局站类型不能为空'">
                <nz-select formControlName="stationCategory" [(ngModel)]="item.stationCategory"
                    [nzOptions]="stationCategoryOptions" [nzDisabled]="currentType === 3 && input.data">
                </nz-select>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">备注</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="description" [(ngModel)]="item.description" />
            </nz-form-control>
        </nz-form-item>
        @if(currentType === 1){
        <nz-form-item>
            <nz-form-label [nzSpan]="4">单位</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="extendFiled1" [(ngModel)]="item.extendFiled1" />
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="4">设备子类</nz-form-label>
            <nz-form-control [nzSpan]="16">
                <input nz-input formControlName="extendFiled2" [(ngModel)]="item.extendFiled2" />
            </nz-form-control>
        </nz-form-item>
        }
    </form>

    <label nz-checkbox [(ngModel)]="setBaseClass" [disabled]="input.data">添加时配置基类{{input.typeName}}</label>

</div>