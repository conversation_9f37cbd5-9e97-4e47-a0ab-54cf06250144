import { Component, ElementRef, Injector, OnInit, ViewChild } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { EditableTip, tableResizeFunc } from 'ng-devui';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-template-station-mapping',
  templateUrl: './template-station-mapping.component.html',
  styleUrls: ['./template-station-mapping.component.less']
})

export class TemplateStationMappingComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;

    editableTip = EditableTip.hover;
    source: any;
    filterSource: any;
    checkedIds: any = [];

    searchText: any = {
        equipmentTemplateName: null,
        id: [],
    }
    searchTimer: any;

    stationCols: any = [
        {
            field: 'equipmentTemplateName',
            width: '80px',
            title: this.translate.instant('customerStandardization.stationMapping.templateName'),
        },
        {
            field: 'id',
            width: '80px',
            title: this.translate.instant('customerStandardization.stationMapping.type'),
        }
    ];

    modelList: any = [];

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '675px';

    // keepalive后记住滚动条位置
    keepScrollTop?: number;

    // 模拟鼠标双击事件
    public clickTimes: Array<Date> = []

    modalBefore: any;
    isEditing: boolean = false;

    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                this.setTabelScroll(event);
            }
        })
        this.getTabelData();
        this.getStationType();
    }

    setTabelScroll(event?: any) {
        // 滚动条回显
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
            if(event && event.url == '/pages/customer-standardization') {
                this.getTabelData();
            }
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
        } catch { /* 呃 好饱 */ }
    }

    getTabelData() {
        this.isUpdating = true;
        this.service.getTemplateStationMapping().then(res => {
            this.isUpdating = false;
            this.source = res;
            this.search();
            this.tabelHeightString = document.documentElement.scrollHeight - 285 + 'px';
        })
    }

    getStationType() {
        this.service.getStationBaseType().then(res => {
            this.modelList = res;
        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            if(this.searchText.equipmentTemplateName) {
                filterList = filterList.filter((item: any) => {
                    return item.equipmentTemplateName ? item.equipmentTemplateName.includes(this.searchText.equipmentTemplateName) : false;
                })
            }
            if(this.searchText.id && this.searchText.id.length > 0) {
                filterList = filterList.filter((item: any) => {
                    let flag = false;
                    this.searchText.id.forEach((obj: any) => {
                        if(obj === item.id) {
                            flag = true;
                        }
                    })
                    return flag;
                })
            }
            this.filterSource = filterList;
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: string): void {
        this.checkedIds = id;
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.stationCols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    getClickTimes(): boolean {
        // 模拟鼠标双击事件
        if (this.clickTimes.length === 0) {
            this.clickTimes.push(new Date())
            return false
        }
        if (this.clickTimes.length === 1 && new Date().valueOf() - this.clickTimes[0].valueOf() > 500) {
            this.clickTimes[0] = new Date()
            return false
        }
        return true
    }

    beforeEditStart = (rowItem: any, field: any) => {
        if(!this.getClickTimes()) {
            return false;
        }
        this.isEditing = true;
        if(field === 'id') {
            this.isEditing = false;
            this.modalBefore = rowItem['id'];
        }
        return true;
    };

    beforeEditEnd = (rowItem: { [x: string]: string | any[]; }, field: string | number) => {
        this.isEditing = false;
        return true;
    };

    selectorChangeConfirm(rowItem: any, field: any) {
        const params = {
            equipmentTemplateId: rowItem.equipmentTemplateId,
            stationCategory: rowItem.id,
        }
        this.service.updateTemplateStationMapping(params).subscribe((res: any) => {
            this.messageService.success('更新成功!');
            this.getTabelData();
        })
        this.isEditing = false;
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: "选择局站类型",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = this.searchText.id;
        modal.afterClose.subscribe((res: any) => {
            this.searchText.id = res;
            this.search();
        })
    }
}