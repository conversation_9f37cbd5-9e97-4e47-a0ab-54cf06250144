import { Component, ElementRef, Injector, OnInit, ViewChild } from '@angular/core'
import { NavigationEnd } from '@angular/router';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { GenericComponent } from '@core/components/basic/generic.component'
import { cloneDeep } from 'lodash';
import { tableResizeFunc } from 'ng-devui';
import { CustomerStandardizationService } from '../customer-standardization.service';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-station-type-mapping',
  templateUrl: './station-type-mapping.component.html',
  styleUrls: ['./station-type-mapping.component.less']
})

export class StationTypeMappingComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    @ViewChild('myTable2') public myDevTable2!: DevuiTableFilterComponent | any;
    
    searchText_1: any = {
        id: null,
        value: null,
    }
    searchText_2: any = {
        counts: null,
        id: null,
        itemId: null,
        itemValue: null,
        type: null,
    }
    searchTimer: any;

    source: any;
    filterSource: any;
    checkedIds: any = [];

    source2: any;
    filterSource2: any;
    checkedIds2: any = [];
    checkAll: boolean = false;

    stationCols: any = [
        {
            field: 'id',
            width: '80px',
            title: this.translate.instant('customerStandardization.stationMapping.id'),
        },
        {
            field: 'value',
            width: '80px',
            title: this.translate.instant('customerStandardization.stationMapping.type'),
        }
    ];

    mappingCols: any = [
        {
            field: 'checked',
            width: '40px',
            title: this.translate.instant('customerStandardization.stationMapping.mapping'),
        },
        {
            field: 'itemId',
            width: '100px',
            title: this.translate.instant('customerStandardization.stationMapping.baseID'),
        },
        {
            field: 'itemValue',
            width: '100px',
            title: this.translate.instant('customerStandardization.stationMapping.baseType'),
        },
        {
            field: 'counts',
            width: '60px',
            title: this.translate.instant('customerStandardization.stationMapping.count'),
        },
        {
            field: 'id',
            width: '100px',
            title: this.translate.instant('customerStandardization.stationMapping.standardizationID'),
        },
        {
            field: 'type',
            width: '100px',
            title: this.translate.instant('customerStandardization.stationMapping.standardizationType'),
        }
    ]

    isUpdating = false;
    minWidth = 40;
    tabelHeightString = '625px';

    // keepalive后记住滚动条位置
    keepScrollTop_1?: number;
    keepScrollTop_2?: number
    
    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private service: CustomerStandardizationService,
        private modal: NzModalService,
    ) {
        super(injector);
    }

    onInit(): void {
        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                this.setTabelScroll(event);
            }
        })

        this.getStandardizationType();
        this.getTabelData();
    }

    setTabelScroll(event?: any) {
        try {
            // 普通表格
            this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop_1;
            this.myDevTable2.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop_2;
            if(event && event.url == '/pages/customer-standardization') {
                this.getMappingData();
            }
            // 开启虚拟滚动表格
            this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop_1;
            this.myDevTable2.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop_2;
          } catch { /* 呃 好饱 */ }
    }

    getStandardizationType() {
        this.service.getStandardType().then(res => {

        })
    }

    getTabelData() {
        Promise.all([
            this.service.getStationBaseType()
        ]).then(res => {
            if(res[0]) {
                let list = res[0].filter((item: any) => {
                    return item.id > 0;
                });
                this.source = list;
                this.filterSource = cloneDeep(this.source);
                this.checkedIds = this.filterSource[0].id;
                this.getMappingData();
            }
        })
    }

    getMappingData() {
        this.isUpdating = true;
        this.service.getBaseStationMappingById(this.checkedIds).then(res => {
            this.isUpdating = false;
            this.source2 = res;
            this.search_2();
            this.tabelHeightString = document.documentElement.scrollHeight - 285 + 'px';
        })
    }

    checkUnmappingData() {
        let list = this.source2.filter((item: any) => {
            return !item.id && item.counts > 0
        })
        if(list && list.length > 0) {
            return true;
        } else {
            return false;
        }
    }

    search_1() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            for(const field in this.searchText_1) {
                if(this.searchText_1[field]) {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.searchText_1[field]) : false;
                    })
                }
            }
            this.filterSource = filterList;
        }, 500)
    }

    search_2() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source2);
            for(const field in this.searchText_2) {
                if(this.searchText_2[field]) {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.searchText_2[field]) : false;
                    })
                }
            }
            this.filterSource2 = filterList;
        }, 500)
    }

    //表格选择事件
    onItemChecked(id: any): void {
        this.checkedIds = id;
        this.getMappingData();
    }

    onItemChecked2(id: string): void {
        this.checkedIds2 = id;
    }

    updateMapping(rowItem: any) {
        let index = this.filterSource2.findIndex((obj: any) => {
            return obj.itemId === rowItem.itemId;
        })
        this.filterSource2[index]['checked'] = !this.filterSource2[index]['checked'];
        if(this.filterSource2[index]['checked']) {
            if(this.filterSource2[index]['id'] > 0) {
                const modal = this.modal.confirm({
                    nzTitle: '提示',
                    nzContent: '该局站类型与标准化局站类型【' + this.filterSource2[index]['id'] + '】已经存在映射关系，是否进行修改？',
                    nzOkText: '确认',
                    nzOkType: 'primary',
                    nzOkDanger: true,
                    nzOnOk: () => {
                        return 'yes'
                    },
                    nzCancelText: '取消',
                    nzOnCancel: () => {
                        return 'no'
                    },
                })
                modal.afterClose.subscribe((res:any) => {
                    if(res === 'no'){
                        this.filterSource2[index]['checked'] = !this.filterSource2[index]['checked'];
                        return;
                    } else {
                        this.filterSource2[index]['id'] = this.checkedIds;
                        this.service.updateStationTypeMapping(this.filterSource2[index]).subscribe((res: any) => {
                            this.messageService.success('更新成功!');
                            this.getMappingData();
                        })
                    }
                })
            } else {
                this.filterSource2[index]['id'] = this.checkedIds;
                this.service.updateStationTypeMapping(this.filterSource2[index]).subscribe((res: any) => {
                    this.messageService.success('更新成功!');
                    this.getMappingData();
                })
            }

        } else {
            this.service.updateStationTypeMapping(this.filterSource2[index]).subscribe((res: any) => {
                this.messageService.success('更新成功!');
                this.getMappingData();
            })
        }
    }

    onAllChecked(checked: boolean): void {
        this.filterSource2.forEach( (item: any) => {
            this.updateCheckedIds(item.samplerId, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.checkedIds2.includes(id)) {
              this.checkedIds2.push(id);
            }
        } else {
            this.checkedIds2.splice(this.checkedIds2.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = this.filterSource2.length && this.filterSource2.every((item: any ) => {
            return this.checkedIds2.includes(item.samplerId)
        });
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.stationCols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    onResize2($event: any, field: any) {
        const func = tableResizeFunc(this.mappingCols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop_1 = event.target.scrollTop
    }

    public tableScrollEvent2(event: any): void {
        this.keepScrollTop_2 = event.target.scrollTop
    }
}