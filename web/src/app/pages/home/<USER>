<!-- <p>home works!</p>
<input nz-input placeholder="Basic usage" [(ngModel)]="test" type="number" />
<br />
<nz-tree [nzData]="nodes" nzDraggable nzBlockNode (nzOnDrop)="nzEvent($event)"></nz-tree> -->
<div style="height: 50px;">
    <button nz-button nzType="primary" (click)="setNewData()">重置数据</button>
    <button nz-button nzType="primary" (click)="setColumn()">重新设置列</button>
    <button nz-button nzType="primary" (click)="loadingClick()">Loading</button>

</div>
<div style="height: calc(100% - 50px);">
    <div style="position: absolute; top: 0; right: 0; bottom: 0; left: 0; background-color: #00000033; display: flex; align-items: center;justify-content: center; z-index: 69;"
        *ngIf="loading">
        <nz-spin nzSimple></nz-spin>
    </div>
    <app-jexcel-table [columns]="columns" [dataSource]="data" (RowDeleted)="onRowDelete($event)" [filters]="true"
        [search]="true" [freezeColumnsCount]="1" (rowChange)="onRowDataChange($event)"
        [contextMenu]="customContextMenu"></app-jexcel-table>
</div>