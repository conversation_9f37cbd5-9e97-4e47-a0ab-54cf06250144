/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, OnInit, inject } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, NonNullableFormBuilder, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { ProtocolManagementService } from '../protocol-management.service';

@Component({
    selector: 'app-protocol-import',
    templateUrl: './protocol-import.component.html',
    styleUrls: ['./protocol-import.component.less']
})
export class ProtocolImportComponent extends GenericComponent {
    showSelectStandard = false;
    equipmentTemplateId: any;
    standardType: any;
    standardTypes: any;
    selecteTip = '';
    // proName?: string;
    // compName?: string;
    // modelName?: string;

    fileList: NzUploadFile[] = [];
    fileList2: NzUploadFile[] = [];
    fileList_2: NzUploadFile[] = [];
    fileList2_2: NzUploadFile[] = [];
    soFileList: NzUploadFile[] = [];
    soFileList2: NzUploadFile[] = [];
    soFileList_2: NzUploadFile[] = [];
    soFileList2_2: NzUploadFile[] = [];
    isUploading: boolean = false;
    uploadType: string = 'protocol';
    path?: string;

    readonly #modal = inject(NzModalRef);

    constructor(
        injector: Injector,
        private message: NzMessageService,
        private service: ProtocolManagementService) {
        super(injector);
    }

    beforeUpload = (file: NzUploadFile): boolean => {
        this.fileList2 = [];
        this.fileList2 = this.fileList2.concat(file);
        return false;
    };

    beforeUpload2 = (file: NzUploadFile): boolean => {
        this.soFileList2 = [];
        this.soFileList2 = this.soFileList2.concat(file);
        return false;
    };

    beforeUpload3 = (file: NzUploadFile): boolean => {
        this.soFileList2_2 = [];
        this.soFileList2_2 = this.soFileList2_2.concat(file);
        return false;
    };

    showSelectStandardSubmit() {
        this.showSelectStandard = false;
        this.service.put('api/config/equipmenttemplate/updatecategory?equipmentTemplateId=' + this.equipmentTemplateId + '&equipmentCategory=' + this.standardType, '').then(res => {
            this.#modal.destroy({ data: 'success' });
        })
    }

    uploadTemp() {
        const formData = new FormData();
        this.fileList2.forEach((file: any) => {
            formData.append('file', file);
        });
        this.service.uploadProtocol(formData).subscribe(
            (res: any) => {
                this.isUploading = false;
                if (res.code === 0) {
                    this.message.success('模板文件上传成功！')
                    // this.#modal.destroy({ data: 'success' });
                    this.path = res.data.protocolCode;
                    let haveEquipmentCategory = res.data.equipmentCategory && res.data.equipmentCategory != 0 && res.data.equipmentCategory != null && res.data.equipmentCategory != "";
                    if (haveEquipmentCategory) {
                        this.equipmentTemplateId = res.data.equipmentTemplateId;
                        this.service.get('api/config/equipmentcategorymap/list/' + res.data.equipmentCategory).then((result: any) => {
                            this.standardTypes = result.data.equipmentCategoryMapList;
                            if (this.standardTypes && this.standardTypes.length > 0) {
                                this.selecteTip = "选择" + result.data.standardName + "标准设备种类";
                                this.showSelectStandard = true;
                            } else {
                                this.#modal.destroy({ data: 'success' });
                            }
                        })
                    }
                    this.upLoadSo(haveEquipmentCategory);
                } else {
                    this.message.error('非法模板文件！')
                }
            },
            error => {
                if (error.error.code === -1) {
                    this.message.error('非法模板文件！')
                }
            }
        )
    }

    upLoadSo(haveBaseType: any) {
        if (this.soFileList2.length > 0) {
            const formData = new FormData();
            this.soFileList2.forEach((file: any) => {
                formData.append('file', file);
            });
            formData.append('protocolCode', this.path ? this.path : '');
            formData.append('protocolType', '1');
            this.service.uploadFile(formData).subscribe(res => {
                this.isUploading = false;
                if (res.code === 0) {
                    this.message.success('335X动态库上传成功！')
                    if (!haveBaseType)
                        this.#modal.destroy({ data: 'success' });
                }
                if (res.code === -1) {
                    this.message.error('非法335X动态库文件！')
                }
            })
        }
        if (this.soFileList2_2.length > 0) {
            const formData = new FormData();
            this.soFileList2_2.forEach((file: any) => {
                formData.append('file', file);
            });
            formData.append('protocolCode', this.path ? this.path : '');
            formData.append('protocolType', '2');
            this.service.uploadFile(formData).subscribe(res => {
                this.isUploading = false;
                if (res.code === 0) {
                    this.message.success('9200动态库上传成功！')
                    if (!haveBaseType)
                        this.#modal.destroy({ data: 'success' });
                }
                if (res.code === -1) {
                    this.message.error('非法9200动态库文件！')
                }
            })
        }
    }

    confirm() {
        if (this.fileList2.length === 0 && this.uploadType === 'protocol') {
            this.message.warning('请先上传模板文件！', {
                nzDuration: 2000,
                nzPauseOnHover: true
            })
            return;
        }
        if (this.soFileList2.length === 0 && this.soFileList2_2.length === 0) {
            this.message.warning('请至少上传一个动态库文件！', {
                nzDuration: 2000,
                nzPauseOnHover: true
            })
            return;
        }
        this.isUploading = true;

        if (this.uploadType === 'protocol') {
            this.uploadTemp();
        }
        if (this.uploadType === 'DLLSO') {
            if (this.soFileList2.length > 0) {
                const formData = new FormData();
                this.soFileList2.forEach((file: any) => {
                    formData.append('file', file);
                });
                formData.append('protocolCode', this.path ? this.path : '');
                formData.append('protocolType', '1');
                this.service.uploadFile(formData).subscribe(res => {
                    this.isUploading = false;
                    if (res.code === 0) {
                        this.message.success('335X动态库上传成功！')
                        this.#modal.destroy({ data: 'success' });
                    }
                    if (res.code === -1) {
                        this.message.error('非法335X动态库文件！')
                    }
                })
            }
            if (this.soFileList2_2.length > 0) {
                const formData = new FormData();
                this.soFileList2_2.forEach((file: any) => {
                    formData.append('file', file);
                });
                formData.append('protocolCode', this.path ? this.path : '');
                formData.append('protocolType', '2');
                this.service.uploadFile(formData).subscribe(res => {
                    this.isUploading = false;
                    if (res.code === 0) {
                        this.message.success('9200动态库上传成功！')
                        this.#modal.destroy({ data: 'success' });
                    }
                    if (res.code === -1) {
                        this.message.error('非法9200动态库文件！')
                    }
                })
            }
        }
    }

    cancel() {
        this.#modal.destroy();
    }
}
