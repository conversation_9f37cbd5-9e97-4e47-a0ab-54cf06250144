<d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true" [tableWidthConfig]="tableColumnConfig"
    [onlyOneColumnSort]="true" [tableHeight]="'300px'" [containFixHeaderHeight]="true" [fixHeader]="true"
    (tableScrollEvent)="tableScrollEvent($event)">
    <thead dTableHead>
        <tr dTableRow>
            <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
                (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)" [minWidth]="colConfig.minWidth!">
                {{ colConfig.title }}
                <!-- 空白 -->
                <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                <!-- 输入框 -->
                <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                    (ngModelChange)="filterChange()" />
            </th>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow>
                <ng-container *ngFor="let col of tableColumnConfig">
                    <td dTableCell [title]="rowItem[col.field]">
                        <div class="span-text">{{ rowItem[col.field] }}</div>
                    </td>
                </ng-container>               
            </tr>
        </ng-template>
    </tbody>
</d-data-table>