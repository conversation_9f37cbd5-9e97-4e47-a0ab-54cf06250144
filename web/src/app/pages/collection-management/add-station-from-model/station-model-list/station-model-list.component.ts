import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { TableColumnConfig, devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model';
import { ApiService } from '@services/api-service';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';


@Component({
    selector: 'app-station-model-list',
    templateUrl: './station-model-list.component.html',
    styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './station-model-list.component.less']
})
export class StationModelListComponent extends DevuiTableFilterComponent implements OnInit {
    // 是否管理模式
    @Input()
    public set isManage(value: any) {
        this._isManage = value
    }
    public _isManage = false;
    showStationModel = false;

    showStationList = false;

    currentModel: any;
    public checkedId: any;
    public selectedItem: any;
    public constructor(
        injector: Injector,
        private apiService: ApiService,
        private nzContextMenuService: NzContextMenuService,
        private message: NzMessageService,
        private modal: NzModalService,
        ele: ElementRef
    ) {
        super(injector, ele)
    }

    // 原数据
    public orgTableDataSource: Array<any> = []
    public tableColumnConfig: TableColumnConfig[] = [
        {
            field: 'swatchStationName',
            width: '180px',
            title: '样板站名称',
        },
        {
            field: 'stationName',
            width: '180px',
            title: '样板局站',
            minWidth: '200',
        },
        {
            field: 'createTime',
            width: '150px',
            title: '创建时间',
            minWidth: '200',
        },
        {
            field: 'description',
            width: '150px',
            title: '备注',
            minWidth: '200',
        }
    ]

    // 方法
    protected onInit(): void {
        super.onInit();
        this.requestTableDataSoure();
    }

    public contextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        this.nzContextMenuService.create($event, menu);
    }

    //表格选择事件
    onItemChecked(item: any): void {
        this.checkedId = item.swatchStationId;
        this.selectedItem = item;
    }

    // 请求数据
    async requestTableDataSoure() {
        await this.apiService.get('api/config/swatchstation/list').then((res: any) => {
            this.orgTableDataSource = res.data;
            if (res.data && res.data.length > 0)
                this.checkedId = res.data[0].swatchStationId;
            this.filterTableDataSource = [...this.orgTableDataSource]
            this.displayTableDataSource = [...this.orgTableDataSource]
        })
    }
    editModel(item: any) {
        this.currentModel = item;
        this.showStationModel = true;
    }
    deleteModel(item: any) {
        this.currentModel = item;
        this.modal.confirm({
            nzTitle: '删除样板站',
            nzContent: '确认要删除样板站[' + this.currentModel.swatchStationName + ']?',
            nzOkText: this.translate.instant('common.ok'),
            nzOkType: 'primary',
            nzOkDanger: true,
            nzOnOk: () => {
                this.apiService.delete('api/config/swatchstation/delete/' + this.currentModel.swatchStationId, { observe: 'response' }).then((res: any) => {
                    this.message.success('删除样板站成功！')
                    setTimeout(() => {
                        this.requestTableDataSoure();
                    }, 500);
                })
            },
            nzCancelText: this.translate.instant('common.cancel'),
            // nzOnCancel: () => console.log('Cancel')
        });
    }
    showStations(item: any) {
        this.currentModel = item;
        this.showStationList = true;
    }
    showStationModelSubmit() {
        this.apiService.put('api/config/swatchstation/config', this.currentModel).then((res: any) => {
            this.message.success('修改样板站成功！')
            this.showStationModel = false;
        })
    }
    showStationListSubmit() {
        this.showStationList = false;
    }
}