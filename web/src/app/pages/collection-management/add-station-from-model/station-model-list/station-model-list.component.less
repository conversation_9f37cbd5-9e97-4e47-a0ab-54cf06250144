@import '../../../../../.././src/styles/mixin.less';

:host {
    td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }


    .span-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .red_text {
        color: red
    }

    .orange_text {
        color: #FF7700
    }

    .green_text {
        color: #068306
    }

    .blue_text {
        color: #0000FF
    }

    .no_text {
        color: #999999
    }

    .nodeName {
        padding-left: 10px;
        font-size: 12px;
    }

    .table-row-selected td {
        background-color: #73b9ff !important;
    }

    d-data-table {
        border: 1px solid #c4c4c4 !important;
    }

}

.dti_info_item_group {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 12px;

    .dti_info_item {
        display: flex;
        flex-direction: row;
        align-items: center;

        .dti_info_title {
            width: 120px;
            text-align: right;
            margin-right: 12px;
        }

        .dti_info_input {
            width: 300px !important;
        }

        .dti_info_textarea {
            width: 300px !important;
        }
    }
}