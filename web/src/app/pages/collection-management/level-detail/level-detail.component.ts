import { Component, Injector, Input, OnChanges, SimpleChanges } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { ApiService } from '@services/api-service';
import { NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-level-detail',
  templateUrl: './level-detail.component.html',
  styleUrl: './level-detail.component.less'
})
export class LevelDetailComponent extends GenericComponent implements OnChanges {
  @Input('currentLevelNode')
  set currentLevelNode(value: any) {
    if (this._currentLevelNode?.stationId != value?.stationId) {
      this._currentLevelNode = value;
      this.getNodeDetail();
    }
    this._currentLevelNode = value;
  };
  _currentLevelNode: any;

  nodeDetail: any = {};
  stationCategories: any[] = [];
  stationGrades: any[] = [];
  stationProjectState: any[] = [];
  agents: any[] = [];
  contacts: any[] = [];
  isInit = false;


  public constructor(injector: Injector, private message: NzMessageService, private apiService: ApiService) {
    super(injector)
  }

  public onInit(): void {
    this.apiService.get('api/config/dataitems?entryId=71').then(res => {
      if (res && res.data && res.data) {
        this.stationCategories = res.data as [];
      }
    })
    this.apiService.get('api/config/dataitems?entryId=2').then(res => {
      if (res && res.data && res.data) {
        this.stationGrades = res.data as [];
      }
    })
    this.apiService.get('api/config/dataitems?entryId=5').then(res => {
      if (res && res.data && res.data) {
        this.stationProjectState = res.data as [];
      }
    })
    this.apiService.get('api/config/dataitems?entryId=15').then(res => {
      if (res && res.data && res.data) {
        this.agents = res.data as [];
        this.agents = this.agents.filter(s => s.itemId > 0)
      }
    })
    this.apiService.get('api/config/employee/list/employeeIdAndDepartmentId').then(res => {
      if (res && res.data && res.data) {
        this.contacts = res.data as [];
      }
    })
  }

  getNodeDetail() {
    if (this._currentLevelNode && this._currentLevelNode.stationId)
      this.apiService.get('api/config/station/config/' + this._currentLevelNode.stationId).then(res => {
        if (res.data)
          this.nodeDetail = res.data;
        this.isInit = true;
      }).catch(error => {

      })
  }

  public ngOnChanges(changes: SimpleChanges): void {
    // console.log('属性变化', changes)
  }

  // 添加设备
  public addDevice(device: any): void {
    console.log(device)
  }

  // 编辑设备
  public editDevice(device: any): void {
    console.log(device)
  }

  // 删除设备
  public deleteDevice(device: any): void {
    console.log(device)
  }

  public submit() {
    if (!this.nodeDetail.stationName || this.nodeDetail.stationName.trim() == '') {
      this.messageService.info("请输入必填项");
      return;
    }
    this.apiService.put('api/config/station/config', this.nodeDetail).then(res => {
      this.nodeDetail = res.data;
      this.message.success('更新成功！')
    }).catch(error => {
    })
  }

  // // 创建行右键菜单
  // public rowContextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
  //   $event.stopPropagation()
  //   this.nzContextMenuService.create($event, menu)
  // }

  // // 创建空白处右键菜单
  // public bgContextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
  //   this.nzContextMenuService.create($event, menu)
  // }
}

