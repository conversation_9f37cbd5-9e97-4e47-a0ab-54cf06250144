:host {
  .ant-tabs {
    line-height: 14px;
  }

  nz-sider.nz-resizable-resizing {
    transition: none;
  }

  nz-content {
    margin: 0 0 0 12px !important;

    div {
      width: 100%;
    }
  }

  nz-content .resizable-box {
    flex: none;
  }

  .nz-resizable-handle.nz-resizable-handle-right.nz-resizable-handle-cursor-type-grid {
    width: 5px;
    right: -2.5px;
    margin-top: -2px;

    .sider-resize-line {
      margin-top: 32px;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      transition-delay: 0.1s;

      &:hover {
        background-color: #afb8c133;
      }

      &>div {
        width: 1px;
        height: 100%;
        background-color: #d0d7de;
      }
    }
  }

  .dtm_top_top {
    width: 100%;
    height: 30px;
    color: #333333;
    padding: 4px;
    padding-bottom: 5px;
    text-align: left;
    border-bottom: 1px solid #CCCCCC;
  }

  .dtm_mu_op {
    display: flex;
    flex-direction: row;
    height: 40px;
    width: 100%;
    align-items: center;
    padding: 4px;
  }

  .dtm_bottom {
    width: 100%;

    nz-tree {
      overflow: hidden;
    }

    /deep/.ant-tree-treenode {
      padding: 0 !important;
    }

    .custom-node {
      cursor: pointer;
      line-height: 28px;

      .ng-star-inserted {
        display: flex !important;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
      }

      .nodeName {
        padding-left: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .dtm_mu {
    width: 100%;
    overflow: scroll;
    padding-right: 0;

    nz-list {
      border-left: none;
      border-right: none;
      height: 100%;

      // overflow:hidden;
      // height: 100%;
      nz-list-item {
        // display:contents
        cursor: pointer !important;
        padding: 0;

        nz-list-item-meta {
          padding: 5px 10px;
        }

        :hover {
          background-color: #C4E2FF;
        }
      }
    }

    .active {
      // color: white !important;
      background-color: #C4E2FF !important;
    }
  }

  .refreshButton {
    float: right !important;
    color: #007FFF;
    cursor: pointer;
    font-size: 16px;
    padding-top: 3px;
    padding-right: 2px;

    &:hover {
      color: #00BFFF;
    }
  }

  .dtm_mid {
    width: 100%;
    padding: 4px;
  }

  .dtm_right {
    height: 100%;
    width: calc(100vw - 528px);
    margin-left: 16px;
    background-color: #FFF;
  }

  .dtm_right {
    height: 100%;
    width: 100%;
    // margin-left: 16px;
    background-color: #FFF;

    /deep/.ant-tabs-content {
      height: 100%;
    }
  }

  .right-menu-icon {
    font-size: 16px;

    ::ng-deep svg {
      margin-right: 8px;
    }
  }
}

// .ant-dropdown-menu-item,
// .ant-dropdown-menu-submenu-title {
//   padding-left: 0 !important;
// }

.ant-tabs {
  line-height: 14px;
}

.dti_info_item_group {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 12px;

  .dti_info_item {
    display: flex;
    flex-direction: row;
    align-items: center;

    .dti_info_title {
      width: 120px;
      text-align: right;
      margin-right: 12px;
    }

    .tip {
      text-align: center;
      width: 100%;
      color: #FF7373;
    }

    .dti_info_input {
      width: 300px !important;
    }
  }
}