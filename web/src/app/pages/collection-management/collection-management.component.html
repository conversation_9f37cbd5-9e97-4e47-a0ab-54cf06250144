<nz-layout id="levelLayout">
  <nz-sider [nzWidth]="siderWidth" nz-resizable [nzMinWidth]="150" [nzMaxWidth]="450" (nzResize)="onSideResize($event)">
    <nz-resize-handle nzDirection="right" nzCursorType="grid">
      <div class="sider-resize-line">
        <div></div>
      </div>
    </nz-resize-handle>
    <div style="display: flex; flex-wrap: wrap; overflow: hidden; width: 100%;">
      <div class="dtm_top_top"> 端局树 <span nz-icon nzType="sync" class="refreshButton"
          title="{{ 'levelManage.refresh' | translate }}" (click)="refreshTree()"></span>
        <span style="float: right;margin-top: 2px;margin-right: 6px;" title="样板站管理" (click)="showStationModel=true">
          <iconfont icon="icon-moban" class="refreshButton"></iconfont>
        </span>
        <span style="float: right;margin-top: 2px;margin-right: 6px;" title="层级同步" (click)="showLevelSyncModel=true">
          <iconfont icon="icon-tongbu" class="refreshButton"></iconfont>
        </span>
      </div>
      <div class="dtm_mid">
        <nz-input-group [nzSuffix]="inputClearTpl">
          <input nz-input style="text-indent: 5px;" placeholder="输入关键字查询" (ngModelChange)="onSearchChange($event)"
            (keyup.enter)="focusNode()" [(ngModel)]="searchValue" type="text" />
        </nz-input-group>
        <ng-template #inputClearTpl>
          @if (searchValue) {
          <span nz-icon class="ant-input-clear-icon" nzTheme="fill" nzType="close-circle"
            (click)="searchValue = ''"></span>
          }
        </ng-template>
      </div>
      <div class="dtm_bottom">
        <nz-tree #nzTree [nzData]="nodes" [nzBlockNode]="true" [nzSearchValue]="searchValue" nzDraggable
          [nzBeforeDrop]="beforeDrop" [nzVirtualHeight]="nzTreeHeight" [nzExpandedKeys]="defaultExpandedKeys"
          [nzSelectedKeys]="defaultSelectedKeys" [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
        <ng-template #nzTreeTemplate let-node let-origin="origin">
          <span class="custom-node ant-tree-title" (click)="clickNode(origin)">
            <span *ngIf="true" (contextmenu)="contextMenu($event, menu)">
              <iconfont icon="icon-xinghao" style="font-size: 12px; color: #FF7373"
                *ngIf="origin.stationId && !origin.houseId && origin.bordNumber === 0">
              </iconfont>
              <iconfont icon="icon-fangjian1" style="font-size: 12px; color: #FF7373"
                *ngIf="origin.stationId && !origin.houseId && (origin.bordNumber === 1 || origin.bordNumber === null || origin.bordNumber === undefined)">
              </iconfont>
              <span nz-icon nzType="bank" *ngIf="origin.parentStructureId!==0 && !origin.stationId"></span>
              <span nz-icon nzType="home" *ngIf="origin.houseId"></span>
              <span nz-icon nzType="global" *ngIf="origin.parentStructureId==0 && !origin.stationId"></span>
              <span class="nodeName" [innerHTML]="highlightSearchText(origin.title)"></span>
            </span>
          </span>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item (click)="addStationGroup(origin)" *ngIf="origin.structureId && !origin.stationId">
                <iconfont icon="icon-add" class="right-menu-icon"
                  title="{{ 'levelManage.addStationGroup' | translate }}">
                </iconfont>
                添加分组
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
              <li nz-menu-item (click)="deleteStationGroup(origin)"
                *ngIf="origin.structureId && !origin.stationId && !(origin.isUngroup && origin.structureGroupId==1)">
                <iconfont icon="icon-delete2" class="right-menu-icon" title="删除局站分组">
                </iconfont>
                删除分组
              </li>
              <li nz-menu-item (click)="renameStationGroup(origin)" *ngIf="origin.structureId && !origin.stationId">
                <iconfont icon="icon-edit-set" class="right-menu-icon" title="重命名">
                </iconfont>
                重命名
              </li>
              <li nz-menu-item (click)="upStation(origin)" *ngIf="origin.stationId && !origin.houseId">
                <iconfont icon="icon-move-up" class="right-menu-icon" title="升级为样板站"></iconfont>
                升级为样板站
              </li>
              <li nz-menu-item (click)="reNameStation(origin)"
                *ngIf="origin.stationId && !origin.houseId && origin.stationId>0">
                <iconfont icon="icon-edit-set" class="right-menu-icon" title="重命名">
                </iconfont>
                重命名
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"
                *ngIf="!origin.houseId || !origin.stationId"></nz-divider>
              <li nz-menu-item (click)="deleteStation(origin)" *ngIf="origin.stationId && !origin.houseId && origin.stationId>0">
                <iconfont icon="icon-delete2" class="right-menu-icon" title="删除局站">
                </iconfont>
                删除局站
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"
                *ngIf="!origin.houseId || !origin.stationId"></nz-divider>
              <li nz-menu-item (click)="addStation(origin)" *ngIf="origin.structureId && !origin.stationId">
                <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.addStation' | translate }}">
                </iconfont>
                {{ 'levelManage.addStation' | translate }}
              </li>
              <li nz-menu-item (click)="addStationFromModel(origin)" *ngIf="origin.structureId && !origin.stationId">
                <iconfont icon="icon-add" class="right-menu-icon"
                  title="{{ 'levelManage.addStationFromModel' | translate }}">
                </iconfont>
                {{ 'levelManage.addStationFromModel' | translate }}
              </li>
              <li nz-menu-item (click)="addStationsFromModel(origin)" *ngIf="origin.structureId && !origin.stationId">
                <iconfont icon="icon-add" class="right-menu-icon"
                  title="{{ 'levelManage.addStationsFromModel' | translate }}">
                </iconfont>
                {{ 'levelManage.addStationsFromModel' | translate }}
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"
                *ngIf="!origin.houseId || !origin.stationId"></nz-divider>
              <li nz-menu-item (click)="addStationHouse(origin)" *ngIf="origin.stationId && !origin.houseId">
                <iconfont icon="icon-add" class="right-menu-icon"
                  title="{{ 'levelManage.addStationHouse' | translate }}">
                </iconfont>
                {{ 'levelManage.addStationHouse' | translate }}
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"
                *ngIf="!origin.houseId || !origin.stationId"></nz-divider>
              <li nz-menu-item (click)="deleteStationHouse(origin)" *ngIf="origin.houseId && origin.stationId">
                <iconfont icon="icon-delete2" class="right-menu-icon" title="删除局房">
                </iconfont>
                删除局房
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
              <li nz-menu-item (click)="renameStationHouse(origin)" *ngIf="origin.houseId && origin.stationId">
                <iconfont icon="icon-edit-set" class="right-menu-icon" title="重命名">
                </iconfont>
                重命名
              </li>
              <li nz-menu-item (click)="addMu(origin)" *ngIf="origin.stationId && !origin.houseId">
                <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.addMu' | translate }}">
                </iconfont>
                {{ 'levelManage.addMu' | translate }}
              </li>
              <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
              <li nz-menu-item (click)="addEqFromMenu(origin)" *ngIf="origin.stationId">
                <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.addEquipment' | translate }}">
                </iconfont>{{
                'levelManage.addEquipment' | translate }}
              </li>
            </ul>
          </nz-dropdown-menu>
        </ng-template>
      </div>
    </div>
  </nz-sider>
  <nz-sider [nzWidth]="siderMuWidth" nz-resizable [nzMinWidth]="150" [nzMaxWidth]="450"
    (nzResize)="onSideMuResize($event)" *ngIf="selectedNode?.stationId">
    <nz-resize-handle nzDirection="right" nzCursorType="grid">
      <div class="sider-resize-line">
        <div></div>
      </div>
    </nz-resize-handle>
    <div style="display: flex; flex-wrap: wrap; overflow: hidden; width: 100%;">
      <div class="dtm_top_top"> 监控单元列表
        <span *ngIf="muData.length>0" style="float: right;margin-top: 2px;margin-right: 6px;" title="生成和分发监控单元"
          (click)="muDistribute()">
          <iconfont icon="icon-deliveryconf" class="refreshButton"></iconfont>
        </span>
        <span nz-icon nzType="plus" class="refreshButton" (click)="addMu()" title="新增监控单元"></span>
      </div>
      <div class="dtm_mu_op">
        <nz-input-group [nzSuffix]="inputClearTplmu">
          <input nz-input style="text-indent: 5px;" placeholder="{{'输入关键字查询     共' + muData.length + '个MU'}}"
            (ngModelChange)="filterChange($event)" [(ngModel)]="searchMu" type="text" />
        </nz-input-group>
        <ng-template #inputClearTplmu>
          @if (searchMu) {
          <span nz-icon class="ant-input-clear-icon" nzTheme="fill" nzType="close-circle"
            (click)="filterChange('')"></span>
          }
        </ng-template>
      </div>
      <div class="dtm_mu">
        <nz-list [nzLoading]="loading" [style.height]="nzTreeHeight" style="overflow: auto;">
          <nz-list-item *ngFor="let item of muShowData" (click)="onSelectMu(item)" [ngClass]="{active: isActive(item)}"
            (contextmenu)="muContextmenu($event, menu2)">
            <nz-list-item-meta nzAvatar="/assets/images/FSU.svg">
              <nz-list-item-meta-title> {{item.monitorUnitName}}</nz-list-item-meta-title>
              <nz-list-item-meta-description *ngIf="item.monitorUnitCategory!=24 && item.monitorUnitCategory!=1">
                {{ (item.muTypeName? item.muTypeName+' [':'')+ (item.ipAddress? item.ipAddress+']':'') }}
              </nz-list-item-meta-description>
              <nz-list-item-meta-description *ngIf="item.monitorUnitCategory==24 || item.monitorUnitCategory==1">
                {{ (item.muTypeName? item.muTypeName+' [':'')+ (item.workStationName? item.workStationName+']':'') }}
              </nz-list-item-meta-description>
            </nz-list-item-meta>
            <nz-dropdown-menu #menu2="nzDropdownMenu">
              <ul nz-menu nzSelectable>
                <li nz-menu-item (click)="editMu(item)">
                  <iconfont icon="icon-edit-set" class="right-menu-icon" title="{{ 'levelManage.editMu' | translate }}">
                  </iconfont>
                  {{ 'levelManage.editMu' | translate }}
                </li>
                <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
                <li nz-menu-item (click)="deleteMu(item)">
                  <iconfont icon="icon-delete2" class="right-menu-icon"
                    title="{{ 'levelManage.deleteMu' | translate }}">
                  </iconfont>
                  {{ 'levelManage.deleteMu' | translate }}
                </li>
                <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
                <li nz-menu-item (click)="toMu(item)">
                  <iconfont icon="icon-xinxi-" class="right-menu-icon" title="打开采集器">
                  </iconfont>
                  打开采集器
                </li>
                <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
                <li nz-menu-item (click)="muDistribute(item)">
                  <iconfont icon="icon-deliveryconf" class="right-menu-icon" title="下发配置">
                  </iconfont>
                  下发配置
                </li>
                <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
                <li nz-menu-item (click)="addPort(item)">
                  <iconfont icon="icon-add" class="right-menu-icon"
                    title="{{ 'levelManage.samplerInfo.addPort' | translate }}">
                  </iconfont>
                  {{ 'levelManage.samplerInfo.addPort' | translate }}
                </li>
                <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
                <li nz-menu-item (click)="addEqFromMu(item)">
                  <iconfont icon="icon-add" class="right-menu-icon"
                    title="{{ 'levelManage.addEquipment' | translate }}">
                  </iconfont>{{
                  'levelManage.addEquipment' | translate }}
                </li>
              </ul>
            </nz-dropdown-menu>
          </nz-list-item>
          <nz-list-empty *ngIf="muData.length === 0" />
        </nz-list>
      </div>
    </div>
  </nz-sider>
  <nz-content>
    <div style="height: 100%;">
      <nz-tabset nzType="card" style="height: 100%;" [nzTabBarExtraContent]="extraTemplate"
        *ngIf="selectedNode?.stationId">
        <nz-tab *ngIf="selectedMu" (nzSelect)="onSelectChange()"
          nzTitle="{{ 'levelManage.samplerInfo.title' | translate }}">
          <app-level-sampler-info [currentMu]="selectedMu" [currentLevelNode]="selectedNode"></app-level-sampler-info>
        </nz-tab>
        <nz-tab (nzSelect)="onSelectChange()" nzTitle="{{ 'levelManage.deviceList.title' | translate }}">
          <app-level-device-list [currentMu]="selectedMu" [muList]="muData"
            [currentLevelNode]="selectedNode"></app-level-device-list>
        </nz-tab>
        <nz-tab *ngIf="!selectedMu && selectedNode && selectedNode.stationId && !selectedNode.houseId"
          nzTitle="{{ 'levelManage.levelNode.detail' | translate }}">
          <app-level-detail [currentLevelNode]="selectedNode"></app-level-detail>
        </nz-tab>
      </nz-tabset>
      <ng-template #extraTemplate>
        {{this.selectedMu ? '监控单元' :(this.selectedNode.houseId? '局房':'局站')}}:
        {{ this.selectedMu ? this.selectedMu.monitorUnitName+' [局站：'+this.currentStationName+ ']' :
        (this.selectedNode ? (this.selectedNode?.houseName? this.selectedNode?.houseName+'
        [局站：'+this.selectedNode?.hStationName+ ']' : this.selectedNode?.stationName) : '') }}
      </ng-template>
    </div>
  </nz-content>
  <nz-modal [(nzVisible)]="showAddNewStationGroup" nzTitle="{{stationGroupTitle}}"
    (nzOnCancel)="showAddNewStationGroup=false" (nzOnOk)="addStationGroupSubmit()">
    <ng-container *nzModalContent>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="dti_info_title"><span style="color: red;">* </span>分组名称</label>
          <input class="dti_info_input" nz-input id="newStationGroupName" [(ngModel)]="newStationGroupName" />
        </div>
      </div>
    </ng-container>
  </nz-modal>
  <nz-modal [(nzVisible)]="showAddNewStationHouse" nzTitle="{{stationHouseTitle}}"
    (nzOnCancel)="showAddNewStationHouse=false" (nzOnOk)="addStationHouseSubmit()">
    <ng-container *nzModalContent>
      <div class="dti_info_item_group" *ngIf="stationHouseTitle !== '修改局房'">
        <div class="dti_info_item">
          <label class="dti_info_title">局站编号</label>
          <input class="dti_info_input" nz-input id="StationNo" disabled [ngModel]="selectedNode.stationId" />
        </div>
      </div>
      <div class="dti_info_item_group" *ngIf="stationHouseTitle !== '修改局房'">
        <div class="dti_info_item">
          <label class="dti_info_title">局站名称</label>
          <input class="dti_info_input" nz-input id="StationName" disabled [ngModel]="selectedNode.stationName" />
        </div>
      </div>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="dti_info_title"><span style="color: red;">* </span>局房名称</label>
          <input class="dti_info_input" nz-input id="newStationHouseName" [(ngModel)]="newStationHouseName" />
        </div>
      </div>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="dti_info_title">备注</label>
          <input class="dti_info_input" nz-input maxlength="254" id="newStationHouseDesc"
            [(ngModel)]="newStationHouseDesc" />
        </div>
      </div>
    </ng-container>
  </nz-modal>
  <nz-modal [(nzVisible)]="showStationModel" nzWidth="720px" nzTitle="样板站管理" (nzOnCancel)="showStationModel=false"
    (nzOnOk)="showStationModel=false">
    <ng-container *nzModalContent>
      <app-station-model-list id="myTable" #myTable isManage="true"></app-station-model-list>
    </ng-container>
  </nz-modal>
  <nz-modal [(nzVisible)]="showLevelSyncModel" nzWidth="450px" nzTitle="层级同步" (nzOnCancel)="showLevelSyncModel=false"
    (nzOnOk)="levelSyncModelSubmit()">
    <ng-container *nzModalContent>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="tip">请谨慎操作：该操作会以采集结构树为基准生成层级结构树</label>
        </div>
      </div>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="tip">1.同步会冲掉已经建好的层级结构</label>
        </div>
      </div>
      <div class="dti_info_item_group">
        <div class="dti_info_item">
          <label class="tip">2.机房不会同步</label>
        </div>
      </div>
    </ng-container>
  </nz-modal>
</nz-layout>