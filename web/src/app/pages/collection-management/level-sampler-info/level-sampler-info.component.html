<div class="dtm_bottom" style="width: 100%;height: 100%;" (contextmenu)="emptyContextMenu($event, emptyMenu)">
  <nz-tree style="padding-left: 60px;" #sampleTree [nzData]="nodes" [nzShowLine]="true" [nzVirtualHeight]="nzTreeHeight"
    (nzClick)="nzEvent($event)" [nzExpandAll]="true" [nzBlockNode]="false" [nzShowExpand]="true"
    [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
  <ng-template #nzTreeTemplate let-node let-origin="origin">
    <span class="custom-node" (click)="clickNode(origin)">
      <span *ngIf="origin.samplerUnits" (contextmenu)="contextMenu($event, menu)">
        <span nz-icon nzType="apartment" nzTheme="outline"></span>
        <span class="nodeName">{{ origin.portName }}</span>
      </span>
      <span *ngIf="origin.equipments" (contextmenu)="contextMenu($event, menu)">
        <span nz-icon nzType="api" nzTheme="outline"></span>
        <span class="nodeName">{{ origin.samplerUnitName }}</span>
      </span>
      <span *ngIf="!origin.samplerUnits && !origin.equipments" (contextmenu)="contextMenu($event, menu)"
        (dblclick)="onEqClick(origin)">
        <span nz-icon nzType="hdd" nzTheme="outline"></span>
        <span class="nodeName">{{ origin.equipmentName }}</span>
      </span>
    </span>
    <nz-dropdown-menu #menu="nzDropdownMenu">
      <ul nz-menu style="overflow: hidden;">
        <!-- <li nz-menu-item (click)="addPort()" *ngIf="origin.portId">
          <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.samplerInfo.addPort' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.addPort' | translate }}
        </li> -->
        <li *ngIf="origin.samplerUnits" nz-menu-item (click)="updatePort(origin)">
          <iconfont icon="icon-edit-set" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.updatePort' | translate }}">
          </iconfont> {{ 'levelManage.samplerInfo.updatePort' | translate }}
        </li>
        <li *ngIf="origin.samplerUnits" nz-menu-item (click)="deletePort(origin)">
          <iconfont icon="icon-delete2" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.deletePort' | translate }}">
          </iconfont> {{ 'levelManage.samplerInfo.deletePort' | translate }}
        </li>
        <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
        <li *ngIf="origin.samplerUnits" nz-menu-item (click)="addSamplerUnit(origin)">
          <iconfont icon="icon-add" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.addSamplerUnit' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.addSamplerUnit' | translate }}
        </li>
        <li *ngIf="origin.equipments" nz-menu-item (click)="updateSamplerUnit(origin)">
          <iconfont icon="icon-edit-set" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.updateSamplerUnit' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.updateSamplerUnit' | translate }}
        </li>
        <li *ngIf="origin.equipments" nz-menu-item (click)="deleteSamplerUnit(origin)">
          <iconfont icon="icon-delete2" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.deleteSamplerUnit' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.deleteSamplerUnit' | translate }}
        </li>
        <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
        <li *ngIf="!origin.equipments && !origin.samplerUnits" nz-menu-item (click)="updateDevice(origin)">
          <iconfont icon="icon-edit-set" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.updateDevice' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.updateDevice' | translate }}
        </li>
        <li *ngIf="!origin.equipments && !origin.samplerUnits" nz-menu-item (click)="deleteDevice(origin)">
          <iconfont icon="icon-delete2" class="right-menu-icon"
            title="{{ 'levelManage.samplerInfo.deleteDevice' | translate }}">
          </iconfont>{{ 'levelManage.samplerInfo.deleteDevice' | translate }}
        </li>
        <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
        <li nz-menu-item *ngIf="_currentMu && _currentLevelNode.stationId && origin.samplerUnits" (click)="addDevice(origin)">
          <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.addEquipment' | translate }}">
          </iconfont> {{ 'levelManage.addEquipment' | translate }}
        </li>
      </ul>
    </nz-dropdown-menu>
  </ng-template>
  <nz-dropdown-menu #emptyMenu="nzDropdownMenu">
    <ul nz-menu style="overflow: hidden;">
      <li nz-menu-item (click)="addPort()" *ngIf="_currentMu">
        <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.samplerInfo.addPort' | translate }}">
        </iconfont>{{ 'levelManage.samplerInfo.addPort' | translate }}
      </li>
      <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
      <li nz-menu-item *ngIf="_currentMu  && _currentLevelNode.stationId" (click)="addDevice(null)">
        <iconfont icon="icon-add" class="right-menu-icon" title="{{ 'levelManage.addEquipment' | translate }}">
        </iconfont>{{ 'levelManage.addEquipment' | translate }}
      </li>
    </ul>
  </nz-dropdown-menu>
</div>