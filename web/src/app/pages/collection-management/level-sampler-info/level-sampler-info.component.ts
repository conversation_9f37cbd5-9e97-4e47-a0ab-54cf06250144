import { ChangeDetectorRef, Component, Injector, Input, ViewChild } from '@angular/core';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzFormatEmitEvent, NzTreeComponent } from 'ng-zorro-antd/tree';
import { ApiService } from '@services/api-service';
import { AddPortComponent } from '../add-port/add-port.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddSamplerComponent } from '../add-sampler/add-sampler.component';
import { title } from 'process';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AddDeviceComponent } from '../add-device/add-device.component';

@Component({
  selector: 'app-level-sampler-info',
  styleUrls: ['./level-sampler-info.component.less'],
  templateUrl: './level-sampler-info.component.html',
})
export class LevelSamplerInfoComponent extends GenericComponent {
  resizeObserver: ResizeObserver | undefined;
  @Input('currentMu')
  set currentMu(value: any) {
    this._currentMu = value;
    this.getMuSampleTree();
  };
  _currentMu: any;

  @Input('currentLevelNode')
  set currentLevelNode(value: any) {
    this._currentLevelNode = value;
  };
  _currentLevelNode: any;

  public nodes: any = [];
  nzTreeHeight: any;
  @ViewChild('sampleTree') sampleTree!: NzTreeComponent;

  public constructor(injector: Injector, private apiService: ApiService,
    private modal: NzModalService,
    private message: NzMessageService,
    private cdr: ChangeDetectorRef,
    private nzContextMenuService: NzContextMenuService) {
    super(injector)
  }

  public onInit(): void {
    this.nzTreeHeight = (document.body.offsetHeight - 150).toString() + 'px';
  }

  protected onAfterViewInit(): void {
    this.resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setTimeout(() => {
          this.nzTreeHeight = (document.body.offsetHeight - 150).toString() + 'px';
        }, 0);
        this.cdr.detectChanges();
      }
    });
    this.resizeObserver.observe(document.body);
  }

  protected onDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  getMuSampleTree() {
    if (this._currentMu && this._currentMu.monitorUnitId)
      this.apiService.get('api/config/monitor-unit/sampler-tree/' + this._currentMu.monitorUnitId).then(res => {
        let tNodes: any = res.data;
        this.processTree(tNodes);
        this.nodes = tNodes;
        if (this.sampleTree)
          this.sampleTree.cdkVirtualScrollViewport.checkViewportSize();
      })
  }

  processTree(data: any) {
    if (!data)
      return;
    data.forEach((node: any) => {
      node.expanded = true;
      if (node.samplerUnits) {
        if (node.samplerUnits.length > 0)
          node.children = node.samplerUnits;
        node.key = 'port_' + node.portId;
        node.title = node.portName;
      } else if (node.equipments) {
        if (node.equipments.length > 0)
          node.children = node.equipments;
        node.key = 'sam_' + node.samplerUnitId;
        node.title = node.samplerUnitName;
      } else {
        node.key = 'eq_' + node.equipmentId;
        node.title = node.equipmentName;
      }

      node.isLeaf = node.children ? false : true;
      this.processTree(node.children)
    });
  }

  clickNode(node: any) {

  }

  public contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    $event.preventDefault();
    $event.stopPropagation(); // 阻止事件冒泡
    this.nzContextMenuService.create($event, menu);
  }

  public emptyContextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    $event.preventDefault();
    this.nzContextMenuService.create($event, menu);
  }

  // 添加端口
  public addPort(): void {
    if (!this._currentMu)
      return;
    const model = this.openDialog<AddPortComponent, any, any>({
      nzTitle: this.translate.instant('levelManage.samplerInfo.addPort'),
      nzContent: AddPortComponent,
      nzWidth: 850,
      nzData: {
        currentMuData: this._currentMu,
        port: null
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddPortComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddPortComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('新增成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
          break;
      }
    })
  }

  // 修改端口
  public updatePort(port: any): void {
    const model = this.openDialog<AddPortComponent, any, any>({
      nzTitle: this.translate.instant('levelManage.samplerInfo.updatePort'),
      nzContent: AddPortComponent,
      nzWidth: 850,
      nzData: {
        currentMuData: this._currentMu,
        port: port
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddPortComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddPortComponent): void => {
            instance.editConfirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('修改成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
          break;
      }
    })
  }

  // 删除端口
  public deletePort(port: any): void {
    this.modal.confirm({
      nzTitle: this.translate.instant('mu.confirmDeletePort'),
      nzContent: port.portName,
      nzOkText: this.translate.instant('common.ok'),
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.apiService.delete('api/config/port/config/' + port.portId, { observe: 'response' }).then(res => {
          this.message.success('删除成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
        })
      },
      nzCancelText: this.translate.instant('common.cancel'),
      // nzOnCancel: () => console.log('Cancel')
    });
  }

  // 添加采集单元
  public addSamplerUnit(port: any): void {
    if (!this._currentMu)
      return;
    const model = this.openDialog<AddSamplerComponent, any, any>({
      nzTitle: this.translate.instant('sampler.new'),
      nzContent: AddSamplerComponent,
      nzWidth: 500,
      nzData: {
        mu: this._currentMu,
        port: port
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddSamplerComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddSamplerComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('新增成功！')
          this.getMuSampleTree();
          break;
      }
    })
  }

  // 修改采集单元
  public updateSamplerUnit(samplerUnit: any): void {
    const model = this.openDialog<AddSamplerComponent, any, any>({
      nzTitle: this.translate.instant('sampler.edit') + '[' + samplerUnit.samplerUnitId + ']',
      nzContent: AddSamplerComponent,
      nzWidth: 500,
      nzData: {
        mu: this._currentMu,
        sampler: samplerUnit
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddSamplerComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddSamplerComponent): void => {
            instance.editConfirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('修改成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
          break;
      }
    })
  }

  onEqClick(eq: any) {
    this.router.navigateByUrl('/pages/device-management/' + eq.equipmentId + '?title=' + eq.equipmentName).finally();
  }

  // 删除采集单元
  public deleteSamplerUnit(samplerUnit: any): void {
    this.modal.confirm({
      nzTitle: this.translate.instant('sampler.confirmDelete'),
      nzContent: '[' + samplerUnit.samplerUnitId + ']' + samplerUnit.samplerUnitName,
      nzOkText: this.translate.instant('common.ok'),
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.apiService.delete('api/config/sampler-unit/config/' + samplerUnit.samplerUnitId, { observe: 'response' }).then(res => {
          this.message.success('删除成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
        })
      },
      nzCancelText: this.translate.instant('common.cancel'),
      // nzOnCancel: () => console.log('Cancel')
    });
  }

  // 修改设备
  public updateDevice(device: any): void {
    this.onEqClick(device);
  }

  // 删除设备
  public deleteDevice(device: any): void {
    this.modal.confirm({
      nzTitle: '警告',
      nzContent: '确认要删除设备' + device.equipmentName + '吗?',
      nzOkText: this.translate.instant('common.ok'),
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.apiService.delete('api/config/equipment/config/' + device.equipmentId, { observe: 'response' }).then(res => {
          this.message.success('删除成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
        })
      },
      nzCancelText: this.translate.instant('common.cancel'),
      // nzOnCancel: () => console.log('Cancel')
    });
  }

  // 添加设备
  public addDevice(node: any): void {
    let houseId = -1;
    let stationName: any;
    if (this._currentLevelNode?.houseId) {
      houseId = this._currentLevelNode?.houseId;
      stationName = this._currentLevelNode?.hStationName;
    }
    else if (this._currentLevelNode.houses && this._currentLevelNode.houses.length > 0) {
      houseId = this._currentLevelNode.houses[0].houseId;
      stationName = this._currentLevelNode.stationName;
    }
    if (houseId == -1) {
      this.message.warning('请先为局站新建局房！');
      return;
    }

    const model = this.openDialog<AddDeviceComponent, any, any>({
      nzTitle: this.translate.instant('levelManage.addEquipment'),
      nzContent: AddDeviceComponent,
      nzWidth: 788,
      nzData: {
        muList: this._currentMu ? [this._currentMu] : [],
        currentNode: this._currentLevelNode,
        currentMuId: this._currentMu?.monitorUnitId,
        currentHouseId: houseId,
        stationName: stationName,
        currentPort: (node?.samplerUnits) ? node : null
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddDeviceComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddDeviceComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('新增成功！')
          setTimeout(() => {
            this.getMuSampleTree();
          }, 500);
          break;
      }
    })
  }

  public nzEvent(event: NzFormatEmitEvent): void {
    console.log(event);
  }
}
