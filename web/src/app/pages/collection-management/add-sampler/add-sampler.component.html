<div class="dti_container">
  <div class="dti_info">
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'sampler.collector'|translate}}</label>
        <nz-select row="4" [nzDisabled]="isEdit" class="dti_select" [(ngModel)]="selectedSampler"
          (ngModelChange)="onChangeSampler()">
          <nz-option *ngFor="let item of samplers" [nzValue]="item" [nzLabel]="item.samplerName"></nz-option>
        </nz-select>
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'sampler.name'|translate}}</label>
        <input class="dti_info_input" maxlength="128" nz-input [(ngModel)]="sampler.samplerUnitName" type="text" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'sampler.address'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="sampler.address" max="32767" min="0" placeholder="(0~32767)"
          type="number" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'sampler.dll'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="sampler.dllPath" type="text" />
      </div>
    </div>
    <div class="dti_info_item_group" *ngIf="!isEdit">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'sampler.port'|translate}}</label>
        <input class="dti_info_input" [disabled]=true nz-input [(ngModel)]="sampler.portNo" type="text" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'sampler.span'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="sampler.spUnitInterval" type="number" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'sampler.tel'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="sampler.phoneNumber" />
      </div>
    </div>
  </div>
</div>