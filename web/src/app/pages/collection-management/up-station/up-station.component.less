@import '../../../../.././src/styles/mixin.less';

.ant-form-item {
    line-height: 20px !important;
    /* 你想要设置的行高 */
    margin-bottom: 12px;
}

.ant-form-item-label>label::after {
    margin-right: 10px;
}

.ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
    margin-right: 4px;
}

.dti_info_item {
    display: flex;
    flex-direction: row;
    align-items: center;

    .dti_info_title {
        width: 120px;
        text-align: right;
        margin-right: 12px;
    }

    .dti_select {
        width: 250px;
    }

    .dti_info_input {
        width: 250px;
    }

    .dti_info_textarea {
        width: 250px;
    }
}

.dti_info_item_group {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 12px;
}

.dti_desc {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 8px;

    .dti_desc_title {
        width: 145px;
        text-align: right;
        margin-right: 16px;
    }
}