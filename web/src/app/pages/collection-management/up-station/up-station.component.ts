import { Component, Injector, ViewChild } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';

@Component({
  selector: 'app-up-station',
  templateUrl: './up-station.component.html',
  styleUrl: './up-station.component.less'
})
export class UpStationComponent extends GenericModalComponent<any, any> {
  upStationName = '';
  description = '';
  currentNode: any;

  public constructor(
    injector: Injector,
    private apiService: ApiService
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.getData();
  }

  getData() {
    if (this.input.currentNode) {
      this.currentNode = this.input.currentNode;
    }
  }

  confirm() {
    if (this.upStationName.trim() == ''){
      this.messageService.error('请输入样板站名称')
      return;
    }
      
    let upStation =
    {
      "swatchStationName": this.upStationName,
      "stationId": this.currentNode.stationId,
      "stationName": this.currentNode.stationName,
      "description": this.description
    }
    this.apiService.post('api/config/swatchstation/config', upStation).then(res => {
      this.close({ action: 'confirm', level: res.data })
    })
  }
}
