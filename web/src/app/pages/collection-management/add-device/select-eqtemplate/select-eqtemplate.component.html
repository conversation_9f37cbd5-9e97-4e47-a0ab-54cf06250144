<div class="dtm_mid" style="width: 100%;margin-bottom: 5px;">  
  <nz-input-group [nzSuffix]="inputClearTpl">
    <input nz-input style="text-indent: 5px;" placeholder="输入关键字查询" (ngModelChange)="onSearchChange($event)"
      (keyup.enter)="selectNode()" [(ngModel)]="searchValue" type="text" />
  </nz-input-group>
  <ng-template #inputClearTpl>
    @if (searchValue) {
      <span
        nz-icon
        class="ant-input-clear-icon"
        nzTheme="fill"
        nzType="close-circle"
        (click)="searchValue = ''"
      ></span>
    }
  </ng-template>
</div>
<div style="display: flex; justify-content: space-between; height: 400px;">
  <nz-tree #nzTree style="border: 1px solid #b5bad6; width: 100%; overflow: scroll;" [nzData]="nodes"
    [nzSearchValue]="searchValue" nzVirtualHeight="390px" [nzSelectedKeys]="selectedKeys" [nzExpandedKeys]="expendKeys"
    nzShowLine (nzClick)="nzEvent($event)" (nzDblClick)="nzDbEvent($event)"></nz-tree>
</div>