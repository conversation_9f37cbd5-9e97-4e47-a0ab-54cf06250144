import { Component, Injector, ViewChild } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import { StationTempListComponent } from './station-temp-list/station-temp-list.component';

@Component({
  selector: 'app-add-station',
  templateUrl: './add-station.component.html',
  styleUrl: './add-station.component.less'
})
export class AddStationComponent extends GenericModalComponent<any, any> {
  nodeDetail: any = {};
  pNodeName = ''
  currentNode: any;
  showTemplate = false;
  showTypes: any = [];
  jzs: any = [];
  jfs: any = [];
  isJz = 'true'

  @ViewChild('myTable') public myStationTempTable: StationTempListComponent | any;
  public constructor(
    injector: Injector,
    private apiService: ApiService
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.getData();
    let projectName = this.sessionService.get<string>('lastprojectName');
    let contractNo = this.sessionService.get<string>('lastcontractNo');
    if (projectName)
      this.nodeDetail.projectName = projectName;
    if (contractNo)
      this.nodeDetail.contractNo = contractNo;
  }

  getData() {
    if (this.input.parentNode)
      this.pNodeName = this.input.parentNode.structureName;
    if (this.input.currentNode) {
      this.currentNode = this.input.currentNode;
      this.nodeDetail.stationName = this.input.currentNode.stationName;
    }
    // if (this.input.type)
    //   this.nodeDetail.stationCategory = this.input.type;
    this.apiService.get('api/config/station/categorizedataitemsbytype').then((res: any) => {
      this.jzs = res.data['基站'];
      this.jfs = res.data['机房'];
      // Initialize showTypes based on current isJz value
      this.onTypeChange(this.isJz);
    })
  }

  confirm() {
    if (!this.nodeDetail.stationName || this.nodeDetail.stationName.trim() == '' || !this.nodeDetail.stationCategory || !this.nodeDetail.projectName || !this.nodeDetail.contractNo) {
      this.messageService.info("请输入必填项");
      return;
    }

    // this.nodeDetail.stationTemplateId = this.myStationTempTable.checkedId;
    this.nodeDetail.stationTemplateId = 0;
    let newStation =
    {
      "stationName": this.nodeDetail.stationName,
      "stationCategory": this.nodeDetail.stationCategory,
      "projectName": this.nodeDetail.projectName,
      "contractNo": this.nodeDetail.contractNo,
      "stationStructureId": this.input.parentNode.structureId,
      "stationTemplateId": this.nodeDetail.stationTemplateId,
      "bordNumber": this.isJz === 'true' ? 0 : 1  // 添加bordnumber参数，基站为0，机房为1
    }
    this.apiService.post('api/config/station/config', newStation).then(res => {
      this.sessionService.set<string>('lastprojectName', this.nodeDetail.projectName);
      this.sessionService.set<string>('lastcontractNo', this.nodeDetail.contractNo);
      this.close({ action: 'confirm', level: res.data })
    })
  }

  onTypeChange(e: any) {
    if (e == 'true')
      this.showTypes = this.jzs || []
    else
      this.showTypes = this.jfs || []

    // Only set stationCategory if showTypes has elements
    if (this.showTypes && this.showTypes.length > 0) {
      this.nodeDetail.stationCategory = this.showTypes[0].itemId;
    }
  }

  onInputChange(): void {
    // 替换掉特殊字符
    let name = this.nodeDetail.stationName.replace(/[\\\/\?<>*:.|""]/g, '');
    this.nodeDetail.stationName = name;
  }

  rename() {
    if (!this.nodeDetail.stationName || this.nodeDetail.stationName.trim() == '') {
      this.messageService.info("请输入必填项");
      return;
    }
    this.apiService.put('api/config/station/rename', {
      "stationId": this.currentNode.stationId,
      "stationName": this.nodeDetail.stationName
    }).then(res => {
      this.close({ action: 'rename', level: res.data })
    })
  }
}
