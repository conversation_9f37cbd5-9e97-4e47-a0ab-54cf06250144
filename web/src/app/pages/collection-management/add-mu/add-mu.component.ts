import { Component, ElementRef, Injector } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import _ from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-add-mu',
  templateUrl: './add-mu.component.html',
  styleUrl: './add-mu.component.less'
})
export class AddMuComponent extends GenericModalComponent<any, any> {
  muData: any = { monitorUnitName: '', ipAddress: '', monitorUnitCategory: 18, workStationId: '', dataServer: [], rdsServer: [], projectName: '', contractNo: '', description: '' };
  rdsServers: any = [];
  dataServers: any = [];
  rmuServers: any = [];
  dataServer: any;
  rdsServer: any;
  monitorUnitCategories: any = [];
  currentLevel: any = {};
  public constructor(
    private fb: NonNullableFormBuilder,
    injector: Injector,
    private ele: ElementRef,
    private apiService: ApiService,
    private message: NzMessageService
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.getTyps();
    this.getServers();
  }

  getTyps() {
    this.apiService.get('api/config/monitor-unit/types').then(res => {
      this.monitorUnitCategories = res.data;
      this.monitorUnitCategories.sort((a: any, b: any) => a.order - b.order);
      if (!this.muData.monitorUnitId)
        this.muData.monitorUnitCategory = this.monitorUnitCategories[0].typeId;
    })
  }

  getServers() {
    this.apiService.get('api/config/workstation/server-source-list').then((res: any) => {
      res.data.forEach((element: any) => {
        element.workStationId = element.workStationId.toString();
        if (element.workStationType == 2) {
          // element.value = element.workStationId.toString();
          this.dataServers.push(element)
        }
        if (element.workStationType == 8) {
          this.rmuServers.push(element)
        }
        if (element.workStationType == 23) {
          // element.value = element.workStationId.toString();
          this.rdsServers.push(element)
        }
      });
      this.getData();
    })
  }

  getData() {
    if (this.input.muData != null) {
      let theData = _.cloneDeep(this.input.muData);
      if (theData.rdsServer)
        this.rdsServer = theData.rdsServer.split(', ');
      if (theData.dataServer)
        this.dataServer = theData.dataServer.split(', ');
      if (theData.workStationId)
        theData.workStationId = theData.workStationId.toString();
      this.muData = theData;
      // if (!theData.monitorUnitId && this.monitorUnitCategories.length > 0)
      // this.muData.monitorUnitCategory = this.monitorUnitCategories[0].typeId;
    }
    if (this.input.currentLevel != null) {
      this.currentLevel = this.input.currentLevel;
    }
  }

  confirm() {
    if (!this.muData.monitorUnitName || this.muData.monitorUnitName.trim() == '' ||
      !this.muData.ipAddress || this.muData.ipAddress.trim() == '' ||
      !this.muData.projectName || this.muData.projectName.trim() == '' ||
      !this.muData.contractNo || this.muData.contractNo.trim() == '') {
      this.message.info("请输入必填项");
      return;
    }
    if (Array.isArray(this.rdsServer))
      this.muData.rdsServer = this.rdsServer.join(', ');
    if (Array.isArray(this.dataServer))
      this.muData.dataServer = this.dataServer.join(', ');
    this.muData.stationId = this.currentLevel.stationId;
    this.apiService.post('api/config/monitor-unit/v3/config', this.muData).then(res => {
      this.sessionService.set<string>('lastprojectName', this.muData.projectName);
      this.sessionService.set<string>('lastcontractNo', this.muData.contractNo);
      this.close({ action: 'confirm', level: res.data })
    })
  }
  editConfirm() {
    if (!this.muData.monitorUnitName || this.muData.monitorUnitName.trim() == '' ||
      !this.muData.ipAddress || this.muData.ipAddress.trim() == '' ||
      !this.muData.projectName || this.muData.projectName.trim() == '' ||
      !this.muData.contractNo || this.muData.contractNo.trim() == '') {
      this.message.info("请输入必填项");
      return;
    }
    this.muData.rdsServer = this.rdsServer?.join(', ');
    this.muData.dataServer = this.dataServer?.join(', ');
    this.apiService.put('api/config/monitor-unit/v3/config', this.muData).then(res => {
      this.sessionService.set<string>('lastprojectName', this.muData.projectName);
      this.sessionService.set<string>('lastcontractNo', this.muData.contractNo);
      this.close({ action: 'confirm', level: res.data })
    })
  }
  onInputChange(): void {
    // 替换掉特殊字符    
    let name = this.muData.monitorUnitName.replace(/[\\\/\?<>*:.|""]/g, '');
    this.muData.monitorUnitName = name;
  }
}
