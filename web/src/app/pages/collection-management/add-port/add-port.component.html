<div class="dti_container">
  <div class="dti_info">
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'mu.muName'|translate}}</label>
        <input class="dti_info_input" [disabled]="true" nz-input [(ngModel)]="currentMuData.monitorUnitName" type="text" />
      </div>
      <div class="dti_info_item">
        <label class="dti_info_title">{{'mu.muNo'|translate}}</label>
        <input class="dti_info_input" [disabled]="true" nz-input [(ngModel)]="currentMuData.monitorUnitId" type="text" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'mu.portName'|translate}}</label>
        <input class="dti_info_input" [disabled]="true" nz-input [(ngModel)]="port.portName" type="text" />
      </div>
      <div class="dti_info_item">
        <label class="dti_info_title"><i class="star"></i>{{'mu.portNo'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="port.portNo" type="number" (ngModelChange)="portChange()" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'mu.portType'|translate}}</label>
        <nz-select row="4" class="dti_select" [(ngModel)]="port.portType" (ngModelChange)="onTypeChange()">
          <nz-option *ngFor="let item of showTypes" [nzValue]="item.typeId" [nzLabel]="item.typeName"></nz-option>
        </nz-select>
        <nz-switch class="custom-switch" (ngModelChange)="onChange($event)" [(ngModel)]="isAll" nzCheckedChildren="所有" nzUnCheckedChildren="所有"></nz-switch>
      </div>
      <div class="dti_info_item">
        <label class="dti_info_title" style="width: 60px;">{{'mu.tel'|translate}}</label>
        <input class="dti_info_input" nz-input [(ngModel)]="port.phoneNumber" type="text" />
      </div>
    </div>
    <div class="dti_info_item_group">
      <div class="dti_info_item">
        <label class="dti_info_title">{{'mu.set'|translate}}</label>
        <input rows="4" class="dti_info_textarea" nz-input [(ngModel)]="port.setting"/>
      </div>
    </div>
  </div>
</div>
