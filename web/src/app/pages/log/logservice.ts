import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'

@Injectable()
export class LogService extends RestfulService {

    /**
    * 获取操作类型
    * @returns
    */
    public async getOperationType(): Promise<[]> {
        const res = await this.get<[]>(`api/config/operationdetailtype`)
        return res.data
    }

    /**
    * 获取全部日志列表
    * @returns
    */
    public async getLogList(page:any, params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/operationdetail/page?size=${page.size}&current=${page.current}`, params)
        return res.data
    }

    /**
    * 获取设备模板日志列表
    * @returns
    */
    public async getEquipmentTemplateLogList(page:any, params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/operationdetail/page/equipmenttemplate?size=${page.size}&current=${page.current}`, params)
        return res.data
    }

    /**
    * 获取设备日志列表
    * @returns
    */
    public async getEquipmentLogList(page:any, params: any): Promise<[]> {
        const res = await this.post<[]>(`api/config/operationdetail/page/equipment?size=${page.size}&current=${page.current}`, params)
        return res.data
    }
}
