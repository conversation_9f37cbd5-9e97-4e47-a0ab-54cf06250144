<div class="ts-line">
  <label nz-radio [(ngModel)]="radioTime" (click)="radioClick('radioTime')" style="width: 100%;">按设定时间触发一次</label>
</div>
<div class="ts-line">
  <nz-date-picker nzShowTime [(ngModel)]="valueTime" [nzAllowClear]="false" (nzOnOk)="onOk()"
    style="width: calc(50% - 6px);" />
</div>
<div class="ts-line">
  <label nz-radio [(ngModel)]="radioTimeRange" (click)="radioClick('radioTimeRange')"
    style="width: 100%;">在设定时间段内按周期定时触发</label>
</div>
<div class="ts-line">
  <nz-date-picker [nzDisabledDate]="disabledStartDate" [nzAllowClear]="false" nzShowTime [(ngModel)]="startValue"
    (nzOnOk)="onOk()" style="flex: 1; margin-right: 12px;" />
  <nz-date-picker [nzDisabledDate]="disabledEndDate" [nzAllowClear]="false" nzShowTime [(ngModel)]="endValue"
    (nzOnOk)="onOk()" #endDatePicker style="flex: 1;" />
</div>
<form nz-form [formGroup]="validateForm" style="display: flex;">
  <div style="width: 50%;">
    <nz-form-item>
      <nz-form-label [nzSpan]="9.5" nzRequired>触发周期（秒）</nz-form-label>
      <nz-form-control [nzSpan]="16" nzErrorTip="请输入正整数" style="margin-right: 6px;">
        <input nz-input formControlName="triggerCycle" [(ngModel)]="triggerCycle" (ngModelChange)="inputChange()"
          style="width: 100%;" />
      </nz-form-control>
    </nz-form-item>
  </div>
  <div style="width: 50%;">
    <nz-form-item>
      <nz-form-label [nzSpan]="9.5" nzRequired>触发次数（次）</nz-form-label>
      <nz-form-control [nzSpan]="16" nzErrorTip="请输入正整数">
        <input nz-input formControlName="triggerTimes" [(ngModel)]="triggerTimes" (ngModelChange)="inputChange()"
          style="width: 100%;" />
      </nz-form-control>
    </nz-form-item>
  </div>
</form>
<div class="ts-line">
  <span style="flex-shrink: 0; line-height: 32px; margin-left: 11px;">时间触发表达式</span>
  <span style="margin: 0 8px 0 2px; line-height: 32px;">:</span>
  <input nz-input [(ngModel)]="startExpression" [disabled]="true" />
</div>