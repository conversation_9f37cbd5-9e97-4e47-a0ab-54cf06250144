<div class="container">
  <div class="lc-top">
    <div>
      <div class="top-part-title">设备</div>
      <div class="top-part-filter">
        <nz-input-group [nzSuffix]="suffixIconSearch">
          <input nz-input [(ngModel)]="filterData.equipmentName" (ngModelChange)="filterChange()" />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <span nz-icon nzType="search"></span>
        </ng-template>
      </div>
      <div class="top-part-content">
        <div class="cell-content" *ngFor="let item of displayDeviceList" (click)="deviceRowClick(item)">
          <span [ngClass]="{ 'table-row-selected': item.$checked }">{{ item.equipmentName }}</span>
        </div>
      </div>
    </div>
    <div>
      <div class="top-part-title">控制</div>
      <div class="top-part-content">
        <div class="cell-content" *ngFor="let item of controlList" (click)="controlRowClick(item)">
          <span [ngClass]="{ 'table-row-selected': item.$checked }">{{ item.controlName }}</span>
        </div>
      </div>
    </div>
    <div>
      <div class="top-part-title">控制参数</div>
      <div class="top-part-content">
        <div class="cell-content" *ngFor="let item of meaningList; let i = index" (click)="meaningRowClick(item, i)">
          <span [ngClass]="{ 'table-row-selected': item.$checked }">{{ item.meanings }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="lc-bottom">
    <div class="table-head">
      <div>设备</div>
      <div>控制</div>
      <div>参数值</div>
      <div>操作</div>
    </div>
    <div class="table-body">
      <div class="tr" *ngFor="let item of resultList; let i = index" [ngClass]="{ 'table-row-selected': item.$checked }"
        (click)="resultRowClick(item)">
        <div [title]="item.equipmentName">{{ item.equipmentName }}</div>
        <div [title]="item.controlName">{{ item.controlName }}</div>
        <div [title]="item.meanings">{{ item.meanings }}</div>
        <div>
          <iconfont [icon]="'icon-delete2'" title="移除" (click)="deleteClick(i)"></iconfont>
        </div>
      </div>
    </div>
  </div>
</div>