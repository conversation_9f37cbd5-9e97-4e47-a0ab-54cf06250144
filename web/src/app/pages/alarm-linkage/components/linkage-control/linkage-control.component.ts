import { Component, Injector } from '@angular/core'
import { GenericModalComponent } from '@core/components/basic/generic.modal'
import { AlarmLinkageService } from '../../alarm-linkage.service'
import { LinkageControlInput } from './linkage-control-model'

@Component({
  selector: 'app-linkage-control',
  templateUrl: './linkage-control.component.html',
  styleUrls: ['./linkage-control.component.less']
})

export class LinkageControlComponent extends GenericModalComponent<LinkageControlInput, undefined> {

  public constructor(
    injector: Injector,
    private service: AlarmLinkageService,
  ) {
    super(injector)
  }
  // 过滤字段
  public filterData: any = {}
  // 设备
  public deviceList: Array<any> = []
  public displayDeviceList: Array<any> = []
  // 控制
  public controlList: Array<any> = []
  // 含义
  public meaningList: Array<any> = []
  public resultList: Array<any> = []


  // 方法
  protected async onInit(): Promise<void> {
    await this.initConfig()
    if (this.input.rowItem!.controlLogActions?.length) {
      this.setDefault()
    }
  }

  // 初始化
  public async initConfig(): Promise<void> {
    // i18

    // 请求
    this.requestEquipmentListWithId()
  }

  // 数据回显
  public setDefault(): void {
    this.input.rowItem?.controlLogActions?.forEach(item => {
      item.ids = `${item.equipmentId}-${item.controlId}-${item.actionValue}`
      this.resultList.push(item)
    })
  }

  // 过滤
  public filterChange(): void {
    let displayList = [...this.deviceList]
    displayList = displayList.filter((item: any) => {
      return (item.equipmentName !== undefined) ? (item.equipmentName + '').toUpperCase().includes(this.filterData.equipmentName.toUpperCase()) : false
    })
    this.displayDeviceList = displayList
  }

  // 设备 点击设备
  public async deviceRowClick(rowItem: any): Promise<void> {
    this.deviceList.forEach(item => {
      item.$checked = false
    })
    rowItem.$checked = true
    this.requestControlList(rowItem.equipmentTemplateId)
  }

  // 控制 点击事件
  public controlRowClick(rowItem: any): void {
    this.controlList.forEach(item => {
      item.$checked = false
    })
    rowItem.$checked = true
    this.meaningList = rowItem.controlMeaningsList
  }

  // 含义 点击事件
  public meaningRowClick(rowItem: any, rowIndex: number): void {
    this.meaningList.forEach(item => {
      item.$checked = false
    })
    rowItem.$checked = true
    const checkedDevice = this.deviceList.filter(item => item.$checked === true)[0]
    const checkedControl = this.controlList.filter(item => item.$checked === true)[0]
    const ret = {
      actionId: ++rowIndex,
      logActionId: this.input.rowItem?.logActionId,
      equipmentId: checkedDevice.equipmentId,
      equipmentName: checkedDevice.equipmentName,
      controlId: checkedControl.controlId,
      controlName: checkedControl.controlName,
      actionValue: rowItem.parameterValue,
      actionName: this.input.rowItem?.actionName,
      meanings: rowItem.meanings,
      ids: `${checkedDevice.equipmentId}-${checkedControl.controlId}-${rowItem.parameterValue}`
    }
    const index = this.resultList.findIndex(item => item.ids === ret.ids)
    if (index === -1) {
      this.resultList.push(ret)
    }
  }

  // 结果 点击事件
  public resultRowClick(rowItem: any): void {
    this.resultList.forEach(item => {
      item.$checked = false
    })
    rowItem.$checked = true
  }

  // 移除
  public deleteClick(rowIndex: number): void {
    this.resultList.splice(rowIndex, 1)
  }

  // 确定
  public async confirm(): Promise<void> {

  }

  // 请求 设备列表
  public async requestEquipmentListWithId(): Promise<void> {
    await this.service.getEquipmentList(this.input.rowItem!.monitorUnitId!).then(res => {
      this.deviceList = res
      this.displayDeviceList = [...this.deviceList]
    })
  }

  // 请求 控制列表
  public async requestControlList(equipmentTemplateId: number): Promise<void> {
    await this.service.getControlList(equipmentTemplateId).then(res => {
      this.controlList = res
      if (this.controlList.length) {
        this.controlRowClick(this.controlList[0])
      } else {
        this.meaningList = []
      }
    })
  }
}
