import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'
import { AlarmLinkageEntity, MonitorUnitEntity } from './alarm-linkage.model'
@Injectable()
export class AlarmLinkageService extends RestfulService {

  public constructor(private http: HttpClient) {
    super(http)
  }

  /**
  * 告警联动 列表
  * @returns
  */
  public async getAlarmLinkageList(): Promise<AlarmLinkageEntity[]> {
    const res = await this.get<AlarmLinkageEntity[]>('/api/config/eventlogaction/list')
    return res.data
  }

  /**
  * 监控单元 增加
  * @returns
  */
  public async addAlarmLinkage(params: AlarmLinkageEntity): Promise<MonitorUnitEntity[]> {
    const res = await this.post<MonitorUnitEntity[]>('/api/config/eventlogaction/create', params)
    return res.data
  }

  /**
  * 监控单元 修改
  * @returns
  */
  public async putAlarmLinkage(params: AlarmLinkageEntity): Promise<MonitorUnitEntity[]> {
    const res = await this.put<MonitorUnitEntity[]>('/api/config/eventlogaction/update', params)
    return res.data
  }

  /**
  * 监控单元 删除
  * @returns
  */
  public async delAlarmLinkage(logActionIds: string): Promise<string> {
    const res = await this.delete<string>(`/api/config/eventlogaction/delete?logActionIds=${logActionIds}`, {} as any)
    return res.data
  }

  /**
  * 监控单元 可选项
  * @returns
  */
  public async getMonitorUnitList(): Promise<MonitorUnitEntity[]> {
    const res = await this.get<MonitorUnitEntity[]>('/api/config/monitor-unit/stationname')
    return res.data
  }

  /**
  * 设备列表，携带Id
  * @returns
  */
  public async getEquipmentListWithId(monitorUnitId: number): Promise<any[]> {
    const res = await this.get<any[]>(`/api/config/equipment/simplifysplicelist?monitorUnitId=${monitorUnitId}&spliceFlag=true`)
    return res.data
  }

  /**
  * 信号列表
  * @returns
  */
  public async getSignalList(equipmentTemplateId: number): Promise<any[]> {
    const res = await this.get<any[]>(`/api/config/signal/simplifysignals?equipmentTemplateId=${equipmentTemplateId}`)
    return res.data
  }

  /**
  * 事件列表
  * @returns
  */
  public async getEventList(equipmentTemplateId: number): Promise<any[]> {
    const res = await this.get<any[]>(`/api/config/event/simplifyeventcondition?equipmentTemplateId=${equipmentTemplateId}`)
    return res.data
  }

  /**
  * 设备列表
  * @returns
  */
  public async getEquipmentList(monitorUnitId: number): Promise<any[]> {
    const res = await this.get<any[]>(`/api/config/equipment/simplifysplicelist?monitorUnitId=${monitorUnitId}`)
    return res.data
  }

  /**
  * 控制
  * @returns
  */
  public async getControlList(equipmentTemplateId: number): Promise<any[]> {
    const res = await this.get<any[]>(`/api/config/control/list?equipmentTemplateId=${equipmentTemplateId}`)
    return res.data
  }
}
