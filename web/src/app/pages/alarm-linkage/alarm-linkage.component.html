<div style="height: 40px; display: flex;">
  <nz-space>
    <button *nzSpaceItem nz-button nzType="primary" (click)="addClick()">
      <span nz-icon nzType="plus"></span>新增
    </button>
    <button *nzSpaceItem nz-button nzType="primary" nzDanger (click)="deleteClick()"
      [disabled]="checkedIds.length === 0">
      <span nz-icon nzType="delete"></span>批量删除
    </button>
  </nz-space>
</div>
<div style="height: calc(100% - 40px);">
  <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
    [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'100%'"
    [containFixHeaderHeight]="true" [fixHeader]="true" (tableScrollEvent)="tableScrollEvent($event)">
    <thead dTableHead>
      <tr dTableRow>
        <th dHeadCell>
          <input type="checkbox" [ngModel]="checkAll" (change)="onAllChecked(!checkAll)">
        </th>
        <ng-container *ngFor="let colConfig of tableColumnDisplay">
          <th dHeadCell [sortable]="colConfig.field !== '$index'"
            (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
            (resizeEndEvent)="onResize($event, colConfig.field)" [minWidth]="colConfig.minWidth!">
            {{ colConfig.title }}
            <div *ngIf="colConfig.field === '$index' || colConfig.field === 'operate'" style="height: 32px;"></div>
            <input *ngIf="colConfig.field !== '$index' && colConfig.field !== 'operate'" nz-input [(ngModel)]="filterData[colConfig.field]"
              (ngModelChange)="filterChange()" />
          </th>
        </ng-container>
      </tr>
    </thead>
    <tbody dTableBody>
      <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
        <tr dTableRow [ngClass]="{ 'table-row-selected': rowItem.$checked }">
          <td dTableCell class="devui-checkable-cell">
            <label class="checkbox-inline custom-checkbox nowrap">
              <input type="checkbox" [ngModel]="checkedIds.includes(rowItem.logActionId)"
                (change)="onItemChecked(rowItem.logActionId, !checkedIds.includes(rowItem.logActionId))">
              <span></span>
            </label>
          </td>
          @for (colConfig of tableColumnDisplay; track colConfig) {
          @if (colConfig.field === '$index') {
          <td dTableCell>{{ rowIndex + 1 }}</td>
          }@else if(colConfig.field === 'controlLogActions') {
          <td dTableCell [title]="controlLogActionsTitle(rowItem)" (click)="onRowDBClick(rowItem)">
            {{ controlLogActionsSet(rowItem) }}
          </td>
          }@else if(colConfig.field === 'operate') {
          <td>
            <iconfont [icon]="'icon-edit-set'" title="修改" (click)="editClick(rowItem)"></iconfont>
            <iconfont [icon]="'icon-delete2'" title="删除" (click)="deleteClick(rowItem)"></iconfont>
          </td>
          }@else {
          <td dTableCell [title]="rowItem[colConfig.field]">{{ rowItem[colConfig.field] }}</td>
          }
          }
        </tr>
      </ng-template>
    </tbody>
  </d-data-table>
</div>