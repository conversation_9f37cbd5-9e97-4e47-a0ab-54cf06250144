/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, OnInit } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, NonNullableFormBuilder, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { GenericComponent } from '@core/components/basic/generic.component';
import { Observable, Observer } from 'rxjs';
import { ConfigInitService } from './config-init.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-config-init',
  templateUrl: './config-init.component.html',
  styleUrls: ['./config-init.component.less']
})
export class ConfigInitComponent extends GenericModalComponent<any, any> {
  validateForm: FormGroup<{
    name: FormControl<string>;
    ip: FormControl<string>;
    area: FormControl<string>;
    // scense: FormControl<string>;
    client: FormControl<string>;
  }>;
  clientList = [
    {
      standardId: '0',
      standardName: '维谛'
    },
    {
      standardId: '1',
      standardName: '移动'
    },
    {
      standardId: '2',
      standardName: '电信'
    },
    {
      standardId: '3',
      standardName: '联通'
    }
  ];
  scenceList = [
    {
      scenseId: '1',
      scenseName: 'IDC'
    },
    {
      scenseId: '2',
      scenseName: '网点'
    },
  ]
  isSpinning = false;
  currentClicent = '';
  areaRegex = /^\d{2,3}$/;
  ipRegex = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private service: ConfigInitService,
    private modal: NzModalService,
    private message: NzMessageService) {
    super(injector);
    this.validateForm = this.fb.group({
      name: ['', [Validators.required]],
      ip: ['', [Validators.required, Validators.pattern(this.ipRegex)]],
      area: ['', [Validators.required, Validators.pattern(this.areaRegex)]],
      // scense: ['', [Validators.required]],
      client: ['', [Validators.required]],
    });
  }
  protected onInit(): void {
    this.service.getCustomType().subscribe(res => {
      this.clientList = res.data;
    })
  }

  public submit(): void {
    if (!this.validateForm.valid)
      return;
    this.modal.confirm({
      nzTitle: '系统配置初始化',
      nzContent: '监控中心ID(区号)一旦设置,后续将不可更改,且作为数据ID的前缀,确认使用现在的ID吗?',
      nzOkText: this.translate.instant('common.ok'),
      nzOkType: 'primary',
      nzOkDanger: false,
      nzOnOk: () => {
        const params = {
          centerName: this.validateForm.value.name,
          centerIp: this.validateForm.value.ip,
          centerId: this.validateForm.value.area,
          // sceneId: this.validateForm.value.scense,
          standardType: this.validateForm.value.client
        }
        this.isSpinning = true;
        this.service.submitConfig(params).subscribe(
          res => {
            this.isSpinning = false;
            // this.message.success('创建成功！', {
            //   nzDuration: 2000,
            //   nzPauseOnHover: true
            // })
            this.close({ action: 'confirm', level: res.data });
            this.sessionService.set<string>('standardType', String(params.standardType));
            window.location.reload();
          }
        )
      },
      nzCancelText: this.translate.instant('common.cancel'),
      // nzOnCancel: () => console.log('Cancel')
    });
  }
}
