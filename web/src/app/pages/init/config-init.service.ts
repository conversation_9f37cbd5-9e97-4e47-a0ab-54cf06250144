import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ApiService } from "app/@services/api-service";
import { Observable } from "rxjs";
@Injectable({
    providedIn: 'root'
})
export class ConfigInitService extends ApiService {

    constructor(private http: HttpClient) {
        super(http);
    }

    getCustomType(): Observable<any> {
        return this.http.get('api/config/standardtype/list');
    }

    submitConfig(params: any): Observable<any> {
        return this.http.post('api/config/center/create', params);
    }

}
