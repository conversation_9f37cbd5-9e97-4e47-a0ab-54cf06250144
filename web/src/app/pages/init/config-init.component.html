<nz-spin [nzSpinning]="isSpinning" nzTip="初始化中...">
  <div class="ci_container">
    <form nz-form [formGroup]="validateForm" (ngSubmit)="submit()">
      <nz-form-item>
        <nz-form-label [nzSpan]="7" nzRequired>监控中心名称</nz-form-label>
        <nz-form-control [nzSpan]="12" nzHasFeedback [nzErrorTip]="nameErrorTpl">
          <input nz-input formControlName="name" placeholder="请输入..." />
          <ng-template #nameErrorTpl let-control>
            <div *ngIf="control.errors?.['required']">不可为空！</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="7" nzRequired>监控中心主机IP</nz-form-label>
        <nz-form-control [nzSpan]="12" nzHasFeedback [nzErrorTip]="ipErrorTpl">
          <input nz-input formControlName="ip" placeholder="请输入..." />
          <ng-template #ipErrorTpl let-control>
            <div *ngIf="control.errors?.['required']">不可为空！</div>
            <div *ngIf="control.errors?.['pattern']">格式错误！</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="7" nzRequired>监控中心ID(区号)</nz-form-label>
        <nz-form-control [nzSpan]="12" nzHasFeedback [nzErrorTip]="areaErrorTpl">
          <input nz-input formControlName="area" placeholder="请输入..." />
          <ng-template #areaErrorTpl let-control>
            <div *ngIf="control.errors?.['required']">不可为空！</div>
            <div *ngIf="control.errors?.['pattern']">格式错误！</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <!-- <nz-form-item>
        <nz-form-label [nzSpan]="7" nzRequired>场景</nz-form-label>
        <nz-form-control [nzSpan]="12" nzHasFeedback>
          <nz-select name="select-validate1" formControlName="scense">
            <nz-option *ngFor="let item of scenceList" [nzValue]="item.scenseId"
              [nzLabel]="item.scenseName"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item> -->
      <nz-form-item>
        <nz-form-label [nzSpan]="7" nzRequired>标准化类型</nz-form-label>
        <nz-form-control [nzSpan]="12" nzHasFeedback>
          <nz-select name="select-validate" formControlName="client">
            <nz-option *ngFor="let item of clientList" [nzValue]="item.standardId"
              [nzLabel]="item.standardName"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <!-- <nz-form-item>
      <nz-form-control [nzOffset]="7" [nzSpan]="12">
        <button nz-button nzType="primary" [disabled]="!validateForm.valid">提交</button>
        <button nz-button (click)="resetForm($event)">Reset</button>
      </nz-form-control>
    </nz-form-item> -->
    </form>
  </div>
</nz-spin>