import { Component, Injector, inject } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { BInterfaceManagementService } from '../../b-interface-management.service';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-station-edit-modal',
  templateUrl: './station-edit-modal.component.html',
  styleUrl: './station-edit-modal.component.less'
})
export class StationEditModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private service: BInterfaceManagementService
  ) {
    super(injector)
  }
  public readonly input = inject(NZ_MODAL_DATA);

  item: any;
  validateForm: FormGroup<{
    siteId: FormControl<string>;
    siteName: FormControl<string>;
    description: FormControl<string>;
  }> = this.fb.group({
    siteId: ['', [Validators.required]],
    siteName: ['', [Validators.required]],
    description: ['']
  });

  onInit() {
    if (this.input.data) {
      this.item = this.input.data;
    }
  }
  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    return new Promise<void>(async (resolve, reject) => {
      if (!valid) {
        reject()
        return
      }
      await this.service.updateItem(this.input.api, this.item).catch(() => {
        reject()
      })
      resolve()
    })
  }

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}
