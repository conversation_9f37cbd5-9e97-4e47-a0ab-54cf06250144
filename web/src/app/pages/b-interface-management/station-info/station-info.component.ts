import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { BInterfaceManagementService } from '../b-interface-management.service';
import { tableResizeFunc } from 'ng-devui/data-table';
import { StationEditModalComponent } from './station-edit-modal/station-edit-modal.component';

@Component({
  selector: 'app-station-info',
  templateUrl: './station-info.component.html',
  styleUrl: './station-info.component.less'
})
export class StationInfoComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: BInterfaceManagementService
  ) {
    super(injector, ele)
  }

  @Input()
  public set tabIndex(index: number | undefined) {
    if (index === 0) {
      this.requestTableDataSoure();
    }
  }
  isUpdating: boolean = false;

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'siteId',
      width: '150px',
      title: '站点编码',
    },
    {
      field: 'siteName',
      width: '150px',
      title: '站点名称',
    },
    {
      field: 'description',
      width: '150px',
      title: '站点描述',
    },
    {
      field: 'operation',
      width: '80px',
      title: '操作',
    }
  ]

  protected onInit(): void {
    super.onInit()
  }

  public async requestTableDataSoure() {
    this.isUpdating = true;
    await this.service.getList('stationcmcc').then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.isUpdating = false;
      }
    })
  }

  openModal(item?: any) {
    const data = item ? JSON.parse(JSON.stringify(item)) : null;
    this.openDialog({
      nzWidth: '650px',
      nzTitle: '修改站点信息',
      nzData: { api: 'stationcmcc', data: data },
      nzContent: StationEditModalComponent,
      nzOnOk: async componentInstance => {
        await componentInstance?.confirm()
        this.messageService.success('修改成功')
        this.requestTableDataSoure()
      }
    });
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
