import { Component, Injector, inject } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { BInterfaceManagementService } from '../../b-interface-management.service';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-su-edit-modal',
  templateUrl: './su-edit-modal.component.html',
  styleUrl: './su-edit-modal.component.less'
})
export class SuEditModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private service: BInterfaceManagementService
  ) {
    super(injector)
  }
  public readonly input = inject(NZ_MODAL_DATA);

  item: any;
  validateForm: FormGroup<{
    suName: FormControl<string>;
    suId: FormControl<string>;
    surId: FormControl<string>;
    userName: FormControl<string>;
    passWord: FormControl<string>;
    suIp: FormControl<string>;
    ftpUserName: FormControl<string>;
    ftpPassWord: FormControl<string>;
  }> = this.fb.group({
    suName: ['', [Validators.required]],
    suId: ['', [Validators.required, Validators.maxLength(12), Validators.minLength(12)]],
    surId: ['', [Validators.required]],
    userName: ['', [Validators.required]],
    passWord: ['', [Validators.required]],
    suIp: ['', [Validators.required]],
    ftpUserName: ['', [Validators.required]],
    ftpPassWord: ['', [Validators.required]]
  });

  onInit() {
    if (this.input.data) {
      this.item = this.input.data;
    }
  }
  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    return new Promise<void>(async (resolve, reject) => {
      if (!valid) {
        reject()
        return
      }
      await this.service.updateItem(this.input.api, this.item).catch(() => {
        reject()
      })
      resolve()
    })
  }

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}