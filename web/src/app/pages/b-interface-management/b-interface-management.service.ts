import { Injectable } from '@angular/core';
import { RestfulService } from '@core/services/restful.service';

@Injectable({
  providedIn: 'root'
})
export class BInterfaceManagementService extends RestfulService {

  /**
  * 获取XX管理列表
  * @returns
  */
  public async getList(api: any): Promise<[]> {
    const res = await this.get<[]>(`api/config/${api}`)
    return res.data
  }

  /**
  * 修改信息
  * @returns
  */
  public async updateItem(api: any, data: any): Promise<[]> {
    const res = await this.put<[]>(`api/config/${api}`, data)
    return res.data
  }

  /**
  * 获取设备类型
  * @returns
  */
  public async getDeviceType(): Promise<[]> {
    const res = await this.get<[]>(`api/config/devicetypecmcc`)
    return res.data
  }

  /**
  * 获取设备子类
  * @returns
  */
  public async getDeviceSubType(id: number): Promise<[]> {
    const res = await this.get<[]>(`api/config/devicesubtypecmcc?deviceTypeId=${id}`)
    return res.data
  }
}
