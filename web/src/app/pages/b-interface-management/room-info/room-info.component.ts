import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { BInterfaceManagementService } from '../b-interface-management.service';
import { tableResizeFunc } from 'ng-devui/data-table';
import { RoomEditModalComponent } from './room-edit-modal/room-edit-modal.component';

@Component({
  selector: 'app-room-info',
  templateUrl: './room-info.component.html',
  styleUrl: './room-info.component.less'
})
export class RoomInfoComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: BInterfaceManagementService
  ) {
    super(injector, ele)
  }

  @Input()
  public set tabIndex(index: number | undefined) {
    if (index === 1) {
      this.requestTableDataSoure();
    }
  }
  typeOptions: any = [];
  operationType: any = [];
  date = [];
  isUpdating: boolean = false;

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'roomID',
      width: '100px',
      title: '机房编码',
    },
    {
      field: 'roomName',
      width: '150px',
      title: '机房名称',
    },
    {
      field: 'siteName',
      width: '150px',
      title: '所属站点',
    },
    {
      field: 'description',
      width: '180px',
      title: '机房描述',
    },
    {
      field: 'operation',
      width: '80px',
      title: '操作',
    }
  ]

  protected onInit(): void {
    super.onInit()
  }

  public async requestTableDataSoure() {
    this.isUpdating = true;
    await this.service.getList('roomcmcc').then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.isUpdating = false;
      }
    })
  }

  openModal(item?: any) {
    const data = item ? JSON.parse(JSON.stringify(item)) : null;
    this.openDialog({
      nzWidth: '650px',
      nzTitle: '修改机房信息',
      nzData: { api: 'roomcmcc', data: data },
      nzContent: RoomEditModalComponent,
      nzOnOk: async componentInstance => {
        await componentInstance?.confirm()
        this.messageService.success('修改成功')
        this.requestTableDataSoure()
      }
    });
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
