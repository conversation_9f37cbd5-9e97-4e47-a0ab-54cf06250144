<div class="room">
    <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true" [virtualScroll]="true"
        [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'calc(100vh - 170px)'"
        [containFixHeaderHeight]="true" [fixHeader]="true" dLoading [loadingStyle]="'default'" [loading]="isUpdating"
        (tableScrollEvent)="tableScrollEvent($event)">
        <thead dTableHead>
            <tr dTableRow>
                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== 'operation'"
                    (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, colConfig.field)">
                    {{ colConfig.title }}
                    <!-- 空白 -->
                    <div *ngIf="colConfig.field === 'operation'" style="height: 32px;"></div>
                    <!-- 输入框 -->
                    <input *ngIf="colConfig.field !== 'operation'" nz-input [(ngModel)]="filterData[colConfig.field]"
                        (ngModelChange)="filterChange()" />
                </th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow>
                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell [title]="rowItem[colConfig.field]">
                        @if (colConfig.field === 'passWord' || colConfig.field === 'ftpPassWord') {
                         ***** 
                        }@else if(colConfig.field === 'operation') {
                        <iconfont [icon]="'icon-edit-set'" title="修改" (click)="openModal(rowItem)"></iconfont>
                        }@else {
                        {{ rowItem[colConfig.field] }}
                        }
                    </td>
                </tr>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div *ngIf="!isUpdating" style="text-align: center; margin-top: 30px">
                <nz-empty nzNotFoundImage="simple" />
            </div>
        </ng-template>
    </d-data-table>
</div>