import { ChangeDetectorRef, Component, ElementRef, Injector, Input, OnInit, ViewChild } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { BInterfaceManagementService } from '../b-interface-management.service';
import { tableResizeFunc } from 'ng-devui/data-table';
import { DeviceEditModalComponent } from './device-edit-modal/device-edit-modal.component';

@Component({
  selector: 'app-device-info',
  templateUrl: './device-info.component.html',
  styleUrl: './device-info.component.less'
})
export class DeviceInfoComponent extends DevuiTableFilterComponent implements OnInit {
  
  public constructor(
    injector: Injector,
    ele: ElementRef,
    private cdr: ChangeDetectorRef,
    private service: BInterfaceManagementService
  ) {
    super(injector, ele)
  }
  @Input()
  public set tabIndex(index: number | undefined) {
    if (index === 1) {
      this.requestTableDataSoure();
    }
  }
  isUpdating: boolean = false;

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'stationName',
      width: '100px',
      title: 'SiteWeb局站',
    },
    {
      field: 'suName',
      width: '100px',
      title: 'SiteWeb监控单元',
    },
    {
      field: 'suId',
      width: '120px',
      title: 'SUID',
    },
    {
      field: 'deviceName',
      width: '180px',
      title: 'Device名称',
    },
    {
      field: 'deviceId',
      width: '100px',
      title: 'Device编码',
    },
    {
      field: 'deviceRId',
      width: '100px',
      title: 'Device资管编号',
    },
    {
      field: 'operation',
      width: '80px',
      title: '操作',
    }
  ]

  protected onInit(): void {
    super.onInit()
  }

  public async requestTableDataSoure() {
    this.isUpdating = true;
    await this.service.getList('equipmentcucc').then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.isUpdating = false;
      }
    })
  }

  openModal(item?: any) {
    const data = item ? JSON.parse(JSON.stringify(item)) : null;
    this.openDialog({
      nzWidth: '650px',
      nzTitle: '修改Device信息',
      nzData: { api: 'equipmentcucc', data: data },
      nzContent: DeviceEditModalComponent,
      nzOnOk: async componentInstance => {
        await componentInstance?.confirm()
        this.messageService.success('修改成功')
        this.requestTableDataSoure()
      }
    });
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
