import { Component, Injector, inject } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { BInterfaceManagementService } from '../../b-interface-management.service';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-fsu-edit-modal',
  templateUrl: './fsu-edit-modal.component.html',
  styleUrl: './fsu-edit-modal.component.less'
})
export class FsuEditModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private service: BInterfaceManagementService
  ) {
    super(injector)
  }
  public readonly input = inject(NZ_MODAL_DATA);

  item: any;
  roomOptions: any;
  validateForm: FormGroup<{
    fsuID: FormControl<string>;
    fsuName: FormControl<string>;
    roomID: FormControl<string>;
    userName: FormControl<string>;
    passWord: FormControl<string>;
    fsuIP: FormControl<string>;
    ftpUserName: FormControl<string>;
    ftpPassWord: FormControl<string>;

  }> = this.fb.group({
    fsuID: ['', [Validators.required]],
    fsuName: ['', [Validators.required]],
    roomID: ['', [Validators.required]],
    userName: ['', [Validators.required]],
    passWord: ['', [Validators.required]],
    fsuIP: ['', [Validators.required]],
    ftpUserName: ['', [Validators.required]],
    ftpPassWord: ['', [Validators.required]],
  });

  onInit() {
    this.getRoom();
    if (this.input.data) {
      this.item = this.input.data;
    }
  }

  getRoom() {
    this.service.getList('roomcmcc').then((res: any) => {
      if (res) {
        this.roomOptions = res.map((item: any) => {
          return { "value": item.roomID, "label": item.roomName };
        });
      }
    })
  }

  roomChange(data: any) {
    this.item.roomName = this.roomOptions.find((item: any) => item.value === data)?.label
  }

  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    return new Promise<void>(async (resolve, reject) => {
      if (!valid) {
        reject()
        return
      }
      await this.service.updateItem(this.input.api, this.item).catch(() => {
        reject()
      })
      resolve()
    })
  }

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}