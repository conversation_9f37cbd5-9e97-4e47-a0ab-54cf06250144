<div>
    <form nz-form [formGroup]="validateForm">
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>FSU编码</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="FSU编码输入错误，请按照标准规则输入:【监控设备厂家名称编码（2位数字）】+【FSU出厂年月日数字YYYYMMDD（8位数字）】+【设备当天不重复序号（4位数字）】">
                <input nz-input formControlName="fsuID" [(ngModel)]="item.fsuID" maxlength="14" pattern="\d{14}"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>FSU名称</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="FSU名称不能为空">
                <input nz-input formControlName="fsuName" [(ngModel)]="item.fsuName"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>所属机房</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="所属机房不能为空">
                <nz-select formControlName="roomID" nzShowSearch [(ngModel)]="item.roomID"
                    [nzOptions]="roomOptions" (ngModelChange)="roomChange($event)">
                </nz-select>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>注册用户名</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="注册用户名不能为空">
                <input nz-input formControlName="userName" [(ngModel)]="item.userName"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>注册口令</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="注册口令不能为空">
                <input nz-input formControlName="passWord" [(ngModel)]="item.passWord"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>FSU IP</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="FSU IP格式不对">
                <input nz-input formControlName="fsuIP" [(ngModel)]="item.fsuIP" pattern="^(((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9]))$"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>FTP用户名</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="FTP登录用户名不能为空">
                <input nz-input formControlName="ftpUserName" [(ngModel)]="item.ftpUserName"/>
            </nz-form-control>
        </nz-form-item>
        <nz-form-item>
            <nz-form-label [nzSpan]="5" nzRequired>FTP口令</nz-form-label>
            <nz-form-control [nzSpan]="15" nzErrorTip="FTP登录密码不能为空">
                <input nz-input formControlName="ftpPassWord" [(ngModel)]="item.ftpPassWord" />
            </nz-form-control>
        </nz-form-item>
    </form>
</div>