import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { BInterfaceManagementService } from '../b-interface-management.service';
import { tableResizeFunc } from 'ng-devui/data-table';
import { FsuEditModalComponent } from './fsu-edit-modal/fsu-edit-modal.component';

@Component({
  selector: 'app-fsu-info',
  templateUrl: './fsu-info.component.html',
  styleUrl: './fsu-info.component.less'
})
export class FSUInfoComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: BInterfaceManagementService
  ) {
    super(injector, ele)
  }

  @Input()
  public set tabIndex(index: number | undefined) {
    if (index === 2) {
      this.requestTableDataSoure();
    }
  }
  typeOptions: any = [];
  operationType: any = [];
  date = [];
  isUpdating: boolean = false;

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'fsuID',
      width: '80px',
      title: 'FSU编码',
    },
    {
      field: 'fsuName',
      width: '130px',
      title: 'FSU名称',
    },
    {
      field: 'siteName',
      width: '130px',
      title: '所属站点',
    },
    {
      field: 'roomName',
      width: '130px',
      title: '所属机房',
    },
    {
      field: 'userName',
      width: '80px',
      title: '注册用户名',
    },
    {
      field: 'passWord',
      width: '80px',
      title: '注册口令',
    },
    {
      field: 'fsuIP',
      width: '80px',
      title: 'FSU IP',
    },
    {
      field: 'ftpUserName',
      width: '80px',
      title: 'FTP用户名',
    },
    {
      field: 'ftpPassWord',
      width: '80px',
      title: 'FTP口令',
    },
    {
      field: 'operation',
      width: '60px',
      title: '操作',
    }
  ]

  protected onInit(): void {
    super.onInit()
  }

  public async requestTableDataSoure() {
    this.isUpdating = true;
    await this.service.getList('monitorunitcmcc').then((res: any) => {
      if (res) {
        this.orgTableDataSource = res
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.isUpdating = false;
      }
    })
  }

  openModal(item?: any) {
    const data = item ? JSON.parse(JSON.stringify(item)) : null;
    this.openDialog({
      nzWidth: '650px',
      nzTitle: '修改FSU信息',
      nzData: { api: 'monitorunitcmcc', data: data },
      nzContent: FsuEditModalComponent,
      nzOnOk: async componentInstance => {
        await componentInstance?.confirm()
        this.messageService.success('修改成功')
        this.requestTableDataSoure()
      }
    });
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}
