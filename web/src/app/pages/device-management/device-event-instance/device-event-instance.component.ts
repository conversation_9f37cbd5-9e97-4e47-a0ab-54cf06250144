/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, OnInit, Output, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { cloneDeep } from 'lodash';
import { forkJoin } from 'rxjs';
import { DeviceManagementService } from '../device-management.service';
import { ExpressionConfigurationComponent } from '@components/selector/expression-configuration/expression-configuration.component';

@Component({
    selector: 'app-device-event-instance',
    templateUrl: './device-event-instance.component.html',
    styleUrls: ['./device-event-instance.component.less']
})

export class DeviceEventInstanceComponent extends GenericComponent implements OnInit {

    params: any;
    #modal = inject(NzModalRef);
    instance: any;
    temp: any;

    constructor(
        injector: Injector,
        private message: NzMessageService,
        private service: DeviceManagementService,
        private modal: NzModalService,) {
        super(injector);
    }

    onInit() {
        console.log(this.params)
        this.getData();
    }

    getData() {
        this.service.getInstanceInfoByIds(this.params.equipmentId, this.params.eventId).subscribe(res => {
            this.instance = res.data;
        })
    }

    editExpression(instance: any, type: string) {
        const modal = this.modal.create<ExpressionConfigurationComponent>({
            nzTitle: "表达式设置",
            nzContent: ExpressionConfigurationComponent,
            nzWidth: 600,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.close()
                },
                {
                    label: '确定',
                    type: 'primary',
                    disabled: componentInstance => !componentInstance!.correct,
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzData: {
                templateId: this.temp,
                isInstance: true,
                expression: type === 'start' ? instance.startExpression : instance.suppressExpression,
                type: 1,
                isAlarmExpress: true,
                instance: cloneDeep(instance),
            },
            nzMaskClosable: false
        });
        modal.afterClose.subscribe((res: any) => {
            if (type === 'start') {
                if (res) {
                    this.instance.startExpression = res.expression;
                }
            } else {
                if (res) {
                    this.instance.suppressExpression = res.expression;
                }
            }
        })
    }

    confirm() {
        this.#modal.destroy({ data: this.instance });
    }

    cancel() {
        this.#modal.destroy();
    }
}