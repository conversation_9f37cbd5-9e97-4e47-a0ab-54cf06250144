@import '../../../../../src/styles/mixin.less';

:host {
    .container {
        height: 100%;
        width: 100%;
        overflow-y: auto;
    }

    .info_item {
        display: flex;
        flex-direction: row;
        align-items: center;
        min-width: 600px;

        .info_title {
            width: 120px;
            text-align: right;
            margin-right: 16px;
        }

        .select {
            width: 400px;
        }

        .info_input {
            width: 400px;
        }

        .info_textarea {
            width: 1000px;
        }
    }

    .info_item_group {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 8px;

        .btn_item {
            margin-top: 10px;
            width: 1071px;
            text-align: center;
        }
    }

    .desc {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 8px;

        .desc_title {
            width: 145px;
            text-align: right;
            margin-right: 16px;
        }
    }

    .button {
        margin-top: 8px;
        margin-left: 135px;
    }

    .error {
        color: red;
        padding-left: 12px;
        align-self: center;
    }

    .errors {
        border-color: red;
    }

    .ant-select.errors .ant-select-selector {
        border-color: red;
    }

    .star {
        padding-left: 12px;
        align-self: center;
    }

    .star::after {
        content: '*';
        font-size: 16px;
        color: red;
    }
}