import { DeviceManagementService } from "./device-management.service";
import { NzIconModule } from 'ng-zorro-antd/icon';
import { CoreModule } from '@core/core.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { CommonModule } from '@angular/common';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { IconModule } from 'ng-devui';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { TranslateModule } from '@ngx-translate/core';
import { NgModule } from "@angular/core";
import { DeviceInfoComponent } from "./device-info/device-info.component";
import { DeviceManagementComponent } from "./device-management.component";
import { ComponentsModule } from "@components/components.module";
import { NzPopoverModule } from "ng-zorro-antd/popover";
import { DeviceTemplateService } from "../device-template/device-template.service";
import { CrossSiteSignalComponent } from "./cross-site-signal/cross-site-signal.component";
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NzIconModule,
    IconModule,
    ReactiveFormsModule,
    CoreModule,
    ComponentsModule,
    NzTreeModule,
    NzFormModule,
    NzTabsModule,
    NzButtonModule,
    NzDropDownModule,
    NzInputModule,
    NzSelectModule,
    TranslateModule,
    NzDatePickerModule,
    NzInputNumberModule,
    NzPopoverModule,
  ],
  declarations: [
    DeviceManagementComponent,
    DeviceInfoComponent,
    CrossSiteSignalComponent,
  ],
  providers: [
    DeviceManagementService,
    DeviceTemplateService,
  ]
})
export class DeviceManagementModule {



}
