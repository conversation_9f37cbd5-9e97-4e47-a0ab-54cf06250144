/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, ElementRef, Injector, Input, OnInit, Output, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NZ_MODAL_DATA, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { cloneDeep } from 'lodash';
import { DeviceManagementService } from '../device-management.service';
import { EditableTip, tableResizeFunc } from 'ng-devui';

@Component({
    selector: 'app-device-signal-instance-list',
    templateUrl: './device-signal-instance-list.component.html',
    styleUrls: ['./device-signal-instance-list.component.less']
})

export class DeviceSignalInstanceListComponent extends GenericComponent implements OnInit  {

    @Input()
    public readonly input: any = inject(NZ_MODAL_DATA);
    #modal = inject(NzModalRef);
    instance: any;

    editableTip = EditableTip.hover;
    cols: any[] = [];
    deiCols = [
        {
            field: 'checkBox',
            titleKey: 'protocol.checkBox',
            width: '40px'
        },
        {
            field: 'equipmentName',
            titleKey: 'deviceManagement.signal.equipmentName',
            width: '80px'
        },
        {
            field: 'signalId',
            titleKey: 'deviceManagement.signal.signalId',
            width: '80px'
        },
        {
            field: 'signalName',
            titleKey: 'deviceManagement.signal.signalName',
            width: '80px'
        },
        {
            field: 'referenceSamplerUnitName',
            titleKey: 'deviceManagement.signal.referenceUnit',
            width: '100px'
        },
        {
            field: 'referenceChannelNo',
            titleKey: 'deviceManagement.signal.referenceChannelNo',
            width: '100px'
        },
        {
            field: 'expression',
            titleKey: 'deviceManagement.signal.expression',
            width: '100px'
        },
    ]
    searchText: any = {
        equipmentName: null,
        signalId: null,
        eventName: null,
        startExpression: null,
        suppressExpression: null,
    }
    checkAll: boolean = false;
    checkedIds: any = [];
    searchTimer: any;
    source: any;
    filterSource: any;
    minWidth = 40;

    constructor(
        injector: Injector,
        private ele: ElementRef,
        private message: NzMessageService,
        private service: DeviceManagementService,
        private modal: NzModalService,) {
        super(injector);
    }

    onInit() {
        this.getInitCols();
    }

    getInitCols() {
        this.cols = this.deiCols.map(item => ({ ...item, title: item['titleKey'] ? this.translate.instant(item['titleKey']) : '' }));
        this.getData();
    }

    getData() {
        this.service.getAllSignalInstanceByEqId(this.input.eqId).subscribe(res => {
            this.source = res.data;
            this.filterSource = res.data;
        })
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.cols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            if(this.searchText.equipmentName) {
                filterList = filterList.filter((item: any) => {
                    return item.equipmentName ? item.equipmentName.includes(this.searchText.equipmentName) : false;
                })
            }
            if(this.searchText.signalId) {
                filterList = filterList.filter((item: any) => {
                    return item.signalId ? item.signalId.toString().includes(this.searchText.signalId) : false;
                })
            }
            if(this.searchText.eventName) {
                filterList = filterList.filter((item: any) => {
                    return item.eventName ? item.eventName.includes(this.searchText.eventName) : false;
                })
            }
            if(this.searchText.startExpression) {
                filterList = filterList.filter((item: any) => {
                    return item.startExpression ? item.startExpression.includes(this.searchText.startExpression) : false;
                })
            }
            if(this.searchText.suppressExpression) {
                filterList = filterList.filter((item: any) => {
                    return item.suppressExpression ? item.suppressExpression.includes(this.searchText.suppressExpression) : false;
                })
            }
            this.filterSource = filterList;
        }, 500)
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.checkedIds.includes(id)) {
              this.checkedIds.push(id);
            }
        } else {
            this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = this.filterSource.length && this.filterSource.every((item: any ) => {
            return this.checkedIds.includes(item.signalId)
        });
    }

    onItemChecked(id: string, checked: boolean): void {
        this.updateCheckedIds(id, checked);
        this.refreshCheckedBoxStatus();
    }

    onAllChecked(checked: boolean): void {
        this.filterSource.forEach( (item: any) => {
            this.updateCheckedIds(item.signalId, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    confirm() {
        if(this.checkedIds.length > 0) {
            const modal = this.modal.confirm({
                nzTitle: '提示',
                nzContent: '确认删除所选对象?',
                nzOkText: '确认',
                nzOkType: 'primary',
                nzOkDanger: true,
                nzOnOk: () => {
                    return 'yes'
                },
                nzCancelText: '取消',
                nzOnCancel: () => {
                    return 'no'
                },
            })
            modal.afterClose.subscribe((res:any) => {
                if(res === 'yes'){
                    let ids = this.checkedIds.join(',');
                    this.service.deleteSignalInstance(this.input.eqId, ids).subscribe(res => {
                        this.message.success('更新成功！');
                        this.#modal.destroy('success');
                    })
                }
            })
        } else {
            this.message.warning('请先选择要删除的对象！')
        }
    }

    cancel() {
        this.#modal.destroy();
    }

}