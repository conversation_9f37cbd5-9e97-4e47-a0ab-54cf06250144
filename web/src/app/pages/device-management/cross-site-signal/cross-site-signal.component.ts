/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, OnInit, Output, inject } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { cloneDeep } from 'lodash';
import { forkJoin } from 'rxjs';
import { DeviceManagementService } from '../device-management.service';
import { ExpressionConfigurationComponent } from '@components/selector/expression-configuration/expression-configuration.component';
import { IndicatorExpressionConfigurationComponent } from '@components/selector/indicator-expression-configuration/indicator-expression-configuration.component';

@Component({
    selector: 'app-cross-site-signal',
    templateUrl: './cross-site-signal.component.html',
    styleUrls: ['./cross-site-signal.component.less']
})

export class CrossSiteSignalComponent extends GenericComponent implements OnInit  {

    params: any;
    #modal = inject(NzModalRef);
    instance: any;
    temp: any;
    referenceSamplerUnit: any = [];
    equipmentTemplateId: any;

    constructor(
        injector: Injector,
        private message: NzMessageService,
        private service: DeviceManagementService,
        private modal: NzModalService,) {
        super(injector);
    }

    onInit() {
        this.instance = { expression: '' };
        this.getData();
    }

    getData() {
        this.service.getCrossSiteSignalById(this.params.equipmentId, this.params.signalId).subscribe(res => {
            this.instance = res.data;
        })
    }

    editExpression(instance: any) {
        const modal = this.modal.create<ExpressionConfigurationComponent>({
            nzTitle: "信号表达式设置",
            nzContent: ExpressionConfigurationComponent,
            nzWidth: 700,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.close()
                },
                {
                    label: '确定',
                    type: 'primary',
                    disabled: componentInstance => !componentInstance!.correct,
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzData: {
                templateId: this.temp,
                isInstance: true,
                expression: instance.expression,
                type: 0,
                instance: cloneDeep(instance),
                isCrossSite: true
            },
            nzMaskClosable: false
        });
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                this.instance.expression = res.expression;
            }
        })
    }
    editIndicatorExpression(instance: any) {
        const modal = this.modal.create<IndicatorExpressionConfigurationComponent>({
            nzTitle: "指标表达式设置",
            nzContent: IndicatorExpressionConfigurationComponent,
            nzWidth: 700,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.close()
                },
                {
                    label: '确定',
                    type: 'primary',
                    disabled: componentInstance => !componentInstance!.correct,
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzData: {
                templateId: this.temp,
                isInstance: true,
                expression: instance.expression,
                type: 0,
                instance: cloneDeep(instance),
                isCrossSite: true
            },
            nzMaskClosable: false
        });
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                this.instance.expression = res.expression;
            }
        })
    }

    confirm() {
        const params = {
            stationId: this.instance.stationId,
            monitorUnitId: this.instance.monitorUnitId,
            equipmentId: this.instance.equipmentId,
            signalId: this.instance.signalId,
            expression: this.instance.expression,
            equipmentTemplateId: this.params.equipmentTemplateId,
        }
        this.service.addCrossSiteSignal(params).subscribe(res => {
            this.message.success('更新成功！');
            this.#modal.destroy('success');
        })
    }

    cancel() {
        this.#modal.destroy();
    }
}