<div class="container">
    <nz-tabset nzType="card" [(nzSelectedIndex)]="selectedTabIndex">
        <nz-tab nzTitle="设备信息">
            <app-device-info></app-device-info>
        </nz-tab>
        <nz-tab nzTitle="信号">
           <app-device-template-signal [muCategory]="muCategory" [tabIndex]="selectedTabIndex"></app-device-template-signal>
        </nz-tab>
        <nz-tab nzTitle="事件">
            <app-device-management-event [muCategory]="muCategory"></app-device-management-event>
        </nz-tab>
        <nz-tab nzTitle="控制">
            <app-device-template-control [tabIndex]="selectedTabIndex"></app-device-template-control>
        </nz-tab>
        <nz-tab nzTitle="变更记录">
            <app-device-template-log [tabIndex]="selectedTabIndex" [objectType]="11"></app-device-template-log>
        </nz-tab>
    </nz-tabset>
    <button nz-button class="dm_button" nzType="primary" (click)="toTemplateManagement()">
        {{ 'deviceManagement.toTemplate' | translate}}
    </button>
</div>
