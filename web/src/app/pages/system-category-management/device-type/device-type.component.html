<div class="device-type">
    <div class="line">
        <button nz-button nzType="primary" (click)="openModal()">
            <i class="icon icon-add"></i> 新增
        </button>
    </div>
    <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
        [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'calc(100vh - 215px)'"
        [containFixHeaderHeight]="true" [fixHeader]="true" (tableScrollEvent)="tableScrollEvent($event)">
        <thead dTableHead>
            <tr dTableRow>
                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== 'operation'"
                    (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, colConfig.field)">
                    {{ colConfig.title }}
                    <!-- 空白 -->
                    <div *ngIf="colConfig.field === 'operation'" style="height: 32px;"></div>
                    <!-- 输入框 -->
                    <input *ngIf="colConfig.field !== 'operation'" nz-input [(ngModel)]="filterData[colConfig.field]"
                        (ngModelChange)="filterChange()" />
                </th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow>
                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell>
                        @if(colConfig.field === 'operation') {
                        <iconfont [icon]="'icon-edit-set'" title="修改" (click)="openModal(rowItem)"></iconfont>
                        <iconfont [icon]="'icon-delete2'" title="删除" (click)="delete(rowItem)"></iconfont>
                        }@else {
                        {{ rowItem[colConfig.field] }}
                        }
                    </td>
                </tr>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div style="text-align: center; margin-top: 20px">
                <nz-empty nzNotFoundImage="simple" />
            </div>
        </ng-template>
    </d-data-table>
</div>