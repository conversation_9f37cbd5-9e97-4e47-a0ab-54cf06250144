import { Component, Injector, inject } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { SystemCategoryManagementService } from '../system-category-management.service';

@Component({
  selector: 'ngx-add-edit-modal',
  templateUrl: './add-edit-modal.component.html',
  styleUrl: './add-edit-modal.component.less'
})
export class AddEditModalComponent extends GenericModalComponent<{}, Boolean> {
  constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private service: SystemCategoryManagementService
  ) {
    super(injector)
  }
  public readonly input = inject(NZ_MODAL_DATA);
  item : any;
  validateForm: FormGroup<{
    itemId: FormControl<string>;
    itemValue: FormControl<string>;
    itemAlias: FormControl<string>;
    description: FormControl<string>;
  }> = this.fb.group({
    itemId: ['', [Validators.required]],
    itemValue: ['', [Validators.required]],
    itemAlias: [''],
    description: ['']
  });

  onInit(){
    if(this.input.data.entryId) {
      const { itemId, entryId, entryItemId, itemValue, itemAlias, description } = this.input.data;
      this.item = { itemId, entryId, entryItemId, itemValue, itemAlias, description };
      console.log(this.item)
    }else{
      this.item = { itemId: '', entryId: this.input.data, itemValue:'', itemAlias:'', description:'' };
      console.log(this.item)
    }
  }

  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    return new Promise<void>(async (resolve, reject) => {
      if (!valid) {
        reject()
        return
      }
      if (this.input.data.entryId) {
          await this.service.updateItem(this.item).catch(() => {
            reject()
          })
      }else{
        await this.service.addItem(this.item).catch(() => {
          reject()
        })
      }
      resolve()
    })
  }

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}
