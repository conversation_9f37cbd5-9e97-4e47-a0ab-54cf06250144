import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { PagesComponent } from './pages.component';
import { NotFoundComponent } from '@core/components/notfound/not-found.component';
import { AuthGuardService } from '@core/services/auth-guard.service';
import { WorkersComponent } from './workers/workers.component';
import { LayoutComponent } from 'app/layout/layout.component';
import { HomeComponent } from './home/<USER>';
import { UserComponent } from './user/user.component';
import { ServerComponent } from './server/server.component';
import { DeviceTemplateManagementComponent } from './device-template/device-template-management.component';
import { ConfigInitComponent } from './init/config-init.component';
import { ProtocolManagementComponent } from './protocol-management/protocol-management.component';
import { DeviceManagementComponent } from './device-management/device-management.component';
import { BasicClassTemplateComponent } from './baseclass-management/baseclass-dictionary/basic-class-template/basic-class-template.component';
import { BaseclassDeviceTypeComponent } from './baseclass-management/baseclass-dictionary/baseclass-device-type/baseclass-device-type.component';
import { BaseclassSignalComponent } from './baseclass-management/baseclass-dictionary/baseclass-signal/baseclass-signal.component';
import { DevuiTableFilterExampleLabelComponent } from '@components/basic/devui-table-filter/Example/label/devui-table-filter-example-label.component';
import { DevuiTableFilterExampExtendsComponent } from '@components/basic/devui-table-filter/Example/extends/devui-table-filter-example-extends.component';
import { BaseclassEventComponent } from './baseclass-management/baseclass-dictionary/baseclass-event/baseclass-event.component';
import { LogComponent } from './log/log.component';
import { BaseclassSignalMappComponent } from './baseclass-management/baseclass-mapp/signal/baseclass-signal-mapp.component';
import { BaseclassEventMappComponent } from './baseclass-management/baseclass-mapp/event/baseclass-event-mapp.component';
import { SnmpListComponent } from './device-management-tools/snmp/snmp-list/snmp-list.component';
import { BacnetListComponent } from './device-management-tools/bacnet/bacnet-list/bacnet-list.component';
import { DrivertemplateListComponent } from './device-management-tools/drivertemplate/drivertemplate-list/drivertemplate-list.component';
import { VirtualEquipListComponent } from './device-management-tools/virtual-equipment/virtual-equip-list/virtual-equip-list.component';
import { BaseclassControlComponent } from './baseclass-management/baseclass-dictionary/baseclass-control/baseclass-control.component';
import { MuManagementComponent } from './mu-management/mu-management.component';
import { BaseclassControlMappComponent } from './baseclass-management/baseclass-mapp/control/baseclass-control-mapp.component';
import { ResizableLayoutExampleComponent } from '@components/basic/resizable-layout/Example/resizable-layout-example.component';
import { VirtualEquipAddComponent } from './device-management-tools/virtual-equipment/virtul-equip-add/virtul-equip-add.component';
import { AlarmLinkageComponent } from './alarm-linkage/alarm-linkage.component';
import { SystemCategoryManagementComponent } from './system-category-management/system-category-management.component';
import { CustomerStandardizationManagementComponent } from './customer-standardization/customer-standardization-management.component';
import { CustomerStandardizationComponent } from './customer-standardization/customer-standardization.component';
import { BInterfaceManagementComponent } from './b-interface-management/b-interface-management.component';
import { CollectionManagementComponent } from './collection-management/collection-management.component';
import { LevelManagementComponent } from './level-management/level-management.component';
import { VirtualEquipAddAcrossComponent } from './device-management-tools/virtual-equipment/virtual-equip-add-across/virtual-equip-add-across.component';
import { VirtualEquipListAcrossComponent } from './device-management-tools/virtual-equipment/virtual-equip-list-across/virtual-equip-list-across.component';
import { BytedanceMappingComponent } from './bytedance-mapping/bytedance-mapping.component';

const routes: Routes = [{
  path: '',
  component: LayoutComponent,
  data: { keepAlive: true },
  children: [
    {
      path: 'home',
      component: HomeComponent,
      // canActivate: [AuthGuardService],
      data: { title: '首页', keepAlive: true }
    },
    {
      path: 'user',
      component: UserComponent,
      // canActivate: [AuthGuardService],
      data: { title: '用户管理', keepAlive: true }
    },
    {
      path: 'level-management',
      component: LevelManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '层级管理', keepAlive: true }
    },
    {
      path: 'collection-management',
      component: CollectionManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '端局管理', keepAlive: true }
    },
    {
      path: 'workers',
      title: 'Wel Come',
      component: WorkersComponent
    },
    {
      path: 'server',
      component: ServerComponent,
      data: { title: '服务器管理', keepAlive: true }
    },
    {
      path: 'log',
      component: LogComponent,
      data: { title: '日志管理', keepAlive: true }
    },
    {
      path: 'device-template',
      component: DeviceTemplateManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '设备模板', keepAlive: true }
    },
    {
      path: 'protocol',
      component: ProtocolManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '协议管理', keepAlive: true }
    },
    {
      path: 'device-management/:id',
      component: DeviceManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '设备管理', keepAlive: true }
    },
    {
      path: 'config-distribute-management',
      component: MuManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '配置分发管理', keepAlive: true }
    },
    {
      path: 'basicClassTemplate',
      component: BasicClassTemplateComponent,
      // canActivate: [AuthGuardService],
      data: { title: '模板', keepAlive: true }
    },
    {
      path: 'baseclass-signal-mapp',
      component: BaseclassSignalMappComponent,
      // canActivate: [AuthGuardService],
      data: { title: '信号', keepAlive: true }
    },
    {
      path: 'baseclass-event-mapp',
      component: BaseclassEventMappComponent,
      // canActivate: [AuthGuardService],
      data: { title: '事件', keepAlive: true }
    },
    {
      path: 'baseclass-control-mapp',
      component: BaseclassControlMappComponent,
      // canActivate: [AuthGuardService],
      data: { title: '控制', keepAlive: true }
    },
    {
      path: 'baseclass-device-type',
      component: BaseclassDeviceTypeComponent,
      // canActivate: [AuthGuardService],
      data: { title: '设备类型字典', keepAlive: true }
    },
    {
      path: 'baseclass-signal',
      component: BaseclassSignalComponent,
      // canActivate: [AuthGuardService],
      data: { title: '信号基类字典', keepAlive: true }
    },
    {
      path: 'baseclass-event',
      component: BaseclassEventComponent,
      // canActivate: [AuthGuardService],
      data: { title: '告警基类字典', keepAlive: true }
    },
    {
      path: 'baseclass-control',
      component: BaseclassControlComponent,
      // canActivate: [AuthGuardService],
      data: { title: '控制基类字典', keepAlive: true }
    },
    {
      path: 'snmp-list',
      component: SnmpListComponent,
      data: { title: 'SNMP配置管理', keepAlive: true }
    },
    {
      path: 'bacnet-list',
      component: BacnetListComponent,
      data: { title: 'BACNet配置管理', keepAlive: true }
    },
    {
      path: 'drivertemplate-list',
      component: DrivertemplateListComponent,
      data: { title: '驱动模板管理', keepAlive: true }
    },
    {
      path: 'virtual-equip-list',
      component: VirtualEquipListComponent,
      data: { title: '虚拟设备构造', keepAlive: true }
    },
    {
      path: 'virtual-equip-add',
      component: VirtualEquipAddComponent,
      data: { title: '新增虚拟设备', keepAlive: true }
    },
    {
      path: 'virtual-equip-list-across',
      component: VirtualEquipListAcrossComponent,
      data: { title: '跨站虚拟设备构造', keepAlive: true }
    },
    {
      path: 'virtual-equip-add-across',
      component: VirtualEquipAddAcrossComponent,
      data: { title: '新增跨站虚拟设备', keepAlive: true }
    },
    {
      path: 'alarm-linkage',
      component: AlarmLinkageComponent,
      // canActivate: [AuthGuardService],
      data: { title: '告警联动', keepAlive: true }
    },
    {
      path: 'system-category',
      component: SystemCategoryManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '系统类别管理', keepAlive: true }
    },
    {
      path: 'customer-standardization',
      component: CustomerStandardizationManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: '映射关系配置', keepAlive: true }
    },
    {
      path: 'customer-standardization-dictionary',
      component: CustomerStandardizationComponent,
      // canActivate: [AuthGuardService],
      data: { title: '字典表维护', keepAlive: true }
    },
    {
      path: 'b-interface-management',
      component: BInterfaceManagementComponent,
      // canActivate: [AuthGuardService],
      data: { title: 'B接口信息管理', keepAlive: true }
    },
    {
      path: 'bytedance-mapping',
      component: BytedanceMappingComponent,
      // canActivate: [AuthGuardService],
      data: { title: '字节标准化映射', keepAlive: true }
    },
    {
      path: 'devui-table-label',
      component: DevuiTableFilterExampleLabelComponent,
      // canActivate: [AuthGuardService],
      data: { title: 'Devui Table 标签', keepAlive: true }
    },
    {
      path: 'devui-table-extends',
      component: DevuiTableFilterExampExtendsComponent,
      // canActivate: [AuthGuardService],
      data: { title: 'Devui Table 继承', keepAlive: true }
    },
    {
      path: 'resizable-layout-example',
      component: ResizableLayoutExampleComponent,
      // canActivate: [AuthGuardService],
      data: { title: 'Resizable 布局', keepAlive: true }
    },
    {
      path: 'notfound',
      title: 'not found',
      component: NotFoundComponent,
      canActivate: [AuthGuardService]
    },
    {
      path: '**',
      component: NotFoundComponent,
      canActivate: [AuthGuardService]
    },
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PagesRoutingModule {
}
