import { NgModule } from '@angular/core';
import { CoreModule } from '@core/core.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NzInputModule } from 'ng-zorro-antd/input';
import { DataTableModule } from 'ng-devui';
import { BaseclassManagementService } from './baseclass-management.service';
import { BaseclassDeviceTypeComponent } from './baseclass-dictionary/baseclass-device-type/baseclass-device-type.component';
import { BaseclassSignalComponent } from './baseclass-dictionary/baseclass-signal/baseclass-signal.component';
import { BasicClassTemplateComponent } from './baseclass-dictionary/basic-class-template/basic-class-template.component';
import { LoadingModule } from 'ng-devui/loading';
import { BasicEquipmentSelectorComponent } from './baseclass-dictionary/basic-equipment-selector/basic-equipment-selector.component';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { BaseclassEventComponent } from './baseclass-dictionary/baseclass-event/baseclass-event.component';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { BaseclassSignalMappComponent } from './baseclass-mapp/signal/baseclass-signal-mapp.component';
import { ComponentsModule } from '@components/components.module';
import { BaseclassEventMappComponent } from './baseclass-mapp/event/baseclass-event-mapp.component';
import { BaseClassTreeComponent } from './baseclass-tree/baseclass-tree.component';
import { BaseclassEventMappTemplateComponent } from './baseclass-mapp/event/baseclass-event-mapp-template.component';
import { SimilarEventSetComponent } from './baseclass-mapp/event/similar-event-set/similar-event-set.component';
import { BaseclassControlComponent } from './baseclass-dictionary/baseclass-control/baseclass-control.component';
import { BaseclassControlMappComponent } from './baseclass-mapp/control/baseclass-control-mapp.component';
import { BaseclassControlMappTemplateComponent } from './baseclass-mapp/control/template-reference/baseclass-control-mapp-template.component';
import { BaseclassSignalMappTemplateComponent } from './baseclass-mapp/signal/baseclass-signal-mapp-template/baseclass-signal-mapp-template.component';
import { BaseclassMappSelectorComponent } from './baseclass-mapp/components/baseclass-mapp-selector/baseclass-mapp-selector.component';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzFormModule } from 'ng-zorro-antd/form';
import { BaseclassMappSimilarComponent } from './baseclass-mapp/components/baseclass-mapp-similar/baseclass-mapp-similar.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
@NgModule({
  imports: [
    CoreModule,
    FormsModule,
    CommonModule,
    TranslateModule,
    NzInputModule,
    DataTableModule,
    LoadingModule,
    NzTreeModule,
    NzDropDownModule,
    NzEmptyModule,
    NzButtonModule,
    NzIconModule,
    NzSpaceModule,
    NzLayoutModule,
    NzTreeModule,
    NzResizableModule,
    ComponentsModule,
    NzRadioModule,
    NzFormModule,
    ReactiveFormsModule,
    NzToolTipModule,
  ],
  declarations: [
    BaseclassDeviceTypeComponent,
    BaseclassSignalComponent,
    BaseclassEventComponent,
    BaseclassControlComponent,
    BasicClassTemplateComponent,
    BasicEquipmentSelectorComponent,
    BaseclassSignalMappComponent,
    BaseclassEventMappComponent,
    BaseclassControlMappComponent,
    BaseClassTreeComponent,
    BaseclassEventMappTemplateComponent,
    SimilarEventSetComponent,
    BaseclassControlMappTemplateComponent,
    BaseclassSignalMappTemplateComponent,
    BaseclassMappSelectorComponent,
    BaseclassMappSimilarComponent,
  ],
  providers: [
    BaseclassManagementService
  ]
})
export class BaseclassManagementModule {

}
