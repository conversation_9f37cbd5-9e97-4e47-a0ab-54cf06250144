import { Component, Injector, Input, ViewChild, inject } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { BaseclassManagementService } from 'app/pages/baseclass-management/baseclass-management.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeNode, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { ControlCommand } from '../../control/baseclass-control-mapp.entity';

@Component({
  selector: 'app-baseclass-mapp-selector',
  templateUrl: './baseclass-mapp-selector.component.html',
  styleUrls: ['./baseclass-mapp-selector.component.less']
})
export class BaseclassMappSelectorComponent extends GenericModalComponent<{}, Boolean> {
  @Input()
  public readonly input: any = inject(NZ_MODAL_DATA);
  searchText!: string;
  selected: any = [];
  nodes: any;
  inputData: any;
  inputType: any;
  expandedKeys: Array<string> = [];
  selectedKeys: Array<string> = [];
  #modal = inject(NzModalRef);
  infoData: any = {};
  level: any;
  coverFlag: boolean = true;
  btn_disabled: boolean = false;
  @ViewChild('nzTree') nzTree!: NzTreeComponent;
  public constructor(
    injector: Injector,
    private service: BaseclassManagementService,
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.inputData = this.input.data;
    this.inputType = this.input.type;
    this.getTreeData();
  }

  getTreeData() {
    let request;
    if (this.inputType === 'signal') {
      request = this.service.getBaseSignalTree(this.inputData.equipmentBaseType, this.inputData.signalCategory);
    } else if (this.inputType === 'event') {
      request = this.service.getBaseEventTreeByEqTypeId(this.inputData.equipmentBaseType);
    } else if (this.inputType === 'control') {
      request = this.service.getBaseControlTree(this.inputData.equipmentBaseType, this.inputData.commandType);
    }
    if (request) {
      request.subscribe((res: any) => {
        let node = res.data;
        this.buildTree(node);
        this.nodes = node;
        setTimeout(() => {
          // 展开节点
          const id = (this.inputData.baseCondId !== null && this.inputData.baseCondId !== undefined) ? this.inputData.baseTypeId + '-' + this.inputData.baseCondId : this.inputData.baseTypeId;
          this.selectedKeys = id ? [id.toString()] : [];
          if(this.selectedKeys.length > 0) {
            this.expandAndSelectNodes(this.nodes, id.toString());
          } else {
            let firstNode = false;
            this.expandDefaultNodes(this.nodes, firstNode);
          }
          this.expandedKeys = [...this.expandedKeys, ...this.selectedKeys];
          // 获取节点详情
          let node = this.nzTree.getTreeNodeByKey(this.selectedKeys[0]);
          this.nzEvent(node);
        }, 0);
      })
    }
  }

  expandAndSelectNodes(nodes: NzTreeNodeOptions[], targetKey: string): boolean {
    for (const node of nodes) {
      if (node.key === targetKey) {
        return true;
      }
      if (node.children && node.children.length > 0) {
        this.expandAndSelectNodes(node.children, targetKey);
        const found = this.expandAndSelectNodes(node.children, targetKey);
        if (found) {
          this.expandedKeys.push(node.key);
          return true;
        }
      }
    }
    return false;
  }

  expandDefaultNodes(nodes: NzTreeNodeOptions[], firstNode: boolean) {
    for (const node of nodes) {
      if (node.children && node.children.length > 0) {
        this.expandDefaultNodes(node.children, firstNode);

        if(node.children && !node.children[0].key.includes('-')){
          this.expandedKeys.push(node.key);
        }

        if(!firstNode && node.index === 1) {
          this.selectedKeys.push(node.key);
          firstNode = true;
        }
      }
    }
  }

  buildTree(nodes: any) {
    for (let i = 0; i < nodes.length; i++) {
      //判断是否为层级节点
      nodes[i]['title'] = nodes[i]['label'];
      if (nodes[i]['children'] && nodes[i]['children'].length > 0) {
        this.buildTree(nodes[i]['children']);
      } else {
        nodes[i]['isLeaf'] = true;
      }
    }
  }

  // 点击树节点
  nzEvent(node: any) {
    this.level = node?.level;
    if (this.level < 2) {
      this.infoData = { title: node.origin.title, value: node.origin.value };
    } else {
      if(this.inputType === 'signal') {
        let baseTypeId, baseCondId;
        baseTypeId = this.level === 3 ? node?.parentNode?.key : this.level === 2 ? node?.key : '';
        baseCondId = this.level === 3 ? node?.origin?.value : '';
        this.service.getBaseSignalDetail(baseTypeId, baseCondId).subscribe((res: any) => {
          this.infoData = res.data;
        })
      }
      if(this.inputType === 'event') {
        let baseTypeId;
        baseTypeId = this.level === 2 ? node?.key : '';
        this.service.getBaseEventDetailById(baseTypeId).subscribe((res: any) => {
          this.infoData = res.data;
        })
      }
      if(this.inputType === 'control') {
        let baseTypeId;
        let baseCondId;
        baseTypeId = this.level === 3 ? node?.parentNode?.key : this.level === 2 ? node?.key : '';
        baseCondId = this.level === 3 ? node?.origin?.value : '';
        this.service.getBaseControlDetailById(baseTypeId, baseCondId).then(res => {
          this.infoData = res;
          res && (this.infoData.baseHysteresis = res.baseStatusId === null ? "不带参" : "带参")
        })
      }
    }
    const { signalCategory, commandType  } = this.inputData;
    if(this.inputType === 'signal') {
      this.btn_disabled = !((signalCategory === 1 && this.level === 2) || (signalCategory === 2 && this.level === 3));
    }
    if(this.inputType === 'event') {
      this.btn_disabled = !(this.level === 2);
    }
    if(this.inputType === 'control') {
      this.btn_disabled = !((commandType === ControlCommand.remoteDebug && this.level === 2) || (commandType === ControlCommand.remoteControl && this.level === 3));
    }
  }

  // 双击展开/收回子节点
  openFolder(data: NzTreeNode | NzFormatEmitEvent): void {
    // do something if u want
    if (data instanceof NzTreeNode) {
      data.isExpanded = !data.isExpanded
    } else {
      const node = data.node
      if (node) {
        node.isExpanded = !node.isExpanded
      }
    }
  }

  confirm() {
    if(this.inputType === 'signal') {
      const { baseTypeId, baseCondId } = this.infoData;
      const data: any = { baseTypeId, baseCondId, coverFlag: this.coverFlag };
      this.close(data);
    }
    if(this.inputType === 'event') {
      const { baseTypeId } = this.infoData;
      const data: any = { baseTypeId, coverFlag: this.coverFlag };
      this.close(data);
    }
    if(this.inputType === 'control') {
      const { baseTypeId, baseCondId } = this.infoData;
      const data: any = { baseTypeId, baseCondId, coverFlag: this.coverFlag };
      this.close(data);
    }
  }
}
