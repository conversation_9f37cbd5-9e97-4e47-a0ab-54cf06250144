<form nz-form [formGroup]="validateForm">
  <nz-form-item class="cell-container">
    <nz-form-label>{{ secName_label }}</nz-form-label>
    <nz-form-control>
      <span class="red-text">{{ secName_value }}</span>
    </nz-form-control>
  </nz-form-item>
  <nz-form-item class="cell-container">
    <div style="min-width: 61%; display: flex;">
      <nz-form-label>样板基类</nz-form-label>
      <nz-form-control>
        <span class="red-text">{{ baseTypeName }}</span>
      </nz-form-control>
    </div>
    <div *ngIf="hasBaseMeaning()" style="width: 39%; display: flex;">
      <nz-form-label>样板基类含义</nz-form-label>
      <nz-form-control>
        <span class="red-text">{{ baseMeaning }}</span>
      </nz-form-control>
    </div>
  </nz-form-item>
  <nz-form-item class="cell-container">
    <nz-form-label [nzSpan]="24">
      <div>
        <span style="white-space: nowrap;">名称通配符</span><span class="red-text">{{ wildcardTip }}</span>
      </div>
    </nz-form-label>
    <nz-form-control [nzSpan]="12" [nzErrorTip]="errorTpl">
      <input nz-input formControlName="wildcard" [(ngModel)]="wildcard" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label nzNoColon [nzSpan]="0" nzRequired></nz-form-label>
    <div style="display: flex; align-items: center; height: 32px;  padding-right: 12px;">从</div>
    <nz-form-control [nzSpan]="3">
      <input nz-input formControlName="startNumber" [(ngModel)]="startNumber" />
    </nz-form-control>
    <div style="display: flex; align-items: center; padding: 0 12px; height: 32px;">到</div>
    <nz-form-control [nzSpan]="3" [nzErrorTip]="errorTpl">
      <input nz-input formControlName="abortNumber" [(ngModel)]="abortNumber" />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label>覆盖方式</nz-form-label>
    <nz-form-control>
      <nz-radio-group [(ngModel)]="coverFlag" formControlName="coverFlag">
        <label nz-radio [nzValue]="true">全部覆盖</label>
        <label nz-radio [nzValue]="false">仅覆盖未设置的基类ID</label>
      </nz-radio-group>
    </nz-form-control>
  </nz-form-item>
</form>
<ng-template #errorTpl let-control>
  @if (control.errors?.['required']) {
  不能为空
  }
  @if (control.errors?.['wildcard']) {
  {{ wildcardErrTip }}
  }
  @if (control.errors?.['startNumber']){
  请输入2-999的整数
  }
</ng-template>