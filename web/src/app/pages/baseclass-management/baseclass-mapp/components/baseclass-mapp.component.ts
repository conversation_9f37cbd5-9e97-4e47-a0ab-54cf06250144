import { Component, ElementRef, Injector } from '@angular/core'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'
import { NzResizeEvent } from 'ng-zorro-antd/resizable'

// @Component({
//     selector: 'app-baseclass-mapp',
//     templateUrl: './baseclass-mapp.component.html',
//     styleUrls: ['./baseclass-mapp.component.less']
// })

export class BaseclassMappComponent extends DevuiTableFilterComponent {

    // sider 宽度
    public siderWidth = 259
    // sider resize id
    public sideResizeId = -1

    // content 高度
    public contentHeight = 450
    public contentHeightString = '450px'
    //另一侧content高度
    public antiContentHeightString = '450px'
    // content resize id
    public contentResizeId = -1

    public constructor(injector: Injector, protected ele: ElementRef) {
        super(injector, ele)
    }

    // side 拖动
    public onSideResize({ width }: NzResizeEvent): void {
        cancelAnimationFrame(this.sideResizeId)
        this.sideResizeId = requestAnimationFrame(() => {
            this.siderWidth = width!
        })
    }

    // content 拖动
    public onContentResize({ height }: NzResizeEvent): void {
        cancelAnimationFrame(this.contentResizeId)
        this.contentResizeId = requestAnimationFrame(() => {
            this.contentHeight = height!
            this.contentHeightString = this.contentHeight + 'px';
            this.antiContentHeightString = document.documentElement.scrollHeight - (this.contentHeight + 145) + 'px';
        })
    }
}