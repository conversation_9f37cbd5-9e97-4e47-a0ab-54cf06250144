import { Component, ElementRef, Injector, ViewChild } from '@angular/core'
import { TableColumnConfig, devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model'
import { BaseclassMappComponent } from '../components/baseclass-mapp.component'
import { BaseclassManagementService } from '../../baseclass-management.service'
import { BaseClassControlMappTableEntity, ConfirmCheckItems, ConfirmCheckParams, ControlCommand, TableType } from './baseclass-control-mapp.entity'
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown'
import { NzTreeNodeOptionsExt } from '@common/interface/NzTreeNodeOptionsExt'
import { BaseclassControlMappTemplateComponent } from './template-reference/baseclass-control-mapp-template.component'
import { BaseclassMappSimilarComponent } from '../components/baseclass-mapp-similar/baseclass-mapp-similar.component'
import { BaseclassMappSimilarInput, BaseclassMappSimilarOutput } from '../components/baseclass-mapp-similar/baseclass-mapp-similar-model'
import { BaseclassMappSelectorComponent } from '../components/baseclass-mapp-selector/baseclass-mapp-selector.component'
import { BaseClassType } from '@common/enum/BaseClassType'
import { BaseClassTreeComponent } from '../../baseclass-tree/baseclass-tree.component'
@Component({
  selector: 'app-baseclass-control-mapp',
  templateUrl: './baseclass-control-mapp.component.html',
  styleUrls: ['../components/baseclass-mapp.component.less', '../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './baseclass-control-mapp.component.less']
})

export class BaseclassControlMappComponent extends BaseclassMappComponent {
  @ViewChild('templateTable') public templateTable!: BaseclassControlMappTemplateComponent;
  @ViewChild('baseTree') public baseTree!: BaseClassTreeComponent;

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: BaseclassManagementService,
    private nzContextMenuService: NzContextMenuService,
  ) {
    super(injector, ele)
  }

  // 选择树节点
  public selectedNode: NzTreeNodeOptionsExt = { key: '', title: '', value: '' }
  // 表格数据加载中
  public showLoading: boolean = false
  // 原数据
  public override orgTableDataSource: BaseClassControlMappTableEntity[] = []
  // 完成数
  public confirmCount?: number
  // 总数
  public sumCount?: number
  // 完成比例
  public confirmPercentage?: string
  // 列配置
  public override tableColumnConfig: TableColumnConfig[] = [
    // {
    //   field: '$index',
    //   width: '60px',
    //   title: '序号',
    // },
    {
      field: 'checkBox',
      width: '50px',
      title: '',
    },
    {
      field: 'equipmentBaseTypeName',
      width: '180px',
      title: '基类设备类型',
    },
    {
      field: 'controlName',
      width: '180px',
      title: '控制名',
    },
    {
      field: 'meanings',
      width: '109px',
      title: '含义',
    },
    {
      field: 'controlNumber',
      width: '79px',
      title: '数量',
      type: devTableColumnType.Numeric,
    },
    {
      field: 'baseTypeId',
      width: '109px',
      title: '基类ID',
      type: devTableColumnType.Numeric,
    },
    {
      field: 'baseTypeName',
      width: '120px',
      title: '基类名',
    },
    {
      field: 'baseCondId',
      width: '100px',
      title: '基类含义ID',
      type: devTableColumnType.Numeric,
    },
    {
      field: 'baseMeaning',
      width: '85px',
      title: '基类含义',
    },
    {
      field: 'childState',
      width: '85px',
      title: '子类状态',
    }
  ]
  // 已选择行
  public selectedRow?: BaseClassControlMappTableEntity

  checkedIds: any = [];
  checkAll: boolean = false;
  checkItems: any = [];

  protected override onInit(): void {
    super.onInit()
    this.refreshList()
  }

  // 树点击事件
  public filterTreeData(event: any): void {
    this.selectedNode = event || { value: '' }
    this.clearFilterData()
    this.baseSelectRow = undefined
    this.requestTableDataSoure()
  }

  // 请求表格数据
  public override async requestTableDataSoure(): Promise<void> {
    // TODO 大数据时后端查询45秒后报错，解决后再看看前端渲染情况，再考虑是否开放全部数据
    if (this.selectedNode!.value === '') {
      return
    }
    this.showLoading = true
    await this.service.getBaseControlById(this.selectedNode!.value!).then(res => {
      const array: BaseClassControlMappTableEntity[] = res.dataList
      this.sumCount = res.sumCount
      this.confirmCount = res.confirmCount
      this.confirmPercentage = this.sumCount ? (this.confirmCount / this.sumCount * 100).toFixed(2) + '%' : '0%'
      array.forEach((item: BaseClassControlMappTableEntity) => {
        item.ids = item.equipmentBaseType + '-' + item.controlName + '-' + item.meanings
      })
      this.orgTableDataSource = array
      this.filterTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource = [...this.orgTableDataSource]
      if (this.orgTableDataSource.length) {
        // 没有已选记忆时，默认选中第一行
        !this.baseSelectRow && this.onRowClick(this.orgTableDataSource[0], 0)
      } else {
        this.templateTable.displayTableDataSource = []
        this.templateTable.baseTableRow = undefined
        this.baseSelectRow = undefined
      }
    }).finally(() => {
      this.showLoading = false
    })
  }

  // 行点击事件
  public override onRowClick(item: BaseClassControlMappTableEntity, index: number): void {
    // 如果选择了其他行，清除下表格的行选择
    if (this.baseSelectRow?.index !== index) {
      this.templateTable.baseSelectRow = undefined
    }
    super.onRowClick(item, index)
    this.selectedRow = { ...item }
  }

  // 行双击事件
  public onRowDBClick(item: BaseClassControlMappTableEntity): void {
    this.openBaseSelector(item)
  }

  // 打开基类选择器
  public openBaseSelector(rowItem: BaseClassControlMappTableEntity): void {
    let commandName: string = rowItem.commandType + ''
    switch (rowItem.commandType) {
      case ControlCommand.remoteControl:
        commandName = '遥控'
        break
      case ControlCommand.remoteDebug:
        commandName = '遥调'
        break
    }
    this.openDialog<BaseclassMappSelectorComponent, {}, Boolean>({
      nzTitle: `设置基类控制ID[${commandName}]-基类设备类型：${rowItem.equipmentBaseTypeName}[${rowItem.equipmentBaseType}]控制名：${rowItem.controlName}`,
      nzWidth: 900,
      nzData: {
        data: this.selectedRow,
        type: 'control'
      },
      nzContent: BaseclassMappSelectorComponent,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.close()
        },
        {
          label: '确定',
          type: 'primary',
          disabled: componentInstance => componentInstance!.btn_disabled,
          // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
          onClick: (async componentInstance => {
            const { baseTypeId, baseCondId } = componentInstance!.infoData
            const res = { baseTypeId, baseCondId, coverFlag: componentInstance!.coverFlag };
            await this.setBaseIdFunc(res.coverFlag, res.baseTypeId, res.baseCondId);
            componentInstance?.close()
          })
        },
      ],
    })
  }

  // 设置基类ID|清除基类ID
  public async setBaseIdFunc(coverFlag: boolean, baseTypeId: number | undefined, baseCondId: number | undefined): Promise<void> {
    const items: ConfirmCheckItems[] = this.templateTable.orgTableDataSource.map(item => { return { equipmentTemplateId: item.equipmentTemplateId, controlId: item.controlId, parameterValue: item.parameterValue } })
    const params: ConfirmCheckParams = {
      conditionIds: items,
      coverFlag: coverFlag,
      baseTypeId: baseTypeId,
      baseCondId: baseCondId,
    }
    await this.service.putBaseControl(params).then(async () => {
      await this.refreshList()
      this.selectedRow = this.displayTableDataSource[this.baseSelectRow!.index]
    })
  }

  // 设置基类ID
  public setBaseIdClick(rowItem: BaseClassControlMappTableEntity): void {
    this.openBaseSelector(rowItem)
  }

  // 清除基类ID
  public cancelBaseIdClick(): void {
    this.setBaseIdFunc(true, undefined, undefined)
  }

  // 确认检查|取消检查
  public async checkFunc(confirm: boolean): Promise<void> {
    const items: ConfirmCheckItems[] = this.templateTable.orgTableDataSource.map(item => { return { equipmentTemplateId: item.equipmentTemplateId, controlId: item.controlId, parameterValue: item.parameterValue } })
    const params: ConfirmCheckParams = {
      conditionIds: items,
      stateFlag: true,
      subState: confirm ? '已检查' : undefined
    }
    await this.service.putBaseControl(params).then(async () => {
      await this.refreshList()
      this.selectedRow = this.displayTableDataSource[this.baseSelectRow!.index]
    })
  }

  // 确认检查
  public confirmCheckClick(): void {
    this.checkFunc(true)
  }

  // 取消检查
  public cancelCheckClick(): void {
    this.checkFunc(false)
  }

  // 有相似按钮
  public hasSimilar(item: BaseClassControlMappTableEntity): boolean {
    if (item.childState === '全相同' && item.baseTypeId && item.baseTypeId.toString().endsWith('001') && (item.baseNameExt?.length > 0) && item.controlName.includes('1')) {
      return true
    }
    return false
  }

  // 相似控制批量指定
  public async similarClick(): Promise<void> {
    this.openDialog<BaseclassMappSimilarComponent, BaseclassMappSimilarInput, BaseclassMappSimilarOutput>({
      nzTitle: '相似控制参数设置',
      nzWidth: 800,
      nzData: {
        rowItem: this.selectedRow,
        type: BaseClassType.command,
        tableType: TableType.baseTable,
      },
      nzContent: BaseclassMappSimilarComponent,
      nzFooter: [{
        label: '取消',
        onClick: componentInstance => componentInstance?.close()
      },
      {
        label: '确定',
        type: 'primary',
        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
        onClick: (async componentInstance => {
          const params = await componentInstance?.confirm()
          if (!params) return
          await this.service.putSimilarControl(params).then(() => {
            this.messageService.success('设置成功')
            componentInstance?.close()
            this.refreshList()
          })
        })
      }]
    })
  }

  // 刷新基类table后，再刷新模板table
  public async refreshAllList(): Promise<void> {
    this.refreshListAndClick()
  }

  // 右键菜单
  public contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    $event.stopPropagation()
    this.nzContextMenuService.create($event, menu);
  }

  // 刷新并点击选中行
  public async refreshListAndClick(): Promise<void> {
    await this.refreshList()
    this.selectedRow = this.orgTableDataSource.filter(item => item.ids === this.selectedRow?.ids)[0]
  }

  //表格选择事件
  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedIds(id, checked);
    this.refreshCheckedBoxStatus();
  }

  onAllChecked(checked: boolean): void {
    this.displayTableDataSource.forEach((item: any) => {
      this.updateCheckedIds(item.ids, checked)
    });
    this.refreshCheckedBoxStatus();
  }

  updateCheckedIds(id: string, checked: boolean): void {
    if (checked) {
      if (!this.checkedIds.includes(id)) {
        this.checkedIds.push(id);
      }
    } else {
      this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
    }
  }

  refreshCheckedBoxStatus(): void {
    this.checkAll = this.displayTableDataSource.length > 0 && this.displayTableDataSource.every((item: any) => {
      return this.checkedIds.includes(item.ids);
    });
  }

  async clearBatchBasicClassID() {
    let params: any = [];
    this.checkedIds.forEach((item: any) => {
      let res = this.orgTableDataSource.find(obj => obj.ids === item);
      let obj = {
        equipmentBaseType: res ? res.equipmentBaseType : '',
        controlName: res ? res.controlName : '',
        meanings: res ? res.meanings : '',
      }
      params.push(obj);
    })
    this.showLoading = true;
    await this.service.batchDeleteBaseControl(params).then((res: any) => {
      this.messageService.success('更新成功!')
      this.onAllChecked(false);
      this.refreshList();
      this.showLoading = false
    })
  }
}