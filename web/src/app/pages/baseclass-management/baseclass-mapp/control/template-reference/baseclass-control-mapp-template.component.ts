import { Component, ElementRef, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core'
import { TableColumnConfig } from '@components/basic/devui-table-filter/devui-table-filter.model'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'
import { BaseClassControlMappTableEntity, BaseClassControlMappTemplateParamsInterface, BaseClassControlMappTemplateTableInterface, ConfirmCheckItems, ConfirmCheckParams, ControlCommand, TableType } from '../baseclass-control-mapp.entity';
import { BaseclassManagementService } from 'app/pages/baseclass-management/baseclass-management.service';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { BaseclassMappSimilarComponent } from '../../components/baseclass-mapp-similar/baseclass-mapp-similar.component';
import { BaseclassMappSimilarInput, BaseclassMappSimilarOutput } from '../../components/baseclass-mapp-similar/baseclass-mapp-similar-model';
import { BaseclassMappSelectorComponent } from '../../components/baseclass-mapp-selector/baseclass-mapp-selector.component';
import { GlobalState } from '@services/global.state';
import { DeviceTemplateService } from 'app/pages/device-template/device-template.service';
import { BaseClassType } from '@common/enum/BaseClassType';

@Component({
  selector: 'app-baseclass-control-mapp-template',
  templateUrl: './baseclass-control-mapp-template.component.html',
  styleUrls: ['../../components/baseclass-mapp.component.less', '../../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './baseclass-control-mapp-template.component.less']
})

export class BaseclassControlMappTemplateComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: BaseclassManagementService,
    private nzContextMenuService: NzContextMenuService,
    private globalState: GlobalState,
    private tempService: DeviceTemplateService,
  ) {
    super(injector, ele)
  }

  // 基类表格行
  public _baseTableRow?: BaseClassControlMappTableEntity
  @Input()
  public set baseTableRow(rowItem: BaseClassControlMappTableEntity | undefined) {
    if (!rowItem) return
    this._baseTableRow = rowItem
    this.clearFilterData()
    this.requestTableDataSoure()
  }
  public get baseTableRow(): BaseClassControlMappTableEntity | undefined {
    return this._baseTableRow
  }

  // 刷新基类表格
  @Output() public refresh: EventEmitter<void> = new EventEmitter<void>()

  // 列数据
  public override orgTableDataSource!: BaseClassControlMappTemplateTableInterface[]
  // 表格数据加载中
  public showLoading: boolean = false
  // 列数据
  public tableColumnConfig: TableColumnConfig[] = [
    {
      field: 'equipmentTemplateName',
      width: '150px',
      title: '模板名',
    },
    {
      field: 'protocolCode',
      width: '150px',
      title: '协议编号',
    },
    {
      field: 'controlName',
      width: '120px',
      title: '控制名',
    },
    {
      field: 'meanings',
      width: '85px',
      title: '含义',
    },
    {
      field: 'equipmentBaseTypeName',
      width: '120px',
      title: '基类设备类型',
    },
    {
      field: 'subState',
      width: '85px',
      title: '基类情况',
    },
    {
      field: 'baseTypeId',
      width: '109px',
      title: '基类ID',
    },
    {
      field: 'baseTypeName',
      width: '85px',
      title: '基类名称',
    },
    {
      field: 'baseCondId',
      width: '100px',
      title: '基类含义ID',
    },
    {
      field: 'baseMeaning',
      width: '85px',
      title: '基类含义',
    },
    {
      field: 'commandTypeName',
      width: '115px',
      title: '控制命令类型',
    },
    {
      field: 'cmdToken',
      width: '100px',
      title: '命令字符串',
    }
  ]
  // 选中行
  public selectedRows?: BaseClassControlMappTemplateTableInterface[]

  // 方法
  protected override onInit(): void {
    super.onInit()
  }

  // 请求表格数据
  public override async requestTableDataSoure(): Promise<void> {
    // url传参使用||''，避免把null传过去了
    const params: BaseClassControlMappTemplateParamsInterface = {
      equipmentBaseType: this.baseTableRow?.equipmentBaseType || '',
      controlName: this.baseTableRow?.controlName || '',
      meanings: this.baseTableRow?.meanings || ''
    }
    this.showLoading = true
    await this.service.getBaseControlDetailByParams(params).then(res => {
      this.orgTableDataSource = res
      this.colorAssign()
      this.filterTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource = [...this.orgTableDataSource]
      this.orgTableDataSource.forEach((item: BaseClassControlMappTemplateTableInterface) => {
        item.ids = item.equipmentTemplateId + '-' + item.controlId + '-' + item.parameterValue
      })
    }).finally(() => {
      this.showLoading = false
    })
  }

  // 行点击事件
  public override onRowClick(item: BaseClassControlMappTemplateTableInterface, index: number): void {
    super.onRowClick(item, index)
    this.selectedRows = [item]
  }

  // 行双击事件
  public onRowDBClick(item: BaseClassControlMappTemplateTableInterface): void {
    this.openBaseSelector(item)
  }

  // 打开基类选择器
  public openBaseSelector(rowItem: BaseClassControlMappTemplateTableInterface): void {
    let commandName: string = rowItem.commandType + ''
    switch (rowItem.commandType) {
      case ControlCommand.remoteControl:
        commandName = '遥控'
        break
      case ControlCommand.remoteDebug:
        commandName = '遥调'
        break
    }
    this.openDialog<BaseclassMappSelectorComponent, {}, Boolean>({
      nzTitle: `设置基类控制ID[${commandName}]-基类设备类型：${rowItem.equipmentBaseTypeName}[${rowItem.equipmentBaseType}]控制名：${rowItem.controlName}`,
      nzWidth: 900,
      nzData: {
        data: this.selectedRows![0],
        type: 'control'
      },
      nzContent: BaseclassMappSelectorComponent,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.close()
        },
        {
          label: '确定',
          type: 'primary',
          disabled: componentInstance => componentInstance!.btn_disabled,
          // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
          onClick: (async componentInstance => {
            const { baseTypeId, baseCondId } = componentInstance!.infoData
            const res = { baseTypeId, baseCondId, coverFlag: componentInstance!.coverFlag };
            await this.setBaseIdFunc(res.coverFlag, res.baseTypeId, res.baseCondId);
            componentInstance?.close()
          })
        },
      ],
    })
  }

  // 设置基类ID|清除基类ID
  public async setBaseIdFunc(coverFlag: boolean, baseTypeId: number | undefined, baseCondId: number | undefined): Promise<void> {
    const items: ConfirmCheckItems[] = this.selectedRows!.map(item => { return { equipmentTemplateId: item.equipmentTemplateId, controlId: item.controlId, parameterValue: item.parameterValue } })
    const params: ConfirmCheckParams = {
      conditionIds: items,
      coverFlag: coverFlag,
      baseTypeId: baseTypeId,
      baseCondId: baseCondId,
    }
    await this.service.putBaseControl(params).then(() => {
      this.refresh.emit()
    })
  }

  // 设置基类ID
  public setBaseIdClick(rowItem: BaseClassControlMappTemplateTableInterface): void {
    this.openBaseSelector(rowItem)
  }

  // 清除基类ID
  public cancelBaseIdClick(): void {
    this.setBaseIdFunc(true, undefined, undefined)
  }

  // 查看模板原始控制
  public toTemplateClick(rowItem: BaseClassControlMappTemplateTableInterface): void {
    this.tempService.setTempId(rowItem.equipmentTemplateId)
    this.globalState.notifyDataChanged('routeToTemplate', rowItem.equipmentTemplateId)
    this.router.navigateByUrl('/pages/device-template')
  }

  // 确认检查|取消检查
  public checkFunc(confirm: boolean): void {
    const items: ConfirmCheckItems[] = this.selectedRows!.map(item => { return { equipmentTemplateId: item.equipmentTemplateId, controlId: item.controlId, parameterValue: item.parameterValue } })
    const params: ConfirmCheckParams = {
      conditionIds: items,
      stateFlag: true,
      subState: confirm ? '已检查' : undefined
    }
    this.service.putBaseControl(params).then(() => {
      this.refresh.emit()
    })
  }

  // 确认检查
  public confirmCheckClick(): void {
    this.checkFunc(true)
  }

  // 取消检查
  public cancelCheckClick(): void {
    this.checkFunc(false)
  }

  // 有相似按钮
  public hasSimilar(): boolean {
    if (this.selectedRows && this.selectedRows![0].baseTypeId && this.selectedRows![0].baseTypeId.toString().endsWith('001') && (this.selectedRows![0].baseNameExt?.length > 0) && this.selectedRows![0].controlName.includes('1')) {
      if (this.selectedRows![0].commandType === ControlCommand.remoteControl && this.selectedRows![0].baseCondId === null) {
        return false
      }
      return true
    }
    return false
  }

  // 相似控制批量指定
  public similarClick(): void {
    this.openDialog<BaseclassMappSimilarComponent, BaseclassMappSimilarInput, BaseclassMappSimilarOutput>({
      nzTitle: '相似控制参数设置',
      nzWidth: 800,
      nzData: {
        rowItem: this.selectedRows![0],
        type: BaseClassType.command,
        tableType: TableType.templateTable,
      },
      nzContent: BaseclassMappSimilarComponent,
      nzFooter: [{
        label: '取消',
        onClick: componentInstance => componentInstance?.close()
      },
      {
        label: '确定',
        type: 'primary',
        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
        onClick: (async componentInstance => {
          const params = await componentInstance?.confirm()
          if (!params) return
          await this.service.putSimilarControl(params).then(() => {
            this.messageService.success('设置成功')
            componentInstance?.close()
            this.refresh.emit()
          })
        })
      }]
    })
  }

  // 右键菜单
  public contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    $event.stopPropagation()
    this.nzContextMenuService.create($event, menu)
  }

  // [基类情况 | 基类ID | 基类名称 | 基类含义ID | 基类含义] 颜色逻辑
  public colorAssign(): void {
    this.orgTableDataSource.forEach(item => {
      // 有遗漏 且 模板表格baseTypeId为空，标红色
      if (this.baseTableRow?.childState === '有遗漏') {
        switch (item.commandType) {
          case ControlCommand.remoteDebug:
            item.baseTypeId === null && (item.baseTypeIdColor = 'red_text')
            break
          case ControlCommand.remoteControl:
            item.baseTypeId === null && (item.baseTypeIdColor = 'red_text')
            item.baseCondId === null && (item.baseCondIdColor = 'red_text')
            item.baseCondId === null && (item.baseMeaningColor = 'red_text')
            break
        }
      }
      // 有不同 且 模板表格baseTypeId和基类baseTypeId不相同，标黄色
      else if (this.baseTableRow?.childState === '有不同') {
        switch (item.commandType) {
          case ControlCommand.remoteDebug:
            this.baseTableRow?.baseTypeId !== item.baseTypeId && (item.baseTypeIdColor = 'orange_text')
            break
          case ControlCommand.remoteControl:
            this.baseTableRow?.baseTypeId !== item.baseTypeId && (item.baseTypeIdColor = 'orange_text')
            this.baseTableRow?.baseCondId !== item.baseCondId && (item.baseCondIdColor = 'orange_text')
            this.baseTableRow?.baseMeaning !== item.baseMeaning && (item.baseMeaningColor = 'orange_text')
            break
        }
      }
      // 已检查Part 且 模板表格subState未检查，标红色
      else if (this.baseTableRow?.childState === '已检查Part' && !item.subState) {
        item.subStateColor = 'red_text'
      }
    })
  }
}