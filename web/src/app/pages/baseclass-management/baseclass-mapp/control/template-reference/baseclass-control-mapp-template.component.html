<div style="height: 100%;">
  <!-- 表格 -->
  <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
    [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'100%'"
    [containFixHeaderHeight]="true" [fixHeader]="true" (tableScrollEvent)="tableScrollEvent($event)" dLoading
    [showLoading]="showLoading">
    <thead dTableHead>
      <tr dTableRow>
        <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
          (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
          (resizeEndEvent)="onResize($event, colConfig.field)" [minWidth]="colConfig.minWidth!">
          {{ colConfig.title }}
          <!-- 空白 -->
          <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
          <!-- 输入框 -->
          <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
            (ngModelChange)="filterChange()" />
        </th>
      </tr>
    </thead>
    <tbody dTableBody>
      <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
        <tr dTableRow (mouseup)="onRowClick(rowItem, rowIndex)"
          [ngClass]="{ 'table-row-selected': baseSelectRow?.item.ids === rowItem.ids }"
          (contextmenu)="contextMenu($event, menu)" (dblclick)="onRowDBClick(rowItem)">
          <td *ngFor="let colConfig of tableColumnConfig" dTableCell [title]="rowItem[colConfig.field]">
            <!-- 复杂的变色逻辑 -->


            @if (colConfig.field === '$index') {
            {{ rowIndex + 1 }}
            }


            @else if (colConfig.field === 'subState' && rowItem.subStateColor)
            {
            <!-- ↑【基类情况】 颜色变化 -->
            <div class="span-text" [ngClass]="rowItem.subStateColor">
              {{ rowItem[colConfig.field] || '未检查' }}
            </div>
            }


            @else if ((colConfig.field === 'baseTypeId' || colConfig.field === 'baseTypeName') &&
            rowItem.baseTypeIdColor)
            {
            <!-- ↑【基类ID | 基类名称】 颜色变化 -->
            <div class="span-text" [ngClass]="rowItem.baseTypeIdColor">
              {{ (rowItem[colConfig.field] === null) ? '未绑定' : rowItem[colConfig.field] }}
            </div>
            }


            @else if (colConfig.field === 'baseCondId' && rowItem.baseCondIdColor)
            {
            <!-- ↑【基类含义ID】 颜色变化 -->
            <div class="span-text" [ngClass]="rowItem.baseMeaningColor">
              {{ (rowItem[colConfig.field] === null) ? '未绑定' : rowItem[colConfig.field] }}
            </div>
            }


            @else if (colConfig.field === 'baseMeaning' && rowItem.baseMeaningColor)
            {
            <!-- ↑【基类含义】 颜色变化 -->
            <div class="span-text" [ngClass]="rowItem.baseMeaningColor">
              {{ (rowItem[colConfig.field] === null) ? '未绑定' : rowItem[colConfig.field] }}
            </div>
            }


            @else {
            <div class="span-text">{{ rowItem[colConfig.field] }}</div>
            }


          </td>
        </tr>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu>
            <li nz-menu-item (click)="setBaseIdClick(rowItem)">
              <iconfont icon="icon-peizhi1" class="right-menu-icon" />设置基类ID
            </li>
            <li nz-menu-item (click)="cancelBaseIdClick()">
              <iconfont icon="icon-peizhi2" class="right-menu-icon" />清除基类ID
            </li>
            <li nz-menu-item (click)="toTemplateClick(rowItem)">
              <iconfont icon="icon-setctrl" class="right-menu-icon" />模板原始控制
            </li>
            <li nz-menu-item (click)="confirmCheckClick()">
              <iconfont icon="icon-confirm2" class="right-menu-icon" />确认检查
            </li>
            <li nz-menu-item (click)="cancelCheckClick()">
              <iconfont icon="icon-resend" class="right-menu-icon" />取消检查
            </li>
            <li nz-menu-item *ngIf="hasSimilar()" (click)="similarClick()">
              <iconfont icon="icon-piliang" class="right-menu-icon" />相似控制批量指定
            </li>
          </ul>
        </nz-dropdown-menu>
      </ng-template>
    </tbody>

    <!-- 空数据 -->
    <ng-template #noResultTemplateRef>
      <div style="text-align: center; margin-top: 20px">
        <nz-empty nzNotFoundImage="simple" />
      </div>
    </ng-template>

  </d-data-table>
</div>