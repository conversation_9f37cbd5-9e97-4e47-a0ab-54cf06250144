import { Component, ElementRef, Injector, ViewChild } from '@angular/core'
import { devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model'
import { BaseclassMappComponent } from '../components/baseclass-mapp.component'
import { BaseclassManagementService } from '../../baseclass-management.service'
import { NzMessageService } from 'ng-zorro-antd/message'
import { BaseclassEventMappTemplateComponent } from './baseclass-event-mapp-template.component'
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown'
import { NzModalService } from 'ng-zorro-antd/modal'
import { SimilarEventSetComponent } from './similar-event-set/similar-event-set.component'
import { cloneDeep } from 'lodash'
import { BaseclassMappSelectorComponent } from '../components/baseclass-mapp-selector/baseclass-mapp-selector.component'
import { BaseClassTreeComponent } from '../../baseclass-tree/baseclass-tree.component'
import { BaseclassMappSimilarComponent } from '../components/baseclass-mapp-similar/baseclass-mapp-similar.component'
import { BaseclassMappSimilarInput, BaseclassMappSimilarOutput } from '../components/baseclass-mapp-similar/baseclass-mapp-similar-model'
import { BaseClassType } from '@common/enum/BaseClassType';
import { TableType } from '../control/baseclass-control-mapp.entity'
import { NavigationEnd } from '@angular/router'

@Component({
    selector: 'app-baseclass-event-mapp',
    templateUrl: './baseclass-event-mapp.component.html',
    styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', '../components/baseclass-mapp.component.less', './baseclass-event-mapp.component.less']
})

export class BaseclassEventMappComponent extends BaseclassMappComponent {

    // 原数据
    public orgTableDataSource: Array<any> = []
    public tableColumnConfig = [
        {
            field: 'checkBox',
            width: '50px',
            title: '',
        },
        {
            field: 'equipmentBaseTypeName',
            width: '100px',
            title: '基类设备类型',
        },
        {
            field: 'eventName',
            width: '120px',
            title: '事件名',
        },
        {
            field: 'meanings',
            width: '120px',
            title: '事件条件',
        },
        {
            field: 'eventNumber',
            width: '80px',
            title: '数量',
        },
        {
            field: 'baseTypeId',
            width: '120px',
            title: '基类ID',
        },
        {
            field: 'baseTypeName',
            width: '120px',
            title: '基类名',
        },
        {
            field: 'childState',
            width: '80px',
            title: '子项情况',
        }
    ]
    isUpdating: boolean = false;
    checkIds: string = '';
    checkItem: any;
    showBatch: boolean = false;
    params: any;
    currentBaseType: any;
    // 完成数
    public confirmCount?: number
    // 总数
    public sumCount?: number
    // 完成比例
    public confirmPercentage?: string

    checkedIds: any = [];
    checkAll: boolean = false;
    checkItems: any = [];

    @ViewChild('eventTemp') eventTemp!: BaseclassEventMappTemplateComponent;
    @ViewChild('baseTree') baseTree!: BaseClassTreeComponent;

    public constructor(
        injector: Injector,
        ele: ElementRef,
        private service: BaseclassManagementService,
        private message: NzMessageService,
        private modal: NzModalService,
        private nzContextMenuService: NzContextMenuService,
    ) {
        super(injector, ele)
    }

    protected onInit(): void {
        this.filterTreeData(null);

        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
                if (event.url == '/pages/baseclass-event-mapp') {
                    this.refreshTable();
                }
            }
        })
    }

    filterTreeDataFromBaseTree(event: any) {
        this.checkItem = null;
        this.filterTreeData(event);
    }

    filterTreeData(event: any) {
        this.isUpdating = true;
        if (event) {
            this.currentBaseType = event.value;
            this.service.getBaseEventById(this.currentBaseType).subscribe((res: any) => {
                let list = res.data.dataList;
                list.forEach((item: any) => {
                    item['ids'] = item.equipmentBaseType.toString() + '-' + item.eventName.toString() + '-' + (item.meanings ? item.meanings?.toString() : '');
                });
                this.orgTableDataSource = list;
                this.filterTableDataSource = [...this.orgTableDataSource];
                this.displayTableDataSource = [...this.orgTableDataSource];
                this.isUpdating = false;
                this.antiContentHeightString = document.documentElement.scrollHeight - (this.contentHeight + 145) + 'px';
                this.filterChange();
                if (this.checkItem) {
                    let ind = this.orgTableDataSource.findIndex(obj => obj['ids'] === this.checkItem['ids']);
                    this.checkItem = this.orgTableDataSource[ind];
                    this.checkIds = this.checkItem.ids;
                    this.getBaseEventDetail(this.checkItem);
                } else {
                    if (this.displayTableDataSource[0]) {
                        this.getBaseEventDetail(this.displayTableDataSource[0]);
                    } else {
                        this.eventTemp.orgTableDataSource = [];
                        this.eventTemp.filterTableDataSource = [...this.eventTemp.orgTableDataSource];
                        this.eventTemp.displayTableDataSource = [...this.eventTemp.orgTableDataSource]
                    }
                }
                this.sumCount = res.data.sumCount
                this.confirmCount = res.data.confirmCount
                if (this.confirmCount && this.confirmCount >= 0 && this.sumCount && this.sumCount !== 0) {
                    this.confirmPercentage = (this.confirmCount / this.sumCount * 100).toFixed(2) + '%';
                } else {
                    this.confirmPercentage = '0%';
                }
            })
        } else {
            // TODO 大数据时后端查询45秒后报错，解决后再看看前端渲染情况，再考虑是否开放全部数据
            return
            this.service.getBaseEventById('').subscribe((res: any) => {
                let list = res.data.dataList;
                list.forEach((item: any) => {
                    item['ids'] = item.equipmentBaseType.toString() + '-' + item.eventName.toString() + '-' + (item.meanings ? item.meanings?.toString() : '');
                });
                this.orgTableDataSource = list;
                this.filterTableDataSource = [...this.orgTableDataSource];
                this.displayTableDataSource = [...this.orgTableDataSource];
                this.isUpdating = false;
                this.antiContentHeightString = document.documentElement.scrollHeight - (this.contentHeight + 145) + 'px';
                this.filterChange();
                if (this.checkItem) {
                    let ind = this.orgTableDataSource.findIndex(obj => obj['ids'] === this.checkItem['ids']);
                    this.checkItem = this.orgTableDataSource[ind];
                    this.checkIds = this.checkItem.ids;
                    this.getBaseEventDetail(this.checkItem);
                } else {
                    if (this.displayTableDataSource[0]) {
                        this.getBaseEventDetail(this.displayTableDataSource[0]);
                    } else {
                        this.eventTemp.orgTableDataSource = [];
                        this.eventTemp.filterTableDataSource = [...this.eventTemp.orgTableDataSource];
                        this.eventTemp.displayTableDataSource = [...this.eventTemp.orgTableDataSource]
                    }
                }
                this.sumCount = res.data.sumCount
                this.confirmCount = res.data.confirmCount
                if (this.confirmCount && this.confirmCount >= 0 && this.sumCount && this.sumCount !== 0) {
                    this.confirmPercentage = (this.confirmCount / this.sumCount * 100).toFixed(2) + '%';
                } else {
                    this.confirmPercentage = '0%';
                }
            })
        }
    }

    getBaseEventDetail(item: any) {
        this.checkIds = item.ids;
        this.checkItem = item;
        this.params = {
            equipmentBaseType: item.equipmentBaseType,
            eventName: encodeURIComponent(item.eventName),
            meanings: encodeURIComponent(item.meanings),
            baseTypeId: item.baseTypeId,
            state: item.childState,
        }
        if (item.baseTypeId && item.baseTypeId.toString().substr(-3, 3) === '001'
            && (item.baseNameExt && item.baseNameExt.length > 0)
            && item.childState === '全相同'
            && item.eventName.includes('1')) {
            this.showBatch = true;
        } else {
            this.showBatch = false;
        }
    }

    refreshTable() {
        if (this.currentBaseType) {
            this.filterTreeData({ value: this.currentBaseType });
        } else {
            this.filterTreeData(null);
        }
    }

    contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        this.nzContextMenuService.create($event, menu);
    }

    setBaseEvent() {
        const title = `设置基类事件ID - 基类设备类型：${this.checkItem.equipmentBaseTypeName}[${this.checkItem.equipmentBaseType}] 事件名：${this.checkItem.eventName} 事件含义：${this.checkItem.meanings}`;

        const modal = this.modal.create<BaseclassMappSelectorComponent>({
            nzTitle: title,
            nzContent: BaseclassMappSelectorComponent,
            nzData: { data: this.checkItem, type: 'event' },
            nzWidth: 900,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.close()
                },
                {
                    label: '确定',
                    type: 'primary',
                    disabled: componentInstance => componentInstance!.btn_disabled,
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        modal.afterClose.subscribe((res: any) => {
            if (res) {
                let list: any[] = [];
                if (this.eventTemp && this.eventTemp.orgTableDataSource && this.eventTemp.orgTableDataSource.length > 0) {
                    this.eventTemp.orgTableDataSource.forEach((item: any) => {
                        list.push({
                            equipmentTemplateId: item.equipmentTemplateId,
                            eventId: item.eventId,
                            eventConditionId: item.eventConditionId,
                        })
                    })
                    const params = {
                        baseTypeId: res.baseTypeId,
                        coverFlag: res.coverFlag,
                        conditionIds: list,
                        stateFlag: false,
                        subState: ''
                    }
                    this.isUpdating = true;
                    this.service.updateBaseEvent(params).subscribe(res => {
                        this.isUpdating = false;
                        this.refreshTable();
                    })
                } else {

                }
            }
        })
    }

    clearBaseEvent() {
        let list: any[] = [];
        if (this.eventTemp && this.eventTemp.orgTableDataSource && this.eventTemp.orgTableDataSource.length > 0) {
            this.eventTemp.orgTableDataSource.forEach((item: any) => {
                list.push({
                    equipmentTemplateId: item.equipmentTemplateId,
                    eventId: item.eventId,
                    eventConditionId: item.eventConditionId,
                })
            })
            const params = {
                baseTypeId: null,
                coverFlag: true,
                conditionIds: list,
                stateFlag: false,
                subState: ''
            }
            this.isUpdating = true;
            this.service.updateBaseEvent(params).subscribe(res => {
                this.isUpdating = false;
                this.refreshTable();
                this.getBaseEventDetail(this.checkItem);
            })
        } else {

        }
    }

    confirmCheck() {
        let list: any[] = [];
        if (this.eventTemp && this.eventTemp.orgTableDataSource && this.eventTemp.orgTableDataSource.length > 0) {
            this.eventTemp.orgTableDataSource.forEach((item: any) => {
                list.push({
                    equipmentTemplateId: item.equipmentTemplateId,
                    eventId: item.eventId,
                    eventConditionId: item.eventConditionId,
                })
            })
            const params = {
                baseTypeId: this.checkItem.baseTypeId,
                coverFlag: false,
                conditionIds: list,
                stateFlag: true,
                subState: '已检查'
            }
            this.isUpdating = true;
            this.service.updateBaseEvent(params).subscribe(res => {
                this.isUpdating = false;
                this.refreshTable();
                this.getBaseEventDetail(this.checkItem);
            })
        } else {

        }
    }

    cancelCheck() {
        let list: any[] = [];
        if (this.eventTemp && this.eventTemp.orgTableDataSource && this.eventTemp.orgTableDataSource.length > 0) {
            this.eventTemp.orgTableDataSource.forEach((item: any) => {
                list.push({
                    equipmentTemplateId: item.equipmentTemplateId,
                    eventId: item.eventId,
                    eventConditionId: item.eventConditionId,
                })
            })
            const params = {
                baseTypeId: this.checkItem.baseTypeId,
                coverFlag: false,
                conditionIds: list,
                stateFlag: true,
                subState: ''
            }
            this.isUpdating = true;
            this.service.updateBaseEvent(params).subscribe(res => {
                this.isUpdating = false;
                this.refreshTable();
                this.getBaseEventDetail(this.checkItem);
            })
        } else {

        }
    }

    openSimilarEventSet() {
        this.openDialog<BaseclassMappSimilarComponent, BaseclassMappSimilarInput, BaseclassMappSimilarOutput>({
            nzTitle: '相似信号参数设置',
            nzWidth: 800,
            nzData: {
                rowItem: this.checkItem,
                type: BaseClassType.event,
                tableType: TableType.baseTable,
            },
            nzContent: BaseclassMappSimilarComponent,
            nzFooter: [{
                label: '取消',
                onClick: componentInstance => componentInstance?.close()
            },
            {
                label: '确定',
                type: 'primary',
                // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
                onClick: (async componentInstance => {
                    const params = await componentInstance?.confirm()
                    if (!params) return
                    await this.service.updateSimilarEvent(params).subscribe(res => {
                        this.message.success('更新成功！');
                        this.refreshTable();
                        this.getBaseEventDetail(this.checkItem);
                        componentInstance?.close();
                    })
                })
            }]
        })
    }

    refreshTree() {
        this.checkItem = null;
        this.baseTree.searchAllData();
    }

    //表格选择事件
    onItemChecked(id: string, checked: boolean): void {
        this.updateCheckedIds(id, checked);
        this.refreshCheckedBoxStatus();
    }

    onAllChecked(checked: boolean): void {
        this.displayTableDataSource.forEach((item: any) => {
            this.updateCheckedIds(item.ids, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.checkedIds.includes(id)) {
                this.checkedIds.push(id);
            }
        } else {
            this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = this.displayTableDataSource.length > 0 && this.displayTableDataSource.every((item: any) => {
            return this.checkedIds.includes(item.ids);
        });
    }

    async clearBatchBasicClassID() {
        let params: any = [];
        this.checkedIds.forEach((item: any) => {
            let res = this.orgTableDataSource.find(obj => obj.ids === item);
            let obj = {
                equipmentBaseType: res.equipmentBaseType,
                eventName: res.eventName,
                meanings: res.meanings,
            }
            params.push(obj);
        })
        this.isUpdating = true;
        await this.service.batchDeleteBaseEvent(params).then((res: any) => {
            this.messageService.success('更新成功!')
            this.onAllChecked(false);
            this.refreshTable();
            this.isUpdating = false;
        })
    }
}