<d-data-table
    #myTable
    [dataSource]="displayTableDataSource" 
    [scrollable]="true"
    [tableWidthConfig]="tableColumnConfig"
    [onlyOneColumnSort]="true"
    [tableHeight]="'100%'"
    [containFixHeaderHeight]="true"
    [fixHeader]="true"
    (tableScrollEvent)="tableScrollEvent($event)"
    (contextmenu)="contextMenu($event, menu)"
    dLoading 
    [loadingStyle]="'default'"
    [loading]="isUpdating">
    <thead dTableHead>
        <tr dTableRow>
            <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
                (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)">
                {{ colConfig.title }}
                <!-- 空白 -->
                <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                <!-- 输入框 -->
                <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                    (ngModelChange)="filterChange()" />
            </th>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow (mousedown)="selectRowItem(rowItem)" (dblclick)="setBaseEvent()" [ngClass]="{'table-row-selected': checkId === rowItem.equipmentTemplateId}">
                <ng-container *ngFor="let col of tableColumnConfig">
                    <td *ngIf="col['field'] !== 'baseTypeId' && col['field'] !== 'baseTypeName' && col['field'] !== 'subState'" dTableCell [title]="rowItem[col.field]">
                        <div class="span-text">{{ rowItem[col.field] }}</div>
                    </td>
                    <td *ngIf="col['field'] === 'subState'" dTableCell [title]="rowItem[col.field]">
                        <div class="span-text" *ngIf="rowItem[col.field]">{{ rowItem[col.field] }}</div>
                        <div class="span-text red_text" *ngIf="!rowItem[col.field] && _params['state'] === '已检查Part'">未检查</div>
                    </td>
                    <td *ngIf="col['field'] === 'baseTypeId' || col['field'] === 'baseTypeName'" dTableCell [title]="rowItem[col.field]">
                        <div class="span-text"
                            *ngIf="!(rowItem['baseTypeId'] === null && _params['state'] === '有遗漏') && !(rowItem['baseTypeId'] !== _params['baseTypeId'] && _params['state'] === '有不同')">{{ rowItem[col.field] }}
                        </div>
                        <div class="span-text red_text"
                            *ngIf="rowItem['baseTypeId'] === null && _params['state'] === '有遗漏'">未绑定
                        </div>
                        <div class="span-text orange_text"
                            *ngIf="rowItem['baseTypeId'] !== _params['baseTypeId'] && _params['state'] === '有不同'">{{ rowItem[col.field] }}
                        </div>
                    </td>
                </ng-container>
            </tr>
        </ng-template>
    </tbody>
</d-data-table>
<nz-dropdown-menu #menu="nzDropdownMenu">
    <ul nz-menu>
        <li nz-menu-item (click)="setBaseEvent()" class="menu_item"><iconfont [icon]="'icon-peizhi1'" style="font-size: 16px;"></iconfont>&nbsp;&nbsp;设置基类ID</li>
        <li nz-menu-item (click)="clearBaseEvent()" class="menu_item"><iconfont [icon]="'icon-peizhi2'" style="font-size: 16px;"></iconfont>&nbsp;&nbsp;清除基类ID</li>
        <li nz-menu-item (click)="toTemplateClick()"><iconfont icon="icon-chashebei" class="right-menu-icon" />模板原始事件</li>
        <li nz-menu-item (click)="confirmCheck()" class="menu_item"><iconfont [icon]="'icon-confirm2'" style="font-size: 16px;"></iconfont>&nbsp;&nbsp;确认检查</li>
        <li nz-menu-item (click)="cancelCheck()" class="menu_item"><iconfont [icon]="'icon-resend'" style="font-size: 16px;"></iconfont>&nbsp;&nbsp;取消检查</li>
        <li nz-menu-item *ngIf="showBatch" (click)="openSimilarEventSet()" class="menu_item"><iconfont [icon]="'icon-piliang'" style="font-size: 16px;"></iconfont>&nbsp;&nbsp;相似事件条件批量指定</li>
    </ul>
</nz-dropdown-menu>