import { Component, ElementRef, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { tableResizeFunc } from 'ng-devui/data-table';
import { NzModalService } from 'ng-zorro-antd/modal';
import { BaseclassMappSelectorComponent } from '../../components/baseclass-mapp-selector/baseclass-mapp-selector.component';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { GlobalState } from '@services/global.state';
import { BaseclassManagementService } from 'app/pages/baseclass-management/baseclass-management.service';
import { DeviceTemplateService } from 'app/pages/device-template/device-template.service';
import { TableType } from '../../control/baseclass-control-mapp.entity';
import { BaseClassType } from '@common/enum/BaseClassType';
import { BaseclassMappSimilarComponent } from '../../components/baseclass-mapp-similar/baseclass-mapp-similar.component';
import { BaseclassMappSimilarInput, BaseclassMappSimilarOutput } from '../../components/baseclass-mapp-similar/baseclass-mapp-similar-model';

@Component({
  selector: 'app-baseclass-signal-mapp-template',
  templateUrl: './baseclass-signal-mapp-template.component.html',
  styleUrls: ['../../../../../@components/basic/devui-table-filter/devui-table-filter.component.less',
    '../../components/baseclass-mapp.component.less', './baseclass-signal-mapp-template.component.less']
})
export class BaseclassSignalMappTemplateComponent extends DevuiTableFilterComponent implements OnInit {

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private modal: NzModalService,
    private service: BaseclassManagementService,
    private nzContextMenuService: NzContextMenuService,
    private _state: GlobalState,
    private tempService: DeviceTemplateService,
  ) {
    super(injector, ele)
  }
  @Input('data')
  set data(value: any) {
    if (value) {
      this.selectedRow = value;
      this.requestTableDataSoure();
    }
  };
  showBatch: boolean = false;
  isUpdating: boolean = false;
  selectedRow: any;

  // 刷新基类表格
  @Output() public refresh: EventEmitter<void> = new EventEmitter<void>()
  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'equipmentTemplateName',
      width: '150px',
      title: '模板名',
    },
    {
      field: 'protocolCode',
      width: '150px',
      title: '协议编码',
    },
    {
      field: 'signalName',
      width: '120px',
      title: '信号名',
    },
    {
      field: 'meanings',
      width: '85px',
      title: '含义',
    },
    {
      field: 'equipmentBaseTypeName',
      width: '120px',
      title: '基类设备类型',
    }, {
      field: 'subState',
      width: '85px',
      title: '基类情况',
    },
    {
      field: 'baseTypeId',
      width: '109px',
      title: '基类ID',
    },
    {
      field: 'baseTypeName',
      width: '85px',
      title: '基类名',
    },
    {
      field: 'baseCondId',
      width: '100px',
      title: '基类含义ID',
    },
    {
      field: 'baseMeaning',
      width: '85px',
      title: '基类含义',
    },
    {
      field: 'signalCategoryName',
      width: '100px',
      title: '信号种类',
    }, {
      field: 'channelNo',
      width: '70px',
      title: '通道',
    },
    {
      field: 'unit',
      width: '70px',
      title: '单位',
    }
  ]

  // 方法
  protected onInit(): void {
    super.onInit()
  }

  async requestTableDataSoure(): Promise<void> {
    const item = this.selectedRow;
    this.isUpdating = true;
    if (Object.keys(item).length) {
      await this.service.getBaseSignalDetailByParams(item.equipmentBaseType, encodeURIComponent(item.signalName || ''), encodeURIComponent(item.meanings || '')).then((res: any) => {
        this.orgTableDataSource = res;
        this.colorAssign();
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.isUpdating = false;
        this.baseSelectRow && (this.displayTableDataSource[this.baseSelectRow.index]['$checked'] = true)
      })
    } else {
      this.orgTableDataSource = [];
      this.filterTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource = [...this.orgTableDataSource]
      this.isUpdating = false;
    }
  }

  // [基类情况 | 基类ID | 基类名称 | 基类含义ID | 基类含义] 颜色逻辑
  public colorAssign(): void {
    this.orgTableDataSource.forEach(item => {
      // 有遗漏，标红色
      if (this.selectedRow?.childState === '有遗漏' && (!item.baseTypeId || item.baseCondId === null)) {

        switch (item.signalCategory) {
          case 1:
            !item.baseTypeId && (item.baseTypeIdColor = 'red_text')
            break
          case 2:
            !item.baseTypeId && (item.baseTypeIdColor = 'red_text')
            item.baseMeaningColor = 'red_text'
            break
        }
      }
      // 有不同 且 模板表格baseTypeId和基类baseTypeId不相同，标黄色
      else if (this.selectedRow?.childState === '有不同') {
        switch (item.signalCategory) {
          case 1:
            this.selectedRow?.baseTypeId !== item.baseTypeId && (item.baseTypeIdColor = 'orange_text')
            break
          case 2:
            this.selectedRow?.baseTypeId !== item.baseTypeId && (item.baseTypeIdColor = 'orange_text')
            this.selectedRow?.baseCondId !== item.baseCondId && (item.baseMeaningColor = 'orange_text')
            break
        }
      }
      // 已检查Part 且 模板表格subState未检查，标红色
      else if (this.selectedRow?.childState === '已检查Part' && !item.subState) {
        item.subStateColor = 'red_text'
      }
    })
  }

  // 打开基类映射选择器
  onRowDBClick(event: any) {
    const signalCategory = event.signalCategory === 2 ? '开关量' : '模拟量';
    const meaning = event.meanings ? ` 含义：${event.meanings}` : '';
    const title = `设置基类信号ID[${signalCategory}] - 基类设备类型：${event.equipmentBaseTypeName}[${event.equipmentBaseType}] 信号名：${event.signalName}` + meaning;

    const modal = this.modal.create<BaseclassMappSelectorComponent>({
      nzTitle: title,
      nzContent: BaseclassMappSelectorComponent,
      nzData: { data: event, type: 'signal' },
      nzWidth: 900,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.close()
        },
        {
          label: '确定',
          type: 'primary',
          disabled: componentInstance => componentInstance!.btn_disabled,
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzMaskClosable: false
    })
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        this.updateBaseSignalFunc(event, res);
      }
    })
  }

  updateBaseSignalFunc(event: any, res?: any): void {
    const list = [{ equipmentTemplateId: event.equipmentTemplateId, signalId: event.signalId, stateValue: event.stateValue }],
      params = {
        baseTypeId: res ? res.baseTypeId : '',
        baseCondId: res ? res.baseCondId : '',
        coverFlag: res ? res.coverFlag : true,  // 覆盖标志 true则覆盖已有值的 空 false不覆盖
        conditionIds: list,
      }
    this.service.updateBaseSignal(params).subscribe(result => {
      const text = res ? '设置成功!' : '清除成功!'
      this.messageService.success(text)
      this.refreshList()
      this.refresh.emit()
    })
  }

  contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    this.nzContextMenuService.create($event, menu);
  }

  // 清除基类
  clearBasicClassID(event: any) {
    this.updateBaseSignalFunc(event);
  }

  // 查看模板原始信号
  originalSignal(event: any) {
    let tempId = event.equipmentTemplateId;
    this.tempService.setTempId(tempId)
    this._state.notifyDataChanged('routeToTemplate', tempId);
    this.router.navigateByUrl('/pages/device-template').finally();
  }

  // 确认检查
  confirmCheck(event: any) {
    this.checkFunc(event, true);
  }

  // 取消检查
  cancelCheck(event: any) {
    this.checkFunc(event, false)
  }

  // 确认检查|取消检查
  checkFunc(event: any, confirm: boolean): void {
    const list = [{ equipmentTemplateId: event.equipmentTemplateId, signalId: event.signalId, stateValue: event.stateValue }],
      params = {
        conditionIds: list,
        stateFlag: true,
        subState: confirm ? '已检查' : null
      }
    this.service.updateBaseSignal(params).subscribe(() => {
      this.refreshList()
      this.refresh.emit()
    })
  }

  onRowClick(item: any, index: number) {
    super.onRowClick(item, index);
    const isBaseType001: boolean = item.baseTypeId && item.baseTypeId.toString().substr(-3, 3) === '001';
    const hasBaseNameExt: boolean = item.baseNameExt && item.baseNameExt.length > 0;
    const hasSignalName1: boolean = item.signalName.includes('1');
    if (isBaseType001 && hasBaseNameExt && hasSignalName1) {
      const isSignalCategory2: boolean = item.signalCategory === 2;
      const hasNoBaseCondId: boolean = item.baseCondId === null || item.baseCondId === undefined;
      this.showBatch = isSignalCategory2 ? !hasNoBaseCondId : true;
    } else {
      this.showBatch = false;
    }
  }

  // 相似信号批量指定
  public async similarClick(item: any): Promise<void> {
    this.openDialog<BaseclassMappSimilarComponent, BaseclassMappSimilarInput, BaseclassMappSimilarOutput>({
      nzTitle: '相似信号参数设置',
      nzWidth: 800,
      nzData: {
        rowItem: item,
        type: BaseClassType.signal,
        tableType: TableType.templateTable,
      },
      nzContent: BaseclassMappSimilarComponent,
      nzFooter: [{
        label: '取消',
        onClick: componentInstance => componentInstance?.close()
      },
      {
        label: '确定',
        type: 'primary',
        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
        onClick: (async componentInstance => {
          const params = await componentInstance?.confirm()
          if (!params) return
          await this.service.putSimilarSignal(params).then(() => {
            this.messageService.success('设置成功')
            componentInstance?.close()
            this.refreshList()
            this.refresh.emit()
          })
        })
      }]
    })
  }
}
