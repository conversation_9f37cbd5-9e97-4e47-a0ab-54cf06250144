import { Component, ElementRef, Injector, ViewChild } from '@angular/core'
import { BaseclassMappComponent } from '../components/baseclass-mapp.component'
import { BaseclassManagementService } from '../../baseclass-management.service'
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { BaseclassMappSelectorComponent } from '../components/baseclass-mapp-selector/baseclass-mapp-selector.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzTreeNodeOptionsExt } from '@common/interface/NzTreeNodeOptionsExt';
import { BaseclassSignalMappTemplateComponent } from './baseclass-signal-mapp-template/baseclass-signal-mapp-template.component';
import { TableType } from '../control/baseclass-control-mapp.entity';
import { BaseclassMappSimilarComponent } from '../components/baseclass-mapp-similar/baseclass-mapp-similar.component';
import { BaseclassMappSimilarInput, BaseclassMappSimilarOutput } from '../components/baseclass-mapp-similar/baseclass-mapp-similar-model';
import { BaseClassType } from '@common/enum/BaseClassType';
import { NavigationEnd } from '@angular/router';
@Component({
  selector: 'app-baseclass-signal-mapp',
  templateUrl: './baseclass-signal-mapp.component.html',
  styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', '../components/baseclass-mapp.component.less', './baseclass-signal-mapp.component.less']
})

export class BaseclassSignalMappComponent extends BaseclassMappComponent {
  @ViewChild('temp_list') public temp_list!: BaseclassSignalMappTemplateComponent;

  // 选择树节点
  public selectedNode: NzTreeNodeOptionsExt = { key: '', title: '', value: '' }
  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig: any = [
    {
      field: 'checkBox',
      width: '50px',
      title: '',
    },
    {
      field: 'equipmentBaseTypeName',
      width: '150px',
      title: '基类设备类型',
    },
    {
      field: 'signalName',
      width: '150px',
      title: '信号名',
    },
    {
      field: 'meanings',
      width: '150px',
      title: '含义',
    },
    {
      field: 'signalNumber',
      width: '150px',
      title: '数量',
    },
    {
      field: 'baseTypeId',
      width: '150px',
      title: '基类ID',
    },
    {
      field: 'baseTypeName',
      width: '150px',
      title: '基类名',
    },
    {
      field: 'baseCondId',
      width: '150px',
      title: '基类含义ID',
    },
    {
      field: 'baseMeaning',
      width: '150px',
      title: '基类含义',
    },
    {
      field: 'childState',
      width: '180px',
      title: '子项情况',
    }
  ]
  isUpdating: boolean = false;
  showBatch: boolean = false;
  selectedRow: any;
  // 完成数
  public confirmCount: number = 0;
  // 总数
  public sumCount: number = 0;
  // 完成比例
  public confirmPercentage?: string

  checkedIds: any = [];
  checkAll: boolean = false;
  checkItems: any = [];

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private modal: NzModalService,
    private service: BaseclassManagementService,
    private nzContextMenuService: NzContextMenuService,
  ) {
    super(injector, ele)
  }

  protected onInit(): void {
    this.refreshList();
    this.subscribe(this.router.events, event => {
      if (event instanceof NavigationEnd) {
        if (event.url == '/pages/baseclass-signal-mapp') {
          this.refreshList();
        }
      }
    })
  }

  // 请求信号列表
  public override async requestTableDataSoure(): Promise<void> {
    // TODO 大数据时后端查询45秒后报错，解决后再看看前端渲染情况，再考虑是否开放全部数据
    if (this.selectedNode!.value === '') {
      return
    }
    this.isUpdating = true;
    await this.service.getBaseSignalById(this.selectedNode!.value!).then((res: any) => {
      this.sumCount = res.sumCount
      this.confirmCount = res.confirmCount
      this.confirmPercentage = (this.confirmCount / this.sumCount * 100).toFixed(2) + '%';
      const arr = res.dataList;
      let count = 1;
      arr.forEach((item: any) => {
        item.ids = item.equipmentBaseType + '-' + item.signalName + '-' + item.meanings
        if (count++ % 2 === 1) {
          item['isOdd'] = true;
        } else {
          item['isOdd'] = false;
        }
      });
      this.orgTableDataSource = arr;
      this.filterTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource = [...this.orgTableDataSource]
      this.isUpdating = false;
      if (this.orgTableDataSource.length) {
        !this.selectedRow && this.onRowClick(this.orgTableDataSource[0], 0)
      } else {
        this.selectedRow = {};
      }
      this.antiContentHeightString = document.documentElement.scrollHeight - (this.contentHeight + 145) + 'px';
    });
  }

  // 树点击事件
  public filterTreeData(event: any): void {
    this.selectedNode = event
    this.clearFilterData()
    this.baseSelectRow = undefined
    this.requestTableDataSoure()
  }

  // 获取信号子类列表
  onRowClick(item: any, index: number): void {
    if (index !== this.baseSelectRow?.index) {
      this.temp_list.baseSelectRow = undefined
    }
    super.onRowClick(item, index);
    this.selectedRow = item;
  }

  // 打开基类映射选择器
  onRowDBClick(event: any) {
    const signalCategory = event.signalCategory === 2 ? '开关量' : '模拟量';
    const meaning = event.meanings ? ` 含义：${event.meanings}` : '';
    const title = `设置基类信号ID[${signalCategory}] - 基类设备类型：${event.equipmentBaseTypeName}[${event.equipmentBaseType}] 信号名：${event.signalName}` + meaning;

    const modal = this.modal.create<BaseclassMappSelectorComponent>({
      nzTitle: title,
      nzContent: BaseclassMappSelectorComponent,
      nzData: { data: event, type: 'signal' },
      nzWidth: 900,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.close()
        },
        {
          label: '确定',
          type: 'primary',
          disabled: componentInstance => componentInstance!.btn_disabled,
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzMaskClosable: false
    })
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        this.updateBaseSignalFunc(res);
      }
    })
  }

  updateBaseSignalFunc(res?: any): void {
    const list = this.temp_list.orgTableDataSource.map((item: any) => { return { equipmentTemplateId: item.equipmentTemplateId, signalId: item.signalId, stateValue: item.stateValue } })
    const params = {
      baseTypeId: res ? res.baseTypeId : '',
      baseCondId: res ? res.baseCondId : '',
      coverFlag: res ? res.coverFlag : true,  // 覆盖标志 true则覆盖已有值的 空 false不覆盖
      conditionIds: list,
    }
    this.isUpdating = true;
    this.service.updateBaseSignal(params).subscribe(async res => {
      this.isUpdating = false;
      const text = res ? '设置成功!' : '清除成功!'
      this.messageService.success(text)
      await this.refreshList();
      this.selectedRow = this.displayTableDataSource[this.baseSelectRow!.index]
    })
  }

  contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    this.nzContextMenuService.create($event, menu);
  }

  // 清除基类
  clearBasicClassID(event: any) {
    this.updateBaseSignalFunc();
  }

  // 确认检查
  confirmCheck() {
    this.checkFunc(true)
  }

  // 取消检查
  cancelCheck() {
    this.checkFunc(false)
  }

  // 确认检查|取消检查
  checkFunc(confirm: boolean): void {
    const list = this.temp_list.orgTableDataSource.map((item: any) => { return { equipmentTemplateId: item.equipmentTemplateId, signalId: item.signalId, stateValue: item.stateValue } })
    const params = {
      conditionIds: list,
      stateFlag: true,
      subState: confirm ? '已检查' : null
    }
    this.isUpdating = true;
    this.service.updateBaseSignal(params).subscribe(() => {
      this.isUpdating = false;
      this.refreshList()
      this.temp_list.refreshList()
    })
  }

  // 刷新基类table后，再刷新模板table
  public async refreshAllList(): Promise<void> {
    await this.refreshList()
    this.onRowClick(this.orgTableDataSource[0], 0)
  }

  // 有相似按钮
  // public hasSimilar(item: any): boolean {
  //   const baseTypeId: any = item.baseTypeId && item.baseTypeId.toString();
  //   if (item.childState === '全相同' && baseTypeId.substring(baseTypeId.length - 3) === '001' && (item.baseNameExt && item.baseNameExt.length > 0) && item.signalName.includes('1')) {
  //     this.showBatch = true;
  //   }else{
  //     this.showBatch = false;
  //   }
  //   return false
  // }

  selectRowItem(item: any) {
    const baseTypeId: any = item.baseTypeId && item.baseTypeId.toString();
    if (item.childState === '全相同' && baseTypeId.substring(baseTypeId.length - 3) === '001' && (item.baseNameExt && item.baseNameExt.length > 0) && item.signalName.includes('1')) {
      this.showBatch = true;
    } else {
      this.showBatch = false;
    }
  }

  // 相似信号批量指定
  public async similarClick(): Promise<void> {
    this.openDialog<BaseclassMappSimilarComponent, BaseclassMappSimilarInput, BaseclassMappSimilarOutput>({
      nzTitle: '相似信号参数设置',
      nzWidth: 800,
      nzData: {
        rowItem: this.selectedRow,
        type: BaseClassType.signal,
        tableType: TableType.baseTable,
      },
      nzContent: BaseclassMappSimilarComponent,
      nzFooter: [{
        label: '取消',
        onClick: componentInstance => componentInstance?.close()
      },
      {
        label: '确定',
        type: 'primary',
        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
        onClick: (async componentInstance => {
          const params = await componentInstance?.confirm()
          if (!params) return
          await this.service.putSimilarSignal(params).then(() => {
            this.messageService.success('设置成功')
            componentInstance?.close()
            this.refreshList()
          })
        })
      }]
    })
  }

  // 重写刷新方法
  public override async refreshList(): Promise<void> {
    await super.refreshList()
    this.baseSelectRow && (this.displayTableDataSource[this.baseSelectRow.index]['$checked'] = true)
  }

  // 刷新并点击选中行
  public async refreshListAndClick(): Promise<void> {
    await this.refreshList()
    this.selectedRow = this.orgTableDataSource.filter(item => item.ids === this.selectedRow?.ids)[0]
  }

  //表格选择事件
  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedIds(id, checked);
    this.refreshCheckedBoxStatus();
  }

  onAllChecked(checked: boolean): void {
    this.displayTableDataSource.forEach((item: any) => {
      this.updateCheckedIds(item.ids, checked)
    });
    this.refreshCheckedBoxStatus();
  }

  updateCheckedIds(id: string, checked: boolean): void {
    if (checked) {
      if (!this.checkedIds.includes(id)) {
        this.checkedIds.push(id);
      }
    } else {
      this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
    }
  }

  refreshCheckedBoxStatus(): void {
    this.checkAll = this.displayTableDataSource.length > 0 && this.displayTableDataSource.every((item: any) => {
      return this.checkedIds.includes(item.ids);
    });
  }

  // onBatchRowDBClick() {

  // }

  clearBatchBasicClassID() {
    let params: any = [];
    this.checkedIds.forEach((item: any) => {
      let res = this.orgTableDataSource.find(obj => obj.ids === item);
      let obj = {
        equipmentBaseType: res.equipmentBaseType,
        signalName: res.signalName,
        meanings: res.meanings,
      }
      params.push(obj);
    })
    this.isUpdating = true;
    this.service.batchDeleteBaseSignal(params).then((res: any) => {
      this.isUpdating = false;
      this.messageService.success('更新成功!')
      this.onAllChecked(false);
      this.refreshList();
    })
  }

} 