import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'
import { BaseClassEntity } from '@models/baseclass.model';
import { ApiService } from '@services/api-service';
import { Observable } from 'rxjs';
import { BaseClassControlMappResponseEntity, BaseClassControlMappTemplateParamsInterface, BaseClassControlMappTemplateTableInterface, ConfirmCheckParams, SimilarParams } from './baseclass-mapp/control/baseclass-control-mapp.entity';
@Injectable()
export class BaseclassManagementService extends RestfulService {

    public constructor(private apiSerivce: ApiService, private http: HttpClient) {
        super(http);
    }

    /**
    * 字典-信号基类 列表
    * @returns
    */
    public async getSignalDictionaryList(): Promise<BaseClassEntity[]> {
        const res = await this.get<BaseClassEntity[]>('/api/config/signalbasedic/dictionary/list')
        return res.data
    }

    /**
    * 字典-事件基类 列表
    * @returns
    */
    public async getEventDictionaryList(): Promise<BaseClassEntity[]> {
        const res = await this.get<BaseClassEntity[]>('/api/config/eventbasedic/dictionary/list')
        return res.data
    }

    /**
    * 字典-控制基类 列表
    * @returns
    */
    public async getControlDictionaryList(): Promise<BaseClassEntity[]> {
        const res = await this.get<BaseClassEntity[]>('/api/config/commandbasedic/dictionary/list')
        return res.data
    }

    /**
     * 控制 基类列表
     * @param equipmentBaseType 设备小类id
     * @returns 
     */
    public async getBaseControlById(equipmentBaseType: number | string): Promise<BaseClassControlMappResponseEntity> {
        const res = await this.get<BaseClassControlMappResponseEntity>(`/api/config/control/baseclass?equipmentBaseType=${equipmentBaseType}`)
        return res.data
    }

    /**
     * 控制 基类关联模板列表
     * @param params 请求参数
     * @returns 
     */
    public async getBaseControlDetailByParams(params: BaseClassControlMappTemplateParamsInterface): Promise<BaseClassControlMappTemplateTableInterface[]> {
        const res = await this.get<BaseClassControlMappTemplateTableInterface[]>('/api/config/control/baseclass/details', { params } as any)
        return res.data
    }

    /**
     * 确认检查
     * @param params 确认检查参数
     * @returns 
     */
    public async putBaseControl(params: ConfirmCheckParams): Promise<BaseClassControlMappTemplateTableInterface[]> {
        const res = await this.put<BaseClassControlMappTemplateTableInterface[]>('api/config/control/basetype', params)
        return res.data
    }

    /**
     * 相似控制批量指定
     * @param params 确认检查参数
     * @returns 
     */
    public async putSimilarControl(params: SimilarParams): Promise<string> {
        const res = await this.put<string>('api/config/control/similarcontrol', params)
        return res.data
    }

    /**
     * 大类、小类、含义树 - 控制
     * @param equipmentBaseType 基类设备类型ID
     * @param commandType 控制类型 2  '遥控' ； 1  '遥调' 字典32
     * @returns 
     */
    public getBaseControlTree(equipmentBaseType: number, commandType: number): Observable<Object> {
        return this.http.get(`api/config/commandbasedic/tree?equipmentBaseType=${equipmentBaseType}&commandType=${commandType}`);
    }

    /**
     * 【大类|小类|含义】详情
     * @param baseTypeId 基类id
     * @param baseCondId 基类含义id
     * @returns 
     */
    public async getBaseControlDetailById(baseTypeId: number, baseCondId: number): Promise<any> {
        const res = await this.get<any>(`api/config/commandbasedic/detail?baseTypeId=${baseTypeId}&baseCondId=${baseCondId}`)
        return res.data
    }

    async getTemplateList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/equipmenttemplate/baseclass`);
        return res.data;
    }

    getEquipmentBaseTypeTree(): Observable<any> {
        return this.http.get(`api/config/equipmentbasetype/tree`);
    }

    updateTemplateItem(params: any): Observable<any> {
        return this.http.put(`api/config/equipmenttemplate`, params);
    }

    clearTemplateBasicClass(id: any) {
        return this.http.delete(`api/config/equipmenttemplate/basetype/${id}`);
    }

    autoBasicType() {
        return this.http.post(`api/config/equipmenttemplate/autobasetype`, null);
    }

    getBaseEventById(id?: any) {
        return this.http.get(`api/config/event/baseclass?equipmentBaseType=${id}`);
    }

    getBaseEventDetailByParams(eqBaseType: any, evName: any, meanings: any) {
        return this.http.get(`api/config/event/baseclass/details?equipmentBaseType=${eqBaseType}&eventName=${evName}&meanings=${meanings}`);
    }

    updateBaseEvent(params: any) {
        return this.http.put(`api/config/event/basetype`, params);
    }

    updateSimilarEvent(params: any) {
        return this.http.put(`api/config/event/similarevent`, params);
    }

    getBaseEventTreeByEqTypeId(id: any) {
        return this.http.get(`api/config/eventbasedic/tree?equipmentBaseType=${id}`);
    }

    getBaseEventDetailById(id: any) {
        return this.http.get(`api/config/eventbasedic/detail?baseTypeId=${id}`);
    }

    public async getBaseSignalById(id?: any) {
        const res = await this.get<any>(`api/config/signal/baseclass?equipmentBaseType=${id}`)
        return res.data
    }

    public async getBaseSignalDetailByParams(eqBaseType: any, sName: any, meanings: any): Promise<[]> {
        const res = await this.get<any>(`api/config/signal/baseclass/details?equipmentBaseType=${eqBaseType}&signalName=${sName}&meanings=${meanings}`)
        return res.data
    }

    public async batchDeleteBaseSignal(params?: any) {
        const res = await this.post<any>(`api/config/signal/basetype/clear`, params)
        return res.data
    }

    public async batchDeleteBaseEvent(params?: any) {
        const res = await this.post<any>(`api/config/event/basetype/clear`, params)
        return res.data
    }

    public async batchDeleteBaseControl(params?: any) {
        const res = await this.post<any>(`api/config/control/basetype/clear`, params)
        return res.data
    }

    /**
    * 基类信号树
    * @returns
    */
    getBaseSignalTree(eqBaseType: number, signalCategory: number) {
        return this.http.get(`api/config/signalbasedic/tree?equipmentBaseType=${eqBaseType}&signalCategory=${signalCategory}`);
    }

    /**
    * 基类信号详情
    * @returns
    */
    getBaseSignalDetail(baseTypeId: number, baseCondId: number) {
        return this.http.get(`api/config/signalbasedic/detail?baseTypeId=${baseTypeId}&baseCondId=${baseCondId}`);
    }

    /**
    * 设置基类信号和检查
    * @returns
    */
    updateBaseSignal(params: any) {
        return this.http.put('api/config/signal/basetype', params);
    }

    /**
    * 相似控制批量指定
    * @param params 确认检查参数
    * @returns 
    */
    public async putSimilarSignal(params: SimilarParams): Promise<string> {
        const res = await this.put<string>('api/config/signal/similarsignal', params)
        return res.data
    }

}
