<div style="height: 40px; display: flex;">
    <nz-space>
        <button *nzSpaceItem nz-button nzType="primary" (click)="addClick()">
            <span nz-icon nzType="plus"></span>
            增加基类信号
        </button>
        <button *nzSpaceItem nz-button nzType="primary" (click)="refreshList()">
            <span nz-icon nzType="reload"></span>
            刷新
        </button>
    </nz-space>
</div>
<div style="height: calc(100% - 40px);" (contextmenu)="contextMenu($event, bgMenu)">
    <d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true"
        [tableWidthConfig]="tableColumnConfig" [onlyOneColumnSort]="true" [tableHeight]="'100%'"
        [containFixHeaderHeight]="true" [fixHeader]="true" [virtualScroll]="true" [virtualItemSize]="33"
        (tableScrollEvent)="tableScrollEvent($event)">
        <thead dTableHead>
            <tr dTableRow>
                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
                    (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                    (resizeEndEvent)="onResize($event, colConfig.field)" [fixedLeft]="colConfig.fixedLeft!">
                    {{ colConfig.title }}
                    @if (orgTableDataSource.length) {
                    <!-- 空白 -->
                    <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                    <!-- 输入框 -->
                    <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                        (ngModelChange)="filterChange()" />
                    }
                </th>
            </tr>
        </thead>
        <tbody dTableBody>
            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow [ngClass]="{ 'table-row-selected': rowItem.$checked }"
                    (mousedown)="onRowClick(rowItem, rowIndex)" (contextmenu)="contextMenu($event, menu)"
                    (dblclick)="updateClick(rowItem)">
                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell [fixedLeft]="colConfig.fixedLeft!"
                        [title]="rowItem[colConfig.field]">
                        @if (colConfig.field === '$index') {
                        {{ rowIndex + 1 }}
                        }@else {
                        {{ rowItem[colConfig.field] }}
                        }
                    </td>
                </tr>
            </ng-template>
        </tbody>
        <ng-template #noResultTemplateRef>
            <div style="text-align: center; margin-top: 20px">
                <nz-empty nzNotFoundImage="simple" />
            </div>
        </ng-template>
    </d-data-table>
    <nz-dropdown-menu #menu="nzDropdownMenu">
        <ul nz-menu>
            <li nz-menu-item (click)="refreshList()"><span nz-icon nzType="reload"></span>刷新</li>
            <li nz-menu-item (click)="updateClick(baseSelectRow?.item)" [nzDisabled]="baseSelectRow?.item.isSystem">
                <span nz-icon nzType="edit"></span>编辑
            </li>
            <li nz-menu-item (click)="deleteClick(baseSelectRow?.item)" [nzDisabled]="baseSelectRow?.item.isSystem">
                <span nz-icon nzType="delete"></span>删除
            </li>
            <li nz-menu-item (click)="addExtClick(baseSelectRow?.item)"
                [nzDisabled]="!baseSelectRow?.item?.baseNameExt">
                <span nz-icon nzType="plus"></span>增加扩展信号
            </li>
        </ul>
    </nz-dropdown-menu>
</div>
<nz-dropdown-menu #bgMenu="nzDropdownMenu">
    <ul nz-menu>
        <li nz-menu-item (click)="addClick()"><span nz-icon nzType="plus"></span>增加基类信号</li>
        <li nz-menu-item (click)="refreshList()"><span nz-icon nzType="reload"></span>刷新</li>
    </ul>
</nz-dropdown-menu>