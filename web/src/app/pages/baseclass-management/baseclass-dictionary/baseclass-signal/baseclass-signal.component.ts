import { Component, ElementRef, Injector, OnInit } from '@angular/core'
import { BaseclassManagementService } from '../../baseclass-management.service'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'
import { devTableColumnType } from '../baseclass-device-type/baseclass-device-type.model'
import { BaseClassAddAction, BaseclassAddInput } from '@components/modal/baseclass-add/baseclass-add-model'
import { BaseclassAddComponent } from '@components/modal/baseclass-add/baseclass-add.component'
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown'
import { BaseClassService } from '@services/baseclass-api-service'
import { TableColumnConfig } from '@components/basic/devui-table-filter/devui-table-filter.model'
import { BaseClassEntity } from '@models/baseclass.model'
import { BaseclassExtAddComponent } from '@components/modal/baseclass-ext-add/baseclass-ext-add.component'
import { BaseclassExtAddInput } from '@components/modal/baseclass-ext-add/baseclass-ext-add-model'
import { BaseClassType } from '@common/enum/BaseClassType'

@Component({
    selector: 'app-baseclass-signal',
    templateUrl: './baseclass-signal.component.html',
    styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './baseclass-signal.component.less']
})

export class BaseclassSignalComponent extends DevuiTableFilterComponent implements OnInit {

    public constructor(
        injector: Injector,
        protected ele: ElementRef,
        private service: BaseclassManagementService,
        private baseService: BaseClassService,
        private nzContextMenuService: NzContextMenuService,
    ) {
        super(injector, ele)
    }

    // 原数据
    public orgTableDataSource: Array<BaseClassEntity> = []
    public tableColumnConfig: TableColumnConfig[] = [
        {
            field: '$index',
            width: '80px',
            title: '序号',
            fixedLeft: '0px',
        },
        {
            field: 'baseTypeId',
            width: '150px',
            title: '信号基础类别ID',
            type: devTableColumnType.Numeric,
            fixedLeft: '80px',
        },
        {
            field: 'baseTypeName',
            width: '180px',
            title: '信号基础类别名称',
            fixedLeft: '230px',
        },
        {
            field: 'baseClassName',
            width: '115px',
            title: '基础设备大类',
        },
        {
            field: 'baseEquipmentName',
            width: '115px',
            title: '基础设备子类',
        },
        {
            field: 'baseLogicCategoryName',
            width: '115px',
            title: '信号逻辑分类',
        },
        {
            field: 'baseUnitSymbol',
            width: '80px',
            title: '单位',
        },
        {
            field: 'storeInterval',
            width: '90px',
            title: '存储周期',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'absValueThreshold',
            width: '100px',
            title: '绝对值阈值',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'percentThreshold',
            width: '100px',
            title: '百分比阀值',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'storeInterval2',
            width: '100px',
            title: '存储周期2',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'absValueThreshold2',
            width: '120px',
            title: '绝对值阀值2',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'percentThreshold2',
            width: '120px',
            title: '百分比阀值2',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'baseStatPeriod',
            width: '90px',
            title: '统计周期',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'baseNameExt',
            width: '130px',
            title: '名称扩展表达式',
        },
        {
            field: 'isSystemStr',
            width: '120px',
            title: '是否系统内置',
        }
    ]

    // 方法
    protected onInit(): void {
        super.onInit()
        this.requestTableDataSoure()
    }

    // 请求设备类型数据
    public async requestTableDataSoure(): Promise<void> {
        await this.service.getSignalDictionaryList().then(res => {
            res.forEach((item: any) => {
                item['isSystemStr'] = item.isSystem ? this.translate.instant('common.yes') : this.translate.instant('common.no');
            })
            this.orgTableDataSource = res
            this.filterTableDataSource = [...this.orgTableDataSource]
            this.displayTableDataSource = [...this.orgTableDataSource]
        })
    }

    // 增加基类信号
    public addClick(): void {
        this.openDialog<BaseclassAddComponent, BaseclassAddInput, undefined>({
            nzTitle: '增加基类信号',
            nzWidth: 650,
            nzData: {
                type: BaseClassType.signal,
                action: BaseClassAddAction.add,
            },
            nzContent: BaseclassAddComponent,
            nzOnOk: async componentInstance => {
                await componentInstance?.confirm()
                this.messageService.success('增加成功')
                this.refreshList()
            }
        })
    }

    // 删除基类信号
    public deleteClick(rowItem: BaseClassEntity): void {
        if (rowItem.isSystem) {
            return
        }
        this.modalService.confirm({
            nzTitle: '提示',
            nzContent: '确定要删除选中的基类信号吗？',
            nzOnOk: async () => {
                await this.baseService.delBasedic(BaseClassType.signal, rowItem.baseTypeId!)
                this.messageService.success('删除成功')
                this.refreshList()
            }
        })
    }

    // 修改基类信号
    public updateClick(rowItem: BaseClassEntity): void {
        if (rowItem.isSystem) {
            return
        }
        this.openDialog<BaseclassAddComponent, BaseclassAddInput, undefined>({
            nzTitle: '修改基类信号',
            nzWidth: 650,
            nzData: {
                type: BaseClassType.signal,
                action: BaseClassAddAction.edit,
                rowItem: rowItem,
            },
            nzContent: BaseclassAddComponent,
            nzOnOk: async componentInstance => {
                await componentInstance?.confirm()
                this.messageService.success('修改成功')
                this.refreshList()
            }
        })
    }

    // 增加扩展信号
    public addExtClick(rowItem: BaseClassEntity): void {
        if (!rowItem.baseNameExt) {
            return
        }
        this.openDialog<BaseclassExtAddComponent, BaseclassExtAddInput, undefined>({
            nzTitle: '增加扩展信号',
            nzWidth: 650,
            nzData: {
                type: BaseClassType.signal,
                rowItem: rowItem,
            },
            nzContent: BaseclassExtAddComponent,
            nzOnOk: async componentInstance => {
                await componentInstance?.confirm()
                this.messageService.success('增加成功')
                this.refreshList()
            }
        })
    }

    // 列宽监听
    public onResize($event: { beforeWidth: number, width: number }, field: string): void {
        super.onResize($event, field)
        if (field === '$index' || field === 'baseTypeId') {
            this.tableColumnConfig[1].fixedLeft = parseInt(this.tableColumnConfig[0].width) + 'px'
            this.tableColumnConfig[2].fixedLeft = parseInt(this.tableColumnConfig[0].width) + parseInt(this.tableColumnConfig[1].width) + 'px'
        }
    }

    // 创建行右键菜单
    public contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        $event.stopPropagation()
        this.nzContextMenuService.create($event, menu)
    }
}