// 修复右键菜单的 nzDanger 且 disabled 状态下样式问题。奇怪了，丢样式了，官方样式是有这两个的。未写host也没污染其他文件
.ant-dropdown-menu-item.ant-dropdown-menu-item-disabled,
.ant-dropdown-menu-item.ant-dropdown-menu-submenu-title-disabled,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-item-disabled,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-submenu-title-disabled {
  color: rgba(0, 0, 0, .25);
  cursor: not-allowed
}

.ant-dropdown-menu-item.ant-dropdown-menu-item-disabled:hover,
.ant-dropdown-menu-item.ant-dropdown-menu-submenu-title-disabled:hover,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-item-disabled:hover,
.ant-dropdown-menu-submenu-title.ant-dropdown-menu-submenu-title-disabled:hover {
  color: rgba(0, 0, 0, .25);
  background-color: #fff;
  cursor: not-allowed
}

:host {
  td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}