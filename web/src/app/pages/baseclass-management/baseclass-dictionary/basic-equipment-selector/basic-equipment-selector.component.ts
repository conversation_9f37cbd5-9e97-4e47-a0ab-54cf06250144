import { Component, Injector, ViewChild, ElementRef, inject, Input } from '@angular/core'
import { GenericModalComponent } from '@core/components/basic/generic.modal'
import { NzMessageService } from 'ng-zorro-antd/message'
import { BaseClassSelectorOut, BaseClassSelectorParameter, BaseClassSelectorType, EquipmentCascaderNode } from '@components/selector/baseclass-selector/baseclass-selector-model'
import { NzModalRef } from 'ng-zorro-antd/modal';
import { cloneDeep } from 'lodash';
import { BaseclassManagementService } from '../../baseclass-management.service';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeNode } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-basic-equipment-selector',
  templateUrl: './basic-equipment-selector.component.html',
  styleUrls: ['./basic-equipment-selector.component.less']
})

export class BasicEquipmentSelectorComponent extends GenericModalComponent<BaseClassSelectorParameter, BaseClassSelectorOut> {
    
    searchText!: string;
    selected: any = [];
    nodes: any;
    getFirstNode: boolean = false;
    getFirstLeaf: any;
    defaultExpandedKeys: Array<string> = [];
    #modal = inject(NzModalRef);
    @ViewChild('nzTree') nzTree!: NzTreeComponent;
    
    public constructor(
        injector: Injector,
        private message: NzMessageService,
        private service: BaseclassManagementService,
    ) {
        super(injector)
    }

    protected onInit(): void {
        this.getTreeData();
    }

    getTreeData() {
        this.service.getEquipmentBaseTypeTree().subscribe(res => {
            let node = res.data;
            this.buildTree(node);
            this.nodes = node;
            if(this.selected) {
                this.selected = [this.selected.toString()];
                if(this.selected[0].length == 3) {
                    this.defaultExpandedKeys = [this.selected.toString().substr(0,1)];
                } else {
                    this.defaultExpandedKeys = [this.selected.toString().substr(0,2)];
                }
            }
        })
    }

    buildTree(nodes: any) {
        for (let i = 0; i < nodes.length; i++) {
            //判断是否为层级节点
            nodes[i]['title'] = nodes[i]['label'];
            nodes[i]['key'] = nodes[i]['value'].toString();

            //展开节点
            if (!this.getFirstNode && !this.selected) {
                if (i === 0) {
                    nodes[i]['expanded'] = true;
                }
            }

            if (nodes[i]['children'] && nodes[i]['children'].length > 0) {
                this.buildTree(nodes[i]['children']);
            } else {
                if (!this.getFirstNode) {
                    if (i === 0) {
                        this.getFirstNode = true;
                        this.getFirstLeaf = nodes[i]
                    }
                }
                nodes[i]['isLeaf'] = true;
            }
        }
    }

    // 双击展开/收回子节点
    openFolder(data: NzTreeNode | NzFormatEmitEvent): void {
        // do something if u want
        if (data instanceof NzTreeNode) {
            data.isExpanded = !data.isExpanded
        } else {
            const node = data.node
            if (node) {
                node.isExpanded = !node.isExpanded
            }
        }
    }

    confirm() {
        if(this.nzTree.getSelectedNodeList().length > 0){
            let item = this.nzTree.getSelectedNodeList()[0]['origin']
            if(item.isLeaf) {
                let name = item.label + '[' + item.key + ']';
                this.#modal.destroy({label: name, id: item.value})
            }
        }
    }

    cancel() {
        this.#modal.destroy();
    }
}