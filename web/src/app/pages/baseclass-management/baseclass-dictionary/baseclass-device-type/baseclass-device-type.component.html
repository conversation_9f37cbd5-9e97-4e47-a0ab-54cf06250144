<d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true" [tableWidthConfig]="tableColumnConfig"
    [onlyOneColumnSort]="true" [tableHeight]="'100%'" [containFixHeaderHeight]="true" [fixHeader]="true"
    (tableScrollEvent)="tableScrollEvent($event)">
    <thead dTableHead>
        <tr dTableRow>
            <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
                (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)">
                {{ colConfig.title }}
                @if (orgTableDataSource.length) {
                <!-- 空白 -->
                <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                <!-- 输入框 -->
                <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                    (ngModelChange)="filterChange()" />
                }
            </th>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow [ngClass]="{ 'table-row-selected': rowItem.$checked }"
                (click)="onRowClick(rowItem, rowIndex)">
                <td *ngFor="let colConfig of tableColumnConfig" dTableCell [title]="rowItem[colConfig.field]">
                    @if (colConfig.field === '$index') {
                    {{ rowIndex + 1 }}
                    }@else {
                    {{ rowItem[colConfig.field] }}
                    }
                </td>
            </tr>
        </ng-template>
    </tbody>
    <ng-template #noResultTemplateRef>
        <div style="text-align: center; margin-top: 20px">
            <nz-empty nzNotFoundImage="simple" />
        </div>
    </ng-template>
</d-data-table>