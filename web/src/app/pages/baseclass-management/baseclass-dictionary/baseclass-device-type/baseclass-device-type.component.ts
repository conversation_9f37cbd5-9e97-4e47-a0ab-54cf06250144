import { Component, ElementRef, Injector, OnInit, ViewChild } from '@angular/core'
import { BaseClassDeviceTypeResponse } from '@models/baseclass.model'
import { BaseClassService } from '@services/baseclass-api-service'
import { devTableColumnType } from './baseclass-device-type.model'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'
import { tableResizeFunc } from 'ng-devui'

@Component({
    selector: 'app-baseclass-device-type',
    templateUrl: './baseclass-device-type.component.html',
    styleUrls: ['../../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './baseclass-device-type.component.less']
})

export class BaseclassDeviceTypeComponent extends DevuiTableFilterComponent implements OnInit {

    public constructor(
        injector: Injector,
        protected ele: ElementRef,
        private service: BaseClassService,
    ) {
        super(injector, ele)
    }

    // 原数据
    public orgTableDataSource: Array<BaseClassDeviceTypeResponse> = []
    public tableColumnConfig = [
        {
            field: '$index',
            width: '80px',
            title: '序号',
        },
        {
            field: 'baseClassName',
            width: '150px',
            title: '基础类别名称',
        },
        {
            field: 'baseEquipmentName',
            width: '180px',
            title: '基础设备子类',
        },
        {
            field: 'baseEquipmentId',
            width: '100px',
            title: '基础设备ID',
            type: devTableColumnType.Numeric,
        },
        {
            field: 'description',
            width: '100px',
            title: '备注',
        }
    ]

    // 方法
    protected onInit(): void {
        super.onInit()
        this.requestTableDataSoure()
    }

    // 请求设备类型数据
    public requestTableDataSoure(): void {
        this.service.getBaseClassEquipmentTypeList().then(res => {
            this.orgTableDataSource = res
            this.filterTableDataSource = [...this.orgTableDataSource]
            this.displayTableDataSource = [...this.orgTableDataSource]
        })
    }

    // 列宽监听
    public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)
}