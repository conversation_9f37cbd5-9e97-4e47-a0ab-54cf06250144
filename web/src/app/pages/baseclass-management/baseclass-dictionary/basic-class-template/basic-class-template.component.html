<div class="pm_container">
    <div class="pm_bottom">
        <d-data-table
            #myTable
            [dataSource]="filterSource"
            [scrollable]="true" 
            [virtualScroll]="true"
            [tableHeight]="tabelHeightString"
            [fixHeader]="true"
            [containFixHeaderHeight]="true"
            [tableWidthConfig]="cols" 
            [tableOverflowType]="'overlay'"
            [resizeable]="true"
            [onlyOneColumnSort]="true"
            [borderType]="'borderless'"
            [striped]="false"
            dLoading 
            [loadingStyle]="'default'"
            [loading]="isUpdating"
            (tableScrollEvent)="tableScrollEvent($event)"
        >
            <thead dTableHead>
                <tr dTableRow>
                    <ng-container *ngFor="let col of cols">
                        <th dHeadCell [resizeEnabled]="true"
                            (resizeEndEvent)="onResize($event, col.field)"
                            [sortable]="false"
                            [showSortIcon]="false"
                        >
                            <div class="devui_title">{{ col.title }}</div>
                            <div class="devui_searcher">
                                <nz-input-group [nzSuffix]="suffixIconSearch">
                                    <input
                                        type="text"
                                        class="devui-form-control"
                                        nz-input
                                        [(ngModel)]="searchText[col.field]"
                                        placeholder="请输入关键字..." 
                                        (ngModelChange)="search()"/>
                                </nz-input-group>
                                <ng-template #suffixIconSearch>
                                    <span nz-icon nzType="search"></span>
                                </ng-template>
                            </div>
                        </th>
                    </ng-container>
                </tr>
            </thead>
            <tbody dTableBody>
                <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                    <tr
                        dTableRow
                        [ngClass]="{'table-row-selected': checkedIds === rowItem.equipmentTemplateId, 'devui-table-odd': rowItem.isOdd, 'devui-table-even': !rowItem.isOdd}"
                        (dblclick)="editBasicClass(rowItem)"
                        (mousedown)="onItemChecked(rowItem.equipmentTemplateId)"
                        (contextmenu)="contextMenu($event, menu)"
                    >
                        <ng-container *ngFor="let col of cols">
                            <td dTableCell 
                                class="span-text"
                                [title]="rowItem[col.field]"
                                *ngIf="col.field !== 'signalProgress' && col.field !== 'eventProgress' && col.field !== 'controlProgress'"
                            >
                                {{ rowItem[col.field] }}
                            </td>
                            <td dTableCell 
                                class="span-text"
                                [title]="rowItem[col.field]"
                                *ngIf="col.field === 'signalProgress' || col.field === 'eventProgress' || col.field === 'controlProgress'"
                            >
                                <div class="red_text" *ngIf="rowItem[col.field] === 0">{{ rowItem[col.field] }}</div>
                                <div class="green_text" *ngIf="rowItem[col.field] === 100">{{ rowItem[col.field] }}</div>
                                <div class="orange_text" *ngIf="rowItem[col.field] !== 0 && rowItem[col.field] !== 100">{{ rowItem[col.field] }}</div>
                            </td>
                        </ng-container>
                    </tr>
                    <nz-dropdown-menu #menu="nzDropdownMenu">
                        <ul nz-menu>
                            <li nz-menu-item (click)="editBasicClassByDropdown()"><iconfont [icon]="'icon-peizhi1'" class="right-menu-icon"></iconfont>设置设备基类类型</li>
                            <li nz-menu-item (click)="clearBasicClassByDropdown()"><iconfont [icon]="'icon-peizhi2'" class="right-menu-icon"></iconfont>清除设备基类类型</li>
                            <li nz-menu-item (click)="autoUpdateBasicType()"><iconfont [icon]="'icon-zidong'" class="right-menu-icon"></iconfont>自动装配基类设备类型</li>
                        </ul>
                    </nz-dropdown-menu>
                </ng-template>
            </tbody>
                <ng-template #noResultTemplateRef>
                    <div style="text-align: center; margin-top: 20px">{{'common.nodata' | translate}}</div>
                </ng-template>
        </d-data-table>
    </div>
</div>