import { Component, Injector, ViewChild, ElementRef, inject, Input, OnInit } from '@angular/core'
import { NzMessageService } from 'ng-zorro-antd/message'
import { cloneDeep } from 'lodash';
import { GenericComponent } from '@core/components/basic/generic.component';
import { CommonSelectorComponent } from '@components/selector/common-selector/common-selector.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { tableResizeFunc } from 'ng-devui';
import { BaseclassManagementService } from '../../baseclass-management.service';
import { BasicEquipmentSelectorComponent } from '../basic-equipment-selector/basic-equipment-selector.component';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-basic-class-template',
  templateUrl: './basic-class-template.component.html',
  styleUrls: ['./basic-class-template.component.less']
})

export class BasicClassTemplateComponent extends GenericComponent implements OnInit {
    
    @ViewChild('myTable') public myDevTable!: DevuiTableFilterComponent | any;
    cols: any = [];
    bctCols = [
        {
            field: 'equipmentTemplateId',
            titleKey: 'basicClassStandard.template.id',
            width: '80px'
        },
        {
            field: 'equipmentTemplateName',
            titleKey: 'basicClassStandard.template.name',
            width: '80px'
        },
        {
            field: 'parentTemplateName',
            titleKey: 'basicClassStandard.template.parentName',
            width: '80px'
        },
        {
            field: 'protocolCode',
            titleKey: 'basicClassStandard.template.protocolCode',
            width: '80px'
        },
        {
            field: 'equipmentCategoryName',
            titleKey: 'basicClassStandard.template.deviceType',
            width: '80px'
        },
        {
            field: 'equipmentBaseTypeName',
            titleKey: 'basicClassStandard.template.basicDeviceType',
            width: '80px'
        },
        {
            field: 'signalProgress',
            titleKey: 'basicClassStandard.template.signalProgress',
            width: '80px'
        },
        {
            field: 'eventProgress',
            titleKey: 'basicClassStandard.template.eventProgress',
            width: '80px'
        },
        {
            field: 'controlProgress',
            titleKey: 'basicClassStandard.template.controlProgress',
            width: '80px'
        },
    ]
    searchText: any = {
        equipmentTemplateId: null,
        equipmentTemplateName: null,
        parentTemplateName: null,
        protocolCode: null,
        equipmentCategoryName: null,
        equipmentBaseTypeName: null,
        signalProgress: null,
        eventProgress: null,
        controlProgress: null,
    }
    checkedIds: any = [];
    source: any;
    filterSource: any;
    isUpdating = false;
    minWidth = 40;
    searchTimer: any;
    modelList: any = [];
    tabelHeightString = '675px';
    
    // keepalive后记住滚动条位置
    keepScrollTop?: number
    public constructor(
        injector: Injector,
        private ele: ElementRef,
        private modal: NzModalService,
        private message: NzMessageService,
        private service: BaseclassManagementService,
        private nzContextMenuService: NzContextMenuService,
    ) {
        super(injector)
    }

    onInit() {
        this.getInitCols();
        this.getModelList();

        this.subscribe(this.router.events, event => {
            if (event instanceof NavigationEnd) {
              // 滚动条回显
              try {
                // 普通表格
                this.myDevTable.tableBodyEl.nativeElement.parentElement.scrollTop = this.keepScrollTop;
                if(event.url == '/pages/basicClassTemplate') {
                    this.getModelList();
                }
                // 开启虚拟滚动表格
                this.myDevTable.tableBodyEl.nativeElement.offsetParent.offsetParent.scrollTop = this.keepScrollTop;
              } catch { /* 呃 好饱 */ }
            }
        })
    }

    // 滚动条监听
    public tableScrollEvent(event: any): void {
        this.keepScrollTop = event.target.scrollTop
    }

    getInitCols() {
        this.cols = this.bctCols.map(item => ({ ...item, title: item['titleKey'] ? this.translate.instant(item['titleKey']) : '' }));
    }

    onResize($event: any, field: any) {
        const func = tableResizeFunc(this.cols, this.ele);
        if ($event.width < this.minWidth) {
          $event.width = this.minWidth;
        }
        func($event, field);
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            let filterList = cloneDeep(this.source);
            for(const field in this.searchText) {
                if(this.searchText[field]) {
                    filterList = filterList.filter((item: any) => {
                        return (item[field] || item[field] === 0) && item[field].toString() ? item[field].toString().includes(this.searchText[field]) : false;
                    })
                }
            }
            let count = 1;
            filterList.forEach((item: any) => {
                if(count++ % 2 === 1) {
                    item['isOdd'] = true;
                } else {
                    item['isOdd'] = false;
                }
            })
            this.filterSource = filterList;
        }, 500)
    }

    getModelList() {
        this.isUpdating = true
        Promise.all([this.service.getTemplateList()]).then(res => {
            let count = 1;
            res[0].forEach((item: any) => {
                if(count++ % 2 === 1) {
                    item['isOdd'] = true;
                } else {
                    item['isOdd'] = false;
                }
            })
            this.source = res[0];
            this.filterSource = res[0];
            this.tabelHeightString = document.documentElement.scrollHeight - 125 + 'px';
        }).finally(() => {
            this.isUpdating = false
        })
    }

    //表格选择事件
    onItemChecked(id: string): void {
        this.checkedIds = id;
    }

    contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        this.nzContextMenuService.create($event, menu);
    }

    editCondition(list: any, label: string, value: string) {
        const modal = this.modal.create<CommonSelectorComponent>({
            nzTitle: "过滤条件编辑",
            nzContent: CommonSelectorComponent,
            nzWidth: 660,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.label = label;
        instance.value = value;
        instance.sourceList = list;
        instance.selected = this.searchText.modal ? this.searchText.modal : [];
        modal.afterClose.subscribe((res: any) => {
            this.searchText.modal = res;
            this.search();
        })
    }

    editBasicClass(item: any) {
        // console.log(item);
        const modal = this.modal.create<BasicEquipmentSelectorComponent>({
            nzTitle: "设置基类设备类型",
            nzContent: BasicEquipmentSelectorComponent,
            nzWidth: 440,
            nzFooter: [
                {
                    label: '取消',
                    type: 'default',
                    onClick: componentInstance => componentInstance!.cancel()
                },
                {
                    label: '确认',
                    type: 'primary',
                    onClick: componentInstance => componentInstance!.confirm()
                },
            ],
            nzMaskClosable: false
        })
        const instance = modal.getContentComponent();
        instance.selected = item.equipmentBaseType;
        modal.afterClose.subscribe((res: any) => {
            if(res) {
                const modal = this.modal.confirm({
                    nzTitle: '请确认是否要切换设备基类！',
                    nzContent: '模板切换设备基类，会清除模板所有信号，事件，控制配置的基类ID。<br />',
                    nzOkText: '确认',
                    nzOkType: 'primary',
                    nzOkDanger: true,
                    nzOnOk: () => {
                        return 'yes'
                    },
                    nzCancelText: '取消',
                    nzOnCancel: () => {
                        return 'no'
                    },
                })
                modal.afterClose.subscribe((result: any) => {
                    if (result === 'yes') {
                        const params = {
                            equipmentTemplateId: item.equipmentTemplateId,
                            equipmentBaseType: res.id
                        }
                        this.service.updateTemplateItem(params).subscribe(result => {
                            this.message.success('更新成功!')
                            item.equipmentBaseTypeName = res.label;
                        })
                    }
                })
            }
        })
    }

    editBasicClassByDropdown() {
        if(this.checkedIds.toString().length > 0) {
            let index = this.filterSource.findIndex((item: any) => item.equipmentTemplateId === this.checkedIds);
            this.editBasicClass(this.filterSource[index]);
        } else {
            this.message.warning('请先选择一条模板！')
        }
    }

    clearBasicClassByDropdown() {
        if(this.checkedIds.toString().length > 0) {
            let index = this.filterSource.findIndex((item: any) => item.equipmentTemplateId === this.checkedIds);
            const modal = this.modal.confirm({
                nzTitle: '提示',
                nzContent: '模板切换设备基类，会清除模板所有信号，事件，控制配置的基类ID。<br />请确认是否要清除设备基类！',
                nzOkText: '确认',
                nzOkType: 'primary',
                nzOkDanger: true,
                nzOnOk: () => {
                    return 'yes'
                },
                nzCancelText: '取消',
                nzOnCancel: () => {
                    return 'no'
                },
            })
            modal.afterClose.subscribe((res: any) => {
                if (res === 'yes') {
                    this.service.clearTemplateBasicClass(this.filterSource[index]['equipmentTemplateId']).subscribe(res => {
                        this.message.success('更新成功！');
                        this.filterSource[index]['equipmentBaseTypeName'] = '';
                    })
                }
            })
        } else {
            this.message.warning('请先选择一条模板！')
        }
    }

    autoUpdateBasicType() {
        const modal = this.modal.confirm({
            nzTitle: '提示',
            nzContent: '模板切换设备基类，会清除模板所有信号，事件，控制配置的基类ID。<br />请确认是否要切换设备基类！',
            nzOkText: '确认',
            nzOkType: 'primary',
            nzOkDanger: true,
            nzOnOk: () => {
                return 'yes'
            },
            nzCancelText: '取消',
            nzOnCancel: () => {
                return 'no'
            },
        })
        modal.afterClose.subscribe((result: any) => {
            if (result === 'yes') {
                this.service.autoBasicType().subscribe(res => {
                    this.message.success('更新成功！');
                    this.getModelList();
                })
            }
        })
    }

}