:host {
    .pm_container {
        height: 100%;

        .pm_bottom {
            height: 100%;
        }
    }

    .pm_t_button {
        margin-left: 4px;

        &:first-child {
            margin-left: 0;
        }
    }

    .span-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .pm_bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 8px;
    }

    .pm_pager {
        margin-top: 8px;
    }

    .pm_t_search {
        margin-left: 16px;
    }

    .devui_title {
        height: 32px;
        line-height: 32px;
    }

    .devui_searcher {
        height: 32px;
        display: flex;
        flex-direction: column;
        justify-content: start;
    }

    .devui-form-control {
        height: 20px;
    }

    .nz-form-selector {
        width: 200px;
        height: 32px;
        overflow: auto
    }

    .pm_b_button {
        height: 24px;
        padding: 1px 15px;
    }

    .ant-input {
        padding: 0 !important;
    }

    .red_text {
        color: red;
    }

    .orange_text {
        color: orange
    }

    .green_text {
        color: green
    }

    .table-row-selected td {
        background-color: #73b9ff !important;
    }
}

.right-menu-icon {
    font-size: 16px;

    ::ng-deep svg {
        margin-right: 8px;
    }
}