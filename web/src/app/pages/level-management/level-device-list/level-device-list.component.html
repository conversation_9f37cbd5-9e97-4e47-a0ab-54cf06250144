<div style="height: 100%;" (contextmenu)="bgContextmenu($event, bgMenu)">
  <d-data-table #dataTable [dataSource]="basicDataSource" [scrollable]="true" [virtualScroll]="true"
    [tableHeight]="nzTableHeight" [fixHeader]="true" [tableOverflowType]="'overlay'">
    <thead dTableHead>
      <tr dTableRow>
        <th dHeadCell>
          <div>{{ 'levelManage.deviceList.name' | translate }}</div>
          <input nz-input [formControl]="filter.name" [(ngModel)]="fiterEqName" (ngModelChange)="filterChange()" />
        </th>
        <th dHeadCell>
          <div>局站名称</div>
          <input nz-input [formControl]="filter.stationName" [(ngModel)]="filterStationName" (ngModelChange)="filterChange()" />
        </th>
        <th dHeadCell>
          <div>所属监控单元</div>
          <input nz-input [formControl]="filter.template" [(ngModel)]="filterMuName" (ngModelChange)="filterChange()" />
        </th>
        <th dHeadCell>
          <div>最后修改时间</div>
          <input nz-input [formControl]="filter.template" [(ngModel)]="filterUpTime" (ngModelChange)="filterChange()" />
        </th>
      </tr>
    </thead>
    <tbody dTableBody>
      <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
        <tr dTableRow (contextmenu)="rowContextmenu($event, rowMenu)" (mousedown)="onRowClick(rowItem, rowIndex)"
          [ngClass]="{'table-row-selected': rowItem.$checked}">
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.equipmentName }}</span>
          </td>
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.stationName }}</span>
          </td>
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.monitorUnitName }}</span>
          </td>
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.updateTime }}</span>
          </td>
        </tr>
        <nz-dropdown-menu #rowMenu="nzDropdownMenu">
          <ul nz-menu nzSelectable>
            <li nz-menu-item (click)="addDeviceMap()">
              <iconfont icon="icon-edit-set" class="right-menu-icon"
                title="{{ 'levelManage.deviceList.editDivece' | translate }}">
              </iconfont>
              新增映射
            </li>
            <li nz-menu-item (click)="deleteDeviceMap(rowItem)">
              <iconfont icon="icon-delete2" class="right-menu-icon"
                title="{{ 'levelManage.samplerInfo.deleteDevice' | translate }}">
              </iconfont>
              删除映射
            </li>
            <!-- <li nz-menu-item>{{ 'levelManage.deviceList.sort' | translate }}</li>
            <li nz-menu-item>{{ 'levelManage.deviceList.switchView' | translate }}</li> -->
          </ul>
        </nz-dropdown-menu>
      </ng-template>
    </tbody>
  </d-data-table>
</div>
<nz-dropdown-menu #bgMenu="nzDropdownMenu">
  <ul nz-menu nzSelectable>
    <li nz-menu-item (click)="addDeviceMap()">
      <iconfont icon="icon-edit-set" class="right-menu-icon"
        title="{{ 'levelManage.deviceList.editDivece' | translate }}">
      </iconfont>
      新增映射
    </li>
  </ul>
</nz-dropdown-menu>