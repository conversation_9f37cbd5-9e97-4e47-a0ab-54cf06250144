import { Component, ElementRef, HostListener, Injector } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SelectTargetLevelComponent } from '../select-targetlevel/select-targetlevel.component';
import _ from 'lodash';

@Component({
  selector: 'app-device-map',
  templateUrl: './device-map.component.html',
  styleUrl: './device-map.component.less'
})
export class DeviceMapComponent extends GenericModalComponent<any, any> {
  currentNode: any = {};
  currentStationId = 0;
  currentHouseId = 0;
  orgDevices: any = [];
  Devices: any = [];
  MapedDevices: any = [];
  searchValue = '';
  isCtrl = false;
  isShift = false;
  lastSelectIndex = -1;
  lastMapedSelectIndex = -1;

  stations = [];
  showStations: any = [];
  houses: any = [];
  onlyShowNoMaped = false;

  public constructor(
    private fb: NonNullableFormBuilder,
    injector: Injector,
    private ele: ElementRef,
    private apiService: ApiService,
    private message: NzMessageService
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.getData();
    this.apiService.get('api/config/station/list').then((res: any) => {
      this.stations = res.data;
      this.showStations = this.stations;
      this.currentStationId = this.stations[0]['stationId'];
      this.onStationChange();
    })
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.ctrlKey && !this.isCtrl) {
      this.isCtrl = true;
    }
    if (event.shiftKey && !this.isShift) {
      this.isShift = true;
    }
  }

  @HostListener('window:keyup', ['$event'])
  handleKeyUp(event: KeyboardEvent) {
    if (event.key === 'Control') {
      this.isCtrl = false;
    }
    if (event.key === 'Shift') {
      this.isShift = false;
    }
  }

  clearSearch() {
    this.searchValue = '';
    this.onSearchChange();
  }

  onSearchChange() {
    if (this.searchValue)
      this.showStations = this.stations.filter((station: any) => station.stationId.toString().indexOf(this.searchValue) != -1 || station.stationName.indexOf(this.searchValue) != -1)
    else
      this.showStations = _.clone(this.stations);
    if (this.showStations.length > 0) {
      this.currentStationId = this.showStations[0].stationId;
      this.onStationChange();
    }
  }

  onOnlyShowNoMapedChange() {
    if (this.onlyShowNoMaped)
      this.Devices = this.orgDevices.filter((s: any) => s.isMappingResourceStructure == false);
    else
      this.Devices = this.orgDevices;
  }

  onStationChange() {
    this.apiService.get('api/config/house/config?stationId=' + this.currentStationId).then((res: any) => {
      if (res && res.data && res.data.length > 0)
        this.houses = res.data;
      this.currentHouseId = this.houses[0].houseId;
      this.getDevices(this.currentStationId, this.currentHouseId);
    })
  }

  getDevices(stationId: any, houseId: any) {
    this.apiService.get('api/config/equipment/search?stationId=' + stationId + '&houseId=' + houseId).then((res: any) => {
      this.orgDevices = res.data;
      this.onOnlyShowNoMapedChange();
    })
  }

  onHouseChange() {
    this.getDevices(this.currentStationId, this.currentHouseId);
  }

  isMouseDown = false;

  // 鼠标按下
  onMouseDown(event: MouseEvent, item: any): void {
    if (event.button === 0) { // 0 表示鼠标左键
      this.isMouseDown = true;
      if (!this.isCtrl)
        this.unSelect(this.Devices);
    }
  }

  onMouseUp(event: MouseEvent, item: any): void {
    if (event.button === 0) {
      this.isMouseDown = false;
    }
  }

  // 鼠标移动
  onMouseMove(event: MouseEvent, item: any): void {
    requestAnimationFrame(() => {
      if (this.isMouseDown && !item.isMappingResourceStructure) {
        item.selected = true;
        this.lastSelectIndex = this.Devices.findIndex((s: any) => s.equipmentId == item.equipmentId);
      }
    })
  }

  onSelectDevice(item: any) {
    if (this.isShift) {
      let currentIndex = this.Devices.findIndex((s: any) => s.equipmentId == item.equipmentId);
      if (this.lastSelectIndex > currentIndex) {
        for (let i = this.lastSelectIndex; i >= currentIndex; i--) {
          if (!this.Devices[i].isMappingResourceStructure)
            this.Devices[i].selected = true;
        }
      } else {
        for (let j = this.lastSelectIndex; j <= currentIndex; j++) {
          if (!this.Devices[j].isMappingResourceStructure)
            this.Devices[j].selected = true;
        }
      }
    } else {
      if (!this.isCtrl)
        this.unSelect(this.Devices);
      this.Devices.forEach((element: any) => {
        if (item.equipmentId != element.equipmentId) {
          if (!this.isCtrl) {
            element.selected = false;
            this.lastSelectIndex = this.Devices.findIndex((s: any) => s.equipmentId == item.equipmentId);
          }
        }
        else {
          if (!element.isMappingResourceStructure) {
            element.selected = true;
            this.lastSelectIndex = this.Devices.findIndex((s: any) => s.equipmentId == item.equipmentId);
          }
        }
      });
    }
  }

  // 鼠标按下
  onMouseDownMaped(event: MouseEvent, item: any): void {
    if (event.button === 0) { // 0 表示鼠标左键
      this.isMouseDown = true;
      if (!this.isCtrl)
        this.unSelect(this.MapedDevices);
    }
  }

  // 鼠标移动
  onMouseMoveMaped(event: MouseEvent, item: any): void {
    if (this.isMouseDown && !item.isMappingResourceStructure) {
      item.selected = true;
      this.lastMapedSelectIndex = this.MapedDevices.findIndex((s: any) => s.equipmentId == item.equipmentId);
    }
  }

  onSelectMapedDevice(item: any) {
    if (this.isShift) {
      let currentIndex = this.MapedDevices.findIndex((s: any) => s.equipmentId == item.equipmentId);
      if (this.lastMapedSelectIndex > currentIndex) {
        for (let i = this.lastMapedSelectIndex; i >= currentIndex; i--) {
          if (!this.MapedDevices[i].isMappingResourceStructure)
            this.MapedDevices[i].selected = true;
        }
      } else {
        for (let j = this.lastMapedSelectIndex; j <= currentIndex; j++) {
          if (!this.MapedDevices[j].isMappingResourceStructure)
            this.MapedDevices[j].selected = true;
        }
      }
    } else {
      if (!this.isCtrl)
        this.unSelect(this.MapedDevices);
      this.MapedDevices.forEach((element: any) => {
        if (item.equipmentId != element.equipmentId) {
          if (!this.isCtrl) {
            element.selected = false;
            this.lastMapedSelectIndex = this.MapedDevices.findIndex((s: any) => s.equipmentId == item.equipmentId);
          }
        }
        else {
          if (!element.isMappingResourceStructure) {
            element.selected = true;
            this.lastMapedSelectIndex = this.MapedDevices.findIndex((s: any) => s.equipmentId == item.equipmentId);
          }
        }
      });
    }
  }

  unSelect(list: any) {
    list.forEach((element: any) => {
      element.selected = false;
    });
  }

  getData() {
    if (this.input.currentNode) {
      this.currentNode = this.input.currentNode;
      this.getMapedDevice(this.currentNode.rId);
    }
  }

  getMapedDevice(rid: any) {
    this.apiService.get('api/config/equipment/search/' + rid).then(res => {
      this.MapedDevices = res.data;
    })
  }

  onSelectLevel() {
    const model = this.openDialog<SelectTargetLevelComponent, any, any>({
      nzTitle: '目标层级',
      nzContent: SelectTargetLevelComponent,
      nzWidth: 1000,
      nzData: {
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: SelectTargetLevelComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: SelectTargetLevelComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      if (res && res.node) {
        this.currentNode = res.node;
        this.getMapedDevice(this.currentNode.rId);
      }
    })
  }

  onAdd() {
    let param = {
      equipmentIds: this.Devices.filter((equipment: any) => equipment.selected)
        .map((equipment: any) => equipment.equipmentId),
      resourceStructureId: this.currentNode.rId
    }
    if (param.equipmentIds.length == 0) {
      this.message.warning('请先选择需要映射的设备！')
      return;
    }
    this.apiService.post('api/config/equipment/mapping', param).then(res => {
      this.message.success('映射设备成功！')
      this.getDevices(this.currentStationId, this.currentHouseId);
      this.getMapedDevice(this.currentNode.rId);
    })
  }

  onDelete() {
    let param = {
      equipmentIds: this.MapedDevices.filter((equipment: any) => equipment.selected)
        .map((equipment: any) => equipment.equipmentId),
      resourceStructureId: this.currentNode.rId
    }
    if (param.equipmentIds.length == 0) {
      this.message.warning('请先选择取消映射的设备！')
      return;
    }
    this.apiService.post('api/config/equipment/unmapping', param).then(res => {
      this.message.success('取消设备映射成功！')
      this.getDevices(this.currentStationId, this.currentHouseId);
      this.getMapedDevice(this.currentNode.rId);
    })
  }

  confirm() {
    this.close({ action: 'confirm' })
  }
}
