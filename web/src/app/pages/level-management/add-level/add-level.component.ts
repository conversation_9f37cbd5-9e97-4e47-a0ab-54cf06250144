import { Component, ElementRef, Injector } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-add-level',
  templateUrl: './add-level.component.html',
  styleUrl: './add-level.component.less'
})
export class AddLevelComponent extends GenericModalComponent<any, any> {
  levelObj = {
    nodeName: '',
    nodeType: 0,
    sortValue: 0,
  }
  isEdit = false;
  disableType = false;
  nodeDetail: any = {};
  nodeTypes: any;
  pNodeName = ''
  type = '';
  public constructor(
    private fb: NonNullableFormBuilder,
    injector: Injector,
    private ele: ElementRef,
    private apiService: ApiService,
    private message: NzMessageService
  ) {
    super(injector)
  }

  protected onInit(): void {
    this.getTypes();
    this.getData();
  }

  public validateForm: FormGroup<{
    pNodeName: FormControl<string>
    nodeName: FormControl<string>
    nodeType: FormControl<number>
    sortValue: FormControl<number>
  }> = this.fb.group({
    pNodeName: [{ value: this.pNodeName, disabled: true }],
    nodeName: [{ value: this.levelObj.nodeName, disabled: false }],
    nodeType: [{ value: this.levelObj.nodeType, disabled: false }],
    sortValue: [{ value: this.levelObj.sortValue, disabled: false }],
  })

  getTypes() {
    this.apiService.get('api/config/resource-structure/types').then(res => {
      this.nodeTypes = res.data;
    })
  }

  getData() {
    if (this.input.currentNode) {
      this.levelObj.nodeName = this.input.currentNode.rName;
      this.levelObj.nodeType = this.input.currentNode.typeId;
      this.levelObj.sortValue = this.input.currentNode.sort;
      this.isEdit = true;
      if (this.input.currentNode.sId || this.input.currentNode.ssId || this.input.currentNode.hId)
        this.disableType = true;
    }
    if (this.input.parentNode)
      this.pNodeName = this.input.parentNode.rName;
    if (this.input.type)
      this.type = this.input.type;
  }

  confirm() {
    if (!this.levelObj.nodeName || this.levelObj.nodeName.trim() == '' || !this.levelObj.nodeType) {
      this.message.info("请输入必填项");
      return;
    }
    const valid = this.submitForm()
    if (!valid) {
      return
    }
    let newLevel: any
    newLevel =
    {
      "sceneId": 1,
      "structureTypeId": this.levelObj.nodeType,
      "resourceStructureName": this.levelObj.nodeName,
      "parentResourceStructureId": this.input.parentNode.rId,
      "position": null,
      "levelOfPath": this.input.parentNode.levelOfPath,
      "display": true,
      "createStation": false,
      "sortValue": this.levelObj.sortValue
    }
    this.apiService.post('api/config/resource-structure/v3/config', newLevel).then(res => {
      this.close({ action: 'confirm', level: res.data })
    })
  }
  editConfirm() {
    if (!this.levelObj.nodeName || this.levelObj.nodeName.trim() == '' || !this.levelObj.nodeType) {
      this.message.info("请输入必填项");
      return;
    }
    const valid = this.submitForm()
    if (!valid) {
      return
    }
    let newLevel =
    {
      resourceStructureId: this.input.currentNode.rId,
      "sceneId": 1,
      "structureTypeId": this.levelObj.nodeType,
      "resourceStructureName": this.levelObj.nodeName,
      "parentResourceStructureId": this.input.currentNode.pId,
      "position": null,
      "levelOfPath": this.input.currentNode.levelOfPath,
      "display": true,
      "createStation": false,
      "originId": this.input.currentNode.sId,
      "sortValue": this.levelObj.sortValue
    }
    this.apiService.put('api/config/resource-structure/v3/config', newLevel).then(res => {
      this.close({ action: 'confirm', level: res.data })
    })
  }
  submitForm() {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }
}
