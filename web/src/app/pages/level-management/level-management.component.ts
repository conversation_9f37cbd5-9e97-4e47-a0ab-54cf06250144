import { ChangeDetectorRef, Component, ElementRef, Injector, OnInit, ViewChild } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { ApiService } from '@services/api-service';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTreeComponent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { AddLevelComponent } from './add-level/add-level.component';
import _ from 'lodash';
import { NzModalService } from 'ng-zorro-antd/modal';
import { filter, forkJoin } from 'rxjs';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { ConfigInitComponent } from '../init/config-init.component';
import { NavigationEnd } from '@angular/router';
import { DeviceMapComponent } from './device-map/device-map.component';

@Component({
  selector: 'ngx-level-management',
  templateUrl: './level-management.component.html',
  styleUrls: ['./level-management.component.less']
})
export class LevelManagementComponent extends GenericComponent {
  // sider 宽度
  siderWidth = 330
  // sider resize id
  sideResizeId = -1
  treeIndex = 0;

  resizeObserver: ResizeObserver | undefined;
  selectedNode: any = undefined;
  nzTreeHeight: any;
  loading = false;
  searchValue = '';
  seachChanged = false;
  mathNodes: NzTreeNodeOptions[] = [];
  mathIndex = 0;
  nodes: any;
  defaultExpandedKeys: Array<string> = [];
  defaultSelectedKeys: Array<string> = [];
  @ViewChild('nzTree') nzTree!: NzTreeComponent;
  @ViewChild('nzTree') nzTreeRef!: ElementRef;

  constructor(injector: Injector,
    private nzContextMenuService: NzContextMenuService,
    private message: NzMessageService,
    private apiService: ApiService,
    private cdr: ChangeDetectorRef,
    private modal: NzModalService) {
    super(injector);
  }

  protected onInit(): void {
    this.apiService.get('api/config/dataitems?entryId=62').then(res => {
      if (res && res.data && (res.data as Array<any>)?.length > 0) {
        this.initTree();
      } else {
        this.initSystem();
      }
    })

    this.resetHeight();

    this.subscribe(this.router.events.pipe(
      filter(event => event instanceof NavigationEnd && event.url == '/pages/level-management')),
      (event) => {
        this.scrollToSelectedNode(this.selectedNode);
      })
  }

  protected onAfterViewInit(): void {
    this.resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        this.resetHeight();
        this.cdr.detectChanges();
      }
    });
    let levelLayout = document.getElementsByClassName('inner-content');
    if (levelLayout && levelLayout.length > 0)
      this.resizeObserver.observe(levelLayout[0]);
  }

  protected onDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  resetHeight() {
    setTimeout(() => {
      this.nzTreeHeight = (document.body.offsetHeight - 180).toString() + 'px';
    }, 0);
  }

  onSearchChange(value: string): void {
    this.searchValue = value;
    this.seachChanged = true;
  }

  highlightSearchText(title: string): string {
    if (!this.searchValue.trim()) {
      return title;
    }
    const regEx = new RegExp(this.searchValue.trim(), 'gi');
    return title.replace(regEx, `<span class="font-highlight">$&</span>`);
  }

  focusNode(): void {
    if (this.seachChanged) {
      this.seachChanged = false;
      this.mathNodes = [];
      this.mathIndex = 0;
      this.findNodesWithTitle(this.searchValue.trim(), this.nodes, this.mathNodes);
      if (this.mathNodes.length > 0) {
        const node = this.mathNodes[this.mathIndex];
        this.defaultSelectedKeys = [node.key];
        this.clickNode(node);
        this.scrollToSelectedNode(node);
      }
    } else {
      this.mathIndex++;
      if (this.mathIndex == this.mathNodes.length)
        this.mathIndex = 0;
      if (this.mathIndex < this.mathNodes.length) {
        const node = this.mathNodes[this.mathIndex];
        this.defaultSelectedKeys = [node.key];
        this.clickNode(node);
        this.scrollToSelectedNode(node);
      }
    }
  }

  private findNodesWithTitle(title: string, parent: any[], mathNodes: NzTreeNodeOptions[]) {
    parent.forEach((p: any) => {
      if (p.title.toLowerCase().includes(title.toLowerCase())) {
        mathNodes.push(p);
      }
      if (p.children && p.children.length > 0) {
        this.findNodesWithTitle(title, p.children, mathNodes);
      }
    });
  }


  initSystem() {
    const model = this.openDialog<ConfigInitComponent, any, any>({
      nzTitle: '配置数据初始化',
      nzContent: ConfigInitComponent,
      nzWidth: 550,
      nzData: {
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: ConfigInitComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: ConfigInitComponent): void => {
            instance.submit()
          }
        }
      ]
    })
    model.afterClose.subscribe((res: any) => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('初始化成功！')
          setTimeout(() => {
            this.initTree();
          }, 1500);
          break;
      }
    })
  }

  onSideResize({ width }: NzResizeEvent): void {
    cancelAnimationFrame(this.sideResizeId)
    this.sideResizeId = requestAnimationFrame(() => {
      this.siderWidth = width!
    })
  }

  public muContextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    this.nzContextMenuService.create($event, menu);
  }

  initTree(): void {
    this.apiService.get('api/config/resource-structure/full-tree').then((res: any) => {
      let oriNodes: any[] = [res.data];
      this.processTree(oriNodes, '');
      oriNodes[0].expanded = true;
      this.sortTree(oriNodes)
      this.defaultExpandedKeys = this.nzTree.getExpandedNodeList().map(n => n.key)
      this.nodes = oriNodes;
    })
  }
  processTree(nodes: any, pPath: string) {
    nodes.forEach((node: any) => {
      node.isLeaf = node.children ? false : true;
      node.key = node.rId;
      node.title = node.rName;
      node.levelOfPath = pPath + node.rId;
      if (node.children) {
        this.processTree(node.children, node.levelOfPath + '.')
      }
    });
  }

  sortTree(nodes: any) {
    if (!Array.isArray(nodes)) return;
    nodes.sort((a, b) => a.sort - b.sort); // 原地排序当前层
    for (const node of nodes) {
      if (Array.isArray(node.children)) {
        this.sortTree(node.children); // 递归对子节点排序
      }
    }
  }

  contextMenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    this.nzContextMenuService.create($event, menu);
  }

  getCurrentNodeName() {
    switch (this.selectedNode.typeId) {
      case 102: return '当前中心'
        break;
      case 103: return '当前片区'
        break;
      case 104: return '当前局站'
        break;
      case 105: return '当前局房'
        break;
    }
    return '';
  }

  deleteLevel(): void {
    let tip = '';
    tip = '删除的节点名称 ' + this.selectedNode.rName
    this.modal.confirm({
      nzTitle: this.translate.instant('levelManage.confirmDelete'),
      nzContent: tip,
      nzOkText: this.translate.instant('common.ok'),
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.apiService.delete('api/config/resource-structure/v3/config/' + this.selectedNode.rId, { observe: 'response' }).then(res => {
          this.message.success('删除成功！')
          setTimeout(() => {
            this.refreshTree();
          }, 500);
        })
      },
      nzCancelText: this.translate.instant('common.cancel'),
      // nzOnCancel: () => console.log('Cancel')
    });
  }

  async clickNode(node: any) {
    this.selectedNode = node;
    setTimeout(() => {
      this.defaultSelectedKeys = [node.key];
    }, 0);
  }

  scrollToSelectedNode(node: any): void {
    this.treeIndex = 0;
    this.buildIndexTree(this.nodes);
    if (node)
      this.nzTree.cdkVirtualScrollViewport.scrollToIndex(node.index);
  }

  buildIndexTree(list: any) {
    list.forEach((item: any) => {
      item['index'] = this.treeIndex;
      this.treeIndex++;
      if (item['expanded'] && item['children'] && item['children'].length > 0) {
        this.buildIndexTree(item['children']);
      }
    })
  }

  selectDropdown(node: any): void {
    if (!node) {
      this.messageService.warning('请先选中一个节点！', {
        nzDuration: 2000,
        nzPauseOnHover: true
      })
    }
  }

  refreshTree() {
    this.apiService.get('api/config/resource-structure/full-tree').then((res: any) => {
      let oriNodes = [res.data];
      this.processTree(oriNodes, '');
      this.defaultExpandedKeys = this.nzTree.getExpandedNodeList().map(n => n.key)
      this.defaultSelectedKeys = this.nzTree.getSelectedNodeList().map(item => {
        return item.origin.key
      });
      this.sortTree(oriNodes)
      this.nodes = oriNodes;
    })
  }

  add(pnode: any) {
    this.selectedNode = pnode;
    this.addLevel();
  }


  edit(cnode: any) {
    this.selectedNode = cnode;
    this.editLevel();
  }
  deleteLevelFromMenu(cnode: any) {
    this.selectedNode = cnode;
    this.deleteLevel();
  }

  addLevel(type: String = '') {
    if (!this.selectedNode) {
      return;
    }
    const model = this.openDialog<AddLevelComponent, any, any>({
      nzTitle: this.translate.instant('levelManage.add'),
      nzContent: AddLevelComponent,
      nzWidth: 550,
      nzData: {
        parentNode: this.selectedNode ? this.selectedNode : null,
        currentNode: null,
        type: type
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddLevelComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddLevelComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('新增成功！')
          setTimeout(() => {
            this.refreshTree();
          }, 500);
          break;
      }
    })
  }
  deviceMap(node: any) {
    this.clickNode(node);

    const model = this.openDialog<DeviceMapComponent, any, any>({
      nzTitle: '设备映射',
      nzContent: DeviceMapComponent,
      nzWidth: 1000,
      nzData: {
        currentNode: this.selectedNode ? this.selectedNode : null,
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: DeviceMapComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: DeviceMapComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      setTimeout(() => {
        this.selectedNode = _.clone(this.selectedNode);
      }, 500);
    })
  }
  editLevel() {
    if (!this.selectedNode) {
      return;
    }
    const model = this.openDialog<AddLevelComponent, any, any>({
      nzTitle: this.translate.instant('levelManage.edit'),
      nzContent: AddLevelComponent,
      nzWidth: 550,
      nzData: {
        parentNode: null,
        currentNode: this.selectedNode ? this.selectedNode : null
      },
      nzFooter: [
        {
          label: this.translate.instant('common.cancel'),
          onClick: (instance: AddLevelComponent): void => {
            instance.close()
          }
        },
        {
          label: this.translate.instant('common.ok'),
          type: 'primary',
          onClick: (instance: AddLevelComponent): void => {
            instance.editConfirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      switch (res?.action) {
        case 'confirm':
          this.message.success('修改成功！')
          setTimeout(() => {
            this.refreshTree();
          }, 500);
          break;
      }
    })
  }
}
