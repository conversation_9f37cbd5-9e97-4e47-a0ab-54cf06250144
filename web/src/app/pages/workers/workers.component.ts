import { Component, EventEmitter, Injector, OnInit, Output } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';

const $import = require as (url: string) => string;


@Component({
  selector: 'app-workers',
  templateUrl: './workers.component.html',
  styleUrls: ['./workers.component.less']
})
export class WorkersComponent extends GenericComponent {
  public constructor(injector: Injector) {
    super(injector)

  }




  protected onQueryChanges(): void {

  }


  protected async onInit(): Promise<void> {
    const result = await this.callWorker('Hanks')
    console.log(result);

  }

  private async callWorker(value: string): Promise<string> {
    const worker = new Worker(new URL('./app.worker', import.meta.url), { type: 'module' });
    return new Promise((resolve, reject) => {
      worker.onmessage = (e: MessageEvent<string>): void => {
        resolve(e.data)
      };
      worker.postMessage(value);
    });
  }






  protected onDestroy(): void {
    super.onDestroy();
  }



}
