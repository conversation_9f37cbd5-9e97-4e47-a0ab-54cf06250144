import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { TableColumnConfig, devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model';
import { ApiService } from '@services/api-service';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { MuDistributionComponent } from '../mu-distribution/mu-distribution.component';
import { TerminalXtermInput } from '@components/modal/terminal-xterm/terminal-xterm-model';
import { TerminalXtermComponent } from '@components/modal/terminal-xterm/terminal-xterm.component';

@Component({
    selector: 'app-mu-list',
    templateUrl: './mu-list.component.html',
    styleUrls: ['../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './mu-list.component.less']
})
export class MuListComponent extends DevuiTableFilterComponent implements OnInit {
    checkAll: boolean = false;
    checkedIds: any = [];
    public _tabIndex: number | undefined
    @Input()
    public set tabIndex(index: number | undefined) {
        this._tabIndex = index
        if (index === 0) {
            this.refreshList()
        }
    }
    public constructor(
        injector: Injector,
        private apiService: ApiService,
        private nzContextMenuService: NzContextMenuService,
        ele: ElementRef
    ) {
        super(injector, ele)
    }

    muTypeObj: any;
    muTypes: any[] = []

    // 原数据
    public orgTableDataSource: Array<any> = []
    public tableColumnConfig: TableColumnConfig[] = [
        {
            field: 'checkbox',
            width: '41px',
            title: '',
        },
        {
            field: 'monitorUnitId',
            width: '100px',
            title: '监控单元ID'
        },
        {
            field: 'monitorUnitName',
            width: '150px',
            title: '监控单元名称'
        },
        {
            field: 'typeName',
            width: '100px',
            title: '类型'
        },
        {
            field: 'ipAddress',
            width: '150px',
            title: 'IP'
        },
        {
            field: 'statusTxt',
            width: '120px',
            title: '配置同步状态'
        },
        {
            field: 'syncTime',
            width: '150px',
            title: '配置同步时间'
        },
        {
            field: 'portNos',
            width: '150px',
            title: '监控单元下端口'
        }
    ]
    public override tableColumnDisplay: TableColumnConfig[] = this.tableColumnConfig.filter(item => item.field !== 'checkbox')

    // 方法
    protected onInit(): void {
        super.onInit();
        this.getType();
        // this.requestTableDataSoure();
    }

    getType() {
        this.apiService.get('api/config/monitor-unit/types').then((res: any) => {
            if (res.data) {
                this.muTypes = res.data;
                this.muTypeObj = {};
                res.data.forEach((element: any) => {
                    this.muTypeObj[element.typeId] = element.typeName
                });
                if (this.orgTableDataSource) {
                    this.orgTableDataSource.forEach(s => {
                        s.typeName = this.muTypeObj[s.monitorUnitCategory];
                    })
                    this.filterTableDataSource = [...this.orgTableDataSource]
                    this.displayTableDataSource = [...this.orgTableDataSource]
                }
            }
        })
    }

    // 请求设备类型数据
    async requestTableDataSoure() {
        await this.apiService.get('api/config/monitor-unit').then((res: any) => {
            this.orgTableDataSource = res.data.filter((s: any) => { return s.monitorUnitCategory != 1 && s.monitorUnitCategory != 24 });
            for (let i = 0; i < this.orgTableDataSource.length; i++) {
                let mu = this.orgTableDataSource[i];
                switch (mu.state) {
                    case 0:
                        mu.statusTxt = "无需下发";
                        break;
                    case 1:
                        mu.statusTxt = "待下发";
                        break;
                    case 2:
                        mu.statusTxt = "正在下发";
                        break;
                    case 3:
                        mu.statusTxt = "下发成功";
                        break;
                    case 4:
                        mu.statusTxt = "下发失败";
                        break;
                    default:
                        break;
                }
            }
            if (this.muTypeObj)
                this.orgTableDataSource.forEach(s => {
                    s.typeName = this.muTypeObj[s.monitorUnitCategory];
                })
            this.filterTableDataSource = [...this.orgTableDataSource]
            this.displayTableDataSource = [...this.orgTableDataSource]
        })
    }

    onShowFail(item: any) {
        this.apiService.get('api/config/monitor-unit/log?monitorUnitId=' + item.monitorUnitId).then((res: any) => {
            if (res) {
                let newData = res.data.replaceAll('\n', '<br>');
                this.modalService.error({
                    nzTitle: item.monitorUnitName + '—下发失败日志',
                    nzContent: newData,
                    nzWidth: 600
                });
            }
        })
    }

    updateCheckedIds(id: string, checked: boolean): void {
        if (checked) {
            if (!this.checkedIds.includes(id)) {
                this.checkedIds.push(id);
            }
        } else {
            this.checkedIds.splice(this.checkedIds.indexOf(id), 1);
        }
    }

    refreshCheckedBoxStatus(): void {
        this.checkAll = !!this.displayTableDataSource.length && this.displayTableDataSource.every((item: any) => {
            return this.checkedIds.includes(item.monitorUnitId)
        });
    }

    //表格选择事件
    onItemChecked(id: string, checked: boolean): void {
        this.updateCheckedIds(id, checked);
        this.refreshCheckedBoxStatus();
    }

    onAllChecked(checked: boolean): void {
        this.displayTableDataSource.forEach((item: any) => {
            this.updateCheckedIds(item.monitorUnitId, checked)
        });
        this.refreshCheckedBoxStatus();
    }

    // 创建行右键菜单
    public rowContextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
        $event.stopPropagation()
        this.nzContextMenuService.create($event, menu)
    }
    getSelected(): any[] {
        return this.checkedIds;
    }
    batchDistribute() {
        let mulist = this.displayTableDataSource.filter((item: any) => {
            return this.checkedIds.includes(item.monitorUnitId)
        })
        mulist.forEach(s => s.selected = true);
        const model = this.openDialog<MuDistributionComponent, any, any>({
            nzTitle: '批量分发监控单元配置',
            nzContent: MuDistributionComponent,
            nzWidth: 970,
            nzData: {
                muList: mulist,
                allSelectedItems: mulist,
                currentMu: null,
                monitorUnitCategories: this.muTypes
            },
            nzFooter: []
        })
        model.afterClose.subscribe(res => {
            switch (res?.action) {
                case 'confirm':
                    this.refreshList();
                    break;
            }
        })
    }
    generate(mu: any) {
        mu.selected = true;
        const model = this.openDialog<MuDistributionComponent, any, any>({
            nzTitle: '分发监控单元配置',
            nzContent: MuDistributionComponent,
            nzWidth: 970,
            nzData: {
                muList: mu ? [mu] : null,
                allSelectedItems: mu ? [mu] : null,
                currentMu: null,
                monitorUnitCategories: this.muTypes
            },
            nzFooter: []
        })
        model.afterClose.subscribe(res => {
            switch (res?.action) {
                case 'confirm':
                    this.refreshList();
                    break;
            }
        })
    }
    ftp(mu: any) {

    }
    public telnet(mu: any): void {
        this.openDialog<TerminalXtermComponent, TerminalXtermInput, undefined>({
            nzTitle: `监控单元 ${mu.monitorUnitName}`,
            nzWidth: 850,
            nzData: {
                monitorUnitId: mu.monitorUnitId
            },
            nzContent: TerminalXtermComponent,
            nzFooter: null
        })
    }
    restart(mu: any) {

    }
    exportExcel() {

    }
}