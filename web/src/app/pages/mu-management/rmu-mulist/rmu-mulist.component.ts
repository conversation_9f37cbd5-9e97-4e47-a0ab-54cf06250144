import { Component, Injector } from '@angular/core';
import { NonNullableFormBuilder } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { GenerateDistributionMuComponent } from '../generate-distribution-mu/generate-distribution-mu.component';

@Component({
    selector: 'app-rmu-mulist',
    templateUrl: './rmu-mulist.component.html',
    styleUrl: './rmu-mulist.component.less'
})
export class RmuMulistComponent extends GenericModalComponent<any, any> {

    public constructor(
        injector: Injector,
        private fb: NonNullableFormBuilder,
        private apiService: ApiService,
    ) {
        super(injector)
    }

    onInit() {
        this.getData();
    }

    getData() {
        if (this.input.currentRMU) {
            this.currentRMU = this.input.currentRMU;
        }
        this.apiService.get('api/config/monitor-unit/rmu/' + this.currentRMU.workStationId).then((res: any) => {
            this.orgTableDataSource = res.data;
            this.orgTableDataSource.forEach(s => {
                s.workStationId = this.currentRMU.workStationId;
                s.workStationName = this.currentRMU.workStationName;
            })
        }).catch(error => {

        })
    }

    currentRMU: any;

    // 原数据
    public orgTableDataSource: Array<any> = []
    public tableColumnConfig = [
        {
            field: 'stationId',
            width: '180px',
            title: '局站ID',
        },
        {
            field: 'stationName',
            width: '180px',
            title: '局站名称',
        },
        {
            field: 'monitorUnitId',
            width: '150px',
            title: '监控单元ID',
        },
        {
            field: 'monitorUnitName',
            width: '180px',
            title: '监控单元名称',
        },
        {
            field: 'ipAddress',
            width: '180px',
            title: '监控单元IP',
        },
        {
            field: 'workStationName',
            width: '180px',
            title: 'RMU名称',
        },
        {
            field: 'workStationId',
            width: '180px',
            title: 'RMU ID',
        },
    ]
    onRowClick(e: any) {

    }
    generate() {
        const model = this.openDialog<GenerateDistributionMuComponent, any, any>({
            nzTitle: '生成与下载',
            nzContent: GenerateDistributionMuComponent,
            nzWidth: 600,
            nzData: {
                rmuList: [this.currentRMU]
            },
            nzCentered: true,
            nzFooter: [
                {
                    label: this.translate.instant('common.cancel'),
                    onClick: (instance: GenerateDistributionMuComponent): void => {
                        instance.close()
                    }
                },
                {
                    label: '生成并下载',
                    type: 'primary',
                    onClick: (instance: GenerateDistributionMuComponent): void => {
                        instance.generate()
                    }
                }
            ]
        })
        model.afterClose.subscribe(res => {
            switch (res?.action) {
                case 'confirm':
                    // if (this.muData.length == 0) {
                    //     this.currentContractNo = res.level.contractNo;
                    //     this.currentProjectName = res.level.projectName;
                    // }
                    // this.message.success('新增成功！')
                    // setTimeout(() => {
                    //     this.getMuList();
                    // }, 1000);
                    break;
            }
        })
    }
    checkPort() {
        this.apiService.get('api/config/port/checkrmuportconflict/' + this.currentRMU.workStationId).then((res: any) => {
            if (!res.data || res.data == '') {
                this.messageService.success('没有发现该RMU下端口或端口设置冲突');
            } else {
                this.messageService.error(res.data);
            }
        }).catch(error => {

        })
    }
    confirm() {

    }
}
