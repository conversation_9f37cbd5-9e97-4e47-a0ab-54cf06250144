import { Component, Injector } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { LoginResponse } from '@core/types/restful';
import { ApiService } from '@services/api-service';
import _ from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-mu-distribution',
  templateUrl: './mu-distribution.component.html',
  styleUrl: './mu-distribution.component.less'
})
export class MuDistributionComponent extends GenericModalComponent<any, any> {
  public constructor(
    injector: Injector,
    private fb: NonNullableFormBuilder,
    private apiService: ApiService,
    private message: NzMessageService
  ) {
    super(injector)
  }

  userName: string = 'root';
  passWord: string = 'hello';
  passwordVisible = false;
  port: number = 21;
  protocol = false;
  skipSoFileDownload = false;
  columns: any;
  muList: any[] = [];
  timeoutId: any;
  muStatusList: any[] = [];
  currentMu: any;
  showMuList: any
  monitorUnitCategories: any[] = [];
  allSelected = false;
  allSelectedItems: any[] = [];
  showAllSelectedItems: any;
  logDetail = '';
  canGenerate = false;
  Completed = false;

  websocket: any;
  websocketId = 'monitor';
  websocketMsg: any[] = [];
  websocketDownMsg: any[] = [];
  errorTimer: any;
  webSocketTimer: any;
  heartBeatTimer: any;
  wsRefreshSpan = 10000;
  wsRefreshStandardSpan = 10000;
  pingCount = 0;

  muName = '';
  muType = '';
  muIp = '';

  muNameStatus = '';
  muTypeStatus = '';
  muIpStatus = '';
  strStatus = '';

  public validateForm: FormGroup<{
    userName: FormControl<string>
    passWord: FormControl<string>
    port: FormControl<number>
    protocol: FormControl<boolean>
    skipSoFileDownload: FormControl<boolean>
  }> = this.fb.group({
    userName: [{ value: this.userName, disabled: false }],
    passWord: [{ value: this.passWord, disabled: false }],
    port: [{ value: this.port, disabled: false }],
    protocol: [{ value: this.protocol, disabled: false }],
    skipSoFileDownload: [{ value: this.skipSoFileDownload, disabled: false }]
  })

  public filter = {
    name: new FormControl(),
    type: new FormControl(),
    address: new FormControl()
  }

  public filterStatus = {
    name: new FormControl(),
    type: new FormControl(),
    address: new FormControl(),
    status: new FormControl()
  }

  filterChange() {
    if (this.muList)
      this.showMuList = _.cloneDeep(this.muList).filter((s: any) => s.monitorUnitName?.toLowerCase().indexOf(this.muName?.toLowerCase()) != -1 && s.typeName?.toLowerCase().indexOf(this.muType?.toLowerCase()) != -1 && s.ipAddress?.toLowerCase().indexOf(this.muIp?.toLowerCase()) != -1);
  }

  filterChangeStatus() {
    if (this.allSelectedItems)
      this.showAllSelectedItems = _.cloneDeep(this.allSelectedItems).filter((s: any) => s.monitorUnitName?.toLowerCase().indexOf(this.muNameStatus?.toLowerCase()) != -1 && s.typeName?.toLowerCase().indexOf(this.muTypeStatus?.toLowerCase()) != -1 && s.ipAddress?.toLowerCase().indexOf(this.muIpStatus?.toLowerCase()) != -1 && s.statusTxt?.toLowerCase().indexOf(this.strStatus?.toLowerCase()) != -1);
  }

  onInit() {
    this.getData();
    this.columns = [
      {
        field: 'monitorUnitName',
        header: '监控单元名称',
        fieldType: 'text'
      },
      {
        field: 'monitorUnitCategory',
        header: '类型',
        fieldType: 'text'
      },
      {
        field: 'ipAddress',
        header: 'IP',
        fieldType: 'text'
      }
    ]
    this.initWebSocket();
    if (this.webSocketTimer) {
      clearTimeout(this.webSocketTimer);
    }
    this.webSocketTimer = setTimeout(() => {
      this.connectWebSocket();
    }, 10000);

    if (this.heartBeatTimer) {
      clearTimeout(this.heartBeatTimer);
    }
    this.heartBeatTimer = setTimeout(() => {
      this.heartBeat();
    }, 3000);
  }
  protected onDestroy(): void {
    super.onDestroy();
    if (this.timeoutId)
      clearTimeout(this.timeoutId)
    if (this.errorTimer) {
      clearTimeout(this.errorTimer);
    }
    if (this.heartBeatTimer) {
      clearTimeout(this.heartBeatTimer);
    }
    if (this.webSocketTimer) {
      clearTimeout(this.webSocketTimer);
    }
    if (this.websocket) this.websocket.close();
  }

  /** socket开始 ↓↓↓ */
  initWebSocket() {
    if (this.websocket && this.websocket.readyState && this.websocket.readyState != WebSocket.CLOSED) {
      this.websocket.close();//后端也会关
    }
    // this.webClientLog('websocket init', 'websocket init');
    console.log('websocket init');
    const origin = window.location.origin.replace('http', 'ws');
    let sessionId = this.sessionService.get<string>('sessionId');
    let user = this.sessionService.get<LoginResponse>('user');
    const wsuri = `${origin}/api/config/websocket/${this.websocketId}?sessionid=${sessionId}&userid=${user?.userId}`;
    this.websocket = new WebSocket(wsuri);
    this.websocket.onmessage = this.websocketonmessage.bind(this);
    this.websocket.onerror = this.websocketonerror.bind(this);
    this.websocket.onclose = this.websocketclose.bind(this);
    this.websocket.onopen = this.websocketopen.bind(this);
  }

  websocketopen() {
    // 连接成功
    // this.webClientLog('websocket open', 'websocket open');
    console.log('websocket open');
  }

  heartBeat() {
    this.pingCount++;
    if (this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send('ping');
    }
    if (this.heartBeatTimer) {
      clearTimeout(this.heartBeatTimer);
    }
    this.heartBeatTimer = setTimeout(() => {
      this.heartBeat();
    }, this.wsRefreshSpan);
  }
  connectWebSocket() {
    if (this.websocket.readyState === WebSocket.CLOSED) {
      // close后要重连
      this.initWebSocket();
    };
    if (this.webSocketTimer) {
      clearTimeout(this.webSocketTimer);
    }
    this.webSocketTimer = setTimeout(() => {
      this.connectWebSocket();
    }, this.wsRefreshSpan);
  }

  websocketonerror() {
    // this.webClientLog('websocket error and reconnect', 'websocket error and reconnect');
    console.log('websocket error and reconnect');
    // 连接建立失败重连
    if (this.errorTimer) {
      clearTimeout(this.errorTimer);
    }
    this.errorTimer = setTimeout(() => {
      this.initWebSocket();
    }, this.wsRefreshSpan);
  }
  websocketonmessage(e: any) {
    if (e.data === 'pong') return;
    // 数据接收
    let msg = JSON.parse(e.data);
    if (msg.webSocketBusinessType === 1) {
      // console.log(msg);
      this.websocketMsg.push(msg.msg);
      this.canGenerate = msg.result;
      this.logDetail = this.websocketMsg.join('\r\n');
    } else if (msg.webSocketBusinessType === 2) {
      this.websocketDownMsg.push(msg.msg);
      this.Completed = msg.result;
      this.logDetail = this.websocketDownMsg.join('\r\n');
    }
  }
  websocketclose() {
    // this.webClientLog('websocket close', 'websocket close');
    console.log('websocket close');
  }

  getData() {
    if (this.input.currentMu) {
      this.currentMu = this.input.currentMu;
    }
    if (this.input.monitorUnitCategories) {
      this.monitorUnitCategories = this.input.monitorUnitCategories;
    }
    if (this.input.muList) {
      this.muList = this.input.muList;
      this.muList.forEach(s => {
        s.typeName = _.find(this.monitorUnitCategories, muc => muc.typeId == s.monitorUnitCategory)?.typeName
      })
      this.showMuList = _.cloneDeep(this.muList);
    }
    if (this.input.allSelectedItems) {
      this.allSelectedItems = this.input.allSelectedItems;
    }
  }


  changeAllSelected(e: any) {
    this.allSelected = e.currentTarget.checked;
    this.showMuList.forEach((element: any) => {
      element.selected = this.allSelected;
    });
    if (this.allSelected) {
      this.allSelectedItems = this.allSelectedItems.concat(this.muList.filter(s => {
        return !this.allSelectedItems.find(o => o.monitorUnitId === s.monitorUnitId);
      }));
    } else {
      _.map(this.muList, i => {
        const theIndex = this.allSelectedItems.findIndex(s => s.sequenceId === i.sequenceId);
        if (theIndex !== -1) {
          this.allSelectedItems.splice(theIndex, 1);
        }
      });
    }
  }

  changeSelected(e: any, item: any) {
    if (e.currentTarget.checked) {
      if (!this.allSelectedItems.find(o => o.monitorUnitId === item.monitorUnitId)) {
        this.allSelectedItems.push(item);
      }
    } else {
      const theIndex = this.allSelectedItems.findIndex(s => s.monitorUnitId === item.monitorUnitId);
      if (theIndex !== -1) {
        this.allSelectedItems.splice(theIndex, 1);
      }
    }
  }

  onGenerate() {
    let ids: any[] = [];
    this.allSelectedItems.forEach(s => {
      ids.push(s.monitorUnitId)
      if (s.monitorUnitCategory == 18 || s.monitorUnitCategory == 17)
        this.passWord = '0202@smsP';
    })
    if (ids.length > 0) {
      let msg = { webSocketBusinessType: 1, user: this.userName, passWord: this.passWord, port: this.port, monitorUnitIds: ids.join(',') }
      this.websocket.send(JSON.stringify(msg));
      if (this.timeoutId)
        clearTimeout(this.timeoutId)
      this.timeoutId = setTimeout(() => {
        this.refreshStatus(ids);
      }, 3000);
    }
  }

  onDownFile() {
    let ids: any[] = [];
    this.allSelectedItems.forEach(s => {
      ids.push(s.monitorUnitId)
    });
    this.httpClient.get('api/config/monitorunitxml/downloadMultipleMonitorUnitConfigXML?monitorUnitIds=' + ids.join(','), { responseType: 'blob' }).subscribe(res => {
      this.message.success('下载成功！');
      const a = document.createElement('a');
      a.href = URL.createObjectURL(res);
      let name = ids.join(',');
      a.download = (name.length <= 30 ? name : name.substring(0, 29) + '+') + "监控单元配置文件.zip";
      a.click();
    })
  }

  onDistribute() {
    let ids: any[] = [];
    this.allSelectedItems.forEach(s => {
      ids.push(s.monitorUnitId)
    })
    if (ids.length > 0) {
      let msg = {
        webSocketBusinessType: 2, user: this.userName, passWord: this.passWord, port: this.port, protocol: this.protocol ? 'sftp' : 'ftp', skipSoFileDownload: this.skipSoFileDownload, monitorUnitIds: ids.join(',')
      }
      this.websocket.send(JSON.stringify(msg));
    }
  }

  refreshStatus(ids: any[]) {
    this.apiService.get('api/config/monitor-unit/active?monitorUnitIds=' + ids.join(',')).then((res: any) => {
      this.muStatusList = res.data;
      for (let i = 0; i < this.allSelectedItems.length; i++) {
        let mu = this.allSelectedItems[i];
        for (let j = 0; j < this.muStatusList.length; j++) {
          if (this.muStatusList[i].monitorUnitId == mu.monitorUnitId) {
            switch (this.muStatusList[i].state) {
              case 0:
                mu.statusTxt = "无需下发";
                break;
              case 1:
                mu.statusTxt = "待下发";
                break;
              case 2:
                mu.statusTxt = "正在下发";
                break;
              case 3:
                mu.statusTxt = "下发成功";
                break;
              case 4:
                mu.statusTxt = "下发失败";
                break;
              default:
                break;
            }
            break;
          }
        }
      }
      if (this.timeoutId)
        clearTimeout(this.timeoutId)
      this.timeoutId = setTimeout(() => {
        this.refreshStatus(ids);
      }, 3000);
    })
  }

  submitForm() {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }


  current = 0;

  index = 'First-content';

  pre(): void {
    this.current -= 1;
    if (this.current == 1) {
      this.logDetail = this.websocketMsg.join('\r\n');;
    }
  }

  next(): void {
    this.current += 1;
    if (this.current == 1) {
      this.showAllSelectedItems = _.cloneDeep(this.allSelectedItems);
    }
    if (this.current == 2) {
      this.logDetail = this.websocketDownMsg.join('\r\n');;
      let isAllGfsuV3 = true;
      this.allSelectedItems.forEach(s => {
        if (s.monitorUnitCategory !== 18)
          isAllGfsuV3 = false;
      })
      if (isAllGfsuV3)
        this.protocol = true;
    }
  }

  done(): void {
    this.close();
  }
}
