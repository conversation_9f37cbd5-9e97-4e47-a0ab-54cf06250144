<nz-steps [nzCurrent]="current">
  <nz-step nzTitle="选择监控单元"></nz-step>
  <nz-step nzTitle="生成配置文件"></nz-step>
  <nz-step nzTitle="配置分发及状态查看"></nz-step>
</nz-steps>

<div class="steps-content">
  <d-data-table *ngIf="current==0" #dataTable tableHeight="500px" [scrollable]="true" [virtualScroll]="true"
    [dataSource]="showMuList" [fixHeader]="true" [containFixHeaderHeight]="true" [tableOverflowType]="'overlay'">
    <thead dTableHead>
      <tr dTableRow>
        <th dHeadCell style="width: 50px;">
          <label class="checkbox-inline custom-checkbox nowrap">
            <input type="checkbox" [ngModel]="allSelected" (change)="changeAllSelected($event)">
            <span></span>
          </label>
        </th>
        <th dHeadCell style="width: 500px;">
          <div>监控单元名称</div>
          <input style="width: 100%;" nz-input [formControl]="filter.name" [(ngModel)]="muName" (ngModelChange)="filterChange()" />
        </th>
        <th dHeadCell>
          <div>类型</div>
          <input nz-input [formControl]="filter.type" [(ngModel)]="muType" (ngModelChange)="filterChange()" />
        </th>
        <th dHeadCell>
          <div>IP</div>
          <input nz-input [formControl]="filter.address" [(ngModel)]="muIp" (ngModelChange)="filterChange()" />
        </th>
      </tr>
    </thead>
    <tbody dTableBody>
      <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
        <tr dTableRow>
          <td dTableCell style="width: 50px;">
            <label style="float: left;"><input type="checkbox" (change)="changeSelected($event,rowItem)"
                [(ngModel)]='rowItem.selected'>
              <span></span>
            </label>
          </td>
          <td dTableCell [rowItem]="rowItem" [title]="rowItem?.monitorUnitName" style="width: 500px;text-align: left;">
            <span>{{ rowItem?.monitorUnitName }}</span>
          </td>
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.typeName }}</span>
          </td>
          <td dTableCell [rowItem]="rowItem">
            <span>{{ rowItem?.ipAddress }}</span>
          </td>
        </tr>

      </ng-template>
    </tbody>
  </d-data-table>
  <div *ngIf="current>0" style="height:532px">
    <div style="display: inline-block; margin-top: 3px;" *ngIf="current==1">
      <button nz-button nzType="primary" (click)="onGenerate()">生成</button>
      <label style="color: blue;cursor: pointer;" (click)="onDownFile()" *ngIf="canGenerate">下载生成的配置文件</label>
    </div>
    <form nz-form *ngIf="current>1" [nzLayout]="'inline'" style="margin-top: 5px;" [formGroup]="validateForm"
      (ngSubmit)="submitForm()">
      <nz-form-item [ngStyle]="{'width': '190px'}">
        <!-- <nz-form-label  nzFor="userName" nzRequired nzTooltipTitle="">
          <span>用户名</span>
        </nz-form-label> -->
        <nz-form-control nzErrorTip="Please input your userName!">
          <nz-input-group nzPrefixIcon="user">
            <input formControlName="userName" [(ngModel)]="userName" nz-input placeholder="请输入用户名" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item [ngStyle]="{'width': '190px'}">
        <!-- <nz-form-label  nzFor="passWord" nzRequired nzTooltipTitle="">
          <span>密码</span>
        </nz-form-label> -->
        <nz-form-control nzErrorTip="Please input your passWord!">
          <nz-input-group [nzSuffix]="suffixTemplate" nzPrefixIcon="lock">
            <input id="passWord" formControlName="passWord" [type]="passwordVisible ? 'text' : 'password'" nz-input
              placeholder="请输入密码" [(ngModel)]="passWord" />
          </nz-input-group>
          <ng-template #suffixTemplate>
            <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
              (click)="passwordVisible = !passwordVisible"></span>
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzFor="port" nzRequired>
          <span>端口</span>
        </nz-form-label>
        <nz-form-control nzErrorTip="Please input your port!">
          <input nz-input id="port" type="number" max="65535" min="0" formControlName="port" [(ngModel)]="port" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label nzFor="protocol">
          <span>启用加密传输(采集器需支持SSH/SFTP)</span>
        </nz-form-label>
        <nz-form-control>
          <input nz-input id="protocol" type="checkbox" formControlName="protocol" [(ngModel)]="protocol" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item style="margin-top: 10px;">
        <nz-form-label nzFor="skipSoFileDownload">
          <span>跳过so下发</span>
        </nz-form-label>
        <nz-form-control>
          <input nz-input id="skipSoFileDownload" type="checkbox" formControlName="skipSoFileDownload" [(ngModel)]="skipSoFileDownload" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item style="margin-top: 10px;">
        <nz-form-control>
          <button nz-button nzType="primary" [disabled]="userName && passWord && port==0"
            (click)="onDistribute()">下发</button>
        </nz-form-control>
      </nz-form-item>
    </form>
    <nz-tabset nzType="card" style="height:500px;margin-top: 5px;">
      <nz-tab nzTitle="日志">
        <textarea rows="4" style="height: 430px;" class="dti_info_textarea" nz-input [(ngModel)]="logDetail"></textarea>
      </nz-tab>
      <nz-tab *ngIf="allSelectedItems.length>0" nzTitle="状态">
        <div style="height: 430px;">
          <d-data-table #dataTable1 [dataSource]="showAllSelectedItems" tableHeight="400px" [scrollable]="true"
            [virtualScroll]="true" [virtualItemSize]="33" [virtualMinBufferPx]="330" [virtualMaxBufferPx]="660"
            [fixHeader]="true" [containFixHeaderHeight]="true" [tableOverflowType]="'overlay'">
            <thead dTableHead>
              <tr dTableRow>
                <th dHeadCell style="width: 500px;">
                  <div>监控单元名称</div>
                  <input nz-input [formControl]="filterStatus.name" [(ngModel)]="muNameStatus"
                    (ngModelChange)="filterChangeStatus()" />
                </th>
                <th dHeadCell>
                  <div>类型</div>
                  <input nz-input [formControl]="filterStatus.type" [(ngModel)]="muTypeStatus"
                    (ngModelChange)="filterChangeStatus()" />
                </th>
                <th dHeadCell>
                  <div>IP</div>
                  <input nz-input [formControl]="filterStatus.address" [(ngModel)]="muIpStatus"
                    (ngModelChange)="filterChangeStatus()" />
                </th>
                <th dHeadCell>
                  <div>状态</div>
                  <input nz-input [formControl]="filterStatus.status" [(ngModel)]="strStatus"
                    (ngModelChange)="filterChangeStatus()" />
                </th>
              </tr>
            </thead>
            <tbody dTableBody>
              <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow>
                  <td dTableCell [rowItem]="rowItem" [title]="rowItem?.monitorUnitName"
                    style="width: 500px;text-align: left;">
                    <span>{{ rowItem?.monitorUnitName }}</span>
                  </td>
                  <td dTableCell [rowItem]="rowItem">
                    <span>{{ rowItem?.typeName }}</span>
                  </td>
                  <td dTableCell [rowItem]="rowItem">
                    <span>{{ rowItem?.ipAddress }}</span>
                  </td>
                  <td dTableCell [rowItem]="rowItem">
                    <span>{{ rowItem?.statusTxt }}</span>
                  </td>
                </tr>
              </ng-template>
            </tbody>
          </d-data-table>
        </div>
      </nz-tab>
    </nz-tabset>
  </div>
</div>
<div class="steps-action" *nzModalFooter>
  <button nz-button nzType="default" (click)="pre()" *ngIf="current > 0" [disabled]="(current==1 && canGenerate)">
    <span>上一步</span>
  </button>
  <button nz-button nzType="default"
    [disabled]="(current==0 && allSelectedItems.length==0) || (current==1 && !canGenerate)" (click)="next()"
    *ngIf="current < 2">
    <span>下一步</span>
  </button>
  <button nz-button nzType="primary" [disabled]="current==2 && !Completed" (click)="done()" *ngIf="current === 2">
    <span>完成</span>
  </button>
  <button nz-button nzType="default" (click)="close()">
    <span>取消</span>
  </button>
</div>