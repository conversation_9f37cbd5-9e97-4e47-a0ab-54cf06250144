<nz-space>
    <button nz-button *nzSpaceItem nzType="primary" [disabled]="getSelected().length==0"
        (click)="batchDistribute()"><span nz-icon nzType="download"></span>批量生成和分发监控单元配置</button>
    <!-- <button nz-button *nzSpaceItem nzType="primary" (click)="refreshList()"><span nz-icon
            nzType="reload"></span>刷新</button> -->
</nz-space>
<d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true" [tableWidthConfig]="tableColumnConfig"
    [virtualScroll]="true" [virtualItemSize]="33" [virtualMinBufferPx]="60" [virtualMaxBufferPx]="90"
    [onlyOneColumnSort]="true" [tableHeight]="'calc(100vh - 250px)'" [containFixHeaderHeight]="false" [fixHeader]="true"
    (tableScrollEvent)="tableScrollEvent($event)">
    <thead dTableHead>
        <tr dTableRow>
            <th dHeadCell>
                <input type="checkbox" [ngModel]="checkAll" (change)="onAllChecked(!checkAll)">
            </th>
            <th *ngFor="let colConfig of tableColumnDisplay" dHeadCell [sortable]="colConfig.field !== '$index'"
                (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)" [width]="colConfig.width">
                {{ colConfig.title }}
                <!-- 空白 -->
                <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                <!-- 输入框 -->
                <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                    (ngModelChange)="filterChange()" />
            </th>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow [ngClass]="{ 'table-row-selected': rowItem.$checked }"
                (contextmenu)="rowContextmenu($event, rowMenu)">
                <td dTableCell class="devui-checkable-cell">
                    <label class="checkbox-inline custom-checkbox nowrap">
                        <input type="checkbox" [ngModel]="checkedIds.includes(rowItem.monitorUnitId)"
                            (change)="onItemChecked(rowItem.monitorUnitId, !checkedIds.includes(rowItem.monitorUnitId))">
                        <span></span>
                    </label>
                </td>
                <ng-container *ngFor="let col of tableColumnDisplay">
                    <td *ngIf="col['field'] === 'statusTxt'" dTableCell [title]="rowItem[col.field]">
                        <div class="span-text red_text" *ngIf="rowItem['state'] ===4 ">
                            <iconfont icon="icon-cuowu-" style="font-size: 12px"></iconfont>
                            <span class="nodeName">{{ rowItem[col.field] }}</span>
                        </div>
                        <div class="span-text orange_text" *ngIf="rowItem['state'] ===2 ">
                            <iconfont icon="icon-zhidi" style="font-size: 12px"></iconfont>
                            <span class="nodeName">{{ rowItem[col.field] }}</span>
                        </div>
                        <div class="span-text green_text" *ngIf="rowItem['state'] ===3 ">
                            <iconfont icon="icon-zhengque-" style="font-size: 12px"></iconfont>
                            <span class="nodeName">{{ rowItem[col.field] }}</span>
                        </div>
                        <div class="span-text blue_text" *ngIf="rowItem['state'] ===1 ">
                            <iconfont icon="icon-zhidi" style="font-size: 12px"></iconfont>
                            <span class="nodeName">{{ rowItem[col.field] }}</span>
                        </div>
                        <div class="span-text no_text" *ngIf="rowItem['state'] ===0 ">
                            <iconfont icon="icon-cancle" style="font-size: 12px"></iconfont>
                            <span class="nodeName">{{ rowItem[col.field] }}</span>
                        </div>
                    </td>
                    <td *ngIf="col['field'] !== 'statusTxt'" dTableCell [title]="rowItem[col.field]"
                        [width]="col.width">
                        <div class="span-text">{{ rowItem[col.field] }}</div>
                    </td>
                </ng-container>
            </tr>
            <nz-dropdown-menu #rowMenu="nzDropdownMenu">
                <ul nz-menu nzSelectable>
                    <li nz-menu-item (click)="generate(rowItem)">
                        <iconfont icon="icon-deliveryconf" class="right-menu-icon" title="生成和分发监控单元配置"></iconfont>
                        生成和分发监控单元配置
                    </li>
                    <li nz-menu-item (click)="exportExcel()" nzDisabled="true">
                        <iconfont icon="icon-export" class="right-menu-icon" title="导出Excel"></iconfont>
                        导出Excel
                    </li>
                </ul>
            </nz-dropdown-menu>
        </ng-template>
    </tbody>
</d-data-table>