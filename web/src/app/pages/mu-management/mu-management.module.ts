import { NzIconModule } from 'ng-zorro-antd/icon';
import { CoreModule } from '@core/core.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { CommonModule } from '@angular/common';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { DevUIModule, IconModule } from 'ng-devui';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { ComponentsModule } from "@components/components.module";
import { NzPopoverModule } from "ng-zorro-antd/popover";
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { RmuMulistComponent } from './rmu-mulist/rmu-mulist.component';
import { GenerateDistributionMuComponent } from './generate-distribution-mu/generate-distribution-mu.component';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { RmuListComponent } from './rmu-list/rmu-list.component';
import { MuListComponent } from './mu-list/mu-list.component';
import { MuManagementComponent } from './mu-management.component';
import { MuGlobalDistributeStatusComponent } from './mu-global-distribute-status/mu-global-distribute-status.component';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        NzIconModule,
        IconModule,
        ReactiveFormsModule,
        CoreModule,
        ComponentsModule,
        NzProgressModule,
        NzTreeModule,
        NzFormModule,
        NzDividerModule,
        NzResizableModule,
        NzTabsModule,
        NzLayoutModule,
        NzSpaceModule,
        NzSpinModule,
        NzButtonModule,
        DevUIModule,
        NzListModule,
        NzDropDownModule,
        NzInputModule,
        NzSelectModule,
        NzCheckboxModule,
        TranslateModule,
        NzDatePickerModule,
        NzInputNumberModule,
        NzPopoverModule,
    ],
    declarations: [
        MuManagementComponent,
        RmuMulistComponent,
        GenerateDistributionMuComponent,
        RmuListComponent,
        MuGlobalDistributeStatusComponent,
        MuListComponent
    ],
    providers: [

    ],
})
export class MuManagementModule {

}
