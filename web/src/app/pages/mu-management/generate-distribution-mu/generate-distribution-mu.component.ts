import { HttpClient } from '@angular/common/http';
import { Component, Injector, ViewChild, viewChild } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { ApiService } from '@services/api-service';
import { NzProgressComponent } from 'ng-zorro-antd/progress';

@Component({
    selector: 'app-generate-distribution-mu',
    templateUrl: './generate-distribution-mu.component.html',
    styleUrl: './generate-distribution-mu.component.less'
})
export class GenerateDistributionMuComponent extends GenericModalComponent<any, any> {
    percent = 0;
    rmuList: any[] = [];
    timerId: any;
    generated: boolean = false;
    @ViewChild('processView') public processView!: NzProgressComponent;
    public constructor(
        injector: Injector,
        private http: HttpClient,
        private apiService: ApiService,
    ) {
        super(injector)
    }

    onInit() {
        this.getData();
    }

    getData() {
        if (this.input.rmuList) {
            this.rmuList = this.input.rmuList;
        }
    }

    protected onDestroy(): void {
        super.onDestroy();
        if (this.timerId)
            clearTimeout(this.timerId)
    }

    processSet() {
        if (this.percent == 88) {
            if (this.timerId)
                clearTimeout(this.timerId)
            return;
        }
        this.percent += 1;
        if (this.timerId)
            clearTimeout(this.timerId)
        this.timerId = setTimeout(() => {
            this.processSet();
        }, 300);
    }

    generate() {
        let ids: any[] = [];
        this.rmuList.forEach(s => {
            ids.push(s.workStationId)
        })
        ids = [...new Set(ids)];
        this.percent = 0;
        this.processSet();
        this.http.get('api/config/monitorunitxml/createAndDownloadMonitorUnitConfigXML?workStationIds=' + ids.join(','),
            {
                responseType: 'blob', observe: 'response'
            }).subscribe((res: any) => {
                if (res) {
                    if (this.timerId)
                        clearTimeout(this.timerId)
                    this.percent = 100;
                    const url = window.URL.createObjectURL(new Blob([res.body]));
                    const filename = res.headers.get('Content-Disposition').split(';')[1].split('=')[1].replaceAll('"', '');
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', filename);
                    document.body.appendChild(link);
                    link.click();
                    this.messageService.success("已成功生成并下载，请到下载目录查看文件");
                }
            }, error => {
                this.messageService.error("生成文件失败！");
                setTimeout(() => {
                    this.percent += 1;
                    this.processView.nzStatus = 'exception';
                }, 10);
                if (this.timerId)
                    clearTimeout(this.timerId)
            })
    }
}
