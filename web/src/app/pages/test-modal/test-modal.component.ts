/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, Injector, OnInit } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';

import * as _ from 'lodash'
import { TestModalParameter } from './test-model';
@Component({
  selector: 'app-test-modal',
  templateUrl: './test-modal.component.html',
  styleUrls: ['./test-modal.component.less']
})
/**
 *
 * 使用方式
 *
   <button nz-button nzType="primary" (click)="testDialog()"  >Test</button>

   private async testOpen(): Promise<void> {
    const VV = this.openDialog({
      nzContent: TestModalComponent,
      nzData: {
        name: '<PERSON><PERSON>',
        age: 18
      }
    });
    const result = await this.waitDialog(VV);
  }

 */


export class TestModalComponent extends GenericModalComponent<TestModalParameter, Boolean> {

  protected onInit(): void {
    console.log(this.input)
  }



  public submit(): void {
    this.close(true);
  }

}
