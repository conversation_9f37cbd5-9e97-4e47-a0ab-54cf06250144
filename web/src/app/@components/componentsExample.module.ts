import { NgModule } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { DataTableModule } from 'ng-devui/data-table'
import { DevuiTableFilterExampleLabelComponent } from './basic/devui-table-filter/Example/label/devui-table-filter-example-label.component'
import { DevuiTableFilterExampExtendsComponent } from './basic/devui-table-filter/Example/extends/devui-table-filter-example-extends.component'
import { CommonModule } from '@angular/common'
import { NzInputModule } from 'ng-zorro-antd/input'
import { ComponentsModule } from '@components/components.module'
import { ResizableLayoutExampleComponent } from './basic/resizable-layout/Example/resizable-layout-example.component'
import { DevUIModule } from 'ng-devui'

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    DataTableModule,
    NzInputModule,
    ComponentsModule,
    DevUIModule
  ],
  declarations: [
    DevuiTableFilterExampleLabelComponent,
    DevuiTableFilterExampExtendsComponent,
    ResizableLayoutExampleComponent,
  ]
})

export class ComponentsExampleModule { }