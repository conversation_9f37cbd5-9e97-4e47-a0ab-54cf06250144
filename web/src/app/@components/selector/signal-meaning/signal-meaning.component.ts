import { Component, Injector } from '@angular/core';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { RestfulService } from '@core/services/restful.service';
import { ControlMeaning, MeaningSelectorType } from './meaning-selector.model';

@Component({
  selector: 'app-signal-meaning',
  templateUrl: './signal-meaning.component.html',
  styleUrls: ['./signal-meaning.component.less']
})
export class SignalMeaningComponent extends GenericModalComponent<any, any> {
  public constructor(
    injector: Injector,
    private service: RestfulService,
  ) {
    super(injector);
  }
  public dataSource: any;
  public basetypemeaningsArr: Array<any> = []
  public baseStatusName: any
  public columns = [
    {
      field: 'stateValue',
      header: '值',
      width: '80px'
    },
    {
      field: 'meanings',
      header: '含义',
      width: '250px'
    },
    {
      field: 'baseCondId',
      header: '基类状态含义',
      width: '200px'
    }
  ];

  public defaultRowData = {
    stateValue: '',
    parameterValue: '',
    meanings: '含义',
    baseCondId: -1,
    equipmentTemplateId: null,
    signalId: null
  };

  public onInit(): void {
    if (this.input.type === MeaningSelectorType.control) {
      this.dataSource = this.input.row.controlMeaningsList
      this.dataSource.forEach((item: any) => {
        item.stateValue = item.parameterValue
      })
    } else {
      this.dataSource = this.input.data.signalMeaningsList
    }
    this.getData()
  }

  // 含义类型
  public getData(): void {
    let t_id
    let s_id
    if (this.input.type === MeaningSelectorType.control) {
      t_id = this.input.row.equipmentTemplateId
      s_id = this.input.row.controlId
    } else {
      t_id = this.input.data.equipmentTemplateId
      s_id = this.input.data.signalId
    }
    this.basetypemeaningsArr.push({ value: -1, meaning: '无' })
    if (this.input.type === MeaningSelectorType.control) {
      this.service.get<[]>(`api/config/control/basetypemeanings?equipmentTemplateId=${t_id}&controlId=${s_id}`).then((res: any) => {
        if (res && res.data) {
          this.baseStatusName = res.data['baseStatusName']
          this.basetypemeaningsArr = this.basetypemeaningsArr.concat(res.data['statusMeanings'])
        }
      })
    } else {
      this.service.get<[]>(`api/config/signal/basetypemeanings?equipmentTemplateId=${t_id}&signalId=${s_id}`).then((res: any) => {
        if (res && res.data) {
          this.baseStatusName = res.data['baseStatusName']
          this.basetypemeaningsArr = this.basetypemeaningsArr.concat(res.data['statusMeanings'])
        }
      })
    }
  }

  // 添加
  public addClick(): void {
    if (this.dataSource.find((item: any) => item.error)) return
    let maxId = Math.max(...this.dataSource.map((item: any) => item['stateValue']))
    maxId = (maxId === -Infinity) ? 0 : ++maxId
    const newData = { ...this.defaultRowData, stateValue: maxId }
    if (this.input.type !== MeaningSelectorType.control) {
      const data = this.dataSource.length? this.dataSource[0] : this.input.data
      newData.signalId = data.signalId
      newData.equipmentTemplateId = data.equipmentTemplateId
    }
    this.dataSource.push(newData)
  }

  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/explicit-function-return-type
  public beforeEditEnd = (rowItem: any, field: string) => {
    const reg = /^[0-9]+?$/
    if (rowItem && reg.test(rowItem[field])) {
      rowItem.error = false
      return true
    } else {
      rowItem.error = true
      return false
    }
  }

  // 删除
  public deleteClick(idx: number): void {
    this.dataSource.splice(idx, 1)
  }

  // 确定
  public confirm(): void {
    if (this.dataSource.find((item: any) => item.error)) return
    if (this.input.type === MeaningSelectorType.control) {
      this.dataSource.forEach((item: any) => {
        item.parameterValue = item.stateValue
      })
    }
    this.close({ action: 'confirm', data: this.dataSource })
  }
}
