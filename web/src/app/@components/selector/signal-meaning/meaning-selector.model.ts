// modal输入
export interface MeaningSelectorIn {
    // 类型
    type: MeaningSelectorType,
    // 行数据
    row: any,
    /**
     * 禁用，默认false
     */
    disabled?: boolean,
}

// modal输出
export interface MeaningSelectorOut {

}

// 类型枚举 信号 | 事件 | 控制
export enum MeaningSelectorType {
    signal = 0,
    event = 1,
    control = 2
}

export interface ControlMeaning {
    id: number,
    equipmentTemplateId: number,
    controlId: number,
    parameterValue: number,
    meanings: string,
    baseCondId: number,
}