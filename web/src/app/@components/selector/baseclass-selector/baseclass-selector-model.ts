import { BaseClassEquipmentTypeResponse, BaseClassEquipmentSubTypeResponse } from '@models/baseclass.model'

// 类型枚举 信号 | 事件 | 控制
export enum BaseClassSelectorType {
  signal = 0,
  event = 1,
  control = 2
}

// modal输入
export interface BaseClassSelectorParameter {
  // 类型
  type: BaseClassSelectorType,
  // 基类id
  baseTypeId: number | null
  // 信号/事件/控制名称
  name: string
  // 类型为事件时，需要传事件条件
  eventCondition?: string
  rowItem: any
  /**
   * 设备模板id，用于初始化大类小类
   */
  equipmentTemplateId: number
}

// modal输出
export interface BaseClassSelectorOut {
  action: string
  baseTypeId: number | null
  baseTypeName?: string
  moduleNumber: number | null
  row: any
}

// 设备大类
export interface EquipmentCascaderNode extends BaseClassEquipmentTypeResponse {
  children: Array<EquipmentSubCascaderNode>
  value: number
  label: string
}

// 设备子类
export interface EquipmentSubCascaderNode extends BaseClassEquipmentSubTypeResponse {
  value: number
  label: string
  isLeaf: boolean
}

// 事件条件
export interface EventConditionRow {
  eventConditionId?: string
}
