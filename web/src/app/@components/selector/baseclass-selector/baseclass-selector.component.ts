import { Component, Injector, ViewChild } from '@angular/core'
import { GenericModalComponent } from '@core/components/basic/generic.modal'
import { FormControl, FormGroup, NonNullableFormBuilder, Validators } from '@angular/forms'
import { DataTableComponent, SortDirection, SortEventArg } from 'ng-devui/data-table'
import { BaseClassService } from '@services/baseclass-api-service'
import { NzMessageService } from 'ng-zorro-antd/message'
import { BaseClassSelectorOut, BaseClassSelectorParameter, BaseClassSelectorType, EquipmentCascaderNode, EventConditionRow } from '@components/selector/baseclass-selector/baseclass-selector-model'
import { DeviceTemplateService } from 'app/pages/device-template/device-template.service'
import { DevuiTableFilterRow } from '@components/basic/devui-table-filter/devui-table-filter.model'

@Component({
  selector: 'app-baseclass-selector',
  templateUrl: './baseclass-selector.component.html',
  styleUrls: ['./baseclass-selector.component.less']
})

export class BaseClassSelectorComponent extends GenericModalComponent<BaseClassSelectorParameter, BaseClassSelectorOut> {

  public constructor(
    private fb: NonNullableFormBuilder,
    injector: Injector,
    private service: BaseClassService,
    private deviceTemplateService: DeviceTemplateService,
    private message: NzMessageService
  ) {
    super(injector)
  }

  // ng-form
  public validateForm: FormGroup<{
    nameValue: FormControl<string>
    eventCondition: FormControl<string>
    equipmentSubType: FormControl<string>
    moduleNumber: FormControl<string>
  }> = this.fb.group({
    nameValue: [{ value: this.input.name, disabled: true }],
    eventCondition: [{ value: this.input.eventCondition || '', disabled: true }],
    equipmentSubType: [''],
    moduleNumber: ['', [Validators.pattern(/^[1-9]\d*$/)]]
  })
  public nameLabel: string = ''
  public bindBaseClassLabel: string = ''
  public remindAgain: string = ''
  public tableHeaderId: string = ''
  public tableHeaderName: string = ''
  // 协助Api，根据选择器类型请求【信号|事件|告警】相关接口
  public assistApiOfEquipmentType: string = ''
  public assistApiOfTable: string = ''

  // devui table
  @ViewChild(DataTableComponent, { static: true }) public datatable: DataTableComponent | undefined
  public orgTableDataSource: Array<any> = []
  public filterTableDataSource: Array<any> = []
  public displayTableDataSource: Array<any> = []
  public tableColumnConfig = [
    {
      field: 'baseTypeId',
      width: '120px',
      title: this.getColumnHeaderTitle('tableHeaderId'),
    },
    {
      field: 'baseTypeName',
      width: '150px',
      title: this.getColumnHeaderTitle('tableHeaderName'),
    },
    {
      field: 'baseNameExt',
      width: '150px',
      title: this.translate.instant('baseClass.extendedExpression'),
    }
  ]
  public sortField: string | undefined
  public sortEvent: SortEventArg = { direction: SortDirection.default }
  public filterIconActiveId = false
  public filterIconActiveName = false
  public filterIconActiveExp = false

  // 设备类型
  public equipmentTypeOptions: EquipmentCascaderNode[] | null = null;
  public equipmentTypeValue: number[] | null = null

  // 选中数据
  public selectedRow = { baseTypeId: null, baseNameExt: '', baseTypeName: '' }

  // 模块号
  public moduleNumber: number | null = null

  // 方法
  protected onInit(): void {
    this.initType()
    this.requestEquipmentTreeData()
    this.setDefault()
  }

  // 获取列头标题
  public getColumnHeaderTitle(variables: string): string {
    switch (this.input.type) {
      case BaseClassSelectorType.signal:
        this.tableHeaderId = this.translate.instant('baseClass.baseSignalId')
        this.tableHeaderName = this.translate.instant('baseClass.baseSignalName')
        break
      case BaseClassSelectorType.event:
        this.tableHeaderId = this.translate.instant('baseClass.baseEventId')
        this.tableHeaderName = this.translate.instant('baseClass.baseEventName')
        break
      case BaseClassSelectorType.control:
        this.tableHeaderId = this.translate.instant('baseClass.baseControlId')
        this.tableHeaderName = this.translate.instant('baseClass.baseControlName')
        break
    }
    return (this as any)[variables]
  }

  // 初始化类型
  public initType(): void {
    switch (this.input.type) {
      case BaseClassSelectorType.signal:
        this.nameLabel = this.translate.instant('baseClass.signalName')
        this.bindBaseClassLabel = this.translate.instant('baseClass.associatedBaseSignal')
        this.remindAgain = this.translate.instant('baseClass.confirmAgainTipCancelBaseSignal')
        this.assistApiOfEquipmentType = 'signalbasedic'
        this.assistApiOfTable = 'signal'
        break
      case BaseClassSelectorType.event:
        this.nameLabel = this.translate.instant('baseClass.eventName')
        this.bindBaseClassLabel = this.translate.instant('baseClass.associatedBaseEvent')
        this.remindAgain = this.translate.instant('baseClass.confirmAgainTipCancelBaseEvent')
        this.assistApiOfEquipmentType = 'eventbasedic'
        this.assistApiOfTable = 'event'
        break
      case BaseClassSelectorType.control:
        this.nameLabel = this.translate.instant('baseClass.controlName')
        this.bindBaseClassLabel = this.translate.instant('baseClass.associatedBaseControl')
        this.remindAgain = this.translate.instant('baseClass.confirmAgainTipCancelBaseControl')
        this.assistApiOfEquipmentType = 'commandbasedic'
        this.assistApiOfTable = 'control'
        break
    }
  }

  // 设备类型回显
  public async setDefault(): Promise<void> {
    if (this.input.baseTypeId) {
      this.service.getEquipmentTypeOfBaseTypeId(this.assistApiOfEquipmentType, this.input.baseTypeId).then(res => {
        this.equipmentTypeValue = [parseInt(res.baseEquipmentId / 100 + ''), res.baseEquipmentId % 100]
      })
    } else {
      const deviceInfo = await this.deviceTemplateService.getTemplateInfoById(this.input.equipmentTemplateId)
      if (!deviceInfo.data.equipmentBaseType) return
      this.service.getEquipmentTypeOfBaseEquipmentId(deviceInfo.data.equipmentBaseType).then(res => {
        if (res?.length)
          this.equipmentTypeValue = [res[0].equipmentTypeId, res[0].equipmentSubTypeId]
      })
    }
  }

  // 请求设备类型数据
  public requestEquipmentTreeData(): void {
    this.service.getBaseClassEquipmentTree().then(res => {
      res.forEach(item => {
        item.children.forEach((sub: { isLeaf: boolean }) => {
          sub.isLeaf = true
        })
      })
      this.equipmentTypeOptions = res
      if (!this.input.baseTypeId) this.equipmentTypeValue = null
    })
  }

  // 设备类型监听
  public onChanges(): void {
    this.requestBaseClassList()
  }

  // 请求基类列表
  public requestBaseClassList(): void {
    if (!this.equipmentTypeValue) {
      return
    }
    const params = {
      eqTypeId: this.equipmentTypeValue[0],
      eqSubTypeId: this.equipmentTypeValue[1]
    }
    this.service.getBaseClasslList(this.assistApiOfEquipmentType, params).then(res => {
      this.orgTableDataSource = res
      this.filterTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource = [...this.orgTableDataSource]
      this.displayTableDataSource.forEach((item, index) => {
        if (item.baseTypeId === this.input.baseTypeId) {
          item['$checked'] = true
          this.selectedRow = item
          setTimeout(() => {
            const targetRowElement = document.getElementById(`row-${index}`)
            if (targetRowElement) {
              targetRowElement.scrollIntoView()
            }
          }, 0)
        }
      })
    })
  }

  // ng-form 校验
  public submitForm(): Boolean {
    if (this.validateForm.valid) {
      return true
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
      return false
    }
  }

  // 点击行
  public onRowClick(row: DevuiTableFilterRow): void {
    this.displayTableDataSource.forEach(item => {
      item['$checked'] = false
    })
    this.displayTableDataSource[row.index]['$checked'] = true
    this.selectedRow = row.item
  }

  // 取消关联
  public delete(): void {
    if (!this.input.baseTypeId) {
      this.message.info(this.translate.instant('baseClass.noAssociatedBaseData'))
      return
    }
    this.openDialog({
      nzTitle: '警告',
      nzContent: this.remindAgain,
      nzOnOk: _ => {
        this.close({ action: 'delete', baseTypeId: null, moduleNumber: this.moduleNumber, row: this.selectedRow })
      }
    })
  }

  // 确定
  public async confirm(): Promise<void> {
    const valid = this.submitForm()
    if (!valid) {
      return
    }
    if (!this.selectedRow.baseTypeId) {
      this.message.create('warning', '请选择一条数据')
      return
    }
    return new Promise<void>(stopLoading => {
      // 有模块号
      if (this.moduleNumber) {
        const baseTypeId = (parseInt(this.selectedRow.baseTypeId! / 1000 + '')) * 1000 + parseInt(this.moduleNumber + '')
        const baseTypeName = this.selectedRow.baseNameExt.replace('{0}', this.moduleNumber + '')
        const row: any = this.input.rowItem
        switch (this.input.type) {
          case BaseClassSelectorType.signal:
            row.baseTypeId = baseTypeId
            row.baseTypeName = this.selectedRow.baseNameExt.replace('{0}', this.moduleNumber + '')
            break
          case BaseClassSelectorType.event:
            // eslint-disable-next-line no-case-declarations
            const conditionIndex = row.eventConditionList.findIndex((item: EventConditionRow) => { return item.eventConditionId == this.input.eventCondition })
            row.eventConditionList[conditionIndex].baseTypeId = baseTypeId
            row.eventConditionList[conditionIndex].baseTypeName = this.selectedRow.baseNameExt.replace('{0}', this.moduleNumber + '')
            break
          case BaseClassSelectorType.control:
            row.baseTypeId = baseTypeId
            row.baseTypeName = this.selectedRow.baseNameExt.replace('{0}', this.moduleNumber + '')
            break
        }
        this.service.updateTableRow(this.assistApiOfTable, row).then(res => {
          if (res === 1 || res === '' || res) {
            this.service.addBaseType(this.assistApiOfEquipmentType, row.equipmentTemplateId).then(_ => {
              this.close({ action: 'confirm', baseTypeId: baseTypeId, baseTypeName: baseTypeName, moduleNumber: this.moduleNumber, row: this.selectedRow })
            }).finally(() => {
              stopLoading()
            })
          } else {
            stopLoading()
          }
        })
      }
      // 没有模块号
      else {
        this.close({ action: 'confirm', baseTypeId: this.selectedRow.baseTypeId, baseTypeName: this.selectedRow.baseTypeName, moduleNumber: this.moduleNumber, row: this.selectedRow })
      }
    })
  }
}
