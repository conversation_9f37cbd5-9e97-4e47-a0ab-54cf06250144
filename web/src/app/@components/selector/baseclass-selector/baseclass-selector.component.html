<form nz-form [formGroup]="validateForm" class="login-form">
    <nz-form-item>
        <nz-form-label [nzSpan]="5">{{ nameLabel }}</nz-form-label>
        <nz-form-control>
            <input nz-input formControlName="nameValue" />
        </nz-form-control>
        <nz-form-label *ngIf="input.type === 1" [nzSpan]="5">{{ 'baseClass.eventCondition' | translate
            }}</nz-form-label>
        <nz-form-control *ngIf="input.type === 1">
            <input nz-input formControlName="eventCondition" />
        </nz-form-control>
    </nz-form-item>
    <nz-form-item>
        <nz-form-label [nzSpan]="5">{{ 'baseClass.equipmentType' | translate }}</nz-form-label>
        <nz-form-control>
            <!-- nz-input-group 解决nz-cascader异步赋nzOptions值出现动画问题 -->
            <nz-input-group>
                <nz-cascader [nzOptions]="equipmentTypeOptions" [(ngModel)]="equipmentTypeValue"
                    (ngModelChange)="onChanges()" formControlName="equipmentSubType"
                    [nzAllowClear]="false"></nz-cascader>
            </nz-input-group>
        </nz-form-control>
    </nz-form-item>
    <nz-form-item>
        <nz-form-label [nzSpan]="5">{{ bindBaseClassLabel }}</nz-form-label>
        <nz-form-control>
            <div style="height: 260px;">
                <app-devui-table-filter [dataSource]="orgTableDataSource" [columnConfig]="tableColumnConfig"
                    (rowClick)="onRowClick($event)" />
            </div>
        </nz-form-control>
    </nz-form-item>
    <nz-form-item *ngIf="selectedRow.baseNameExt">
        <nz-form-label [nzSpan]="5">{{ 'baseClass.index' | translate }}</nz-form-label>
        <nz-form-control [nzErrorTip]="'baseClass.indexRule' | translate">
            <input formControlName="moduleNumber" nz-input [(ngModel)]="moduleNumber" />
        </nz-form-control>
    </nz-form-item>
</form>