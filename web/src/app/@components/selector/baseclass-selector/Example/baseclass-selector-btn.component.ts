import { Component, Injector, TemplateRef } from '@angular/core'
import { GenericComponent } from '@core/components/basic/generic.component'
import { BaseClassSelectorComponent } from '../baseclass-selector.component'
import { BaseClassSelectorOut, BaseClassSelectorParameter, BaseClassSelectorType } from '@components/selector/baseclass-selector/baseclass-selector-model'
import { ModalButtonOptions } from 'ng-zorro-antd/modal'

@Component({
    selector: 'app-baseclass-selector-btn',
    templateUrl: './baseclass-selector-btn.component.html',
    styleUrls: ['./baseclass-selector-btn.component.less'],
})
export class BaseClassSelectorBtnComponent extends GenericComponent {

    public constructor(injector: Injector) {
        super(injector)
    }

    public baseTypeId: number | null = null

    public createComponentModal(): void {
        const nzFooter: Array<ModalButtonOptions> = [{
            label: '取消',
            onClick: componentInstance => componentInstance?.close()
        },
        {
            label: '确定',
            type: 'primary',
            onClick: async componentInstance => componentInstance?.confirm()
        }]
        if (this.baseTypeId) {
            nzFooter.unshift({
                label: '取消关联基类',
                danger: true,
                ghost: true,
                onClick: componentInstance => componentInstance?.delete()
            })
        }
        const model = this.openDialog<BaseClassSelectorComponent, BaseClassSelectorParameter, BaseClassSelectorOut>({
            nzTitle: '基类设置',
            nzContent: BaseClassSelectorComponent,
            nzWidth: 750,
            nzData: {
                type: BaseClassSelectorType.signal,
                baseTypeId: this.baseTypeId,
                name: '设备通讯状态',
                eventCondition: '0',
                rowItem: {
                    'id': 427,
                    'equipmentTemplateId': 573000014,
                    'signalId': 510000641,
                    'enable': true,
                    'visible': true,
                    'description': '',
                    'signalName': 'DI6',
                    'signalCategory': 2,
                    'signalType': 1,
                    'channelNo': 34,
                    'channelType': 2,
                    'expression': '',
                    'dataType': 0,
                    'showPrecision': '0',
                    'unit': '',
                    'storeInterval': 0,
                    'absValueThreshold': 0,
                    'percentThreshold': null,
                    'staticsPeriod': null,
                    'baseTypeId': null,
                    'baseTypeName': null,
                    'chargeStoreInterVal': 0,
                    'chargeAbsValue': null,
                    'displayIndex': 23,
                    'moduleNo': 0,
                    'signalPropertyList': [
                        {
                            'id': 358,
                            'equipmentTemplateId': 573000014,
                            'signalId': 510000641,
                            'signalPropertyId': 27
                        }
                    ],
                    'signalMeaningsList': [
                        {
                            'id': 639,
                            'equipmentTemplateId': 573000014,
                            'signalId': 510000641,
                            'stateValue': 0,
                            'meanings': '有告警',
                            'baseCondId': null
                        },
                        {
                            'id': 640,
                            'equipmentTemplateId': 573000014,
                            'signalId': 510000641,
                            'stateValue': 1,
                            'meanings': '无告警',
                            'baseCondId': null
                        }
                    ],
                    'mdbsignalId': 0
                }
            },
            nzFooter
        })
        model.afterClose.subscribe(res => {
            console.log('[afterClose] The result is:', res)
            switch (res?.action) {
                case 'confirm':
                    this.baseTypeId = res.baseTypeId
                    break
                case 'delete':
                    this.baseTypeId = null
                    break
            }
        })
    }
}
