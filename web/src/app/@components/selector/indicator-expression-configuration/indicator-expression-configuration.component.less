:host ::ng-deep {
    .expression-modal {
        width: 100%;
    }
    .ant-input-affix-wrapper {
        padding: 4px 10px !important;
    }

    .search {
        padding: 0 !important;
    }

    .btn-group {
        button {
            width: 45px;
            margin: 2px;
        }
    }

    .signalList {
        border: 1px solid #ddd;
        height: 150px;
        padding: 5px 0;
        overflow: auto;

        .item {
            cursor: pointer;
            padding-left: 10px;
            &:hover {
                background-color: #f2f5fc;
            }
        }
        .disabled {
            opacity: 0.5;
        }
    }

    textarea {
        padding: 5px 10px !important;
    }

    .expression {
        position: relative;
    }

    .correct {
        border-color: #a2db59;
    }

    .error {
        border-color: #fa758e;
    }

    .form-control {
        width: 100%;
        height: 100px;
    }

    .operation-configuration {
        ul {
            overflow: hidden;
            width: auto;
            padding: 0;
            border: 1px solid rgba(var(--line-modal), 0.2);

            li {
                float: left;
                width: 40px;
                height: 20px;
                text-align: center;
                cursor: pointer;

                &:hover {
                    background-color: #007fff;
                }
            }
        }
    }

    .name {
        width: 100%;
        padding-bottom: 5px;
    }

    .selected{
        background-color: #C4E2FF;
    }

    .form-group {
        margin: 10px 0;
    }

    .iconfont {
        position: absolute;
        top: 25px;
        right: 3px;
        cursor: pointer;
    }
}