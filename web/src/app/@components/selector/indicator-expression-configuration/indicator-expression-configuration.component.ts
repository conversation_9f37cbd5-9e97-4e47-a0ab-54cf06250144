import { Component, EventEmitter, Injector, Input, Output, SimpleChanges, inject } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { IndicatorExpressionService } from './indicator-expression.service';
import { GenericModalComponent } from '@core/components/basic/generic.modal';
import { TableColumnConfig } from '@components/basic/devui-table-filter/devui-table-filter.model';

interface ResourceStructureType {
    typeId: number;
    sceneId: number;
    typeName: string;
    desc: string;
  }
  
@Component({
  selector: 'app-indicator-expression-configuration',
  templateUrl: './indicator-expression-configuration.component.html',
  styleUrls: ['./indicator-expression-configuration.component.less']
})
export class IndicatorExpressionConfigurationComponent extends GenericModalComponent<any, any> {
  @Input()
  public readonly input: any = inject(NZ_MODAL_DATA);
  @Output() getValue = new EventEmitter<string>();
  tabs = ['指标'];
  currentTab = 0;
  correct = true;
  isInstance: boolean = false;
  isCrossSite: boolean = false;
  isAlarmLinkage: boolean = false;
  public constructor(
    injector: Injector,
    private service: IndicatorExpressionService,
    private modal: NzModalService,
  ) {
    super(injector);
  }
  expression = '';
  description = '';
  signalSelectedData: any = []
  mousePosition: any
  isMouse = false;
  formulaMathArray = [
    { name: "+", description: "加" },
    { name: "-", description: "减" },
    { name: "*", description: "乘" },
    { name: "/", description: "除" },
    { name: ">", description: "大于" },
    { name: "==", description: "等于" },
    { name: ">=", description: "大于等于" },
    { name: "<", description: "小于" },
    { name: "<=", description: "小于等于" },
    { name: "AND", description: "逻辑与" },
    { name: "OR", description: "逻辑或" },
    { name: "NOT", description: "逻辑非" },
    { name: "(", description: "左括号" },
    { name: ")", description: "右括号" },
    { name: "max()", description: "最大值" },
    { name: "min()", description: "最小值" },
    { name: "avg()", description: "平均值" },
    { name: ":", description: "延时" },
    { name: "?", description: "设备预警持续时间" },
    { name: "#", description: "分割表达式" },
    { name: "$", description: "后面跟事件条件ID，高频告警屏蔽" },
    { name: "@", description: "后面跟单次告警有效时间，告警屏蔽" },
    { name: "%", description: "后面跟统计周期，配置高频次告警屏蔽" },
    { name: "^", description: "后面跟统计周期数，用于高频次告警屏蔽" },
    { name: ";", description: "后面跟周期内告警频次阀值，用于高频次告警屏蔽" },
  ];
  instance: any;
  searchText: any;
  indexSearchKey: any;
  selectedValue: number = -1; // 选中的下拉值
  selectOptions: any;

  public  oriSource: Array<any> = [];
  public  showSource: Array<any> = [];
    public  tableColumnConfig: TableColumnConfig[] = [
        {
            field: 'complexIndexId',
            width: '80px',
            title: '指标编号',
        },
        {
            field: 'complexIndexName',
            width: '150px',
            title: '指标名称',
            minWidth: '200',
        },
        {
            field: 'businessTypeName',
            width: '150px',
            title: '业务类型',
            minWidth: '200',
        },
        {
            field: 'objectName',
            width: '150px',
            title: '指标对象',
            minWidth: '200',
        },
    ]

  public onSelectChange(value: number): void {
    if (value === null) {
      this.service.getAllComplexIndexList().then(res => {
        this.oriSource = res;
        console.info(this.oriSource)
        this.filterSource();
      });
    }
    else {
      this.service.getComplexIndexList(value).then(res => {
        this.oriSource = res;
        console.info(this.oriSource)
        this.filterSource();
      });
    }
  }
  public tableScrollEvent(event: any) :void {
  }
  public onInputChange(event: any): void {
    this.filterSource();
  }
  public filterSource():void{
    if (!this.indexSearchKey){
      this.showSource = this.oriSource;
      return;
    }
    const searchTerm = this.indexSearchKey;
    this.showSource = this.oriSource.filter(item => {
      const complexIndexNameMatch = item.complexIndexName.includes(searchTerm);
      const businessTypeNameMatch = item.businessTypeName.includes(searchTerm);
      const objectNameMatch = item.objectName.includes(searchTerm);
      const complexIndexIdMatch = item.complexIndexId.toString().includes(searchTerm);
      return complexIndexNameMatch || businessTypeNameMatch || objectNameMatch || complexIndexIdMatch;
    });
  }
  public onInit() :void{
    this.expression = this.input.expression ? this.input.expression : '';
    this.currentTab = this.input.type;
    this.isInstance = this.input.isInstance ? this.input.isInstance : false;
    this.instance = this.input.instance ? this.input.instance : null;
    if (this.instance.expression && this.instance.expression.length > 0){
      this.complexIndexCheckExpression(this.instance.expression)
  }
    this.isCrossSite = this.input.isCrossSite ? this.input.isCrossSite : false;
    this.isAlarmLinkage = this.input.isAlarmLinkage ? this.input.isAlarmLinkage : false;
    this.getResourceSturectureType()
    if (this.isInstance) {
      this.service.getAllComplexIndexList().then(res => {
        this.oriSource = res;
        this.showSource = this.oriSource;
      });
      if (this.isCrossSite) {
        this.tabs = ['指标'];
        this.formulaMathArray = this.formulaMathArray.slice(0, 17);
      }
    } 
  }

  public selectIndex(event:any) :void{
      if (this.input.disabled) {
        return;
      }
      const ci = this.instance ? this.instance.equipmentId : -1;
      const expressionName = `CI(${event.complexIndexId})`;
      const descriptionName = `${event.complexIndexName}`;
    
      if (this.isInstance) {
        event['expressionName'] = expressionName;
        event['descriptionName'] = descriptionName;
      } else {
        event['expressionName'] = expressionName;
        event['descriptionName'] = descriptionName;
      }
    
      // 长度限制检查
      if (this.isCrossSite) {
        if (this.expression.length + event['expressionName'].length > 1024) {
          this.messageService.error('跨站表达式长度不能超过1024字符');
          return;
        }
      } else {
        if (this.expression.length + event['expressionName'].length > 1024) {
          this.messageService.error('表达式长度不能超过1024字符');
          return;
        }
      }
    
      this.signalSelectedData.push({
        descriptionName: event['descriptionName'],
        expressionName: event['expressionName']
      });
    
      // 根据是否获取到鼠标位置更新表达式
      if (this.isMouse) {
        // 获取数据位置并插入
        this.expression = this.expression.slice(0, this.mousePosition) + event['expressionName'] + this.expression.slice(this.mousePosition);
        this.mousePosition = this.expression.length;
    
        let exp = this.expression;
        this.signalSelectedData.forEach((item: any) => {
          const express = item['expressionName'];
          if (exp.includes(express)) {
            exp = exp.replace(express, item['descriptionName']);
          }
        });
        this.description = exp;
      } else {
        // 未获取位置的处理
        this.expression += event['expressionName'];
        this.description += event['descriptionName'];
      }
    
      // 检查表达式的合法性
       this.complexIndexCheckExpression(this.expression);
    
    
  }
  public getMousePosition(event: any) :void {
    if (event.target.selectionStart !== this.expression.length) {
      this.isMouse = true
      this.mousePosition = event.target.selectionStart
    } else {
      this.isMouse = false
    }
  }
  public complexIndexCheckExpression(event: any) :void {
    const inputElement = document.querySelector('.expression-text') as HTMLInputElement;
    if (inputElement) this.mousePosition = inputElement.selectionStart;
    //表达式方框检验
    this.service.complexIndexExpressionCheck(event).then((res: any) => {
      this.correct = res.valid;
      this.description = res.resultString;
    })
  }
  public getResourceSturectureType() : void{
    this.service.getResourceStructureType().then((res: ResourceStructureType[]) => {
      this.selectOptions = res.map((item: ResourceStructureType) => ({
        label: item.typeName,
        value: item.typeId
      }));
    });
  }
  

  // 选择运算符
  public operate(event: any) : void{

    if (this.isCrossSite) {
      if (this.expression.length + event.length > 1024) {
        this.messageService.error('跨站表达式长度不能超过1024字符');
        return;
      }
    } else {
      if (this.expression.length + event.length > 1024) {
        this.messageService.error('表达式长度不能超过1024字符');
        return;
      }
    }

    if (this.isMouse) {
      //获取鼠标位置
      this.expression = this.expression.slice(0, this.mousePosition) + event + this.expression.slice(this.mousePosition)
      this.mousePosition = this.expression.length + 1

      let exp = this.expression;
      this.signalSelectedData.forEach((item: any) => {
        const express = item['expressionName'];
        if (exp.includes(express)) {
          exp = exp.replace(express, item['descriptionName']);
        }
      })
      this.description = exp
    } else {
      this.expression = this.expression + event
      this.description = this.description + event
    }

    this.complexIndexCheckExpression(this.expression)
  }



  public confirm() :void{
    this.close({ action: 'update', expression: this.expression })
  }

  public deleteExpression() : void{
    this.expression = '';
    this.description = '';
  }

}
