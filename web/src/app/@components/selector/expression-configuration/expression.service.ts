import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'

@Injectable()
export class ExpressionService extends RestfulService {

    /**
    * 根据设备模板ID获取信号列表
    * @returns
    */
    public async getSignalList(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/expression/signal/${id}`)
        return res.data
    }

    /**
    * 根据设备模板ID获取信号列表
    * @param eqTypeId 设备模板ID
    * @returns
    */
    public async getEventList(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/expression/event/${id}`)
        return res.data
    }

    /**
    * 表达式验证
    * @returns
    */
    public async getExpressionValidate(expression: any): Promise<any[]> {
        const params = { expression: expression }
        const res = await this.post<any[]>('/api/config/expression/validate', params)
        return res.data
    }

    /**
    * 获取表达式含义
    * @param eqTypeId 设备模板ID
    * @returns
    */
    public async getDescription(id: number, expression: any, isCrossStation?: boolean): Promise<[]> {
        const params = isCrossStation? { equipmentTemplateId: id, expression: expression, isCrossStationMonitorUnit : true } : { equipmentTemplateId: id, expression: expression }
        const res = await this.post<[]>(`api/config/expression/analysis`, params)
        return res.data
    }

    /**
    * 根据设备ID获取信号列表
    * @returns
    */
    public async getSignalListByEqId(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/expression/signal?equipmentId=${id}`)
        return res.data
    }

    /**
    * 根据设备ID获取事件列表
    * @returns
    */
    public async getEventListByEqId(id: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/expression/event?equipmentId=${id}`)
        return res.data
    }

    /**
    * 根据局站ID获取设备列表
    * @returns
    */
    public async getEquipmentListByStationId(stationId: number, monitorUnitId: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/equipment/simplifyequipments/${stationId}/${monitorUnitId}`)
        return res.data
    }

    public async getAllEquipmentList(): Promise<[]> {
        const res = await this.get<[]>(`api/config/equipment/across/list`)
        return res.data
    }

   /**
    * 设备列表 告警联动功能使用过
    * @param monitorUnitId 监控单元id
    * @returns 
    */
    public async getEquipmentListByMonitorUnit(monitorUnitId: number): Promise<any[]> {
        const res = await this.get<any[]>(`/api/config/equipment/simplifysplicelist?monitorUnitId=${monitorUnitId}&spliceFlag=true`)
        return res.data
    }

}
