/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, EventEmitter, Injector, Input, Output, ViewChild } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { DataDictionaryService } from '@services/data-dictionary.service';
import { BaseClassSelectorComponent } from '@components/selector/baseclass-selector/baseclass-selector.component';
import { BaseClassSelectorOut, BaseClassSelectorParameter } from '@components/selector/baseclass-selector/baseclass-selector-model';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ExpressionConfigurationComponent } from '@components/selector/expression-configuration/expression-configuration.component';
import { SignalMeaningComponent } from '@components/selector/signal-meaning/signal-meaning.component';
import _, { cloneDeep, isArray } from 'lodash';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { JexcelTableComponent } from '@components/jexcel-table/jexcel-table.component';
import { jExcelColumnType } from '@components/jexcel-table/model/column-config';
import { NavigationEnd, Router } from '@angular/router';
import { DeviceManagementService } from 'app/pages/device-management/device-management.service';
import { DeviceTemplateService } from 'app/pages/device-template/device-template.service';
import { DeviceTemplateConfirmComponent } from 'app/pages/device-template/device-template-confirm/device-template-confirm.component';
import { DeviceTemplateSecSelectorComponent } from 'app/pages/device-template/component/addOjcectFormTemplate/device-template-sec-selector.component';
import { DeviceSignalInstanceComponent } from 'app/pages/device-management/device-signal-instance/device-signal-instance.component';
import { DeviceSignalInstanceListComponent } from 'app/pages/device-management/device-signal-instance-list/device-signal-instance-list.component';
import { Subscription } from 'rxjs';
import { CrossSiteSignalComponent } from 'app/pages/device-management/cross-site-signal/cross-site-signal.component';
import { DeviceTemplateConfirmActionOptions } from 'app/pages/device-template/device-template-confirm/device-template-confirm.model';
import { FieldCopyToSelector } from 'app/pages/device-template/field-copy-to-selector/field-copy-to-selector.component';
@Component({
  selector: 'app-device-template-signal',
  templateUrl: './device-template-signal.component.html',
  styleUrls: ['./device-template-signal.component.less']
})

export class DeviceTemplateSignalComponent extends GenericComponent {
  @ViewChild('JEtable') jspreadsheet!: JexcelTableComponent;
  template: any;
  isRootTemp: boolean = false;
  _tabIndex: any;
  _muCategory: any;
  equipmentId: any;
  buttonFlag: boolean = false;
  private sub1: Subscription | undefined;
  private sub2: Subscription | undefined;
  private sub3: Subscription | undefined;

  @Input()
  get temp(): any { return this.template }
  set temp(temp: any) {
    this.template = temp;
    if (this.template && this.template.template) {
      this.message.remove();
      this.isRootTemp = this.template.parentId < 1000000 ? true : false;
      (window as any).isRootTemplate = this.isRootTemp
      if (this._tabIndex === 1) {
        this.getColumns();
        this.getList();
        this.jspreadsheet?.jexcelTable?.updateFreezePosition()
      }
    }
  }
  // Tabs下标，解决jexcel固定列bug
  @Input()
  public set tabIndex(index: number | undefined) {
    this._tabIndex = index;
    if (index === 1) {
      this.getColumns();
      this.getList();
      this.jspreadsheet?.jexcelTable?.updateFreezePosition()
    }
  }

  // muCategory，解决跨站信号配置
  @Input()
  public set muCategory(muCategory: number | undefined) {
    this._muCategory = muCategory;
  }

  tabelSearcherTimer: any;
  _tabelSearchText: any;
  @Input()
  get tabelSearchText(): any { return this._tabelSearchText }
  set tabelSearchText(text: any) {
    this._tabelSearchText = text;
    if (this.tabelSearcherTimer) {
      clearTimeout(this.tabelSearcherTimer);
    }
    this.tabelSearcherTimer = setTimeout(() => {
      this.tabelSourceFilter();
    }, 500);
  }

  @Output() refresh: EventEmitter<void> = new EventEmitter<void>();
  @Output() selectTab = new EventEmitter<any>();
  langMap: any;
  langKey = [
    'server.type',
    'server.name',
    'server.number',
    'server.IP',
    'server.status'
  ];
  tableData: any = [];
  filterSource: any;
  columnArr: any;
  signalCategoryArr = [];
  signalTypeArr = [];
  channelTypeArr = [];
  dataTypeArr = [];
  signalPropertyArr = [];
  signalPropertyList = [];
  rowData: any;
  searchText: any = {}
  searchTimer: any;
  storeIntervalArr = ['86400', '28800', '14400', '3600', '1800', '600', '300', '43200', '21600', '17280', '10800', '9600', '8640', '7200', '5760', '5400', '4800', '4320', '3456',
    '3200', '2880', '2700', '2400', '2160', '1920', '1728', '1600', '1440', '1350', '1200', '1152', '1080', '960', '900', '864', '800', '720', '675', '640', '576', '540', '480', '450',
    '432', '400', '384', '360', '320', '288', '270', '240', '225', '216', '200', '192', '180', '160', '150', '144', '135', '128', '120', '108', '100', '96', '90', '80', '75', '72', '64', '60',
    '54', '50', '48', '45', '40', '36', '32', '30', '27', '25', '24', '20', '18', '16', '15', '12', '10', '9', '8', '6', '5', '4', '3', '2', '1', '0']
  selectedRowIndex = [];
  batteryData: any;
  columnList = [
    { name: 'signalName', title: '名称', width: '150px', readOnly: false },
    { name: 'signalId', title: '信号ID', width: '100px', readOnly: true },
    { name: 'displayIndex', title: '显示顺序', width: '70px', type: 'numeric', mask: '0', readOnly: false },
    { name: 'signalCategory', title: '种类', width: '120px', type: 'dropdown', readOnly: false },
    { name: 'signalType', title: '分类', width: '120px', type: 'dropdown', readOnly: false },
    { name: 'channelNo', title: '通道号', width: '90px', readOnly: false },
    { name: 'channelType', title: '通道类型', width: '120px', type: 'dropdown', readOnly: false },
    { name: 'expression', title: '表达式', width: '170px', readOnly: true, onClick: (obj: any) => { this.openExpression(obj) } },
    { name: 'dataType', title: '数据类型', width: '180px', type: 'dropdown', readOnly: false },
    { name: 'showPrecision', title: '精度', width: '100px', readOnly: false },
    { name: 'unit', title: '单位', width: '100px', readOnly: false },
    { name: 'storeInterval', title: '存储周期(秒)', width: '120px', type: 'dropdown', source: this.storeIntervalArr, autocomplete: true, readOnly: false },
    { name: 'absValueThreshold', title: '绝对值阈值', width: '100px', readOnly: false },
    { name: 'percentThreshold', title: '百分比阈值', width: '100px', readOnly: false },
    { name: 'staticsPeriod', title: '统计周期(小时)', width: '130px', readOnly: false },
    { name: 'enable', title: '有效', type: 'checkbox', width: '80px', readOnly: false },
    { name: 'visible', title: '可见', type: 'checkbox', width: '80px', readOnly: false },
    { name: 'chargeStoreInterVal', title: '后备状态电池存储周期(秒)', width: '180px', type: 'numeric', mask: '0', readOnly: false },
    { name: 'chargeAbsValue', title: '后备状态电池绝对值阀值', width: '170px', type: 'numeric', mask: '0', readOnly: false },
    { name: 'description', title: '说明', width: '80px', readOnly: false },
    { name: 'signalProperty', title: '信号属性', width: '100px', multiple: true, autocomplete: true, type: 'dropdown', readOnly: false },
    { name: 'stateValue', title: '状态信号', width: '200px', readOnly: true, onClick: (obj: any) => { this.openModal(obj) } },
    { name: 'moduleNo', title: '所属模块', width: '100px', readOnly: false },
    { name: 'baseTypeName', title: '基类信号', width: '180px', readOnly: false, onClick: (obj: any) => { this.createComponentModal(obj) } },
    { type: jExcelColumnType.Hidden, name: 'acrossSignal', width: 0 },
    { type: jExcelColumnType.Hidden, name: 'hasInstance', width: 0 },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden },
    { type: jExcelColumnType.Hidden }
  ];
  displayTable = true;
  // 模拟鼠标双击事件
  public clickTimes: Array<Date> = []
  isConfirming = false;
  // 修改的行，可多行
  public modifyRows: Array<any> = []
  // 辅助计时器
  public modifyDelayTimer: NodeJS.Timeout | undefined
  updating: any;

  //复制名称
  maxIndex = 1;
  copyName = '';

  constructor(injector: Injector,
    private message: NzMessageService,
    private service: DeviceTemplateService,
    private d_service: DeviceManagementService,
    private dataDictionaryservice: DataDictionaryService,
    private nzContextMenuService: NzContextMenuService,
    private modal: NzModalService,
    protected router: Router,
  ) {
    super(injector);
    this.sub1 = this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        if (event.url === '/pages/device-template') {
          this.jspreadsheet?.jexcelTable.updateFreezePosition()
        }
      }
    })
    this.sub2 = this.activatedRoute.url.subscribe((url) => {
      this.buttonFlag = url[0]['path'].includes('device-management');
    })
  }

  onInit() {
    this.initTranslateMessage();
    this.getInfo();
  }

  public onDestroy(): void {
    clearTimeout(this.modifyDelayTimer);

    this.displayTable = false;
    if (this.sub1)
      this.sub1.unsubscribe();
    if (this.sub2)
      this.sub2.unsubscribe();
    if (this.sub3)
      this.sub3.unsubscribe();
  }

  tabelSourceFilter() {
    if (!this._tabelSearchText || this._tabelSearchText.length === 0) {
      this.filterSource = cloneDeep(this.tableData);
      this.jspreadsheet.jexcelTable?.resetFilters();
      return;
    }
    let filterList = cloneDeep(this.tableData);
    filterList = filterList.filter((item: any) => {
      if ((item.signalName && item.signalName.includes(this._tabelSearchText))
        || (item.signalId && item.signalId.toString().includes(this._tabelSearchText))
        || (item.description && item.description.includes(this._tabelSearchText))
      ) {
        return true;
      } else {
        return false;
      }
    })
    this.filterSource = filterList;
    this.jspreadsheet.jexcelTable.resetFilters();
  }

  initTranslateMessage() {
    this.translate.get(this.langKey).subscribe(res => {
      this.langMap = res;
    });
  }

  getInfo() {
    this.service.getBatteryDeviceCategory().then(res => {
      if (res && res.data) {
        this.batteryData = res.data;
      }
    })
    if (!this.template) {
      this.sub3 = this.activatedRoute.params.subscribe((params) => {
        if (params && params['id']) {
          this.isRootTemp = true;
          this.equipmentId = params['id'];
          this.d_service.getTemplateByEquipmentId(params['id']).subscribe(res => {
            if (res && res.data.equipmentTemplateId) {
              this.template = { id: res.data.equipmentTemplateId, equipmentCategory: res.data.equipmentCategory };
            }
          })
        }
      })
    }

    //获取数据字典
    const mapping: { [key: string]: number } = {
      signalCategory: 17, //信号种类 signalCategoryArr
      signalType: 18, //信号分类 signalTypeArr
      channelType: 22, //通道类型  channelTypeArr
      dataType: 70, //数据类型  dataTypeArr
      signalProperty: 21 //信号属性  signalPropertyArr
    };

    Promise.all(Object.values(mapping).map(entryId => this.dataDictionaryservice.getDataItems(entryId))).then(results => {
      Object.keys(mapping).forEach((key, index) => {
        const res = results[index];
        if (res.length) {
          if (key === 'signalProperty') {
            this.signalPropertyList = res;
          }
          //转成数组 下拉框
          (this as any)[key + 'Arr'] = res.map((item: any) => ({
            name: item['itemValue'], id: item['itemId'].toString()
          }));
          this.columnList.map((col: any) => {
            if (col.name === key) {
              col['source'] = (this as any)[key + 'Arr'];
            }
          })
        }
      });
      this.getColumns();
      this.getList();
    });
  }

  getClickTimes(): boolean {
    // 模拟鼠标双击事件
    if (this.clickTimes.length === 0) {
      this.clickTimes.push(new Date())
      return false
    }
    if (this.clickTimes.length === 1 && new Date().valueOf() - this.clickTimes[0].valueOf() > 250) {
      this.clickTimes[0] = new Date()
      return false
    }
    return true
  }

  getColumns() {
    const num = this.columnArr && this.columnArr.length;
    let arr = []
    if (this.batteryData && (this.template.equipmentCategory in this.batteryData)) {
      let ind1 = this.columnList.findIndex(obj => obj.name === 'chargeStoreInterVal');
      let ind2 = this.columnList.findIndex(obj => obj.name === 'chargeAbsValue')
      this.columnList[ind1]['readOnly'] = false;
      this.columnList[ind2]['readOnly'] = false;
      arr = this.columnList;
    } else {
      let ind1 = this.columnList.findIndex(obj => obj.name === 'chargeStoreInterVal');
      let ind2 = this.columnList.findIndex(obj => obj.name === 'chargeAbsValue')
      this.columnList[ind1]['readOnly'] = true;
      this.columnList[ind2]['readOnly'] = true;
      arr = this.columnList;
    }
    let cols = cloneDeep(arr)
    if (this.isRootTemp) {
      cols.forEach((item: any) => {
        item['readOnly'] = true;
      })
    }
    this.columnArr = cols;
    if (num !== arr.length) {
      this.displayTable = false
      setTimeout(() => {
        this.displayTable = true;
      }, 0)
    }
  }

  getList() {
    if (this._muCategory == 24) {
      this.service.getSignalListByTempIdEqId(this.template.id, this.equipmentId).then((res: any) => {
        if (res && res.length) {
          this.getTable(res);
        } else {
          this.filterSource = [];
        }
      })
    } else {
      this.service.getSignalList(this.template.id).then((res: any) => {
        if (res && res.length) {
          this.getTable(res);
        } else {
          this.filterSource = [];
        }
      })
    }
  }

  getTable(data: any) {
    data.map((item: any) => {
      if (item.signalMeaningsList && item.signalMeaningsList.length) {
        item['stateValue'] = item.signalMeaningsList.map((data: any) => data.stateValue).join('/');
      }
      if (item.signalPropertyList) {
        item['signalProperty'] = item.signalPropertyList.length ? item.signalPropertyList.map((data: any) => data.signalPropertyId).join(';') : '';
      }
    })
    this.tableData = data;
    this.tabelSourceFilter();
    const scrollContainer = document.querySelector('.jexcel_content')
    if (!scrollContainer) return
    scrollContainer.scrollLeft = scrollContainer.scrollLeft - 1

    // 调用接口获取已设置实例的信号
    this.checkSignalInstances();
  }

  // 检查信号实例设置情况
  checkSignalInstances(): void {
    if (!this.tableData || this.tableData.length === 0 || !this.equipmentId) return;
    this.d_service.getAllSignalInstanceByEqId(this.equipmentId).subscribe(res => {
      if (res && res.data && res.data.length > 0) {
        // 为匹配的signalId添加标记
        this.tableData.forEach((item: any) => {
          const found = res.data.find((signal: any) => signal.signalId === item.signalId);
          if (found) {
            item.hasInstance = true; // 添加已设置实例标记
          } else {
            item.hasInstance = false;
          }
        });
        // 更新表格显示
        this.tabelSourceFilter();
      }
    })
  }

  // 表达式
  openExpression(obj: any) {
    if (!this.getClickTimes()) return
    this.rowData = JSON.parse(JSON.stringify(this.tableData)).find((item: any) =>
      item.signalId === obj.signalId
    );
    if (!this.rowData) return
    const model = this.openDialog({
      nzTitle: '',
      nzContent: ExpressionConfigurationComponent,
      nzWidth: 600,
      nzData: {
        templateId: this.rowData['equipmentTemplateId'], expression: this.rowData['expression'], type: 0, disabled: this.isRootTemp
      },
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: (instance: ExpressionConfigurationComponent): void => {
            instance.close()
          }
        },
        {
          label: '确定',
          type: 'primary',
          disabled: componentInstance => (!componentInstance!.correct || this.isRootTemp),
          onClick: (instance: ExpressionConfigurationComponent): void => {
            instance.confirm()
          }
        }
      ]
    })
    model.afterClose.subscribe(res => {
      if (res && res.action === 'update') {
        this.rowData['expression'] = res.expression;
        this.submit([this.rowData], 'expression')
      }
    })
  }

  // 基类信号
  createComponentModal(data: any): void {
    if (!this.getClickTimes() || this.isRootTemp) return
    setTimeout(() => {
      if (this.isConfirming) return
      this.rowData = JSON.parse(JSON.stringify(this.tableData)).find((item: any) =>
        item.signalId === data.signalId
      );
      if (!this.rowData) return
      const model = this.openDialog<BaseClassSelectorComponent, BaseClassSelectorParameter, BaseClassSelectorOut>({
        nzTitle: '基类设置',
        nzContent: BaseClassSelectorComponent,
        nzWidth: 750,
        nzData: {
          type: 0,
          rowItem: this.rowData,
          baseTypeId: this.rowData.baseTypeId,
          name: this.rowData.signalName, //'设备通讯状态',
          eventCondition: '0',
          equipmentTemplateId: this.rowData.equipmentTemplateId,
        },
        nzFooter: [
          {
            label: '取消关联基类',
            danger: true,
            ghost: true,
            onClick: (instance: BaseClassSelectorComponent): void => {
              instance.delete()
            }
          },
          {
            label: '取消',
            onClick: (instance: BaseClassSelectorComponent): void => {
              instance.close()
            }
          },
          {
            label: '确定',
            type: 'primary',
            onClick: (instance: BaseClassSelectorComponent): void => {
              instance.confirm()
            }
          }
        ]
      })
      model.afterClose.subscribe(res => {
        if (res) {
          this.rowData.baseTypeId = res.baseTypeId;
          if (res.action === 'confirm') this.rowData.baseTypeName = res.baseTypeName;
          this.submit([this.rowData], 'baseTypeName');
        }
      })
    }, 250)
  }

  // 状态信号
  openModal(obj: any) {
    if (!this.getClickTimes()) return;
    if (obj.signalCategory !== 2) {
      this.message.warning('非开关信号不可编辑!')
      return;
    }
    setTimeout(() => {
      this.rowData = JSON.parse(JSON.stringify(this.tableData)).find((item: any) =>
        item.signalId === obj.signalId
      );
      if (!this.rowData) return;
      const model = this.openDialog({
        nzTitle: '信号含义设置',
        nzContent: SignalMeaningComponent,
        nzWidth: 750,
        nzData: {
          data: this.rowData,
          disabled: this.isRootTemp,
        },
        nzFooter: [
          {
            label: '取消',
            onClick: (instance: SignalMeaningComponent): void => {
              instance.close()
            }
          },
          {
            label: '确定',
            type: 'primary',
            disabled: this.isRootTemp,
            onClick: (instance: SignalMeaningComponent): void => {
              instance.confirm()
            }
          }
        ]
      })
      model.afterClose.subscribe(res => {
        if (res?.action === 'confirm') {
          this.submit([this.rowData], 'stateValue')
        }
      })
    }, 250)
  }

  submit(data: any, field?: any) {
    if (this.service.getNotShowState()) {
      this.updateSignal(data, null, field === 'baseTypeName');
    } else {
      this.isConfirming = true;
      const modal = this.modal.create<DeviceTemplateConfirmComponent>({
        nzTitle: "模板修改确认",
        nzContent: DeviceTemplateConfirmComponent,
        nzWidth: 900,
        nzData: { template: this.template },
        nzFooter: [
          {
            label: '更新原模板',
            type: 'primary',
            onClick: componentInstance => componentInstance!.confirm()
          },
          {
            label: '另存为新模板',
            type: 'primary',
            onClick: async (componentInstance: DeviceTemplateConfirmComponent): Promise<void> => {
              const result = await componentInstance.copyTemplate();
              if (!result.newTemplateId) return;
              this.updateSignal(data, result.newTemplateId);
              modal.close()
            }
          },
          {
            label: '取消',
            type: 'default',
            onClick: async (componentInstance: DeviceTemplateConfirmComponent): Promise<void> => {
              componentInstance!.close({ action: DeviceTemplateConfirmActionOptions.cancel })
            }
          },
        ]
      });
      modal.afterClose.subscribe((res: any) => {
        this.isConfirming = false;
        if (res && res.data === 'update') {
          this.updateSignal(data, null, field === 'baseTypeName');
          modal.close()
        } else if (res.action === DeviceTemplateConfirmActionOptions.cancel) {
          this.getList()
        } else {
          this.message.success('更新成功！')
          this.getList()
        }
      })
    }
  }

  updateSignal(data: any, newTemp?: any, copy?: any): Promise<void> {
    return new Promise((resolve, reject) => {
      const updatePromises = data.map((item: any) => {
        const idx = this.filterSource.findIndex((row: any) => {
          return item['signalId'] === row['signalId']
        })
        const tableRow = this.filterSource[idx];
        for (let key in tableRow) {
          if (!item.hasOwnProperty(key)) {
            item[key] = tableRow[key]
          } else if (key === 'signalProperty' && item[key] !== tableRow[key]) {
            let ids;
            if (item[key].length === 0) {
              ids = [];
            } else {
              if (item[key].slice(-1) == ';') {
                ids = isArray(item[key]) ? item[key] : item[key].slice(0, item[key].length - 1).split(";").map(Number);
              } else {
                ids = isArray(item[key]) ? item[key] : item[key].slice(0, item[key].length).split(";").map(Number);
              }
            }
            let list: any[] = [];
            ids.map((id: any) => {
              list.push({
                equipmentTemplateId: tableRow.equipmentTemplateId,
                id: tableRow.id,
                signalId: tableRow.signalId,
                signalPropertyId: id
              })
            })
            item.signalPropertyList = list;
          } else if (key === 'baseTypeName' && !item[key]) {
            item.baseTypeId = null;
          } else if (key === 'baseTypeName' && item[key] && !copy) {
            let sourceObj = this.filterSource.filter((obj: any) => {
              return (item['baseTypeName'] === obj['baseTypeName']) && (item['baseTypeId'] !== obj['baseTypeId']);
            })
            if (sourceObj[0]) {
              item['baseTypeId'] = sourceObj[0]['baseTypeId'];
            } else {
              let sourceObj2 = this.filterSource.filter((obj: any) => {
                return (item['baseTypeName'] === obj['baseTypeName']) && (item['baseTypeId'] === obj['baseTypeId']);
              })
              if (sourceObj2[0]) {
                item['baseTypeId'] = sourceObj2[0]['baseTypeId'];
              } else {
                item.baseTypeId = null
              }
            }
          }
        }
        if (newTemp) {
          item['equipmentTemplateId'] = newTemp;
          if (item['signalMeaningsList'] && item['signalMeaningsList'].length) {
            item['signalMeaningsList'].forEach((data: any) => {
              data.equipmentTemplateId = newTemp;
            })
          }
          if (item['signalPropertyList'] && item['signalPropertyList'].length) {
            item['signalPropertyList'].forEach((data: any) => {
              data.equipmentTemplateId = newTemp;
              delete data.id
            })
          }
        }
        if (!!item['channelNo'] || item['channelNo'] == 0) {
          return this.service.updateSignal(item).then(result => {
            if (result && !newTemp) {
              this.tableData[idx] = item;
              this.getTable(cloneDeep(this.tableData));
            } else if (!newTemp) {
              this.tableData[idx] = this.tableData[idx];
              this.tableData = cloneDeep(this.tableData);
            }
          }).catch(error => {
            this.tableData[idx] = this.tableData[idx];
            this.tableData = cloneDeep(this.tableData);
            return Promise.reject(error);
          })
        } else {
          this.tableData[idx] = this.tableData[idx];
          this.tableData = cloneDeep(this.tableData);
          return Promise.resolve();
        }
      });
      Promise.all(updatePromises).then(() => {
        this.message.success('更新成功！')
        this.getList();
        this.refresh.emit()
      }).catch(reject);
    });
  }

  // 创建行右键菜单
  public rowContextmenu($event: MouseEvent, menu: NzDropdownMenuComponent): void {
    if (this.isRootTemp) return
    this.nzContextMenuService.create($event, menu)
  }

  getSheetSelect() {
    setTimeout(() => {
      this.selectedRowIndex = this.jspreadsheet.jexcelTable.getSelectedRows(true);
    }, 0)
  }

  rowChangeConfirm(type: string) {
    if (this.service.getNotShowState()) {
      if (type === 'add') {
        this.addNewSignal();
      } else if (type === 'addTemp') {
        this.addTemplateSignal();
      } else if (type === 'delete') {
        this.deleteSignal();
      } else if (type === 'copy') {
        this.copySignal();
      } else if (type === 'addRelateEvent') {
        this.addRelateEvent();
      }
    } else {
      this.isConfirming = true;
      const modal = this.modal.create<DeviceTemplateConfirmComponent>({
        nzTitle: "模板修改确认",
        nzContent: DeviceTemplateConfirmComponent,
        nzWidth: 900,
        nzFooter: [
          {
            label: '更新原模板',
            type: 'primary',
            onClick: componentInstance => componentInstance!.confirm()
          },
          {
            label: '另存为新模板',
            type: 'primary',
            onClick: componentInstance => componentInstance!.saveAs('signal')
          },
          {
            label: '取消',
            type: 'default',
            onClick: componentInstance => componentInstance!.close()
          },
        ],
      });
      const instance = modal.getContentComponent();
      instance.params = this.template.id;
      instance.template = this.template;
      modal.afterClose.subscribe((res: any) => {
        this.isConfirming = false;
        if (res && res.data === 'update') {
          if (type === 'add') {
            this.addNewSignal();
          } else if (type === 'addTemp') {
            this.addTemplateSignal();
          } else if (type === 'delete') {
            this.deleteSignal();
          } else if (type === 'copy') {
            this.copySignal();
          } else if (type === 'addRelateEvent') {
            this.addRelateEvent();
          }
        } else if (res && res.newTemplateId) {
          let newTemp = res.newTemplateId;
          if (type === 'add') {
            this.addNewSignal(newTemp);
          } else if (type === 'addTemp') {
            this.addTemplateSignal(newTemp);
          } else if (type === 'delete') {
            this.deleteSignal(newTemp);
          } else if (type === 'copy') {
            this.copySignal(newTemp);
          } else if (type === 'addRelateEvent') {
            this.addRelateEvent(newTemp);
          }
        }
      })
    }
  }

  async copySignal(newTemp?: any) {
    const arr = cloneDeep(this.filterSource)
    let newSignalArr = this.selectedRowIndex.map(i => arr[i]);
    if (newSignalArr.length) {
      this.updating = this.message.loading('正在进行更新，请勿进行其他操作...', { nzDuration: 0 }).messageId;
      for (let i = 0; i < newSignalArr.length; i++) {
        if (newTemp) {
          newSignalArr[i].equipmentTemplateId = newTemp;
        }
        this.maxIndex = 1;
        this.copyName = cloneDeep(newSignalArr[i].signalName);
        this.createCopyName();
        newSignalArr[i].signalName = this.copyName;
        newSignalArr[i].displayIndex = Math.max.apply(Math, this.tableData.map((item: any) => { return item.displayIndex })) + (i + 1),
          newSignalArr[i].channelNo = Math.max.apply(Math, this.tableData.map((item: any) => { return item.channelNo })) + (i + 1),
          await this.service.addSignal(newSignalArr[i]).then(res => {
            if (res) {
              if (i === newSignalArr.length - 1) {
                this.message.remove(this.updating);
                this.message.success('复制成功！')
                if (newTemp) {
                  this.refresh.emit();
                } else {
                  this.tableData = cloneDeep(this.tableData);
                  this.getList();
                }
              }
            }
          })
      }
    }
  }

  createCopyName() {
    if (this.tableData.findIndex((obj: any) => obj.signalName === (this.copyName + this.maxIndex + '#')) >= 0) {
      this.maxIndex++;
      this.createCopyName();
    } else {
      this.copyName = this.copyName + this.maxIndex + '#';
    }
  }

  addNewSignal(newTemp?: any) {
    let maxId = Math.max.apply(Math, this.tableData.map((item: any) => { return item.signalId }));
    if (maxId <= 0) {
      maxId = 0;
    }
    maxId++;
    let newSignal = {
      id: null,
      equipmentTemplateId: newTemp ? newTemp : this.template.id,
      signalId: null,
      enable: true,
      visible: true,
      signalName: "新增信号" + maxId.toString(),
      signalCategory: 0,
      signalType: 0,
      channelNo: 0,
      channelType: 0,
      displayIndex: Math.max.apply(Math, this.tableData.map((item: any) => { return item.displayIndex })) + 1,
      moduleNo: 0
    }
    this.service.addSignal(newSignal).then(res => {
      this.message.success('更新成功！')
      if (newTemp) {
        this.refresh.emit();
      } else {
        this.getList();
      }
    })
  }

  addTemplateSignal(newTemp?: any) {
    const modal = this.modal.create<DeviceTemplateSecSelectorComponent>({
      nzTitle: "从模板增加信号",
      nzContent: DeviceTemplateSecSelectorComponent,
      nzWidth: 900,
      nzFooter: [
        {
          label: '确认',
          type: 'primary',
          onClick: componentInstance => componentInstance!.confirm()
        },
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.close()
        },
      ],
      nzData: {
        originEquipmentTemplateId: this.template.id,
        equipmentCategory: this.template.equipmentCategory,
        originSecList: this.tableData,
        type: 0
      },
    });
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        let newSignal = res.rows[0];
        newSignal.equipmentTemplateId = newTemp ? newTemp : this.template.id;
        this.service.addSignal(newSignal).then(res => {
          this.message.success('更新成功！')
          if (newTemp) {
            this.refresh.emit();
          } else {
            this.getList();
          }
        })
      } else {
        if (newTemp) {
          this.service.deleteTemplate(newTemp);
        }
      }
    })
  }

  deleteSignal(newTemp?: any) {
    this.updating = this.message.loading('正在进行更新，请勿进行其他操作...', { nzDuration: 0 }).messageId;
    const ids = this.selectedRowIndex.map(i => this.filterSource[i].signalId).join(',');
    this.service.deleteSignal(newTemp ? newTemp : this.template.id, ids).then(res => {
      this.message.remove(this.updating);
      this.message.success('删除成功！')
      if (newTemp) {
        this.refresh.emit();
      } else {
        this.getList();
      }
    })
  }

  addRelateEvent(newTemp?: any) {
    this.updating = this.message.loading('正在进行更新，请勿进行其他操作...', { nzDuration: 0 }).messageId;
    const id = this.selectedRowIndex.map(i => this.filterSource[i].signalId)[0];
    this.service.addRelateEvent(newTemp ? newTemp : this.template.id, id).then(res => {
      this.message.remove(this.updating);
      this.message.success('增加关联事件成功！')
      if (newTemp) {
        this.refresh.emit();
      } else {
        this.selectTab.emit({ 'index': 2 });
      }
    })
  }

  onRowDataChange(data: any) {
    this.modifyRows.push(data)
    //判断精度变更是否合规
    let index: any = this.tableData.findIndex((item: any) => {
      return item['eventId'] === data['eventId']
    })
    if (data['showPrecision'] !== this.tableData[index]['showPrecision']) {
      const isValid = /^0(\.0*)?$/.test(data['showPrecision']);
      if (!isValid) {
        clearTimeout(this.modifyDelayTimer)
        this.modifyDelayTimer = setTimeout(() => {
          this.message.warning('只允许输入以 0 开头，且小数部分只能是 0 的数字，如 0, 0.0, 0.00')
          this.getList();
        }, 50)
      } else {
        clearTimeout(this.modifyDelayTimer)
        this.modifyDelayTimer = setTimeout(() => {
          this.submit(this.modifyRows, null);
          this.modifyRows = [];
        }, 50)
      }
    } else {
      clearTimeout(this.modifyDelayTimer)
      this.modifyDelayTimer = setTimeout(() => {
        this.submit(this.modifyRows, null);
        this.modifyRows = [];
      }, 50)
    }
  }

  addSignalInstance() {
    if (this.selectedRowIndex.length === 0) {
      this.message.warning('请先选择要实例化的信号！');
      return;
    }
    const modal = this.modal.create<DeviceSignalInstanceComponent>({
      nzTitle: "设备信号实例",
      nzContent: DeviceSignalInstanceComponent,
      nzWidth: 650,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        },
        {
          label: '确定',
          type: 'primary',
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzMaskClosable: false
    });
    const instance = modal.getContentComponent();
    instance.temp = this.template.id;
    instance.params = {
      equipmentId: this.equipmentId,
      signalId: this.filterSource[this.selectedRowIndex[0]]['signalId'],
    };
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        this.checkSignalInstances();
      }
    })
  }

  addCrossSiteInstance() {
    if (this.selectedRowIndex.length === 0) {
      this.message.warning('请先选择要跨站的信号！');
      return;
    }
    if (this.filterSource[this.selectedRowIndex[0]]['channelNo'] !== -2) {
      this.message.warning('只有通道号为-2的信号可以设置跨站信号');
      return;
    }
    if (this.filterSource[this.selectedRowIndex[0]]['expression'].length > 0) {
      this.message.warning('已配置了表达式，确实需要配置请先清除！');
      return;
    }
    const modal = this.modal.create<CrossSiteSignalComponent>({
      nzTitle: "设置跨站信号",
      nzContent: CrossSiteSignalComponent,
      nzWidth: 650,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        },
        {
          label: '确定',
          type: 'primary',
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzMaskClosable: false
    });
    const instance = modal.getContentComponent();
    instance.temp = this.template.id;
    instance.params = {
      equipmentId: this.equipmentId,
      signalId: this.filterSource[this.selectedRowIndex[0]]['signalId'],
      equipmentTemplateId: this.filterSource[this.selectedRowIndex[0]]['equipmentTemplateId'],
    };
    modal.afterClose.subscribe((res: any) => {
      if (res && res == 'success') {
        this.getList();
      }
    })
  }

  viewSignalInstance() {
    const modal = this.modal.create<DeviceSignalInstanceListComponent>({
      nzTitle: "设备信号实例",
      nzContent: DeviceSignalInstanceListComponent,
      nzWidth: 900,
      nzFooter: [
        {
          label: '关闭',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        },
        {
          label: '删除',
          type: 'primary',
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzData: {
        eqId: this.equipmentId,
      },
      nzMaskClosable: false
    });
    modal.afterClose.subscribe((res: any) => {
      if (res) {
        this.checkSignalInstances();
      }
    })
  }

  copyFieldTo(type: string) {
    const modal = this.modal.create<FieldCopyToSelector>({
      nzTitle: "选择信号",
      nzContent: FieldCopyToSelector,
      nzWidth: 700,
      nzFooter: [
        {
          label: '取消',
          type: 'default',
          onClick: componentInstance => componentInstance!.cancel()
        },
        {
          label: '确认',
          type: 'primary',
          onClick: componentInstance => componentInstance!.confirm()
        },
      ],
      nzMaskClosable: false
    });
    const instance = modal.getContentComponent();
    instance.templateId = this.template.id;
    instance.type = 'signal';
    modal.afterClose.subscribe((res: any) => {
      //获取复制对象
      let copyItem = this.tableData[this.selectedRowIndex[0]];
      if (res && res.data && res.data.length > 0) {
        let list: any[] = [];
        res.data.forEach((item: any) => {
          let ind = this.tableData.find((obj: any) => {
            return obj.signalId === item;
          })
          list.push(ind)
        })
        if (type === 'copyExpression') {
          let params: any[] = [];
          list.forEach((obj: any) => {
            obj['expression'] = copyItem['expression'];
            params.push({
              equipmentTemplateId: this.template.id,
              signalId: obj['signalId'],
              fieldName: 'expression',
              fieldValue: obj['expression'],
            })
          })
          this.service.batchSignalFieldCopy(params).then(result => {
            this.message.success('更新成功！')
            this.getList();
          })
        } else if (type === 'copySignalMeaning') {
          let params: any[] = [];
          if (copyItem['signalCategory'] === 2) {
            list.forEach((obj: any) => {
              if (obj['signalCategory'] === 2) {
                params.push({
                  equipmentTemplateId: this.template.id,
                  signalId: copyItem['signalId'],
                  fieldName: 'signalMeaningsList',
                  fieldValue: this.template.id.toString() + '.' + obj['signalId'].toString(),
                })
              }
            })
            this.service.batchSignalFieldCopy(params).then(result => {
              this.message.success('更新成功！')
              this.getList();
            })
          } else {
            this.message.warning('该信号没有状态信号！');
          }
        }
      }

    })
  }

  // 单元格渲染前勾子
  public updateTable(instance: any, cell: any, col: any, row: any, val: any, id: any, uu: any): void {
    const battery = this.batteryData && (this.template.equipmentCategory in this.batteryData);
    if (col === 7 || col === 18 || col === 19 || (battery && col === 20) || col === 21) {
      if (val) {
        if ((window as any).isRootTemplate) {
          cell.style.color = 'rgba(0, 0, 0, 0.3)'
        } else {
          cell.style.color = '#333'
        }
      }
    }
    if (instance.jexcel.getRowData(row)[24] === true)//跨站信号配置过后变色
      cell.style.color = '#0000FF'

    // 当hasInstance为true时，设置背景色为绿色
    if (instance.jexcel.getRowData(row)[25] === true) {
      cell.style.backgroundColor = '#B0F47D';
    }
  }
}
