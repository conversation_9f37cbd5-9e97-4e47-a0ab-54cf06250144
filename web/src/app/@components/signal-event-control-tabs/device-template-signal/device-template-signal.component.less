@import '../../../../styles/mixin.less';

.themeMixin({
    :host ::ng-deep {
        
        .table {
            height: 100%;
            width: 100%;
        }
    
        .dataTypeSelect {
            min-width: 140px;
            max-width: 240px;
        }
    
        .storeIntervalSelect {
            width: 80px;
        }
    
        .idx {
            height: 42px;
        }
        .dms_top{
            height: 40px;
        }

        .dms_right_button {
            margin: 0 2px 8px 2px;
        }
    }
});

.right-menu-icon {
    font-size: 16px;

    ::ng-deep svg {
        margin-right: 8px;
    }
}
