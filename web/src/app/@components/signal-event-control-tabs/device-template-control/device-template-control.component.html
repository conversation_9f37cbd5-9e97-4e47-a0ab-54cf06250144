<app-jexcel-table *ngIf="displayTableView" #UITableView [columns]="displayCols" [dataSource]="filterSource" [filters]="true"
  [freezeColumnsCount]="1" (rowChange)="onRowDataChange($event)" (contextmenu)="contextMenu($event, menu)"
  (mouseup)="onMouseupFetchRow()" (mousedown)="onMousedown()" [updateTable]="updateTable" [isRootTemp]="isRootTemplate()"></app-jexcel-table>
<nz-dropdown-menu #menu="nzDropdownMenu">
  <ul nz-menu *ngIf="!isRootTemplate()">
    <li nz-menu-item (click)="onRightClickMenu('增加控制')"><iconfont icon="icon-add" class="right-menu-icon"></iconfont>增加控制</li>
    <li nz-menu-item (click)="onRightClickMenu('从模板增加控制')"><iconfont icon="icon-addpage" class="right-menu-icon"></iconfont>从模板增加控制</li>
    <li *ngIf="mouseupFetchRowIndexs.length" nz-menu-item (click)="onRightClickMenu('删除控制')"><iconfont icon="icon-delete2" class="right-menu-icon"></iconfont>删除控制</li>
    <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
    <li *ngIf="mouseupFetchRowIndexs.length"  nz-menu-item (click)="onRightClickMenu('行拷贝')"><iconfont icon="icon-copyrow" class="right-menu-icon"></iconfont>行拷贝并粘贴</li>
    <li nz-menu-item *ngIf="mouseupFetchRowIndexs.length === 1" (click)="copyFieldTo('copyControlMeaningsList')">
      <iconfont [icon]="'icon-copyrow'" class="right-menu-icon"></iconfont>拷贝参数含义到...
  </li>
    <!-- <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
    <li nz-menu-item (click)="onRightClickMenu('单元格复制')"><iconfont icon="icon-copy" class="right-menu-icon"></iconfont>单元格复制   Ctrl+C</li>
    <li nz-menu-item (click)="onRightClickMenu('单元格粘贴')" [nzDisabled]="copyCellRowIndexs[0]===undefined"><iconfont icon="icon-paste" class="right-menu-icon"></iconfont>单元格粘贴   Ctrl+V</li>
    <nz-divider style="margin: 0 12px; width: auto; min-width: auto;"></nz-divider>
    <li nz-menu-item (click)="onRightClickMenu('刷新')"><iconfont icon="icon-jiazai" class="right-menu-icon"></iconfont>刷新</li> -->
  </ul>
</nz-dropdown-menu>