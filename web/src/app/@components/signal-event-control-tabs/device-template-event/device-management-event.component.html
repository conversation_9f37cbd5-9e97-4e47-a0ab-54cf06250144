<div class="dme_container">
    <div class="dme_top">
        <button nz-button nzType="primary" *ngIf="isDeviceManagement && _muCategory!=24" class="dme_right_button"
            (click)="addEventInstance()">设置事件实例</button>
        <button nz-button nzType="primary" *ngIf="isDeviceManagement && _muCategory!=24" class="dme_right_button"
            (click)="viewEventInstance()">查看所有事件实例</button>
    </div>
    <div class="dme_bottom" [ngStyle]="{'height': isDeviceManagement ? 'calc(100% - 150px)': '100%'}">
        <app-jexcel-table #JEtable [columns]="evCols" [filters]="true" [dataSource]="filterSource"
            (RowDeleted)="onRowDelete($event)" tableHeight="100%" [freezeColumnsCount]="1"
            (rowChange)="onRowDataChange($event)" (contextmenu)="contextMenu($event, menu)" (mouseup)="getSheetSelect()"
            [updateTable]="updateTable" [isRootTemp]="isOriginTemp"></app-jexcel-table>
        <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul nz-menu>
                <li *ngIf="selectedRowIndex?.length" nz-menu-item (click)="rowChangeConfirm('add')">
                    <iconfont [icon]="'icon-add'" class="right-menu-icon"></iconfont>增加事件
                </li>
                <li nz-menu-item (click)="rowChangeConfirm('addTemp')">
                    <iconfont [icon]="'icon-addpage'" class="right-menu-icon"></iconfont>从模板增加事件
                </li>
                <li *ngIf="selectedRowIndex?.length" nz-menu-item (click)="rowChangeConfirm('delete')">
                    <iconfont [icon]="'icon-delete2'" class="right-menu-icon"></iconfont>删除事件
                </li>
                <li *ngIf="selectedRowIndex?.length" nz-menu-item (click)="rowChangeConfirm('copyRow')">
                    <iconfont icon="icon-copyrow" class="right-menu-icon"></iconfont>行拷贝并粘贴
                </li>
                <li nz-menu-item *ngIf="selectedRowIndex?.length === 1" (click)="copyFieldTo('copyStartExpression')">
                    <iconfont [icon]="'icon-copyrow'" class="right-menu-icon"></iconfont>拷贝开始表达式到...
                </li>
                <li nz-menu-item *ngIf="selectedRowIndex?.length === 1" (click)="copyFieldTo('copySuppressExpression')">
                    <iconfont [icon]="'icon-copyrow'" class="right-menu-icon"></iconfont>拷贝抑制表达式到...
                </li>
                <li nz-menu-item *ngIf="selectedRowIndex?.length === 1" (click)="copyFieldTo('copyEventCondition')">
                    <iconfont [icon]="'icon-copyrow'" class="right-menu-icon"></iconfont>拷贝条件到...
                </li>
            </ul>
        </nz-dropdown-menu>
    </div>
</div>