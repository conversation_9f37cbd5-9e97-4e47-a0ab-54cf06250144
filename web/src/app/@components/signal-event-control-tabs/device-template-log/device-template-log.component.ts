import { Component, ElementRef, Injector, Input, OnInit } from '@angular/core';
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component';
import { DateUtil } from '@core/util/date.util';
import { LogService } from 'app/pages/log/logservice';
import { tableResizeFunc } from 'ng-devui/data-table';

@Component({
  selector: 'app-device-template-log',
  templateUrl: './device-template-log.component.html',
  styleUrls: ['../../../@components/basic/devui-table-filter/devui-table-filter.component.less', './device-template-log.component.less']
})
export class DeviceTemplateLogComponent extends DevuiTableFilterComponent implements OnInit {
  _tabIndex: any;
  _objectType: any;
  _objectId: any;
  date = [];
  showLoading: boolean = false;
  pageIndex: number = 1;
  total: number = 0;
  pageSize: number = 20;

  @Input()
  public set tabIndex(index: number | undefined) {
    this._tabIndex = index;
    if (this._tabIndex === 4) this.getList();
  }

  @Input()
  public set objectType(index: number | undefined) {
    this._objectType = index;
  }

  @Input()
  get objectId(): any { return this._objectId }
  set objectId(id: number) {
    this._objectId = id;
    if (this._tabIndex === 4) this.getList();
  }

  public constructor(
    injector: Injector,
    ele: ElementRef,
    private service: LogService
  ) {
    super(injector, ele)
  }

  // 原数据
  public orgTableDataSource: Array<any> = []
  public tableColumnConfig = [
    // {
    //   field: '$index',
    //   width: '70px',
    //   title: '',
    // },
    {
      field: 'propertyName',
      width: '150px',
      title: '操作模块',
    },
    {
      field: 'oldValue',
      width: '200px',
      title: '操作前的值',
    },
    {
      field: 'newValue',
      width: '200px',
      title: '操作后的值',
    },
    {
      field: 'operationType',
      width: '80px',
      title: '操作类型',
    },
    {
      field: 'operationTime',
      width: '100px',
      title: '操作时间',
    },
    {
      field: 'userName',
      width: '100px',
      title: '操作人',
    }
  ]

  protected onInit(): void {
    super.onInit()
    this.activatedRoute.params.subscribe((params) => {
      if (params && params['id']) this._objectId = params['id'];
    });
  }

  // 根据操作类型请求日志
  getList() {
    this.showLoading = true;
    const data = {
      objectTypes: this._objectType,
      objectId: this._objectId,
      startTime: this.date[0] ? DateUtil.format(this.date[0], 'yyyy-MM-dd hh:mm:ss') : null,
      endTime: this.date[1] ? DateUtil.format(this.date[1], 'yyyy-MM-dd hh:mm:ss') : null
    }
    const page = {
      current: this.pageIndex,
      size: this.pageSize,
    }
    if (this._objectType === 10) {
      this.service.getEquipmentTemplateLogList(page, data).then((res: any) => {
        this.orgTableDataSource = res.records
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.showLoading = false;
        this.pageIndex = res.current;
        this.total = res.total;
        this.pageSize = res.size
      })
    } else if (this._objectType === 11) {
      this.service.getEquipmentLogList(page, data).then((res: any) => {
        this.orgTableDataSource = res.records
        this.filterTableDataSource = [...this.orgTableDataSource]
        this.displayTableDataSource = [...this.orgTableDataSource]
        this.showLoading = false;
        this.pageIndex = res.current;
        this.total = res.total;
        this.pageSize = res.size
      })
    }
  }

  // 列宽监听
  public onResize = tableResizeFunc(this.tableColumnConfig, this.ele)

}
