import { Component, ElementRef, Injector, OnInit } from '@angular/core'
import { TableColumnConfig, devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model'
import { DevuiTableFilterComponent } from '@components/basic/devui-table-filter/devui-table-filter.component'

@Component({
    selector: 'app-devui-table-filter-example-extends',
    templateUrl: './devui-table-filter-example-extends.component.html',
    styleUrls: ['../../devui-table-filter.component.less', './devui-table-filter-example-extends.component.less']
})

export class DevuiTableFilterExampExtendsComponent extends DevuiTableFilterComponent implements OnInit {

    public constructor(injector: Injector, ele: ElementRef) {
        super(injector, ele)
    }

    // 原数据
    public override orgTableDataSource: Array<any> = []
    public override tableColumnConfig: TableColumnConfig[] = [
        {
            field: '$index',
            width: '80px',
            title: '序号',
        },
        {
            field: 'name',
            width: '150px',
            title: '姓名',
            minWidth: '200',
        },
        {
            field: 'age',
            width: '180px',
            title: '年龄',
            type: devTableColumnType.Numeric,
        }
    ]

    // 方法
    protected override onInit(): void {
        super.onInit()
        this.requestTableDataSoure()
    }

    // 请求设备类型数据
    public override async requestTableDataSoure(): Promise<void> {
        for (let i = 0; i < 100; i++) {
            const people = { name: `banana${i}`, age: 17 + i }
            this.orgTableDataSource.push(people)
            this.filterTableDataSource = [...this.orgTableDataSource]
            this.displayTableDataSource = [...this.orgTableDataSource]
        }
    }
}