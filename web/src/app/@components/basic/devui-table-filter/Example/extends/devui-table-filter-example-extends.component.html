<d-data-table #myTable [dataSource]="displayTableDataSource" [scrollable]="true" [tableWidthConfig]="tableColumnConfig"
    [onlyOneColumnSort]="true" [tableHeight]="'100%'" [containFixHeaderHeight]="true" [fixHeader]="true"
    (tableScrollEvent)="tableScrollEvent($event)">
    <thead dTableHead [checkable]="true">
        <tr dTableRow>
            <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [sortable]="colConfig.field !== '$index'"
                (sortChange)="onSortChange($event, colConfig.field, colConfig)" [resizeEnabled]="true"
                (resizeEndEvent)="onResize($event, colConfig.field)" [minWidth]="colConfig.minWidth!">
                {{ colConfig.title }}
                <!-- 空白 -->
                <div *ngIf="colConfig.field === '$index'" style="height: 32px;"></div>
                <!-- 输入框 -->
                <input *ngIf="colConfig.field !== '$index'" nz-input [(ngModel)]="filterData[colConfig.field]"
                    (ngModelChange)="filterChange()" />
            </th>
        </tr>
    </thead>
    <tbody dTableBody>
        <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
            <tr dTableRow>
                <td dTableCell class="devui-checkable-cell" [fixedLeft]="'0px'">
                    <d-checkbox [ngModelOptions]="{ standalone: true }" [(ngModel)]="rowItem.$checked"
                        [halfchecked]="rowItem.$halfChecked" [disabled]="rowItem.$checkDisabled"
                        [position]="['top', 'right', 'bottom', 'left']" dTooltip>
                    </d-checkbox>
                </td>
                <td *ngFor="let colConfig of tableColumnConfig" dTableCell>
                    @if (colConfig.field === '$index') {
                    {{ rowIndex + 1 }}
                    }@else if(colConfig.field === 'name') {
                    {{ rowItem[colConfig.field] }} 自定义内容
                    }@else {
                    {{ rowItem[colConfig.field] }}
                    }
                </td>
            </tr>
        </ng-template>
    </tbody>
</d-data-table>