import { Component, Injector, OnInit } from '@angular/core'
import { GenericComponent } from '@core/components/basic/generic.component'
import { DevuiTableFilterRow, devTableColumnType } from '@components/basic/devui-table-filter/devui-table-filter.model'

@Component({
    selector: 'app-devui-table-filter-example-label',
    templateUrl: './devui-table-filter-example-label.component.html',
    styleUrls: ['./devui-table-filter-example-label.component.less']
})

export class DevuiTableFilterExampleLabelComponent extends GenericComponent implements OnInit {

    public constructor(
        injector: Injector
    ) {
        super(injector)
    }

    // 原数据
    public orgTableDataSource: Array<any> = []
    public tableColumnConfig = [
        {
            field: '$index',
            width: '80px',
            title: '序号',
        },
        {
            field: 'name',
            width: '150px',
            title: '姓名',
        },
        {
            field: 'age',
            width: '180px',
            title: '年龄',
            type: devTableColumnType.Numeric,
        }
    ]

    // 方法
    protected override onInit(): void {
        this.requestTableDataSoure()
    }

    // 请求设备类型数据
    public async requestTableDataSoure(): Promise<void> {
        for (let i = 0; i < 100; i++) {
            const people = { name: `banana${i}`, age: 17 + i }
            this.orgTableDataSource.push(people)
        }
    }
    // 点击行
    public onRowClick(row: DevuiTableFilterRow): void {
        console.log(row)
        this.orgTableDataSource.forEach(item => {
            item['$checked'] = false
        })
        this.orgTableDataSource[row.index]['$checked'] = true
    }
}