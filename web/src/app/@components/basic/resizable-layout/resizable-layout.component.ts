import { Component, Injector } from '@angular/core'
import { GenericComponent } from '@core/components/basic/generic.component'
import { NzResizeEvent } from 'ng-zorro-antd/resizable'

@Component({
  selector: 'app-resizable-layout',
  templateUrl: './resizable-layout.component.html',
  styleUrls: ['./resizable-layout.component.less']
})

export class ResizableLayoutComponent extends GenericComponent {

  // sider 宽度
  public siderWidth = 259
  // sider resize id
  public sideResizeId = -1

  // content 高度
  public contentHeight = 450
  // content resize id
  public contentResizeId = -1

  public constructor(injector: Injector) {
    super(injector)
  }

  // side 拖动
  public onSideResize({ width }: NzResizeEvent): void {
    cancelAnimationFrame(this.sideResizeId)
    this.sideResizeId = requestAnimationFrame(() => {
      this.siderWidth = width!
    })
  }

  // content 拖动
  public onContentResize({ height }: NzResizeEvent): void {
    cancelAnimationFrame(this.contentResizeId)
    this.contentResizeId = requestAnimationFrame(() => {
      this.contentHeight = height!
    })
  }
}