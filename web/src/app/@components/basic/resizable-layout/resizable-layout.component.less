:host {
  nz-sider.nz-resizable-resizing {
    transition: none;
  }

  .nz-resizable-handle.nz-resizable-handle-right.nz-resizable-handle-cursor-type-grid {
    width: 5px;
    right: -2.5px;

    .sider-resize-line {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      transition-delay: 0.1s;

      &:hover {
        background-color: #afb8c133;
      }

      &>div {
        width: 1px;
        height: 100%;
        background-color: #d0d7de;
      }
    }
  }

  nz-content {
    display: flex;
    flex-direction: column;
  }

  .nz-resizable .nz-resizable-handle-cursor-type-grid.nz-resizable-handle-bottom {
    height: 7px;
    bottom: -7px;

    .content-resize-line {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      transition-delay: 0.1s;
      border-radius: 10px;
      border: 1px solid #93ccffa6;
      background-color: #93ccff4d;

      &:hover {
        background-color: #fff !important;
      }

      &>div {
        width: 20px;
        height: 100%;
        background-color: #93ccff;
      }
    }

    .content-2 {
      overflow: auto;
      padding-top: 7px;
      background-color: #00ffff4d;
    }
  }
}