@logo-margin-left: 100px;
@left-width: 50%;
@icon-size: 30px;

.login_main{
  width: auto;
  height: auto;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  position:absolute;
  background:url(../../assets/images/bg1.png) center no-repeat;
  background-size:cover;
  display: flex;

  .login_box {
    width: 1000px;
    height: 600px;
    margin: auto;
    background: #fff;
    -webkit-box-shadow: 0 5px 15px rgb(0 0 0 / 15%);
    box-shadow: 0 5px 15px rgb(0 0 0 / 15%);
    border-radius: 8px;

    .left {
      background-color: rgba(68, 100, 255, 0.9);
      width: @left-width;
      height: 100%;
      float: left;
      border-top-left-radius: inherit;
      border-bottom-left-radius: inherit;
      color: #fff;
      padding: 0 100px;
    }

    .logo_container {
      display: block;
      width: 60px;
      height: 60px;
      // background-color: #fff;
      border-radius: 5px;
      margin-top: 150px;
    }

    .logo {
      width: 100%;
      height: 100%;
    }

    .title {
      display: block;
      margin-top: 10px;
      font-size: 24px;
    }

    .about {
      display: block;
      margin-top: 5px;
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
    }

    .right {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: calc(100% - @left-width);
      float: right;
      background-color: #fff;
      border-top-right-radius: inherit;
      border-bottom-right-radius: inherit;
    }

    .form-header {
      padding: 110px 0 50px 0;
      font-weight: 600;
      font-size: 16px;
    }

    .login-form {
      margin: 0 auto;
      width: 380px;
    }

    .login-form-margin {
      margin-bottom: 24px;
    }

    .login-form-forgot {
      float: right;
    }

    .login-form-button {
      width: 100%;
    }

    .toggle-pwd-btn {
      cursor: pointer;
    }

    .space-item {
      display: inline-block;
      height: @icon-size;
      line-height: @icon-size;
      text-align: center;
      vertical-align: middle;
    }

    .other-login {
      color: #8f959e;
      margin-left: 61px;
      font-size: 12px;
    }

    .icon-item {
      width: @icon-size;
      border-radius: @icon-size;
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }

    .wechat {
      background-color: #51c332;
    }

    .dingding {
      background-color: #0089ff;
    }

    .qq {
      background-color: #EC502B;
    }

    .mobile {
      background-color: #ffb012;
    }
  }
}
