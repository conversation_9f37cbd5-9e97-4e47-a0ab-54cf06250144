import { Component, Injector, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import * as CryptoJS from 'crypto-js';
import { LoginService } from './login.service';
import { GenericComponent } from '@core/components/basic/generic.component';
import { reject } from 'lodash';
import { PlatformLocation } from '@angular/common';
import { LoginResponse } from '@core/types/restful';
import { Guid } from '@core/util/guid';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.less']
})
export class LoginComponent extends GenericComponent {
  public validateForm!: FormGroup;
  public passwordVisible: boolean = false;

  submitForm(): void {
    if (this.validateForm.valid) {
      const data = this.getLoginData();
      if (data.password.length != 64) {
        data.password = CryptoJS.SHA256(data.password + '[' + data.account + ']').toString();
        let hashPass = '';
        for (let i = 0; i < data.password.length - 1; i = i + 2) {
          hashPass += parseInt(data.password.substring(i, i + 2), 16).toString(10);
        }
        data.password = hashPass;
      }
      this.loginService.login(data.account, data.password).then(res => {
        const uuid = Guid.new().toString();
        this.sessionService.set<string>('sessionId', uuid);
        this.sessionService.set<LoginResponse>('user', { token: res[0].token, username: res[0].UserName, userId: res[0].userId, nice: '', expire: '' });
        this.loginService.get('api/config/standardtype/judgetableexist').then((res: any) => {
          this.sessionService.set<string>('standardType', Object.keys(res.data)[0]);
          if (res.data[Object.keys(res.data)[0]] == false) {
            this.messageService.warning('当前标准化模式对应的数据表不存在，请务必先刷相关表的脚本！');
          }
        })
        this.router.navigateByUrl('pages/collection-management');
      },
        reject => {
          switch (reject.error[0].errorcode) {
            case 7: {
              // this.errInfo = this.errArrayInfo['general.login.accountExpired'];
              break;
            }
            case 11: {
              // this.errInfo = this.errArrayInfo['general.login.accountLocked'];
              break;
            }
            case 12: {
              // this.errInfo = this.errArrayInfo['general.login.accountDisabled'];
              break;
            }
            case 13: {
              this.messageService.error(reject.error[0].error);
              break;
            }
            case 14: {
              // this.errInfo = this.errArrayInfo['general.login.forcehack'];
              break;
            }
            case 15: {
              // const second = data.error[0].error.split(':')[1];
              // this.errInfo = this.errArrayInfo['general.login.freezeAccount_1']
              //     + second + this.errArrayInfo['general.login.freezeAccount_2'];
              break;
            }
            case 3: {
              this.messageService.error(reject.error[0].error);
              break;
            }
            default: {
              // this.errInfo = this.errArrayInfo['general.login.errInfo'];
            }
          }
        }).catch(error => {
          console.log(error.message)
        });
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  getLoginData(): any {
    const data = {
      url: 'login',
      account: this.validateForm.controls['userName'].value.trim(),
      password: this.validateForm.controls['password'].value
    };
    return data;
  }

  constructor(injector: Injector,
    private fb: FormBuilder,
    private platformLocation: PlatformLocation,
    private loginService: LoginService
  ) {
    super(injector)
  }

  protected onInit(): void {
    document.title = 'SiteWeb6 配置工具'
    const tabElement = document.getElementById('tab-icon');
    if (tabElement) {
      tabElement.setAttribute('href', '/assets/images/icon2.png');
    }
    this.validateForm = this.fb.group({
      userName: [null, [Validators.required]],
      password: [null, [Validators.required]],
      remember: [true]
    });

    this.platformLocation.onPopState((res: any) => {
      if (res.target.window.location.href.indexOf("signin") != -1) {
        window.location.reload();
      }
    })
  }
}
