import { Injectable } from '@angular/core';
import { RestfulService } from '@core/services/restful.service';
import { RestfulResponse } from '@core/types/restful';

@Injectable({
  providedIn: 'root'
})
export class CoreApiService extends RestfulService {
  /**
   * 上传文件
   * @param file File文件对象
   * @param type 上传的类型，存放的目录， 如json\images\等。
   * @returns
   */
  public async uploadFile(file: File, type: string): Promise<RestfulResponse<string>> {
    const upData = new FormData();
    upData.append('type', type);
    upData.append('file', file);
    return this.post('/api/core/v1/upload/file', upData, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    });
  }

}
