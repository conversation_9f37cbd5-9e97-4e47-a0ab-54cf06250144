import { RouteReuseStrategy, ActivatedRouteSnapshot, DetachedRouteHandle } from '@angular/router';

export class SimpleReuseStrategy implements RouteReuseStrategy {
  public static routeSnapshots: { [key: string]: DetachedRouteHandle } = {};

  public shouldDetach(route: ActivatedRouteSnapshot): boolean {
    return Boolean(route.data.keepAlive);
  }
  public store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
    const url = this.getFullRouteUrl(route);
    SimpleReuseStrategy.routeSnapshots[url] = handle;
  }
  public shouldAttach(route: ActivatedRouteSnapshot): boolean {
    const url = this.getFullRouteUrl(route);
    return !!SimpleReuseStrategy.routeSnapshots[url];
  }
  public retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
    const url = this.getFullRouteUrl(route);
    return route.routeConfig ? SimpleReuseStrategy.routeSnapshots[url] : null;
  }
  public shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    return future.routeConfig === curr.routeConfig &&
      JSON.stringify(future.params) === JSON.stringify(curr.params);
  }

  private getFullRouteUrl(route: ActivatedRouteSnapshot): string {
    return this.getFullRouteUrlPaths(route).filter(Boolean).join('/').replace(/\//g, '_');
  }

  private getFullRouteUrlPaths(route: ActivatedRouteSnapshot): string[] {
    const paths = this.getRouteUrlPaths(route);
    return route.parent
      ? [...this.getFullRouteUrlPaths(route.parent), ...paths]
      : paths;
  }

  private getRouteUrlPaths(route: ActivatedRouteSnapshot): string[] {
    return route.url.map(urlSegment => urlSegment.path);
  }

  public static deleteRouteCache(url: string): void {
    if (SimpleReuseStrategy.routeSnapshots[url]) {
      let handle: any = SimpleReuseStrategy.routeSnapshots[url];
      try {
        handle.componentRef.destroy();
        if (handle.componentRef.instance) {
          handle.componentRef.instance = null;
        }
        handle.componentRef = null;
        delete SimpleReuseStrategy.routeSnapshots[url];
      } catch (e) {
        handle = null
      }
    }
  }
}
