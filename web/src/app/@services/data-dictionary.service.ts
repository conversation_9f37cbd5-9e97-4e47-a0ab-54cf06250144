import { Injectable } from '@angular/core'
import { RestfulService } from '@core/services/restful.service'

@Injectable()
export class DataDictionaryService extends RestfulService {

    /**
    * 获取数据字典项
    * 信号种类 17
    * 信号分类 18
    * 数据属性 21
    * 通道类型 22
    * 驱动模板类型 23
    * 服务器类型列表 58
    * 服务器所属中心 62
    * 数据类型 70
    * 字典项类型 153
    */
    public async getDataItems(entryId: number): Promise<[]> {
        const res = await this.get<[]>(`api/config/dataitems?entryId=${entryId}`);
        return res.data;
    }
}