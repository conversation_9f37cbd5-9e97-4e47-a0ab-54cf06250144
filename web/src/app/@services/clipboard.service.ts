import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ClipboardService {
  /**
   * 清空剪贴板 - 优先使用现代 API（navigator.clipboard）
   */
  clearClipboard() {
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      navigator.clipboard.writeText(' ').then(() => {
        console.log('剪贴板已清空 (navigator.clipboard)');
      }).catch(err => {
        console.error('剪贴板清空失败，尝试兼容方法', err);
        this.clearClipboardFallback();
      });
    } else {
      this.clearClipboardFallback();
    }
  }

  /**
   * 兼容方法：使用 textarea + document.execCommand
   */
  private clearClipboardFallback() {
    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    textarea.value = ' ';  // 赋值一个空格
    textarea.select();
    document.execCommand('copy'); // 执行复制
    document.body.removeChild(textarea);
    console.log('剪贴板已清空 (execCommand 兼容模式)');
  }
}
