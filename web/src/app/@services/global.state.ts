import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable()
export class GlobalState {
  private static _data = new Subject<Object>();
  private static _dataStream$ = GlobalState._data.asObservable();

  private static _subscriptions: Map<string, Array<Function>> = new Map<
    string,
    Array<Function>
  >();

  constructor() {
    GlobalState._dataStream$.subscribe(data => this._onEvent(data));
  }

  notifyDataChanged(event: string, value?: any) {
    if (value) {
      let current: any = GlobalState._data[event as keyof Subject<object>];
      if (current !== value) {
        current = value;
 
        GlobalState._data.next({
          event: event,
          data: current,
        });
      }
    } else {
      GlobalState._data.next({ event: event });
    }
  }

  notifyEvent(event: string, value?: any) {
    if (value) {
      let current: any = GlobalState._data[event as keyof Subject<object>];
      current = value;
 
      GlobalState._data.next({
        event: event,
        data: current,
      });
    }
  }

  subscribe(event: string, callback: Function) {
    const subscribers = GlobalState._subscriptions.get(event) || [];
    subscribers.push(callback);

    GlobalState._subscriptions.set(event, subscribers);
  }

  unSubscribe(event: string) {
    GlobalState._subscriptions.delete(event);
  }

  _onEvent(data: any) {
    const subscribers = GlobalState._subscriptions.get(data['event']) || [];

    subscribers.forEach(callback => {
      callback.call(null, data['data']);
    });
  }
}
