import { NgModule, NgModuleFactory } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ErrorComponent } from './@core/components/error/error.component';
import { NotFoundComponent } from './@core/components/notfound/not-found.component';
import { AuthGuardService } from './@core/services/auth-guard.service';
import { LayoutComponent } from './layout/layout.component';
import { LoginComponent } from './login/login.component';


const routes: Routes = [
  {
    path: '',
    redirectTo: 'signin',
    pathMatch: 'full'
  },
  {
    path: 'signin',
    title: 'not found',
    data: { keepAlive: false },
    component: LoginComponent,
  },
  {
    path: 'notfound',
    title: 'not found',
    component: NotFoundComponent,
    // canActivate: [AuthGuardService]
  },
  {
    path: 'error',
    title: 'error',
    component: ErrorComponent,
  },
  {
    path: 'pages',
    data: { keepAlive: true },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    loadChildren: async (): Promise<any> => import('./pages/pages.module').then(m => m.PagesModule),
    // canActivate: [AuthGuardService]
  },
  // {
  //   path: 'dashboard',
  //   title: 'dashboard',
  //   // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //   loadChildren: async (): Promise<any> => import('./@dashboard/dashboard.module').then(m => m.DashboardModule),
  // },
  // {
  //   path: '',
  //   redirectTo: 'pages/level',
  //   pathMatch: 'full'
  // },
  // {
  //   path: '',
  //   component: LayoutComponent,
  //   children: [
  //     // { path: '', redirectTo: '/home', pathMatch: 'full' },
  //     { path: 'home', component: HomeComponent, canActivate: [AuthGuardService], data: { title: '首页' } },
  //     { path: 'user', component: UserComponent, canActivate: [AuthGuardService], data: { title: '用户管理' } }
  //   ]
  // },
  // {
  //   path: '**',
  //   component: NotFoundComponent,
  //   //  canActivate: [AuthGuardService]
  // },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { useHash: true })
  ],
  exports: [RouterModule],
  // providers: [
  //   {
  //     provide: WidgetSchemaService,
  //   }
  // ]
})
export class AppRoutingModule { }
