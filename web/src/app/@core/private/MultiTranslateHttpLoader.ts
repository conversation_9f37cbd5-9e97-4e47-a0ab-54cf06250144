import { HttpClient } from '@angular/common/http';
import { TranslateLoader } from '@ngx-translate/core';
import { Observable } from 'rxjs';


export class MultiTranslateHttpLoader implements TranslateLoader {
    public constructor(private http: HttpClient, private iz8nPath: string) { }

    public getTranslation(lang: string): Observable<any> {
        const url = `${this.iz8nPath}/${lang}.json`;
        return this.http.get(url, {}).pipe();
    }
}
