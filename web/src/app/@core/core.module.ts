import { APP_BOOTSTRAP_LISTENER, APP_INITIALIZER, ComponentRef, <PERSON>rror<PERSON><PERSON><PERSON>, Injector, ModuleWithProviders, NgModule, Optional, Provider, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
// import { MatNativeDateModule, MAT_RIPPLE_GLOBAL_OPTIONS } from '@angular/material/core';
// import { NbAuthModule } from '@nebular/auth';
// import { NbSecurityModule, NbRoleProvider } from '@nebular/security';


import { RestfulService } from './services/restful.service';
import { AuthGuardService } from './services/auth-guard.service';
import { DialogService } from './services/dialog.service';
import { AccountService } from './services/account.service';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { RouterModule } from '@angular/router';
import { LoginPageComponent } from './components/login/loginpage.component';
import { DefaultPipe } from './pipes/default.pipe';
import { TranslatorPipe } from './pipes/translator.pipe';
import { DocumentTitleService } from './services/document-title.service';
import { NetWorkService } from './services/network.service';
import { NotFoundComponent } from './components/notfound/not-found.component';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ThemeService } from './services/theme.service';
import { BootstrapService } from './services/bootstrap.service';
import { ErrorComponent } from './components/error/error.component';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { MenuService } from './services/menu.service';
import { HoverDirective } from './directives/hover.directive';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { GlobalErrorHandler } from './private/GlobalErrorHandler';
import { NzNotificationModule, NzNotificationService } from 'ng-zorro-antd/notification';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { UnSelectedDirective } from './directives/unselected.directive';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { EVENT_MANAGER_PLUGINS } from '@angular/platform-browser';
import { OutSideEventPluginService } from './services/outside-event.plugin.service';
import { SessionService } from './services/session.service';
import { EventBusService } from './services/event-bus.service';
import { AngularSplitModule } from 'angular-split';
import { CollapseComponent } from './components/ngx-collapse/ngx-collapse.component';
import { TimerPoolService } from './services/timer-pool.service';
import { NotificationService } from './services/notification.service';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpHandlerInterceptor, } from './interceptors/http-handler.interceptor';
import { IconFontService } from './services/iconfont.service';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { WindowComponent } from './components/basic/window.component';
import { PortalModule } from '@angular/cdk/portal';
import { SafePipe } from './pipes/safe.pipe';
import { ClipboardService } from './services/clipboard.service';
import { ClipboardModule } from 'ngx-clipboard';
import { DisableFocusDirective } from './directives/disable-focus.directive';
import { IconFontComponent } from './components/icon/iconfont.component';
import { SvgViewComponent } from './components/svg-view/svgview.component';
import { DynamicCompilerService } from './services/dynamic-compiler.service';
import { ThumbPipe } from './pipes/thumb.pipe';
import { MediaComponent } from './components/media/media.component';
import { NzTreeModule } from 'ng-zorro-antd/tree';



const EXPORT_PIPES: Provider[] = [
  DefaultPipe,
  TranslatorPipe,
  SafePipe,
  ThumbPipe
];


const EXPORT_DIRECTIVES: Provider[] = [
  HoverDirective,
  UnSelectedDirective,
  DisableFocusDirective
];


/**
 * EXPORT CONPONENTS
 */
const EXPORT_COMPONENTS = [
  WindowComponent,
  LoginPageComponent,
  NotFoundComponent,
  ErrorComponent,
  CollapseComponent,
  IconFontComponent,
  SvgViewComponent,
  MediaComponent,
];


/**
 * custom providers services
 */
const PROVIDERS: Provider[] = [
  SessionService,
  DocumentTitleService,
  RestfulService,
  AuthGuardService,
  DialogService,
  AccountService,
  NetWorkService,
  ThemeService,
  BootstrapService,
  MenuService,
  NzDrawerService,
  EventBusService,
  TimerPoolService,
  NotificationService,
  IconFontService,
  ClipboardService,
  DynamicCompilerService
];





@NgModule({
  imports: [
    CommonModule,
    // BrowserModule,
    ClipboardModule,
    HttpClientModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
    PortalModule,
    NzIconModule,
    NzMenuModule,
    NzLayoutModule,
    NzGridModule,
    NzInputModule,
    NzFormModule,
    NzModalModule,
    NzSpaceModule,
    NzButtonModule,
    NzFormModule,
    NzNotificationModule,
    NzMessageModule,
    NzAvatarModule,
    NzPopoverModule,
    NzTreeModule,
    AngularSplitModule,
    NzCheckboxModule,

  ],
  exports: [ // 导出 的组件 服务 模块等
    // ... modules
    EXPORT_COMPONENTS,
    EXPORT_DIRECTIVES,
    EXPORT_PIPES
  ],
  declarations: [
    EXPORT_COMPONENTS,
    EXPORT_DIRECTIVES,
    EXPORT_PIPES,

  ]
})

export class CoreModule {
  public constructor(@Optional() @SkipSelf() parentModule: CoreModule, private themeService: ThemeService, private documentTitleService: DocumentTitleService, private networkService: NetWorkService) { }


  /**
   * 一次性加载单例服务，
   * 仅从 appModule 中 引入一次？
   * 把 单例服务 都放进来?
   * forRoot（） 将用于注入应用程序顶部的提供程序。每次调用组件和指令时，都会创建一个新实例。
   * 当您在应用程序的根目录下提及相同实例时（使用 forRoot（）），只会创建一个实例。
   * 首先，我们需要了解注入的提供程序与注入的组件和指令有一些区别。
   * 使用 @Injectable 批注类时，调用时只会创建此类的一个实例，并在整个应用程序中共享。
   * 为此，我们必须在 NgModule 中导入类时添加 this（forRoot（）） 方法。
   * https://stackoverflow.com/questions/47524011/what-is-purpose-of-using-forroot-in-ngmodule
   * @returns
   */
  public static forRoot(): ModuleWithProviders<CoreModule> {
    return {
      ngModule: CoreModule,
      providers: [
        ...PROVIDERS,
        {
          provide: APP_BOOTSTRAP_LISTENER,
          useFactory: BootstrapService.BootstrapFactory,
          deps: [BootstrapService],
          multi: true
        },
        {
          provide: APP_INITIALIZER,
          useFactory: BootstrapService.InitializerFactory,
          deps: [BootstrapService],
          multi: true
        },
        {
          provide: ErrorHandler,
          useClass: GlobalErrorHandler,
          deps: [NzNotificationService]
        },
        {
          provide: EVENT_MANAGER_PLUGINS,
          useClass: OutSideEventPluginService,
          multi: true
        },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: HttpHandlerInterceptor,
          multi: true
        },


      ]
    };
  }
}
