import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { BaseDirective } from './base.directive';

@Directive({
    selector: '[disable-focus]'
})
export class DisableFocusDirective extends BaseDirective {

    @Input() public to?: HTMLElement;

    protected onInit(): void {

    }

    @HostListener('mouseup')
    public onMouseLeave(): void {
        this.element.blur();
        if(this.to) this.to.focus();
    }

}
