import { Component, Injector, Input, Output, inject } from '@angular/core';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { GenericComponent } from './generic.component';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';



@Component({
  selector: 'ngx-generic-drawer',
  template: '<ng-container #view></ng-container>'
})
export class GenericDrawerComponent<TInput, TOut> extends GenericComponent {
  @Input()
  public readonly input: TInput = inject(NZ_MODAL_DATA);
  @Output()
  public output: TOut | undefined;

  protected readonly drawerRef: NzDrawerRef<TOut>;

  public constructor(injector: Injector) {
    super(injector);
    this.drawerRef = injector.get(NzDrawerRef);
  }

  /**
   * close drawer and result value
   * @param value
   */
  public close(value?: TOut): void {
    this.output = value;
    this.drawerRef.close(this.output);
  }
}
