import { Component, Injector, Input, SimpleChanges } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';

import { GenericComponent } from '../basic/generic.component';


@Component({
  selector: 'svg-view',
  styleUrls: ['./svgview.component.less'],
  templateUrl: './svgview.component.html',
})
export class SvgViewComponent extends GenericComponent {

  @Input()
  public src: string = '';


  @Input()
  public width: string = '';

  @Input()
  public height: string = '';



  public svgTemplate: SafeHtml = '';

  public constructor(injector: Injector) {
    super(injector);
  }


  protected onInit(): void {
    this.refresh();
  }

  public ngOnChanges(changes: SimpleChanges): void {
    const src = changes['src'];
    if (src && !src.firstChange) {
      this.src = src.currentValue;
      this.refresh();
    }
  }





  public async refresh(): Promise<void> {
    if (this.src) {
      const response = await fetch(this.src);
      this.svgTemplate = this.trustHTML(await response.text());
    } else {
      this.svgTemplate = '';
    }
  }


}
