@import 'mixin.less';


.themeMixin( {
    :host {
      .pwd-imput {
        width: 300px;
        color: @text-color;
        background-color: fade(@text-color, 10%) !important;
        border-color: fade(@text-color, 20%) !important;
      }

      .ts-2 {
        text-shadow: 2px 2px 2px @text-color;
      }

      .ts-5 {
        text-shadow: 5px 5px 5px @text-color;
      }

      button {
        border-color: fade(@text-color, 10%);
        background-color: fade(@text-color, 10%);
      }

      nz-avatar {
        background-color: fade(@text-color, 30%);
      }

    }
  }

);



:host {
  .login-form {
    max-width: 300px;
  }

  .login-form-margin {
    margin-bottom: 16px;
  }

  .login-form-forgot {
    float: right;
  }

  .login-form-button {
    width: 100%;
  }

  nz-input-group{

    border: rgba(255, 255, 255, 0.3) solid 1px;
  }







  .background {
    position: absolute;
    width: 100%;
    height: 100%;
    // background-image: url('/assets/images/team.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .blur {
    filter: blur(10px);
  }


  .mb-30 {
    margin-bottom: 65px;
  }


  .pos-center {
    // background-color: transparent !important;
    border: none;
    width: 100%;
    height: 300px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }


  .no-select {
    user-select: none;
  }

  .title-label {
    display: block;
    font-size: 65px;
    text-align: center;
    width: 100%;
    height: 100px;
    line-height: 100px;
  }


  .ts-5 {
    text-shadow: 5px 5px 5px #ffffff;
  }


  .login-form {
    width: 350px;
    margin: auto;
  }


  nz-avatar {
    position: absolute;
    bottom: 48px;
    right: 48px;
    font-size: 30px;
    vertical-align: middle;
    text-align: center;
  }


  ::ng-deep{
    .anticon svg {
      margin-top: -4px !important;
    }

    .anticon{
      cursor: pointer;
    }


  }


}