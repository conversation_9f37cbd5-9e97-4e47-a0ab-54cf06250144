import { Injectable, Injector } from '@angular/core';
import { RouteConfigure } from '../models/route-configure';
import { RestfulService } from './restful.service';


@Injectable({
    providedIn: 'root',
})
export class MenuService extends RestfulService {

    public isCollapsed: boolean = false;

    /**
     *
     */
    protected onInit(): void {
        this.isCollapsed = false;
    }

    public toggleCollapsed(): void {
        this.isCollapsed = !this.isCollapsed;
    }


    public async load(): Promise<void> {
        // const res = await this.get<RouteConfigure[]>("/api/menutree");
        // this.menus = res.data
    }



    public menus: RouteConfigure[] = [
        {
            title: 'Dashboard Demo',
            icon: 'grace-home',
            path: 'dashboard',
        },
        {
            title: '自定义组件',
            icon: 'grace-puzzle-piece-plugin',
            opened: true,
            selected: false,
            children: [
                {
                    title: '图片组件',
                    icon: 'grace-pic-fill',
                    path: 'examples/image',
                },
                {
                    title: '按钮组件',
                    icon: 'grace-anniu3',
                    path: 'examples/button',
                },
                {
                    title: '文本组件',
                    icon: 'grace-wenzi',
                    path: 'examples/text',
                },
                {
                    title: '信号组件',
                    icon: 'grace-radioguangbo',
                    path: 'examples/signal',
                },
                {
                    title: '图表组件',
                    icon: 'grace-baobiaotongji',
                    path: 'examples/charts',
                }
            ]
        },
        {
            title: '属性组件',
            icon: 'grace-plugin3',
            opened: true,
            selected: false,
            children: [
                {
                    title: '图片选择器',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-image',
                },
                {
                    title: '按钮属性',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-button',
                },
                {
                    title: '按钮属性工厂',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-button-factory',
                },
                {
                    title: '图标选择',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-icons',
                },
                {
                    title: '下拉选择框',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-select',
                },
                {
                    title: '文本输入框',
                    icon: 'grace-a-Basic_setup_a',
                    path: 'examples/property-text',
                }
            ]
        },
        {
            title: '属性配置',
            icon: 'grace-zu',
            path: 'examples/propertys',
        },
        {
            title: '使用方式',
            icon: 'grace-zu',
            path: 'examples/used',
        },
        {
            title: '关于',
            icon: 'grace-about',
            path: 'about',
        }
    ];


}
