import { Compiler, Component, ComponentRef, Injectable, Injector, NgModule, NgModuleRef, ViewChild, ViewContainerRef } from '@angular/core';

// https://github.com/phalgunv/Angular-Dynamic-Component-From-String/tree/main


/**
 *
 * @deprecated 已废弃的服务
 * @export
 * @class DynamicCompilerService
 */
@Injectable({
  providedIn: 'root'
})
export class DynamicCompilerService {
  @ViewChild('vc', { read: ViewContainerRef }) public vc!: ViewContainerRef;
  private cmpRef!: ComponentRef<any>;
  /**
   *
   */
  public constructor(private compiler: Compiler,
    private injector: Injector,
    private moduleRef: NgModuleRef<any>) {
  }




  // Here we create the component.
  private async createComponentFromRaw(template: string): Promise<void> {
    // Let's say your template looks like `<h2><some-component [data]="data"></some-component>`
    // As you see, it has an (existing) angular component `some-component` and it injects it [data]

    // Now we create a new component. It has that template, and we can even give it data.
    const styles: never[] = [];

    const TemplateConstructorFunction = function TmpCmpconstructor(
      this: any
    ): any {
      this.data = { some: 'data' };
      this.getX = (): string => 'X';

      this.clickMe = (i: any): void => {
        alert(i);
      };
    };
    const tmpCmp: any = Component({ template, styles })(
      new (TemplateConstructorFunction as any)().constructor
    );

    // Now, also create a dynamic module.
    const tmpModule = NgModule({
      // imports: [CommonModule],
      // declarations: [tmpCmp, DynamicTemplateComponent],
      // providers: [] - e.g. if your dynamic component needs any service, provide it here.
    })(class { });

    // Now compile this module and component, and inject it into that #vc in your current component template.
    const factories = await this.compiler.compileModuleAndAllComponentsAsync(tmpModule)
    const f = factories.componentFactories[0];
    this.cmpRef = f.create(this.injector, [], null, this.moduleRef);
    this.cmpRef.instance.name = 'my-dynamic-component';
    this.vc.insert(this.cmpRef.hostView);

  }


}
