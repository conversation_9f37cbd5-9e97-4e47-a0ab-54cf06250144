

import { HttpClient, HttpContext, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable, Injector } from '@angular/core';
import { RestfulResponse } from '@core/types/restful';





@Injectable({
    providedIn: 'root'
})
export class RestfulService {


    /**
     *
     */
    public constructor(protected client: HttpClient) {
        this.onInit()
    }


    protected onInit(): void {

    }







    public async get<T>(url: string, options?: {
        headers?: HttpHeaders | {
            [header: string]: string | string[];
        };
        context?: HttpContext;
        observe?: 'body';
        params?: HttpParams | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
        };
        reportProgress?: boolean;
        responseType?: 'json';
        withCredentials?: boolean;
    }): Promise<RestfulResponse<T>> {
        return new Promise<RestfulResponse<T>>((resolve, reject) => {
            this.client.get<RestfulResponse<T>>(url, options).subscribe({ next: resolve, error: reject });
        });
    }

    public async put<T>(url: string, body: Object, options?: {
        headers?: HttpHeaders | {
            [header: string]: string | string[];
        };
        context?: HttpContext;
        observe?: 'body';
        params?: HttpParams | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
        };
        reportProgress?: boolean;
        responseType?: 'json';
        withCredentials?: boolean;
    }): Promise<RestfulResponse<T>> {
        return new Promise<RestfulResponse<T>>((resolve, reject) => {
            this.client.put<RestfulResponse<T>>(url, body, options).subscribe({ next: resolve, error: reject });
        });
    }



    public async post<T>(url: string, body: Object, options?: {
        headers?: HttpHeaders | {
            [header: string]: string | string[];
        };
        context?: HttpContext;
        observe?: 'body';
        params?: HttpParams | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
        };
        reportProgress?: boolean;
        responseType?: 'json';
        withCredentials?: boolean;
    }): Promise<RestfulResponse<T>> {
        return new Promise<RestfulResponse<T>>((resolve, reject) => {
            this.client.post<RestfulResponse<T>>(url, body, options).subscribe({ next: resolve, error: reject });
        });
    }

    public async delete<T>(url: string, options: {
        headers?: HttpHeaders | {
            [header: string]: string | string[];
        };
        observe: 'response';
        context?: HttpContext;
        params?: HttpParams | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
        };
        reportProgress?: boolean;
        responseType?: 'json';
        withCredentials?: boolean;
        body?: any | null;
    }): Promise<RestfulResponse<T>> {
        return new Promise<RestfulResponse<T>>((resolve, reject) => {
            this.client.delete<RestfulResponse<T>>(url, options)
                .subscribe({ next: resolve as any, error: reject });
        });
    }



}
