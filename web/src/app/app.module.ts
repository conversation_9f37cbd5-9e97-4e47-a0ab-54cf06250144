/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ApplicationRef, NgModule, NgZone } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { LOCALE_ID } from '@angular/core';
import { CommonModule, registerLocaleData } from '@angular/common';
import { NZ_I18N } from 'ng-zorro-antd/i18n';
import { zh_CN } from 'ng-zorro-antd/i18n';
import zh from '@angular/common/locales/zh';
import { BootstrapService } from '@core/services/bootstrap.service';
import { NetWorkService } from '@core/services/network.service';
import { CoreModule } from '@core/core.module';
import { ComponentsModule } from '@components/components.module';
import { DocumentTitleService } from '@core/services/document-title.service';
import { ThemeService } from '@core/services/theme.service';
import { NzIconModule, NzIconService } from 'ng-zorro-antd/icon';
import { SessionService } from '@core/services/session.service';
import { NgxEchartsModule } from 'ngx-echarts';
import { MenuService } from '@core/services/menu.service';
import { APP_BASE_HREF } from '@angular/common';
import { CoreApiService } from './@services/core-api-service';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from '@core/private/MultiTranslateHttpLoader';
import { LayoutComponent } from './layout/layout.component';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { IconsProviderModule } from './icons-provider.module';
import { NzContextMenuService, NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { SimpleReuseStrategy } from './@services/route-strategy.sercice';
import { RouteReuseStrategy } from '@angular/router';
import { IconModule } from 'ng-devui';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { LoginModule } from './login/login.module';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

registerLocaleData(zh);



@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent
  ],
  imports: [
    CommonModule,
    BrowserModule,
    CoreModule.forRoot(),
    ComponentsModule.forRoot(),
    LoginModule,
    IconsProviderModule,
    IconModule,
    AppRoutingModule,
    NzLayoutModule,
    NzAvatarModule,
    NzMenuModule,
    NzTabsModule,
    NzTagModule,
    BrowserAnimationsModule,
    HttpClientModule,
    NzDropDownModule,
    NgxEchartsModule.forRoot({
      echarts: import('echarts')
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) =>
          new MultiTranslateHttpLoader(http, './assets/i18n'),
        deps: [HttpClient]
      }
    })
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'zh-CN' }, // replace "de-at" with your locale
    { provide: NZ_I18N, useValue: zh_CN },
    { provide: APP_BASE_HREF, useValue: './' },
    { provide: RouteReuseStrategy, useClass: SimpleReuseStrategy },
    NzContextMenuService,
    CoreApiService
  ],
  bootstrap: [AppComponent]
})
export class AppModule {

  public static translateService: TranslateService | null;
  public static themeService: ThemeService | null;



  public constructor(private bootstrapService: BootstrapService,
    public translate: TranslateService,
    private netWorkService: NetWorkService,
    private sessionService: SessionService,
    private http: HttpClient,
    private zone: NgZone,
    private appRef: ApplicationRef,
    private themeService: ThemeService,
    private documentTitleService: DocumentTitleService,
    private networkService: NetWorkService,
    private iconService: NzIconService,
    public menuService: MenuService) {
    AppModule.translateService = translate;
    AppModule.themeService = themeService;
    translate.addLangs(['zh', 'en']);
    translate.use(window.weboot?.sharedData.language.split('-')[0] ?? 'zh');
    themeService.registerTheme({ deep_blue: '深蓝主题', blue_white: '蓝白主题' });
    themeService.changeTheme(window.weboot?.sharedData.theme ?? 'blue_white')

    // loading
    bootstrapService.loadingElement = document.getElementById('global-spinner');
    bootstrapService.runAtBootstrap(this.init, this);
    // title service
    documentTitleService.defaultTitle = { value: 'Administrator System', needsTranslator: false };
    documentTitleService.register();
    // register iconfont
    this.iconService.fetchFromIconfont({
      scriptUrl: 'assets/fonts/iconfont.js'
    });

  }


  private async init(): Promise<void> {
  }

}
