@import "mixin";

// .themeMixin({
//   :host {
//     .sidebar-logo {
//       background: @sidebar-logo-bg-color;
//       h1 {
//         color: @header-text-color;
//       }
//     }

//     .app-header {
//       background: @layout-header-background;
//     }

//     .header-trigger {
//       color: @header-text-color;
//     }

//     .inner-content {
//       background: @layout-trigger-background;
//     }

//     .trigger:hover {
//       color: #1890ff;
//     }
//   }
// });

:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  .app-layout {
    height: 100vh;

    .menu-sidebar {
      position: relative;
      z-index: 10;
      min-height: 100vh;
      box-shadow: 2px 0 6px rgba(0, 21, 41, .35);

      ::ng-deep .ant-menu-inline {
        border-right: none;
        height: calc(100% - 47px);
        overflow: hidden;
        overflow-y: auto;
      }

      ::ng-deep .ant-menu-vertical-left {
        border-right-width: 0 !important;
      }
    }

    .ant-menu {
      // font-size: 16px;
    }

    /deep/.ant-menu.ant-menu-inline-collapsed>.ant-menu-item {
      display: flex;
      align-items: center;
    }

    .ant-menu.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
      display: flex;
      align-items: center;
    }

    .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
      padding: 0;
    }

    .sidebar-logo {
      position: relative;
      height: 47px;
      padding-left: 24px;
      overflow: hidden;
      transition: all .3s;
      color: #fff;
      font-size: 20px;
      background-color: #0053A6;
      display: flex;
      align-items: center;

      span {
        padding-left: 10px;
        white-space: nowrap;
      }
    }

    .sidebar-logo img {
      display: inline-block;
      height: 34px;
      width: 34px;
      vertical-align: middle;
    }

    .sidebar-logo h1 {
      display: inline-block;
      margin: 0 0 0 20px;
      font-weight: 600;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }

    .menu-btn {
      margin-left: 10px;
    }

    .tab-remove-btn {
      margin-left: 8px;
      margin-right: -4px;
      color: #fff;
    }

    .tab-remove-btn:hover {
      color: #000000d9;
    }

    .ant-menu-item-selected {
      background-color: #C4E2FF;
    }

    .ant-menu-item {
      border-bottom: none;
    }
  }

  .ant-menu-item-selected {
    color: #007fff;
  }

  nz-header {
    padding: 0;
    width: 100%;
    height: 87px;
    line-height: 45px;
    z-index: 2;
    color: #fff;
    background-color: #0053A6;

    .app-header {
      position: relative;
      height: 100%;
      padding: 0;
      box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

      .app-navbar {
        .header-trigger {
          padding: 0 24px;
          font-size: 20px;
          cursor: pointer;
          transition: all .3s, padding 0s;
        }
      }

      .app-tags {
        overflow: auto;
        background-color: #fff;

        .tags-content {
          background-color: #fff;
          height: 40px;
          display: flex;
          align-items: center;
          padding: 0 10px;

          .tag {
            height: 38px;
            line-height: 38px;
            padding: 0 18px;
            cursor: pointer;
            background-color: #fafafa;
          }

          .active {
            color: #007fff;
            border-bottom-color: #007fff;
            border-bottom-width: 2px;

            /deep/.ant-tag-close-icon {
              color: #007fff;
            }
          }
        }
      }
    }
  }

  nz-content {
    margin: 12px 15px;

    .inner-content {
      height: 100%;
      overflow: hidden;
    }
  }
}
