// https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
@import (multiple) '../../../node_modules/ng-zorro-antd/src/style/themes/default.less';
@import './base.less';


@white: #fff;
@component-background: @white;


@menu-border-bottom: solid 1px #DBDBDB;

@layout-sider-background: @white;
@layout-trigger-background: @white;
@layout-header-background: @white;

@menu-item-border-color: #DBDBDB;
@menu-item-active-bg : #a6e3ff;


@shadow-color: rgba(0, 0, 0, 0.15);

@scroll-width: 5px;
@scroll-height: 5px;
@scroll-background: #DBDBDB;
@scroll-thumb-radius: 2px;
@scroll-thumb-color: #535353;
@scroll-thumb-hover-color: #164a72;
@scroll-thumb-active-color: #157DCF;

@header-menu-divider: 1px solid rgba(0, 255, 0, 0.12);
@header-menu-divider-shadow: 0px 0px 10px 1px #d6d6d6;

// 主按钮active
@active-color: #007fff;
// hover状态
@hover-color: #26c9ff;
// 按钮默认状态
@button-normal-bg-color: #005096;
// 标题、强调文字、按钮文字
@care-color: #003366;

// Tabs
@tabs-tab-padding: 12px 0px;
@tabs-tab-active: @active-color;
@tabs-tab-hover: @hover-color;
@tabs-bottom-color: #0050964d;


// Tooltip max width
@tooltip-max-width: 250px;
@tooltip-color: @text-color;
@tooltip-bg: @component-background;
@tooltip-arrow-width: 5px;
@tooltip-distance: @tooltip-arrow-width - 1px + 4px;
@tooltip-arrow-color: @tooltip-bg;

//devui-table
@text-color-inverse: rgba(255, 255, 255, 1);
@table-selected: 0, 127, 255;
@color-text-paragraph-form: 191, 223, 255;
@table-selected: rgba(0, 127, 255, 1);
@background-color-split: #eee;
@color-text-paragraph-form: 51, 51, 51;
@primary-color-secondary: #ffe2d0;