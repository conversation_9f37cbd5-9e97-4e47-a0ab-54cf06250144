// https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less

// @font-family: <PERSON><PERSON>, sans-serif, "Microsoft Yahei UI", "Segoe UI",
//   -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;

@active-color : #007fff;
@tabs-tab-padding: 12px 0px;
@tabs-tab-active: '';
@tabs-tab-hover: '';
@tabs-bottom-color: '';
@button-primary-bg-color: '';
@button-primary-text-color: '';
@button-primary-hover-bg-color: '';
@button-primary-hover-text-color: '';
@button-primary-active-bg-color: '';
@button-primary-active-text-color: '';
@button-text-color: '';
@button-border-color: '';
@button-text-hover-color: '';
@button-border-hover-color: '';
@button-text-active-color: '';
@button-border-active-color: '';
@button-link-text-color: '';
@button-primary-disabled-bg-color: '';
@button-primary-disabled-text-color: '';
@modal-border-color: '';
@panel-header-bg-color: '';
@collapse-shadow-color: rgb(0, 0, 0);

// .ant-btn>.anticon {
//   line-height: 0px;
//   vertical-align: middle;
// }
body {
  overflow: hidden;
}

::selection {
  color: inherit !important;
}

.cm-editor {
  height: 100%;
}
// Tabs
.ant-tabs, .ant-tabs-content{
  height: 100%;
}
.ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap,
.ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-wrap {
  // padding: 0px 10px;
}

.ant-input-affix-wrapper {
  padding: 0 4px;
}


.ant-tabs-tab {
  padding: @tabs-tab-padding;
  margin-right: 4px !important;
  &:hover {
    color: @tabs-tab-hover;
  }
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: @tabs-tab-active;
}

.ant-tabs-ink-bar {
  background: @tabs-tab-active;
}

.ant-tabs-top>.ant-tabs-nav {
  margin-bottom: 8px !important;
  &::before {
    // border-bottom-color: @tabs-bottom-color;
  }
}

.ant-drawer-title {
  text-align: center;
}

// 次按钮
.ant-btn {
  color: @button-text-color;
  border-color: @button-border-color;
  border-radius: 4px;

  &:hover {
    color: @button-text-hover-color;
    border-color: @button-border-hover-color;
  }

  &:active {
    color: @button-text-active-color;
    border-color: @button-border-active-color;
  }
}

// 主按钮
.ant-btn-primary {
  color: @button-primary-text-color;
  background-color: @button-primary-bg-color;
  border-color: @button-primary-bg-color;

  &:hover {
    color: @button-primary-hover-text-color;
    background-color: @button-primary-hover-bg-color;
    border-color: @button-primary-hover-bg-color;
  }

  &:active {
    color: @button-primary-active-text-color;
    background-color: @button-primary-active-bg-color;
    border-color: @button-primary-active-bg-color;
  }
}

// Link按钮
.ant-btn-link {
  border: none;
  color: @button-link-text-color;
}

// Select
.ant-select-dropdown {
  background-color: @component-background;
  border: 1px solid @border-color-base;
}

.ant-select-item {
  padding: 5px 12px;
}

.ant-select-arrow {
  color: @text-color;
}

// Modal
.ant-modal-content {
  border: 1px solid @modal-border-color;
  background-color: @component-background;

  .ant-modal-header {
    background-color: transparent;
  }
}

// none select
.none-select {
  user-select: none;
}

.iconfont {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.svg-container {
  svg {
    width: 100%;
    height: 100%;
  }
}

.anticon {
  vertical-align: normal;
}

// icon button
.icon-button {
  cursor: pointer;
  color: @text-color;
}

.icon-button:hover {
  color: @primary-3;
}

.icon-button:active {
  color: @primary-5;
}

.ant-layout .ant-layout-content {
  position: relative;
}

.ant-layout .ant-layout-content>.ng-star-inserted {
  display: block;
  width: 100%;
  height: 100%;
  overflow: auto;
}

body {
  margin: 0;
  font-size: 14px;
  font-family: Roboto, "Segoe UI", Arial, sans-serif;
  font-weight: 400;
  line-height: 1.5715;
  font-feature-settings: "tnum", "tnum";
}

.cdk-overlay-connected-position-bounding-box {
  z-index: 9999999;
}

// side menu style
.ant-menu.ant-menu-inline-collapsed {
  width: 64px !important;
}

.ant-menu-submenu-popup>.ant-menu {
  background-color: @component-background;
}

.ant-menu-submenu-popup>.ant-menu>.nz-menu-item,
.ant-menu-item,
.ant-menu-submenu,
.ant-menu-submenu-title {
  // line-height: 0px !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  // border-bottom: solid 1px;
  border-color: @menu-item-border-color;
}

.ant-menu-item .ant-menu-item-icon,
.ant-menu-submenu-title .ant-menu-item-icon,
.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  min-width: 14px;
  font-size: 20px;
  transition: font-size 0.15s cubic-bezier(0.215, 0.61, 0.355, 1),
    margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), color 0.3s;
}

.ant-tooltip {
  user-select: none;
  pointer-events: none;
  max-width: none;
}

// fix side menu border-botton
.ant-menu-submenu.ant-menu-submenu-inline {
  border-bottom: solid 0px;
}

// drop down icon
.ant-dropdown-menu .anticon {
  margin-left: 15px;
  margin-right: 15px;
}

.ant-dropdown {
  min-width: 150px;
  user-select: none;

  .ant-dropdown-menu {
    border-width: 1px;
    border-style: solid;
    border-color: @border-color-base;

    .ant-dropdown-menu-item {
      line-height: 30px;

      &:hover {
        color: #007FFF;
        background-color: #C4E2FF;
      }

      &:active {
        color: #007FFF;
        background-color: #73B9FF;
      }
    }

    hr {
      margin: 3px 10px;
      border-top-width: 0;
      border-bottom-width: 1px;
      border-color: @border-color-base;
    }
  }
}

/* 全局滚动条样式 */
@scroll-width: 3px;
@scroll-height: 3px;
@scroll-background: #1d1d1d;
@scroll-thumb-radius: 2px;
@scroll-thumb-color: #535353;
@scroll-thumb-hover-color: #164a72;
@scroll-thumb-active-color: #157dcf;

/*滚动条整体样式*/
::-webkit-scrollbar {
  width: @scroll-width;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: @scroll-height;
}

/*滚动条里面小方块*/
::-webkit-scrollbar-thumb {
  border-radius: @scroll-thumb-radius;
  // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: @scroll-thumb-color;
}

/* 滚动条右下角边角*/
::-webkit-scrollbar-corner {
  background-color: @scroll-background;
}


/*滚动条里面小方块*/
::-webkit-scrollbar-thumb:hover {
  background: @scroll-thumb-hover-color;
}

/*滚动条里面小方块*/
::-webkit-scrollbar-thumb:active {
  background: @scroll-thumb-active-color;
}

/*滚动条里面轨道*/
::-webkit-scrollbar-track {
  // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  // border-radius: 5px;
  background: @scroll-background;
}

/* 去除input自动填充的背景色 */
input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  -webkit-text-fill-color: @text-color !important;
  transition: background-color 50000s ease-in-out 0s !important;
}

input {
  border: 1px solid @border-color-base;
}

// 页头 菜单的分割线
@header-menu-divider: 1px solid rgba(255, 255, 255, 0.12);
@header-menu-divider-shadow: 0px 0px 10px 1px #3a3e3e;

@layout-footer-padding: 10px 10px;
@layout-footer-background: @component-background;

nz-radio-group {
  user-select: none;
}

label {
  user-select: none;
}

.ant-modal-close-x {
  width: 48px;
  height: 40px;
  line-height: 40px;
}
.ant-upload.ant-upload-drag .ant-upload {
  padding: 0;
}
.ant-modal-header {
  padding: 10px 16px;
  height: 40px;
  line-height: 40px;
}
.ant-modal-title{
  div{
    width: calc(100% - 24px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
// Tooltip
.ant-tooltip-content>.ant-tooltip-arrow>.ant-tooltip-arrow-content {
  background-color: #108ee9;

  &::before {
    background: #108ee9;
  }
}

.ant-tooltip-content>.ant-tooltip-inner {
  background-color: #108ee9;
  // box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
